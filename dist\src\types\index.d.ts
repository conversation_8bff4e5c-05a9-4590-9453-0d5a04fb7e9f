export interface MonitorOptions {
    dsn: string;
    appName: string;
    appCode?: string;
    appVersion?: string;
    userUuid?: string;
    debug?: boolean;
    error?: boolean | ErrorConfig;
    performance?: boolean | PerformanceConfig;
    event?: boolean | EventConfig;
    http?: boolean | HttpConfig;
    pv?: boolean | PvConfig;
    maxEvents?: number;
    flushInterval?: number;
    sampleRate?: number;
    ignoreErrors?: Array<string | RegExp>;
    ignoreRequest?: Array<string | RegExp>;
    ext?: Record<string, any>;
    beforeSend?: (data: any) => any | false;
    afterSend?: (data: any) => void;
    customSend?: (data: SendData) => void | Promise<void>;
    baseInfoOnce?: boolean;
    separateBaseInfo?: boolean;
    record?: {
        enable: boolean;
        maxDuration?: number;
        sampleRate?: number;
        compress?: boolean;
        maskAllInputs?: boolean;
        blockSelector?: string;
    };
}
export interface ErrorConfig {
    enable?: boolean;
    server?: boolean;
}
export interface PerformanceConfig {
    enable?: boolean;
    firstResource?: boolean;
    server?: boolean;
}
export interface EventConfig {
    enable?: boolean;
    targetElements?: string[];
}
export interface HttpConfig {
    enable?: boolean;
}
export interface PvConfig {
    enable?: boolean;
}
export interface BaseInfo {
    clientHeight: number;
    clientWidth: number;
    colorDepth: number;
    pixelDepth: number;
    screenWidth: number;
    screenHeight: number;
    userUuid: string;
    sdkUserUuid: string;
    appName: string;
    appCode: string;
    appVersion: string;
    pageId: string;
    sessionId: string;
    sdkVersion: string;
    url: string;
    userAgent: string;
    timestamp: number;
    ext: Record<string, any>;
}
export interface EventData {
    eventId: string;
    type: string;
    data: any;
    timestamp: number;
    triggerTime?: number;
    duration?: number;
}
export interface SendData {
    baseInfo: BaseInfo;
    eventInfo: EventData[];
    sendTime: number;
}
export declare const SEND_ID: {
    readonly CODE: "code-error";
    readonly PROMISE: "promise-reject";
    readonly RESOURCE: "resource-error";
    readonly PERFORMANCE: "performance";
    readonly RESOURCE_PERF: "resource-perf";
    readonly CLICK: "click";
    readonly PV: "page-view";
    readonly SERVER: "server-error";
    readonly SERVER_PERF: "server-perf";
    readonly RECORD: "record";
};
export declare const EVENT_TYPES: {
    readonly ERROR: "error";
    readonly PERFORMANCE: "performance";
    readonly EVENT: "event";
    readonly HTTP: "http";
    readonly PV: "pv";
    readonly RECORD: "record";
};
export type AnyObj<T = any> = Record<string, T>;
export type VoidFun = (...args: any[]) => void;
export type AnyFun = (...args: any[]) => any;
//# sourceMappingURL=index.d.ts.map