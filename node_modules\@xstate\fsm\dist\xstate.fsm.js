var t,e;t=this,e=function(t){"use strict";var e;(e=t.InterpreterStatus||(t.InterpreterStatus={}))[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped";const n={type:"xstate.init"};function o(t){return void 0===t?[]:[].concat(t)}function i(t,e){return"string"==typeof(t="string"==typeof t&&e&&e[t]?e[t]:t)?{type:t}:"function"==typeof t?{type:t.name,exec:t}:t}function s(t){return e=>t===e}function r(t){return"string"==typeof t?{type:t}:t}function a(t,e){return{value:t,context:e,actions:[],changed:!1,matches:s(t)}}function c(t,e,n){let o=e,i=!1;return[t.filter(t=>{if("xstate.assign"===t.type){i=!0;let e=Object.assign({},o);return"function"==typeof t.assignment?e=t.assignment(o,n):Object.keys(t.assignment).forEach(i=>{e[i]="function"==typeof t.assignment[i]?t.assignment[i](o,n):t.assignment[i]}),o=e,!1}return!0}),o,i]}const u=(t,e)=>t.actions.forEach(({exec:n})=>n&&n(t.context,e));t.assign=function(t){return{type:"xstate.assign",assignment:t}},t.createMachine=function(t,e={}){const[u,f]=c(o(t.states[t.initial].entry).map(t=>i(t,e.actions)),t.context,n),p={config:t,_options:e,initialState:{value:t.initial,actions:u,context:f,matches:s(t.initial)},transition:(e,n)=>{const{value:u,context:f}="string"==typeof e?{value:e,context:t.context}:e,l=r(n),g=t.states[u];if(g.on){const e=o(g.on[l.type]);for(const n of e){if(void 0===n)return a(u,f);const{target:e,actions:r=[],cond:d=(()=>!0)}="string"==typeof n?{target:n}:n,y=void 0===e,x=null!=e?e:u,m=t.states[x];if(d(f,l)){const t=(y?o(r):[].concat(g.exit,r,m.entry).filter(t=>t)).map(t=>i(t,p._options.actions)),[n,a,d]=c(t,f,l),x=null!=e?e:u;return{value:x,context:a,actions:n,changed:e!==u||n.length>0||d,matches:s(x)}}}}return a(u,f)}};return p},t.interpret=function(e){let o=e.initialState,i=t.InterpreterStatus.NotStarted;const a=new Set,c={_machine:e,send:n=>{i===t.InterpreterStatus.Running&&(o=e.transition(o,n),u(o,r(n)),a.forEach(t=>t(o)))},subscribe:t=>(a.add(t),t(o),{unsubscribe:()=>a.delete(t)}),start:r=>{if(r){const t="object"==typeof r?r:{context:e.config.context,value:r};o={value:t.value,actions:[],context:t.context,matches:s(t.value)}}return i=t.InterpreterStatus.Running,u(o,n),c},stop:()=>(i=t.InterpreterStatus.Stopped,a.clear(),c),get state(){return o},get status(){return i}};return c},Object.defineProperty(t,"__esModule",{value:!0})},"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).XStateFSM={});
