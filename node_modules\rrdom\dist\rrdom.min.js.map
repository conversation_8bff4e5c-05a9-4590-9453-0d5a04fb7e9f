{"version": 3, "file": "rrdom.min.js", "sources": ["../../rrweb-snapshot/es/rrweb-snapshot.js", "../src/style.ts", "../src/document.ts", "../src/diff.ts", "../src/index.ts"], "sourcesContent": ["var NodeType;\r\n(function (NodeType) {\r\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\r\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\r\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\r\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\r\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\r\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\r\n})(NodeType || (NodeType = {}));\n\nfunction isElement(n) {\r\n    return n.nodeType === n.ELEMENT_NODE;\r\n}\r\nfunction isShadowRoot(n) {\r\n    var host = n === null || n === void 0 ? void 0 : n.host;\r\n    return Boolean((host === null || host === void 0 ? void 0 : host.shadowRoot) === n);\r\n}\r\nfunction isNativeShadowDom(shadowRoot) {\r\n    return Object.prototype.toString.call(shadowRoot) === '[object ShadowRoot]';\r\n}\r\nfunction fixBrowserCompatibilityIssuesInCSS(cssText) {\r\n    if (cssText.includes(' background-clip: text;') &&\r\n        !cssText.includes(' -webkit-background-clip: text;')) {\r\n        cssText = cssText.replace(' background-clip: text;', ' -webkit-background-clip: text; background-clip: text;');\r\n    }\r\n    return cssText;\r\n}\r\nfunction getCssRulesString(s) {\r\n    try {\r\n        var rules = s.rules || s.cssRules;\r\n        return rules\r\n            ? fixBrowserCompatibilityIssuesInCSS(Array.from(rules).map(getCssRuleString).join(''))\r\n            : null;\r\n    }\r\n    catch (error) {\r\n        return null;\r\n    }\r\n}\r\nfunction getCssRuleString(rule) {\r\n    var cssStringified = rule.cssText;\r\n    if (isCSSImportRule(rule)) {\r\n        try {\r\n            cssStringified = getCssRulesString(rule.styleSheet) || cssStringified;\r\n        }\r\n        catch (_a) {\r\n        }\r\n    }\r\n    return cssStringified;\r\n}\r\nfunction isCSSImportRule(rule) {\r\n    return 'styleSheet' in rule;\r\n}\r\nvar Mirror = (function () {\r\n    function Mirror() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n    Mirror.prototype.getId = function (n) {\r\n        var _a;\r\n        if (!n)\r\n            return -1;\r\n        var id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\r\n        return id !== null && id !== void 0 ? id : -1;\r\n    };\r\n    Mirror.prototype.getNode = function (id) {\r\n        return this.idNodeMap.get(id) || null;\r\n    };\r\n    Mirror.prototype.getIds = function () {\r\n        return Array.from(this.idNodeMap.keys());\r\n    };\r\n    Mirror.prototype.getMeta = function (n) {\r\n        return this.nodeMetaMap.get(n) || null;\r\n    };\r\n    Mirror.prototype.removeNodeFromMap = function (n) {\r\n        var _this = this;\r\n        var id = this.getId(n);\r\n        this.idNodeMap[\"delete\"](id);\r\n        if (n.childNodes) {\r\n            n.childNodes.forEach(function (childNode) {\r\n                return _this.removeNodeFromMap(childNode);\r\n            });\r\n        }\r\n    };\r\n    Mirror.prototype.has = function (id) {\r\n        return this.idNodeMap.has(id);\r\n    };\r\n    Mirror.prototype.hasNode = function (node) {\r\n        return this.nodeMetaMap.has(node);\r\n    };\r\n    Mirror.prototype.add = function (n, meta) {\r\n        var id = meta.id;\r\n        this.idNodeMap.set(id, n);\r\n        this.nodeMetaMap.set(n, meta);\r\n    };\r\n    Mirror.prototype.replace = function (id, n) {\r\n        var oldNode = this.getNode(id);\r\n        if (oldNode) {\r\n            var meta = this.nodeMetaMap.get(oldNode);\r\n            if (meta)\r\n                this.nodeMetaMap.set(n, meta);\r\n        }\r\n        this.idNodeMap.set(id, n);\r\n    };\r\n    Mirror.prototype.reset = function () {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    };\r\n    return Mirror;\r\n}());\r\nfunction createMirror() {\r\n    return new Mirror();\r\n}\r\nfunction maskInputValue(_a) {\r\n    var maskInputOptions = _a.maskInputOptions, tagName = _a.tagName, type = _a.type, value = _a.value, maskInputFn = _a.maskInputFn;\r\n    var text = value || '';\r\n    if (maskInputOptions[tagName.toLowerCase()] ||\r\n        maskInputOptions[type]) {\r\n        if (maskInputFn) {\r\n            text = maskInputFn(text);\r\n        }\r\n        else {\r\n            text = '*'.repeat(text.length);\r\n        }\r\n    }\r\n    return text;\r\n}\r\nvar ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\r\nfunction is2DCanvasBlank(canvas) {\r\n    var ctx = canvas.getContext('2d');\r\n    if (!ctx)\r\n        return true;\r\n    var chunkSize = 50;\r\n    for (var x = 0; x < canvas.width; x += chunkSize) {\r\n        for (var y = 0; y < canvas.height; y += chunkSize) {\r\n            var getImageData = ctx.getImageData;\r\n            var originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData\r\n                ? getImageData[ORIGINAL_ATTRIBUTE_NAME]\r\n                : getImageData;\r\n            var pixelBuffer = new Uint32Array(originalGetImageData.call(ctx, x, y, Math.min(chunkSize, canvas.width - x), Math.min(chunkSize, canvas.height - y)).data.buffer);\r\n            if (pixelBuffer.some(function (pixel) { return pixel !== 0; }))\r\n                return false;\r\n        }\r\n    }\r\n    return true;\r\n}\n\nvar _id = 1;\r\nvar tagNameRegex = new RegExp('[^a-z0-9-_:]');\r\nvar IGNORED_NODE = -2;\r\nfunction genId() {\r\n    return _id++;\r\n}\r\nfunction getValidTagName(element) {\r\n    if (element instanceof HTMLFormElement) {\r\n        return 'form';\r\n    }\r\n    var processedTagName = element.tagName.toLowerCase().trim();\r\n    if (tagNameRegex.test(processedTagName)) {\r\n        return 'div';\r\n    }\r\n    return processedTagName;\r\n}\r\nfunction stringifyStyleSheet(sheet) {\r\n    return sheet.cssRules\r\n        ? Array.from(sheet.cssRules)\r\n            .map(function (rule) { return rule.cssText || ''; })\r\n            .join('')\r\n        : '';\r\n}\r\nfunction extractOrigin(url) {\r\n    var origin = '';\r\n    if (url.indexOf('//') > -1) {\r\n        origin = url.split('/').slice(0, 3).join('/');\r\n    }\r\n    else {\r\n        origin = url.split('/')[0];\r\n    }\r\n    origin = origin.split('?')[0];\r\n    return origin;\r\n}\r\nvar canvasService;\r\nvar canvasCtx;\r\nvar URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\r\nvar RELATIVE_PATH = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\\\|\\/\\/|#).*/;\r\nvar DATA_URI = /^(data:)([^,]*),(.*)/i;\r\nfunction absoluteToStylesheet(cssText, href) {\r\n    return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {\r\n        var filePath = path1 || path2 || path3;\r\n        var maybeQuote = quote1 || quote2 || '';\r\n        if (!filePath) {\r\n            return origin;\r\n        }\r\n        if (!RELATIVE_PATH.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (DATA_URI.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (filePath[0] === '/') {\r\n            return \"url(\".concat(maybeQuote).concat(extractOrigin(href) + filePath).concat(maybeQuote, \")\");\r\n        }\r\n        var stack = href.split('/');\r\n        var parts = filePath.split('/');\r\n        stack.pop();\r\n        for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\r\n            var part = parts_1[_i];\r\n            if (part === '.') {\r\n                continue;\r\n            }\r\n            else if (part === '..') {\r\n                stack.pop();\r\n            }\r\n            else {\r\n                stack.push(part);\r\n            }\r\n        }\r\n        return \"url(\".concat(maybeQuote).concat(stack.join('/')).concat(maybeQuote, \")\");\r\n    });\r\n}\r\nvar SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\r\nvar SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\r\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\r\n    if (attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var pos = 0;\r\n    function collectCharacters(regEx) {\r\n        var chars;\r\n        var match = regEx.exec(attributeValue.substring(pos));\r\n        if (match) {\r\n            chars = match[0];\r\n            pos += chars.length;\r\n            return chars;\r\n        }\r\n        return '';\r\n    }\r\n    var output = [];\r\n    while (true) {\r\n        collectCharacters(SRCSET_COMMAS_OR_SPACES);\r\n        if (pos >= attributeValue.length) {\r\n            break;\r\n        }\r\n        var url = collectCharacters(SRCSET_NOT_SPACES);\r\n        if (url.slice(-1) === ',') {\r\n            url = absoluteToDoc(doc, url.substring(0, url.length - 1));\r\n            output.push(url);\r\n        }\r\n        else {\r\n            var descriptorsStr = '';\r\n            url = absoluteToDoc(doc, url);\r\n            var inParens = false;\r\n            while (true) {\r\n                var c = attributeValue.charAt(pos);\r\n                if (c === '') {\r\n                    output.push((url + descriptorsStr).trim());\r\n                    break;\r\n                }\r\n                else if (!inParens) {\r\n                    if (c === ',') {\r\n                        pos += 1;\r\n                        output.push((url + descriptorsStr).trim());\r\n                        break;\r\n                    }\r\n                    else if (c === '(') {\r\n                        inParens = true;\r\n                    }\r\n                }\r\n                else {\r\n                    if (c === ')') {\r\n                        inParens = false;\r\n                    }\r\n                }\r\n                descriptorsStr += c;\r\n                pos += 1;\r\n            }\r\n        }\r\n    }\r\n    return output.join(', ');\r\n}\r\nfunction absoluteToDoc(doc, attributeValue) {\r\n    if (!attributeValue || attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var a = doc.createElement('a');\r\n    a.href = attributeValue;\r\n    return a.href;\r\n}\r\nfunction isSVGElement(el) {\r\n    return Boolean(el.tagName === 'svg' || el.ownerSVGElement);\r\n}\r\nfunction getHref() {\r\n    var a = document.createElement('a');\r\n    a.href = '';\r\n    return a.href;\r\n}\r\nfunction transformAttribute(doc, tagName, name, value) {\r\n    if (name === 'src' ||\r\n        (name === 'href' && value && !(tagName === 'use' && value[0] === '#'))) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'xlink:href' && value && value[0] !== '#') {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'background' &&\r\n        value &&\r\n        (tagName === 'table' || tagName === 'td' || tagName === 'th')) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'srcset' && value) {\r\n        return getAbsoluteSrcsetString(doc, value);\r\n    }\r\n    else if (name === 'style' && value) {\r\n        return absoluteToStylesheet(value, getHref());\r\n    }\r\n    else if (tagName === 'object' && name === 'data' && value) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else {\r\n        return value;\r\n    }\r\n}\r\nfunction _isBlockedElement(element, blockClass, blockSelector) {\r\n    if (typeof blockClass === 'string') {\r\n        if (element.classList.contains(blockClass)) {\r\n            return true;\r\n        }\r\n    }\r\n    else {\r\n        for (var eIndex = element.classList.length; eIndex--;) {\r\n            var className = element.classList[eIndex];\r\n            if (blockClass.test(className)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    if (blockSelector) {\r\n        return element.matches(blockSelector);\r\n    }\r\n    return false;\r\n}\r\nfunction classMatchesRegex(node, regex, checkAncestors) {\r\n    if (!node)\r\n        return false;\r\n    if (node.nodeType !== node.ELEMENT_NODE) {\r\n        if (!checkAncestors)\r\n            return false;\r\n        return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n    }\r\n    for (var eIndex = node.classList.length; eIndex--;) {\r\n        var className = node.classList[eIndex];\r\n        if (regex.test(className)) {\r\n            return true;\r\n        }\r\n    }\r\n    if (!checkAncestors)\r\n        return false;\r\n    return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n}\r\nfunction needMaskingText(node, maskTextClass, maskTextSelector) {\r\n    var el = node.nodeType === node.ELEMENT_NODE\r\n        ? node\r\n        : node.parentElement;\r\n    if (el === null)\r\n        return false;\r\n    if (typeof maskTextClass === 'string') {\r\n        if (el.classList.contains(maskTextClass))\r\n            return true;\r\n        if (el.closest(\".\".concat(maskTextClass)))\r\n            return true;\r\n    }\r\n    else {\r\n        if (classMatchesRegex(el, maskTextClass, true))\r\n            return true;\r\n    }\r\n    if (maskTextSelector) {\r\n        if (el.matches(maskTextSelector))\r\n            return true;\r\n        if (el.closest(maskTextSelector))\r\n            return true;\r\n    }\r\n    return false;\r\n}\r\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\r\n    var win = iframeEl.contentWindow;\r\n    if (!win) {\r\n        return;\r\n    }\r\n    var fired = false;\r\n    var readyState;\r\n    try {\r\n        readyState = win.document.readyState;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (readyState !== 'complete') {\r\n        var timer_1 = setTimeout(function () {\r\n            if (!fired) {\r\n                listener();\r\n                fired = true;\r\n            }\r\n        }, iframeLoadTimeout);\r\n        iframeEl.addEventListener('load', function () {\r\n            clearTimeout(timer_1);\r\n            fired = true;\r\n            listener();\r\n        });\r\n        return;\r\n    }\r\n    var blankUrl = 'about:blank';\r\n    if (win.location.href !== blankUrl ||\r\n        iframeEl.src === blankUrl ||\r\n        iframeEl.src === '') {\r\n        setTimeout(listener, 0);\r\n        return iframeEl.addEventListener('load', listener);\r\n    }\r\n    iframeEl.addEventListener('load', listener);\r\n}\r\nfunction onceStylesheetLoaded(link, listener, styleSheetLoadTimeout) {\r\n    var fired = false;\r\n    var styleSheetLoaded;\r\n    try {\r\n        styleSheetLoaded = link.sheet;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (styleSheetLoaded)\r\n        return;\r\n    var timer = setTimeout(function () {\r\n        if (!fired) {\r\n            listener();\r\n            fired = true;\r\n        }\r\n    }, styleSheetLoadTimeout);\r\n    link.addEventListener('load', function () {\r\n        clearTimeout(timer);\r\n        fired = true;\r\n        listener();\r\n    });\r\n}\r\nfunction serializeNode(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c;\r\n    var rootId = getRootId(doc, mirror);\r\n    switch (n.nodeType) {\r\n        case n.DOCUMENT_NODE:\r\n            if (n.compatMode !== 'CSS1Compat') {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: [],\r\n                    compatMode: n.compatMode\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: []\r\n                };\r\n            }\r\n        case n.DOCUMENT_TYPE_NODE:\r\n            return {\r\n                type: NodeType.DocumentType,\r\n                name: n.name,\r\n                publicId: n.publicId,\r\n                systemId: n.systemId,\r\n                rootId: rootId\r\n            };\r\n        case n.ELEMENT_NODE:\r\n            return serializeElementNode(n, {\r\n                doc: doc,\r\n                blockClass: blockClass,\r\n                blockSelector: blockSelector,\r\n                inlineStylesheet: inlineStylesheet,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn,\r\n                dataURLOptions: dataURLOptions,\r\n                inlineImages: inlineImages,\r\n                recordCanvas: recordCanvas,\r\n                keepIframeSrcFn: keepIframeSrcFn,\r\n                newlyAddedElement: newlyAddedElement,\r\n                rootId: rootId\r\n            });\r\n        case n.TEXT_NODE:\r\n            return serializeTextNode(n, {\r\n                maskTextClass: maskTextClass,\r\n                maskTextSelector: maskTextSelector,\r\n                maskTextFn: maskTextFn,\r\n                rootId: rootId\r\n            });\r\n        case n.CDATA_SECTION_NODE:\r\n            return {\r\n                type: NodeType.CDATA,\r\n                textContent: '',\r\n                rootId: rootId\r\n            };\r\n        case n.COMMENT_NODE:\r\n            return {\r\n                type: NodeType.Comment,\r\n                textContent: n.textContent || '',\r\n                rootId: rootId\r\n            };\r\n        default:\r\n            return false;\r\n    }\r\n}\r\nfunction getRootId(doc, mirror) {\r\n    if (!mirror.hasNode(doc))\r\n        return undefined;\r\n    var docId = mirror.getId(doc);\r\n    return docId === 1 ? undefined : docId;\r\n}\r\nfunction serializeTextNode(n, options) {\r\n    var _a;\r\n    var maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, maskTextFn = options.maskTextFn, rootId = options.rootId;\r\n    var parentTagName = n.parentNode && n.parentNode.tagName;\r\n    var textContent = n.textContent;\r\n    var isStyle = parentTagName === 'STYLE' ? true : undefined;\r\n    var isScript = parentTagName === 'SCRIPT' ? true : undefined;\r\n    if (isStyle && textContent) {\r\n        try {\r\n            if (n.nextSibling || n.previousSibling) {\r\n            }\r\n            else if ((_a = n.parentNode.sheet) === null || _a === void 0 ? void 0 : _a.cssRules) {\r\n                textContent = stringifyStyleSheet(n.parentNode.sheet);\r\n            }\r\n        }\r\n        catch (err) {\r\n            console.warn(\"Cannot get CSS styles from text's parentNode. Error: \".concat(err), n);\r\n        }\r\n        textContent = absoluteToStylesheet(textContent, getHref());\r\n    }\r\n    if (isScript) {\r\n        textContent = 'SCRIPT_PLACEHOLDER';\r\n    }\r\n    if (!isStyle &&\r\n        !isScript &&\r\n        textContent &&\r\n        needMaskingText(n, maskTextClass, maskTextSelector)) {\r\n        textContent = maskTextFn\r\n            ? maskTextFn(textContent)\r\n            : textContent.replace(/[\\S]/g, '*');\r\n    }\r\n    return {\r\n        type: NodeType.Text,\r\n        textContent: textContent || '',\r\n        isStyle: isStyle,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction serializeElementNode(n, options) {\r\n    var doc = options.doc, blockClass = options.blockClass, blockSelector = options.blockSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c, rootId = options.rootId;\r\n    var needBlock = _isBlockedElement(n, blockClass, blockSelector);\r\n    var tagName = getValidTagName(n);\r\n    var attributes = {};\r\n    var len = n.attributes.length;\r\n    for (var i = 0; i < len; i++) {\r\n        var attr = n.attributes[i];\r\n        attributes[attr.name] = transformAttribute(doc, tagName, attr.name, attr.value);\r\n    }\r\n    if (tagName === 'link' && inlineStylesheet) {\r\n        var stylesheet = Array.from(doc.styleSheets).find(function (s) {\r\n            return s.href === n.href;\r\n        });\r\n        var cssText = null;\r\n        if (stylesheet) {\r\n            cssText = getCssRulesString(stylesheet);\r\n        }\r\n        if (cssText) {\r\n            delete attributes.rel;\r\n            delete attributes.href;\r\n            attributes._cssText = absoluteToStylesheet(cssText, stylesheet.href);\r\n        }\r\n    }\r\n    if (tagName === 'style' &&\r\n        n.sheet &&\r\n        !(n.innerText || n.textContent || '').trim().length) {\r\n        var cssText = getCssRulesString(n.sheet);\r\n        if (cssText) {\r\n            attributes._cssText = absoluteToStylesheet(cssText, getHref());\r\n        }\r\n    }\r\n    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {\r\n        var value = n.value;\r\n        var checked = n.checked;\r\n        if (attributes.type !== 'radio' &&\r\n            attributes.type !== 'checkbox' &&\r\n            attributes.type !== 'submit' &&\r\n            attributes.type !== 'button' &&\r\n            value) {\r\n            attributes.value = maskInputValue({\r\n                type: attributes.type,\r\n                tagName: tagName,\r\n                value: value,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn\r\n            });\r\n        }\r\n        else if (checked) {\r\n            attributes.checked = checked;\r\n        }\r\n    }\r\n    if (tagName === 'option') {\r\n        if (n.selected && !maskInputOptions['select']) {\r\n            attributes.selected = true;\r\n        }\r\n        else {\r\n            delete attributes.selected;\r\n        }\r\n    }\r\n    if (tagName === 'canvas' && recordCanvas) {\r\n        if (n.__context === '2d') {\r\n            if (!is2DCanvasBlank(n)) {\r\n                attributes.rr_dataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n        }\r\n        else if (!('__context' in n)) {\r\n            var canvasDataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            var blankCanvas = document.createElement('canvas');\r\n            blankCanvas.width = n.width;\r\n            blankCanvas.height = n.height;\r\n            var blankCanvasDataURL = blankCanvas.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            if (canvasDataURL !== blankCanvasDataURL) {\r\n                attributes.rr_dataURL = canvasDataURL;\r\n            }\r\n        }\r\n    }\r\n    if (tagName === 'img' && inlineImages) {\r\n        if (!canvasService) {\r\n            canvasService = doc.createElement('canvas');\r\n            canvasCtx = canvasService.getContext('2d');\r\n        }\r\n        var image_1 = n;\r\n        var oldValue_1 = image_1.crossOrigin;\r\n        image_1.crossOrigin = 'anonymous';\r\n        var recordInlineImage = function () {\r\n            try {\r\n                canvasService.width = image_1.naturalWidth;\r\n                canvasService.height = image_1.naturalHeight;\r\n                canvasCtx.drawImage(image_1, 0, 0);\r\n                attributes.rr_dataURL = canvasService.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n            catch (err) {\r\n                console.warn(\"Cannot inline img src=\".concat(image_1.currentSrc, \"! Error: \").concat(err));\r\n            }\r\n            oldValue_1\r\n                ? (attributes.crossOrigin = oldValue_1)\r\n                : image_1.removeAttribute('crossorigin');\r\n        };\r\n        if (image_1.complete && image_1.naturalWidth !== 0)\r\n            recordInlineImage();\r\n        else\r\n            image_1.onload = recordInlineImage;\r\n    }\r\n    if (tagName === 'audio' || tagName === 'video') {\r\n        attributes.rr_mediaState = n.paused\r\n            ? 'paused'\r\n            : 'played';\r\n        attributes.rr_mediaCurrentTime = n.currentTime;\r\n    }\r\n    if (!newlyAddedElement) {\r\n        if (n.scrollLeft) {\r\n            attributes.rr_scrollLeft = n.scrollLeft;\r\n        }\r\n        if (n.scrollTop) {\r\n            attributes.rr_scrollTop = n.scrollTop;\r\n        }\r\n    }\r\n    if (needBlock) {\r\n        var _d = n.getBoundingClientRect(), width = _d.width, height = _d.height;\r\n        attributes = {\r\n            \"class\": attributes[\"class\"],\r\n            rr_width: \"\".concat(width, \"px\"),\r\n            rr_height: \"\".concat(height, \"px\")\r\n        };\r\n    }\r\n    if (tagName === 'iframe' && !keepIframeSrcFn(attributes.src)) {\r\n        if (!n.contentDocument) {\r\n            attributes.rr_src = attributes.src;\r\n        }\r\n        delete attributes.src;\r\n    }\r\n    return {\r\n        type: NodeType.Element,\r\n        tagName: tagName,\r\n        attributes: attributes,\r\n        childNodes: [],\r\n        isSVG: isSVGElement(n) || undefined,\r\n        needBlock: needBlock,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction lowerIfExists(maybeAttr) {\r\n    if (maybeAttr === undefined) {\r\n        return '';\r\n    }\r\n    else {\r\n        return maybeAttr.toLowerCase();\r\n    }\r\n}\r\nfunction slimDOMExcluded(sn, slimDOMOptions) {\r\n    if (slimDOMOptions.comment && sn.type === NodeType.Comment) {\r\n        return true;\r\n    }\r\n    else if (sn.type === NodeType.Element) {\r\n        if (slimDOMOptions.script &&\r\n            (sn.tagName === 'script' ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'preload' &&\r\n                    sn.attributes.as === 'script') ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'prefetch' &&\r\n                    typeof sn.attributes.href === 'string' &&\r\n                    sn.attributes.href.endsWith('.js')))) {\r\n            return true;\r\n        }\r\n        else if (slimDOMOptions.headFavicon &&\r\n            ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon') ||\r\n                (sn.tagName === 'meta' &&\r\n                    (lowerIfExists(sn.attributes.name).match(/^msapplication-tile(image|color)$/) ||\r\n                        lowerIfExists(sn.attributes.name) === 'application-name' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'apple-touch-icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'shortcut icon')))) {\r\n            return true;\r\n        }\r\n        else if (sn.tagName === 'meta') {\r\n            if (slimDOMOptions.headMetaDescKeywords &&\r\n                lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaSocial &&\r\n                (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) ||\r\n                    lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) ||\r\n                    lowerIfExists(sn.attributes.name) === 'pinterest')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaRobots &&\r\n                (lowerIfExists(sn.attributes.name) === 'robots' ||\r\n                    lowerIfExists(sn.attributes.name) === 'googlebot' ||\r\n                    lowerIfExists(sn.attributes.name) === 'bingbot')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaHttpEquiv &&\r\n                sn.attributes['http-equiv'] !== undefined) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaAuthorship &&\r\n                (lowerIfExists(sn.attributes.name) === 'author' ||\r\n                    lowerIfExists(sn.attributes.name) === 'generator' ||\r\n                    lowerIfExists(sn.attributes.name) === 'framework' ||\r\n                    lowerIfExists(sn.attributes.name) === 'publisher' ||\r\n                    lowerIfExists(sn.attributes.name) === 'progid' ||\r\n                    lowerIfExists(sn.attributes.property).match(/^article:/) ||\r\n                    lowerIfExists(sn.attributes.property).match(/^product:/))) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaVerification &&\r\n                (lowerIfExists(sn.attributes.name) === 'google-site-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'yandex-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'csrf-token' ||\r\n                    lowerIfExists(sn.attributes.name) === 'p:domain_verify' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verify-v1' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'shopify-checkout-api-token')) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction serializeNodeWithId(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.inlineStylesheet, inlineStylesheet = _b === void 0 ? true : _b, _c = options.maskInputOptions, maskInputOptions = _c === void 0 ? {} : _c, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, slimDOMOptions = options.slimDOMOptions, _d = options.dataURLOptions, dataURLOptions = _d === void 0 ? {} : _d, _e = options.inlineImages, inlineImages = _e === void 0 ? false : _e, _f = options.recordCanvas, recordCanvas = _f === void 0 ? false : _f, onSerialize = options.onSerialize, onIframeLoad = options.onIframeLoad, _g = options.iframeLoadTimeout, iframeLoadTimeout = _g === void 0 ? 5000 : _g, onStylesheetLoad = options.onStylesheetLoad, _h = options.stylesheetLoadTimeout, stylesheetLoadTimeout = _h === void 0 ? 5000 : _h, _j = options.keepIframeSrcFn, keepIframeSrcFn = _j === void 0 ? function () { return false; } : _j, _k = options.newlyAddedElement, newlyAddedElement = _k === void 0 ? false : _k;\r\n    var _l = options.preserveWhiteSpace, preserveWhiteSpace = _l === void 0 ? true : _l;\r\n    var _serializedNode = serializeNode(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: newlyAddedElement\r\n    });\r\n    if (!_serializedNode) {\r\n        console.warn(n, 'not serialized');\r\n        return null;\r\n    }\r\n    var id;\r\n    if (mirror.hasNode(n)) {\r\n        id = mirror.getId(n);\r\n    }\r\n    else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||\r\n        (!preserveWhiteSpace &&\r\n            _serializedNode.type === NodeType.Text &&\r\n            !_serializedNode.isStyle &&\r\n            !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, '').length)) {\r\n        id = IGNORED_NODE;\r\n    }\r\n    else {\r\n        id = genId();\r\n    }\r\n    var serializedNode = Object.assign(_serializedNode, { id: id });\r\n    mirror.add(n, serializedNode);\r\n    if (id === IGNORED_NODE) {\r\n        return null;\r\n    }\r\n    if (onSerialize) {\r\n        onSerialize(n);\r\n    }\r\n    var recordChild = !skipChild;\r\n    if (serializedNode.type === NodeType.Element) {\r\n        recordChild = recordChild && !serializedNode.needBlock;\r\n        delete serializedNode.needBlock;\r\n        var shadowRoot = n.shadowRoot;\r\n        if (shadowRoot && isNativeShadowDom(shadowRoot))\r\n            serializedNode.isShadowHost = true;\r\n    }\r\n    if ((serializedNode.type === NodeType.Document ||\r\n        serializedNode.type === NodeType.Element) &&\r\n        recordChild) {\r\n        if (slimDOMOptions.headWhitespace &&\r\n            serializedNode.type === NodeType.Element &&\r\n            serializedNode.tagName === 'head') {\r\n            preserveWhiteSpace = false;\r\n        }\r\n        var bypassOptions = {\r\n            doc: doc,\r\n            mirror: mirror,\r\n            blockClass: blockClass,\r\n            blockSelector: blockSelector,\r\n            maskTextClass: maskTextClass,\r\n            maskTextSelector: maskTextSelector,\r\n            skipChild: skipChild,\r\n            inlineStylesheet: inlineStylesheet,\r\n            maskInputOptions: maskInputOptions,\r\n            maskTextFn: maskTextFn,\r\n            maskInputFn: maskInputFn,\r\n            slimDOMOptions: slimDOMOptions,\r\n            dataURLOptions: dataURLOptions,\r\n            inlineImages: inlineImages,\r\n            recordCanvas: recordCanvas,\r\n            preserveWhiteSpace: preserveWhiteSpace,\r\n            onSerialize: onSerialize,\r\n            onIframeLoad: onIframeLoad,\r\n            iframeLoadTimeout: iframeLoadTimeout,\r\n            onStylesheetLoad: onStylesheetLoad,\r\n            stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n            keepIframeSrcFn: keepIframeSrcFn\r\n        };\r\n        for (var _i = 0, _m = Array.from(n.childNodes); _i < _m.length; _i++) {\r\n            var childN = _m[_i];\r\n            var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n            if (serializedChildNode) {\r\n                serializedNode.childNodes.push(serializedChildNode);\r\n            }\r\n        }\r\n        if (isElement(n) && n.shadowRoot) {\r\n            for (var _o = 0, _p = Array.from(n.shadowRoot.childNodes); _o < _p.length; _o++) {\r\n                var childN = _p[_o];\r\n                var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n                if (serializedChildNode) {\r\n                    isNativeShadowDom(n.shadowRoot) &&\r\n                        (serializedChildNode.isShadow = true);\r\n                    serializedNode.childNodes.push(serializedChildNode);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (n.parentNode &&\r\n        isShadowRoot(n.parentNode) &&\r\n        isNativeShadowDom(n.parentNode)) {\r\n        serializedNode.isShadow = true;\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'iframe') {\r\n        onceIframeLoaded(n, function () {\r\n            var iframeDoc = n.contentDocument;\r\n            if (iframeDoc && onIframeLoad) {\r\n                var serializedIframeNode = serializeNodeWithId(iframeDoc, {\r\n                    doc: iframeDoc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedIframeNode) {\r\n                    onIframeLoad(n, serializedIframeNode);\r\n                }\r\n            }\r\n        }, iframeLoadTimeout);\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'link' &&\r\n        serializedNode.attributes.rel === 'stylesheet') {\r\n        onceStylesheetLoaded(n, function () {\r\n            if (onStylesheetLoad) {\r\n                var serializedLinkNode = serializeNodeWithId(n, {\r\n                    doc: doc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedLinkNode) {\r\n                    onStylesheetLoad(n, serializedLinkNode);\r\n                }\r\n            }\r\n        }, stylesheetLoadTimeout);\r\n    }\r\n    return serializedNode;\r\n}\r\nfunction snapshot(n, options) {\r\n    var _a = options || {}, _b = _a.mirror, mirror = _b === void 0 ? new Mirror() : _b, _c = _a.blockClass, blockClass = _c === void 0 ? 'rr-block' : _c, _d = _a.blockSelector, blockSelector = _d === void 0 ? null : _d, _e = _a.maskTextClass, maskTextClass = _e === void 0 ? 'rr-mask' : _e, _f = _a.maskTextSelector, maskTextSelector = _f === void 0 ? null : _f, _g = _a.inlineStylesheet, inlineStylesheet = _g === void 0 ? true : _g, _h = _a.inlineImages, inlineImages = _h === void 0 ? false : _h, _j = _a.recordCanvas, recordCanvas = _j === void 0 ? false : _j, _k = _a.maskAllInputs, maskAllInputs = _k === void 0 ? false : _k, maskTextFn = _a.maskTextFn, maskInputFn = _a.maskInputFn, _l = _a.slimDOM, slimDOM = _l === void 0 ? false : _l, dataURLOptions = _a.dataURLOptions, preserveWhiteSpace = _a.preserveWhiteSpace, onSerialize = _a.onSerialize, onIframeLoad = _a.onIframeLoad, iframeLoadTimeout = _a.iframeLoadTimeout, onStylesheetLoad = _a.onStylesheetLoad, stylesheetLoadTimeout = _a.stylesheetLoadTimeout, _m = _a.keepIframeSrcFn, keepIframeSrcFn = _m === void 0 ? function () { return false; } : _m;\r\n    var maskInputOptions = maskAllInputs === true\r\n        ? {\r\n            color: true,\r\n            date: true,\r\n            'datetime-local': true,\r\n            email: true,\r\n            month: true,\r\n            number: true,\r\n            range: true,\r\n            search: true,\r\n            tel: true,\r\n            text: true,\r\n            time: true,\r\n            url: true,\r\n            week: true,\r\n            textarea: true,\r\n            select: true,\r\n            password: true\r\n        }\r\n        : maskAllInputs === false\r\n            ? {\r\n                password: true\r\n            }\r\n            : maskAllInputs;\r\n    var slimDOMOptions = slimDOM === true || slimDOM === 'all'\r\n        ?\r\n            {\r\n                script: true,\r\n                comment: true,\r\n                headFavicon: true,\r\n                headWhitespace: true,\r\n                headMetaDescKeywords: slimDOM === 'all',\r\n                headMetaSocial: true,\r\n                headMetaRobots: true,\r\n                headMetaHttpEquiv: true,\r\n                headMetaAuthorship: true,\r\n                headMetaVerification: true\r\n            }\r\n        : slimDOM === false\r\n            ? {}\r\n            : slimDOM;\r\n    return serializeNodeWithId(n, {\r\n        doc: n,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        skipChild: false,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        slimDOMOptions: slimDOMOptions,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        preserveWhiteSpace: preserveWhiteSpace,\r\n        onSerialize: onSerialize,\r\n        onIframeLoad: onIframeLoad,\r\n        iframeLoadTimeout: iframeLoadTimeout,\r\n        onStylesheetLoad: onStylesheetLoad,\r\n        stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: false\r\n    });\r\n}\r\nfunction visitSnapshot(node, onVisit) {\r\n    function walk(current) {\r\n        onVisit(current);\r\n        if (current.type === NodeType.Document ||\r\n            current.type === NodeType.Element) {\r\n            current.childNodes.forEach(walk);\r\n        }\r\n    }\r\n    walk(node);\r\n}\r\nfunction cleanupSnapshot() {\r\n    _id = 1;\r\n}\n\nvar commentre = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\r\nfunction parse(css, options) {\r\n    if (options === void 0) { options = {}; }\r\n    var lineno = 1;\r\n    var column = 1;\r\n    function updatePosition(str) {\r\n        var lines = str.match(/\\n/g);\r\n        if (lines) {\r\n            lineno += lines.length;\r\n        }\r\n        var i = str.lastIndexOf('\\n');\r\n        column = i === -1 ? column + str.length : str.length - i;\r\n    }\r\n    function position() {\r\n        var start = { line: lineno, column: column };\r\n        return function (node) {\r\n            node.position = new Position(start);\r\n            whitespace();\r\n            return node;\r\n        };\r\n    }\r\n    var Position = (function () {\r\n        function Position(start) {\r\n            this.start = start;\r\n            this.end = { line: lineno, column: column };\r\n            this.source = options.source;\r\n        }\r\n        return Position;\r\n    }());\r\n    Position.prototype.content = css;\r\n    var errorsList = [];\r\n    function error(msg) {\r\n        var err = new Error(\"\".concat(options.source || '', \":\").concat(lineno, \":\").concat(column, \": \").concat(msg));\r\n        err.reason = msg;\r\n        err.filename = options.source;\r\n        err.line = lineno;\r\n        err.column = column;\r\n        err.source = css;\r\n        if (options.silent) {\r\n            errorsList.push(err);\r\n        }\r\n        else {\r\n            throw err;\r\n        }\r\n    }\r\n    function stylesheet() {\r\n        var rulesList = rules();\r\n        return {\r\n            type: 'stylesheet',\r\n            stylesheet: {\r\n                source: options.source,\r\n                rules: rulesList,\r\n                parsingErrors: errorsList\r\n            }\r\n        };\r\n    }\r\n    function open() {\r\n        return match(/^{\\s*/);\r\n    }\r\n    function close() {\r\n        return match(/^}/);\r\n    }\r\n    function rules() {\r\n        var node;\r\n        var rules = [];\r\n        whitespace();\r\n        comments(rules);\r\n        while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\r\n            if (node !== false) {\r\n                rules.push(node);\r\n                comments(rules);\r\n            }\r\n        }\r\n        return rules;\r\n    }\r\n    function match(re) {\r\n        var m = re.exec(css);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var str = m[0];\r\n        updatePosition(str);\r\n        css = css.slice(str.length);\r\n        return m;\r\n    }\r\n    function whitespace() {\r\n        match(/^\\s*/);\r\n    }\r\n    function comments(rules) {\r\n        if (rules === void 0) { rules = []; }\r\n        var c;\r\n        while ((c = comment())) {\r\n            if (c !== false) {\r\n                rules.push(c);\r\n            }\r\n            c = comment();\r\n        }\r\n        return rules;\r\n    }\r\n    function comment() {\r\n        var pos = position();\r\n        if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\r\n            return;\r\n        }\r\n        var i = 2;\r\n        while ('' !== css.charAt(i) &&\r\n            ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {\r\n            ++i;\r\n        }\r\n        i += 2;\r\n        if ('' === css.charAt(i - 1)) {\r\n            return error('End of comment missing');\r\n        }\r\n        var str = css.slice(2, i - 2);\r\n        column += 2;\r\n        updatePosition(str);\r\n        css = css.slice(i);\r\n        column += 2;\r\n        return pos({\r\n            type: 'comment',\r\n            comment: str\r\n        });\r\n    }\r\n    function selector() {\r\n        var m = match(/^([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return trim(m[0])\r\n            .replace(/\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*\\/+/g, '')\r\n            .replace(/\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'/g, function (m) {\r\n            return m.replace(/,/g, '\\u200C');\r\n        })\r\n            .split(/\\s*(?![^(]*\\)),\\s*/)\r\n            .map(function (s) {\r\n            return s.replace(/\\u200C/g, ',');\r\n        });\r\n    }\r\n    function declaration() {\r\n        var pos = position();\r\n        var propMatch = match(/^(\\*?[-#\\/\\*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/);\r\n        if (!propMatch) {\r\n            return;\r\n        }\r\n        var prop = trim(propMatch[0]);\r\n        if (!match(/^:\\s*/)) {\r\n            return error(\"property missing ':'\");\r\n        }\r\n        var val = match(/^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^\\)]*?\\)|[^};])+)/);\r\n        var ret = pos({\r\n            type: 'declaration',\r\n            property: prop.replace(commentre, ''),\r\n            value: val ? trim(val[0]).replace(commentre, '') : ''\r\n        });\r\n        match(/^[;\\s]*/);\r\n        return ret;\r\n    }\r\n    function declarations() {\r\n        var decls = [];\r\n        if (!open()) {\r\n            return error(\"missing '{'\");\r\n        }\r\n        comments(decls);\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            if (decl !== false) {\r\n                decls.push(decl);\r\n                comments(decls);\r\n            }\r\n            decl = declaration();\r\n        }\r\n        if (!close()) {\r\n            return error(\"missing '}'\");\r\n        }\r\n        return decls;\r\n    }\r\n    function keyframe() {\r\n        var m;\r\n        var vals = [];\r\n        var pos = position();\r\n        while ((m = match(/^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/))) {\r\n            vals.push(m[1]);\r\n            match(/^,\\s*/);\r\n        }\r\n        if (!vals.length) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'keyframe',\r\n            values: vals,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    function atkeyframes() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?keyframes\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = m[1];\r\n        m = match(/^([-\\w]+)\\s*/);\r\n        if (!m) {\r\n            return error('@keyframes missing name');\r\n        }\r\n        var name = m[1];\r\n        if (!open()) {\r\n            return error(\"@keyframes missing '{'\");\r\n        }\r\n        var frame;\r\n        var frames = comments();\r\n        while ((frame = keyframe())) {\r\n            frames.push(frame);\r\n            frames = frames.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@keyframes missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'keyframes',\r\n            name: name,\r\n            vendor: vendor,\r\n            keyframes: frames\r\n        });\r\n    }\r\n    function atsupports() {\r\n        var pos = position();\r\n        var m = match(/^@supports *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var supports = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@supports missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@supports missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'supports',\r\n            supports: supports,\r\n            rules: style\r\n        });\r\n    }\r\n    function athost() {\r\n        var pos = position();\r\n        var m = match(/^@host\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@host missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@host missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'host',\r\n            rules: style\r\n        });\r\n    }\r\n    function atmedia() {\r\n        var pos = position();\r\n        var m = match(/^@media *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var media = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@media missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@media missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'media',\r\n            media: media,\r\n            rules: style\r\n        });\r\n    }\r\n    function atcustommedia() {\r\n        var pos = position();\r\n        var m = match(/^@custom-media\\s+(--[^\\s]+)\\s*([^{;]+);/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'custom-media',\r\n            name: trim(m[1]),\r\n            media: trim(m[2])\r\n        });\r\n    }\r\n    function atpage() {\r\n        var pos = position();\r\n        var m = match(/^@page */);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var sel = selector() || [];\r\n        if (!open()) {\r\n            return error(\"@page missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@page missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'page',\r\n            selectors: sel,\r\n            declarations: decls\r\n        });\r\n    }\r\n    function atdocument() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?document *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = trim(m[1]);\r\n        var doc = trim(m[2]);\r\n        if (!open()) {\r\n            return error(\"@document missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@document missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'document',\r\n            document: doc,\r\n            vendor: vendor,\r\n            rules: style\r\n        });\r\n    }\r\n    function atfontface() {\r\n        var pos = position();\r\n        var m = match(/^@font-face\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@font-face missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@font-face missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'font-face',\r\n            declarations: decls\r\n        });\r\n    }\r\n    var atimport = _compileAtrule('import');\r\n    var atcharset = _compileAtrule('charset');\r\n    var atnamespace = _compileAtrule('namespace');\r\n    function _compileAtrule(name) {\r\n        var re = new RegExp('^@' + name + '\\\\s*([^;]+);');\r\n        return function () {\r\n            var pos = position();\r\n            var m = match(re);\r\n            if (!m) {\r\n                return;\r\n            }\r\n            var ret = { type: name };\r\n            ret[name] = m[1].trim();\r\n            return pos(ret);\r\n        };\r\n    }\r\n    function atrule() {\r\n        if (css[0] !== '@') {\r\n            return;\r\n        }\r\n        return (atkeyframes() ||\r\n            atmedia() ||\r\n            atcustommedia() ||\r\n            atsupports() ||\r\n            atimport() ||\r\n            atcharset() ||\r\n            atnamespace() ||\r\n            atdocument() ||\r\n            atpage() ||\r\n            athost() ||\r\n            atfontface());\r\n    }\r\n    function rule() {\r\n        var pos = position();\r\n        var sel = selector();\r\n        if (!sel) {\r\n            return error('selector missing');\r\n        }\r\n        comments();\r\n        return pos({\r\n            type: 'rule',\r\n            selectors: sel,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    return addParent(stylesheet());\r\n}\r\nfunction trim(str) {\r\n    return str ? str.replace(/^\\s+|\\s+$/g, '') : '';\r\n}\r\nfunction addParent(obj, parent) {\r\n    var isNode = obj && typeof obj.type === 'string';\r\n    var childParent = isNode ? obj : parent;\r\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\r\n        var k = _a[_i];\r\n        var value = obj[k];\r\n        if (Array.isArray(value)) {\r\n            value.forEach(function (v) {\r\n                addParent(v, childParent);\r\n            });\r\n        }\r\n        else if (value && typeof value === 'object') {\r\n            addParent(value, childParent);\r\n        }\r\n    }\r\n    if (isNode) {\r\n        Object.defineProperty(obj, 'parent', {\r\n            configurable: true,\r\n            writable: true,\r\n            enumerable: false,\r\n            value: parent || null\r\n        });\r\n    }\r\n    return obj;\r\n}\n\nvar tagMap = {\r\n    script: 'noscript',\r\n    altglyph: 'altGlyph',\r\n    altglyphdef: 'altGlyphDef',\r\n    altglyphitem: 'altGlyphItem',\r\n    animatecolor: 'animateColor',\r\n    animatemotion: 'animateMotion',\r\n    animatetransform: 'animateTransform',\r\n    clippath: 'clipPath',\r\n    feblend: 'feBlend',\r\n    fecolormatrix: 'feColorMatrix',\r\n    fecomponenttransfer: 'feComponentTransfer',\r\n    fecomposite: 'feComposite',\r\n    feconvolvematrix: 'feConvolveMatrix',\r\n    fediffuselighting: 'feDiffuseLighting',\r\n    fedisplacementmap: 'feDisplacementMap',\r\n    fedistantlight: 'feDistantLight',\r\n    fedropshadow: 'feDropShadow',\r\n    feflood: 'feFlood',\r\n    fefunca: 'feFuncA',\r\n    fefuncb: 'feFuncB',\r\n    fefuncg: 'feFuncG',\r\n    fefuncr: 'feFuncR',\r\n    fegaussianblur: 'feGaussianBlur',\r\n    feimage: 'feImage',\r\n    femerge: 'feMerge',\r\n    femergenode: 'feMergeNode',\r\n    femorphology: 'feMorphology',\r\n    feoffset: 'feOffset',\r\n    fepointlight: 'fePointLight',\r\n    fespecularlighting: 'feSpecularLighting',\r\n    fespotlight: 'feSpotLight',\r\n    fetile: 'feTile',\r\n    feturbulence: 'feTurbulence',\r\n    foreignobject: 'foreignObject',\r\n    glyphref: 'glyphRef',\r\n    lineargradient: 'linearGradient',\r\n    radialgradient: 'radialGradient'\r\n};\r\nfunction getTagName(n) {\r\n    var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;\r\n    if (tagName === 'link' && n.attributes._cssText) {\r\n        tagName = 'style';\r\n    }\r\n    return tagName;\r\n}\r\nfunction escapeRegExp(str) {\r\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n}\r\nvar HOVER_SELECTOR = /([^\\\\]):hover/;\r\nvar HOVER_SELECTOR_GLOBAL = new RegExp(HOVER_SELECTOR.source, 'g');\r\nfunction addHoverClass(cssText, cache) {\r\n    var cachedStyle = cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.get(cssText);\r\n    if (cachedStyle)\r\n        return cachedStyle;\r\n    var ast = parse(cssText, {\r\n        silent: true\r\n    });\r\n    if (!ast.stylesheet) {\r\n        return cssText;\r\n    }\r\n    var selectors = [];\r\n    ast.stylesheet.rules.forEach(function (rule) {\r\n        if ('selectors' in rule) {\r\n            (rule.selectors || []).forEach(function (selector) {\r\n                if (HOVER_SELECTOR.test(selector)) {\r\n                    selectors.push(selector);\r\n                }\r\n            });\r\n        }\r\n    });\r\n    if (selectors.length === 0) {\r\n        return cssText;\r\n    }\r\n    var selectorMatcher = new RegExp(selectors\r\n        .filter(function (selector, index) { return selectors.indexOf(selector) === index; })\r\n        .sort(function (a, b) { return b.length - a.length; })\r\n        .map(function (selector) {\r\n        return escapeRegExp(selector);\r\n    })\r\n        .join('|'), 'g');\r\n    var result = cssText.replace(selectorMatcher, function (selector) {\r\n        var newSelector = selector.replace(HOVER_SELECTOR_GLOBAL, '$1.\\\\:hover');\r\n        return \"\".concat(selector, \", \").concat(newSelector);\r\n    });\r\n    cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.set(cssText, result);\r\n    return result;\r\n}\r\nfunction createCache() {\r\n    var stylesWithHoverClass = new Map();\r\n    return {\r\n        stylesWithHoverClass: stylesWithHoverClass\r\n    };\r\n}\r\nfunction buildNode(n, options) {\r\n    var doc = options.doc, hackCss = options.hackCss, cache = options.cache;\r\n    switch (n.type) {\r\n        case NodeType.Document:\r\n            return doc.implementation.createDocument(null, '', null);\r\n        case NodeType.DocumentType:\r\n            return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);\r\n        case NodeType.Element: {\r\n            var tagName = getTagName(n);\r\n            var node_1;\r\n            if (n.isSVG) {\r\n                node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);\r\n            }\r\n            else {\r\n                node_1 = doc.createElement(tagName);\r\n            }\r\n            var specialAttributes = {};\r\n            for (var name_1 in n.attributes) {\r\n                if (!Object.prototype.hasOwnProperty.call(n.attributes, name_1)) {\r\n                    continue;\r\n                }\r\n                var value = n.attributes[name_1];\r\n                if (tagName === 'option' &&\r\n                    name_1 === 'selected' &&\r\n                    value === false) {\r\n                    continue;\r\n                }\r\n                if (value === true)\r\n                    value = '';\r\n                if (name_1.startsWith('rr_')) {\r\n                    specialAttributes[name_1] = value;\r\n                    continue;\r\n                }\r\n                var isTextarea = tagName === 'textarea' && name_1 === 'value';\r\n                var isRemoteOrDynamicCss = tagName === 'style' && name_1 === '_cssText';\r\n                if (isRemoteOrDynamicCss && hackCss && typeof value === 'string') {\r\n                    value = addHoverClass(value, cache);\r\n                }\r\n                if ((isTextarea || isRemoteOrDynamicCss) && typeof value === 'string') {\r\n                    var child = doc.createTextNode(value);\r\n                    for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {\r\n                        var c = _a[_i];\r\n                        if (c.nodeType === node_1.TEXT_NODE) {\r\n                            node_1.removeChild(c);\r\n                        }\r\n                    }\r\n                    node_1.appendChild(child);\r\n                    continue;\r\n                }\r\n                try {\r\n                    if (n.isSVG && name_1 === 'xlink:href') {\r\n                        node_1.setAttributeNS('http://www.w3.org/1999/xlink', name_1, value.toString());\r\n                    }\r\n                    else if (name_1 === 'onload' ||\r\n                        name_1 === 'onclick' ||\r\n                        name_1.substring(0, 7) === 'onmouse') {\r\n                        node_1.setAttribute('_' + name_1, value.toString());\r\n                    }\r\n                    else if (tagName === 'meta' &&\r\n                        n.attributes['http-equiv'] === 'Content-Security-Policy' &&\r\n                        name_1 === 'content') {\r\n                        node_1.setAttribute('csp-content', value.toString());\r\n                        continue;\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'preload' &&\r\n                        n.attributes.as === 'script') {\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'prefetch' &&\r\n                        typeof n.attributes.href === 'string' &&\r\n                        n.attributes.href.endsWith('.js')) {\r\n                    }\r\n                    else if (tagName === 'img' &&\r\n                        n.attributes.srcset &&\r\n                        n.attributes.rr_dataURL) {\r\n                        node_1.setAttribute('rrweb-original-srcset', n.attributes.srcset);\r\n                    }\r\n                    else {\r\n                        node_1.setAttribute(name_1, value.toString());\r\n                    }\r\n                }\r\n                catch (error) {\r\n                }\r\n            }\r\n            var _loop_1 = function (name_2) {\r\n                var value = specialAttributes[name_2];\r\n                if (tagName === 'canvas' && name_2 === 'rr_dataURL') {\r\n                    var image_1 = document.createElement('img');\r\n                    image_1.onload = function () {\r\n                        var ctx = node_1.getContext('2d');\r\n                        if (ctx) {\r\n                            ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);\r\n                        }\r\n                    };\r\n                    image_1.src = value.toString();\r\n                    if (node_1.RRNodeType)\r\n                        node_1.rr_dataURL = value.toString();\r\n                }\r\n                else if (tagName === 'img' && name_2 === 'rr_dataURL') {\r\n                    var image = node_1;\r\n                    if (!image.currentSrc.startsWith('data:')) {\r\n                        image.setAttribute('rrweb-original-src', n.attributes.src);\r\n                        image.src = value.toString();\r\n                    }\r\n                }\r\n                if (name_2 === 'rr_width') {\r\n                    node_1.style.width = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_height') {\r\n                    node_1.style.height = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_mediaCurrentTime' &&\r\n                    typeof value === 'number') {\r\n                    node_1.currentTime = value;\r\n                }\r\n                else if (name_2 === 'rr_mediaState') {\r\n                    switch (value) {\r\n                        case 'played':\r\n                            node_1\r\n                                .play()[\"catch\"](function (e) { return console.warn('media playback error', e); });\r\n                            break;\r\n                        case 'paused':\r\n                            node_1.pause();\r\n                            break;\r\n                    }\r\n                }\r\n            };\r\n            for (var name_2 in specialAttributes) {\r\n                _loop_1(name_2);\r\n            }\r\n            if (n.isShadowHost) {\r\n                if (!node_1.shadowRoot) {\r\n                    node_1.attachShadow({ mode: 'open' });\r\n                }\r\n                else {\r\n                    while (node_1.shadowRoot.firstChild) {\r\n                        node_1.shadowRoot.removeChild(node_1.shadowRoot.firstChild);\r\n                    }\r\n                }\r\n            }\r\n            return node_1;\r\n        }\r\n        case NodeType.Text:\r\n            return doc.createTextNode(n.isStyle && hackCss\r\n                ? addHoverClass(n.textContent, cache)\r\n                : n.textContent);\r\n        case NodeType.CDATA:\r\n            return doc.createCDATASection(n.textContent);\r\n        case NodeType.Comment:\r\n            return doc.createComment(n.textContent);\r\n        default:\r\n            return null;\r\n    }\r\n}\r\nfunction buildNodeWithSN(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.hackCss, hackCss = _b === void 0 ? true : _b, afterAppend = options.afterAppend, cache = options.cache;\r\n    var node = buildNode(n, { doc: doc, hackCss: hackCss, cache: cache });\r\n    if (!node) {\r\n        return null;\r\n    }\r\n    if (n.rootId && mirror.getNode(n.rootId) !== doc) {\r\n        mirror.replace(n.rootId, doc);\r\n    }\r\n    if (n.type === NodeType.Document) {\r\n        doc.close();\r\n        doc.open();\r\n        if (n.compatMode === 'BackCompat' &&\r\n            n.childNodes &&\r\n            n.childNodes[0].type !== NodeType.DocumentType) {\r\n            if (n.childNodes[0].type === NodeType.Element &&\r\n                'xmlns' in n.childNodes[0].attributes &&\r\n                n.childNodes[0].attributes.xmlns === 'http://www.w3.org/1999/xhtml') {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">');\r\n            }\r\n            else {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">');\r\n            }\r\n        }\r\n        node = doc;\r\n    }\r\n    mirror.add(node, n);\r\n    if ((n.type === NodeType.Document || n.type === NodeType.Element) &&\r\n        !skipChild) {\r\n        for (var _i = 0, _c = n.childNodes; _i < _c.length; _i++) {\r\n            var childN = _c[_i];\r\n            var childNode = buildNodeWithSN(childN, {\r\n                doc: doc,\r\n                mirror: mirror,\r\n                skipChild: false,\r\n                hackCss: hackCss,\r\n                afterAppend: afterAppend,\r\n                cache: cache\r\n            });\r\n            if (!childNode) {\r\n                console.warn('Failed to rebuild', childN);\r\n                continue;\r\n            }\r\n            if (childN.isShadow && isElement(node) && node.shadowRoot) {\r\n                node.shadowRoot.appendChild(childNode);\r\n            }\r\n            else {\r\n                node.appendChild(childNode);\r\n            }\r\n            if (afterAppend) {\r\n                afterAppend(childNode, childN.id);\r\n            }\r\n        }\r\n    }\r\n    return node;\r\n}\r\nfunction visit(mirror, onVisit) {\r\n    function walk(node) {\r\n        onVisit(node);\r\n    }\r\n    for (var _i = 0, _a = mirror.getIds(); _i < _a.length; _i++) {\r\n        var id = _a[_i];\r\n        if (mirror.has(id)) {\r\n            walk(mirror.getNode(id));\r\n        }\r\n    }\r\n}\r\nfunction handleScroll(node, mirror) {\r\n    var n = mirror.getMeta(node);\r\n    if ((n === null || n === void 0 ? void 0 : n.type) !== NodeType.Element) {\r\n        return;\r\n    }\r\n    var el = node;\r\n    for (var name_3 in n.attributes) {\r\n        if (!(Object.prototype.hasOwnProperty.call(n.attributes, name_3) &&\r\n            name_3.startsWith('rr_'))) {\r\n            continue;\r\n        }\r\n        var value = n.attributes[name_3];\r\n        if (name_3 === 'rr_scrollLeft') {\r\n            el.scrollLeft = value;\r\n        }\r\n        if (name_3 === 'rr_scrollTop') {\r\n            el.scrollTop = value;\r\n        }\r\n    }\r\n}\r\nfunction rebuild(n, options) {\r\n    var doc = options.doc, onVisit = options.onVisit, _a = options.hackCss, hackCss = _a === void 0 ? true : _a, afterAppend = options.afterAppend, cache = options.cache, _b = options.mirror, mirror = _b === void 0 ? new Mirror() : _b;\r\n    var node = buildNodeWithSN(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        skipChild: false,\r\n        hackCss: hackCss,\r\n        afterAppend: afterAppend,\r\n        cache: cache\r\n    });\r\n    visit(mirror, function (visitedNode) {\r\n        if (onVisit) {\r\n            onVisit(visitedNode);\r\n        }\r\n        handleScroll(visitedNode, mirror);\r\n    });\r\n    return node;\r\n}\n\nexport { IGNORED_NODE, Mirror, NodeType, addHoverClass, buildNodeWithSN, classMatchesRegex, cleanupSnapshot, createCache, createMirror, genId, getCssRuleString, getCssRulesString, is2DCanvasBlank, isCSSImportRule, isElement, isNativeShadowDom, isShadowRoot, maskInputValue, needMaskingText, rebuild, serializeNodeWithId, snapshot, transformAttribute, visitSnapshot };\n", "export function parseCSSText(cssText: string): Record<string, string> {\n  const res: Record<string, string> = {};\n  const listDelimiter = /;(?![^(]*\\))/g;\n  const propertyDelimiter = /:(.+)/;\n  const comment = /\\/\\*.*?\\*\\//g;\n  cssText\n    .replace(comment, '')\n    .split(listDelimiter)\n    .forEach(function (item) {\n      if (item) {\n        const tmp = item.split(propertyDelimiter);\n        tmp.length > 1 && (res[camelize(tmp[0].trim())] = tmp[1].trim());\n      }\n    });\n  return res;\n}\n\nexport function toCSSText(style: Record<string, string>): string {\n  const properties = [];\n  for (const name in style) {\n    const value = style[name];\n    if (typeof value !== 'string') continue;\n    const normalizedName = hyphenate(name);\n    properties.push(`${normalizedName}: ${value};`);\n  }\n  return properties.join(' ');\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nconst camelizeRE = /-([a-z])/g;\nconst CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9-]+$/;\nexport const camelize = (str: string): string => {\n  if (CUSTOM_PROPERTY_REGEX.test(str)) return str;\n  return str.replace(camelizeRE, (_, c: string) => (c ? c.toUpperCase() : ''));\n};\n\n/**\n * Hyphenate a camelCase string.\n */\nconst hyphenateRE = /\\B([A-Z])/g;\nexport const hyphenate = (str: string): string => {\n  return str.replace(hyphenateRE, '-$1').toLowerCase();\n};\n", "import { NodeType as RRNodeType } from 'rrweb-snapshot';\nimport { parseCSSText, camelize, toCSSText } from './style';\nexport interface IRRNode {\n  parentElement: IRRNode | null;\n  parentNode: IRRNode | null;\n  childNodes: IRRNode[];\n  ownerDocument: IRRDocument;\n  readonly ELEMENT_NODE: number;\n  readonly TEXT_NODE: number;\n  // corresponding nodeType value of standard HTML Node\n  readonly nodeType: number;\n  readonly nodeName: string; // https://dom.spec.whatwg.org/#dom-node-nodename\n  readonly RRNodeType: RRNodeType;\n\n  firstChild: IRRNode | null;\n\n  lastChild: IRRNode | null;\n\n  nextSibling: IRRNode | null;\n\n  textContent: string | null;\n\n  contains(node: IRRNode): boolean;\n\n  appendChild(newChild: IRRNode): IRRNode;\n\n  insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;\n\n  removeChild(node: IRRNode): IRRNode;\n\n  toString(): string;\n}\nexport interface IRRDocument extends IRRNode {\n  documentElement: IRRElement | null;\n\n  body: IRRElement | null;\n\n  head: IRRElement | null;\n\n  implementation: IRRDocument;\n\n  firstElementChild: IRRElement | null;\n\n  readonly nodeName: '#document';\n\n  compatMode: 'BackCompat' | 'CSS1Compat';\n\n  createDocument(\n    _namespace: string | null,\n    _qualifiedName: string | null,\n    _doctype?: DocumentType | null,\n  ): IRRDocument;\n\n  createDocumentType(\n    qualifiedName: string,\n    publicId: string,\n    systemId: string,\n  ): IRRDocumentType;\n\n  createElement(tagName: string): IRRElement;\n\n  createElementNS(_namespaceURI: string, qualifiedName: string): IRRElement;\n\n  createTextNode(data: string): IRRText;\n\n  createComment(data: string): IRRComment;\n\n  createCDATASection(data: string): IRRCDATASection;\n\n  open(): void;\n\n  close(): void;\n\n  write(content: string): void;\n}\nexport interface IRRElement extends IRRNode {\n  tagName: string;\n  attributes: Record<string, string>;\n  shadowRoot: IRRElement | null;\n  scrollLeft?: number;\n  scrollTop?: number;\n  id: string;\n  className: string;\n  classList: ClassList;\n  style: CSSStyleDeclaration;\n\n  attachShadow(init: ShadowRootInit): IRRElement;\n\n  getAttribute(name: string): string | null;\n\n  setAttribute(name: string, attribute: string): void;\n\n  setAttributeNS(\n    namespace: string | null,\n    qualifiedName: string,\n    value: string,\n  ): void;\n\n  removeAttribute(name: string): void;\n\n  dispatchEvent(event: Event): boolean;\n}\nexport interface IRRDocumentType extends IRRNode {\n  readonly name: string;\n  readonly publicId: string;\n  readonly systemId: string;\n}\nexport interface IRRText extends IRRNode {\n  readonly nodeName: '#text';\n  data: string;\n}\nexport interface IRRComment extends IRRNode {\n  readonly nodeName: '#comment';\n  data: string;\n}\nexport interface IRRCDATASection extends IRRNode {\n  readonly nodeName: '#cdata-section';\n  data: string;\n}\n\ntype ConstrainedConstructor<T = Record<string, unknown>> = new (\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ...args: any[]\n) => T;\n\n/**\n * This is designed as an abstract class so it should never be instantiated.\n */\nexport class BaseRRNode implements IRRNode {\n  public childNodes: IRRNode[] = [];\n  public parentElement: IRRNode | null = null;\n  public parentNode: IRRNode | null = null;\n  public textContent: string | null;\n  public ownerDocument: IRRDocument;\n  public readonly ELEMENT_NODE: number = NodeType.ELEMENT_NODE;\n  public readonly TEXT_NODE: number = NodeType.TEXT_NODE;\n  // corresponding nodeType value of standard HTML Node\n  public readonly nodeType: number;\n  public readonly nodeName: string;\n  public readonly RRNodeType: RRNodeType;\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any\n  constructor(..._args: any[]) {\n    //\n  }\n\n  public get firstChild(): IRRNode | null {\n    return this.childNodes[0] || null;\n  }\n\n  public get lastChild(): IRRNode | null {\n    return this.childNodes[this.childNodes.length - 1] || null;\n  }\n\n  public get nextSibling(): IRRNode | null {\n    const parentNode = this.parentNode;\n    if (!parentNode) return null;\n    const siblings = parentNode.childNodes;\n    const index = siblings.indexOf(this);\n    return siblings[index + 1] || null;\n  }\n\n  public contains(node: IRRNode) {\n    if (node === this) return true;\n    for (const child of this.childNodes) {\n      if (child.contains(node)) return true;\n    }\n    return false;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  public appendChild(_newChild: IRRNode): IRRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  public insertBefore(_newChild: IRRNode, _refChild: IRRNode | null): IRRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  public removeChild(_node: IRRNode): IRRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  public toString(): string {\n    return 'RRNode';\n  }\n}\n\nexport function BaseRRDocumentImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  return class BaseRRDocument extends RRNodeClass implements IRRDocument {\n    public readonly nodeType: number = NodeType.DOCUMENT_NODE;\n    public readonly nodeName: '#document' = '#document';\n    public readonly compatMode: 'BackCompat' | 'CSS1Compat' = 'CSS1Compat';\n    public readonly RRNodeType = RRNodeType.Document;\n    public textContent: string | null = null;\n\n    public get documentElement(): IRRElement | null {\n      return (\n        (this.childNodes.find(\n          (node) =>\n            node.RRNodeType === RRNodeType.Element &&\n            (node as IRRElement).tagName === 'HTML',\n        ) as IRRElement) || null\n      );\n    }\n\n    public get body(): IRRElement | null {\n      return (\n        (this.documentElement?.childNodes.find(\n          (node) =>\n            node.RRNodeType === RRNodeType.Element &&\n            (node as IRRElement).tagName === 'BODY',\n        ) as IRRElement) || null\n      );\n    }\n\n    public get head(): IRRElement | null {\n      return (\n        (this.documentElement?.childNodes.find(\n          (node) =>\n            node.RRNodeType === RRNodeType.Element &&\n            (node as IRRElement).tagName === 'HEAD',\n        ) as IRRElement) || null\n      );\n    }\n\n    public get implementation(): IRRDocument {\n      return this;\n    }\n\n    public get firstElementChild(): IRRElement | null {\n      return this.documentElement;\n    }\n\n    public appendChild(childNode: IRRNode): IRRNode {\n      const nodeType = childNode.RRNodeType;\n      if (\n        nodeType === RRNodeType.Element ||\n        nodeType === RRNodeType.DocumentType\n      ) {\n        if (this.childNodes.some((s) => s.RRNodeType === nodeType)) {\n          throw new Error(\n            `RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${\n              nodeType === RRNodeType.Element ? 'RRElement' : 'RRDoctype'\n            } on RRDocument allowed.`,\n          );\n        }\n      }\n      childNode.parentElement = null;\n      childNode.parentNode = this;\n      this.childNodes.push(childNode);\n      return childNode;\n    }\n\n    public insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode {\n      const nodeType = newChild.RRNodeType;\n      if (\n        nodeType === RRNodeType.Element ||\n        nodeType === RRNodeType.DocumentType\n      ) {\n        if (this.childNodes.some((s) => s.RRNodeType === nodeType)) {\n          throw new Error(\n            `RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${\n              nodeType === RRNodeType.Element ? 'RRElement' : 'RRDoctype'\n            } on RRDocument allowed.`,\n          );\n        }\n      }\n      if (refChild === null) return this.appendChild(newChild);\n      const childIndex = this.childNodes.indexOf(refChild);\n      if (childIndex == -1)\n        throw new Error(\n          \"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(childIndex, 0, newChild);\n      newChild.parentElement = null;\n      newChild.parentNode = this;\n      return newChild;\n    }\n\n    public removeChild(node: IRRNode) {\n      const indexOfChild = this.childNodes.indexOf(node);\n      if (indexOfChild === -1)\n        throw new Error(\n          \"Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(indexOfChild, 1);\n      node.parentElement = null;\n      node.parentNode = null;\n      return node;\n    }\n\n    public open() {\n      this.childNodes = [];\n    }\n\n    public close() {\n      //\n    }\n\n    /**\n     * Adhoc implementation for setting xhtml namespace in rebuilt.ts (rrweb-snapshot).\n     * There are two lines used this function:\n     * 1. doc.write('\\<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\"\\>')\n     * 2. doc.write('\\<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\"\\>')\n     */\n    public write(content: string) {\n      let publicId;\n      if (\n        content ===\n        '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">'\n      )\n        publicId = '-//W3C//DTD XHTML 1.0 Transitional//EN';\n      else if (\n        content ===\n        '<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">'\n      )\n        publicId = '-//W3C//DTD HTML 4.0 Transitional//EN';\n      if (publicId) {\n        const doctype = this.createDocumentType('html', publicId, '');\n        this.open();\n        this.appendChild(doctype);\n      }\n    }\n\n    createDocument(\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      _namespace: string | null,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      _qualifiedName: string | null,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      _doctype?: DocumentType | null,\n    ): IRRDocument {\n      return new BaseRRDocument();\n    }\n\n    createDocumentType(\n      qualifiedName: string,\n      publicId: string,\n      systemId: string,\n    ): IRRDocumentType {\n      const doctype = new (BaseRRDocumentTypeImpl(BaseRRNode))(\n        qualifiedName,\n        publicId,\n        systemId,\n      );\n      doctype.ownerDocument = this;\n      return doctype;\n    }\n\n    createElement(tagName: string): IRRElement {\n      const element = new (BaseRRElementImpl(BaseRRNode))(tagName);\n      element.ownerDocument = this;\n      return element;\n    }\n\n    createElementNS(_namespaceURI: string, qualifiedName: string): IRRElement {\n      return this.createElement(qualifiedName);\n    }\n\n    createTextNode(data: string): IRRText {\n      const text = new (BaseRRTextImpl(BaseRRNode))(data);\n      text.ownerDocument = this;\n      return text;\n    }\n\n    createComment(data: string): IRRComment {\n      const comment = new (BaseRRCommentImpl(BaseRRNode))(data);\n      comment.ownerDocument = this;\n      return comment;\n    }\n\n    createCDATASection(data: string): IRRCDATASection {\n      const CDATASection = new (BaseRRCDATASectionImpl(BaseRRNode))(data);\n      CDATASection.ownerDocument = this;\n      return CDATASection;\n    }\n\n    toString() {\n      return 'RRDocument';\n    }\n  };\n}\n\nexport function BaseRRDocumentTypeImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRDocumentType\n    extends RRNodeClass\n    implements IRRDocumentType {\n    public readonly nodeType: number = NodeType.DOCUMENT_TYPE_NODE;\n    public readonly RRNodeType = RRNodeType.DocumentType;\n    public readonly nodeName: string;\n    public readonly name: string;\n    public readonly publicId: string;\n    public readonly systemId: string;\n    public textContent: string | null = null;\n\n    constructor(qualifiedName: string, publicId: string, systemId: string) {\n      super();\n      this.name = qualifiedName;\n      this.publicId = publicId;\n      this.systemId = systemId;\n      this.nodeName = qualifiedName;\n    }\n\n    toString() {\n      return 'RRDocumentType';\n    }\n  };\n}\n\nexport function BaseRRElementImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRElement extends RRNodeClass implements IRRElement {\n    public readonly nodeType: number = NodeType.ELEMENT_NODE;\n    public readonly RRNodeType = RRNodeType.Element;\n    public readonly nodeName: string;\n    public tagName: string;\n    public attributes: Record<string, string> = {};\n    public shadowRoot: IRRElement | null = null;\n    public scrollLeft?: number;\n    public scrollTop?: number;\n\n    constructor(tagName: string) {\n      super();\n      this.tagName = tagName.toUpperCase();\n      this.nodeName = tagName.toUpperCase();\n    }\n\n    public get textContent(): string {\n      let result = '';\n      this.childNodes.forEach((node) => (result += node.textContent));\n      return result;\n    }\n\n    public set textContent(textContent: string) {\n      this.childNodes = [this.ownerDocument.createTextNode(textContent)];\n    }\n\n    public get classList(): ClassList {\n      return new ClassList(\n        this.attributes.class as string | undefined,\n        (newClassName) => {\n          this.attributes.class = newClassName;\n        },\n      );\n    }\n\n    public get id() {\n      return this.attributes.id || '';\n    }\n\n    public get className() {\n      return this.attributes.class || '';\n    }\n\n    public get style() {\n      const style = (this.attributes.style\n        ? parseCSSText(this.attributes.style)\n        : {}) as CSSStyleDeclaration;\n      const hyphenateRE = /\\B([A-Z])/g;\n      style.setProperty = (\n        name: string,\n        value: string | null,\n        priority?: string,\n      ) => {\n        if (hyphenateRE.test(name)) return;\n        const normalizedName = camelize(name);\n        if (!value) delete style[normalizedName];\n        else style[normalizedName] = value;\n        if (priority === 'important') style[normalizedName] += ' !important';\n        this.attributes.style = toCSSText(style);\n      };\n      style.removeProperty = (name: string) => {\n        if (hyphenateRE.test(name)) return '';\n        const normalizedName = camelize(name);\n        const value = style[normalizedName] || '';\n        delete style[normalizedName];\n        this.attributes.style = toCSSText(style);\n        return value;\n      };\n      return style;\n    }\n\n    public getAttribute(name: string) {\n      return this.attributes[name] || null;\n    }\n\n    public setAttribute(name: string, attribute: string) {\n      this.attributes[name] = attribute;\n    }\n\n    public setAttributeNS(\n      _namespace: string | null,\n      qualifiedName: string,\n      value: string,\n    ): void {\n      this.setAttribute(qualifiedName, value);\n    }\n\n    public removeAttribute(name: string) {\n      delete this.attributes[name];\n    }\n\n    public appendChild(newChild: IRRNode): IRRNode {\n      this.childNodes.push(newChild);\n      newChild.parentNode = this;\n      newChild.parentElement = this;\n      return newChild;\n    }\n\n    public insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode {\n      if (refChild === null) return this.appendChild(newChild);\n      const childIndex = this.childNodes.indexOf(refChild);\n      if (childIndex == -1)\n        throw new Error(\n          \"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(childIndex, 0, newChild);\n      newChild.parentElement = this;\n      newChild.parentNode = this;\n      return newChild;\n    }\n\n    public removeChild(node: IRRNode): IRRNode {\n      const indexOfChild = this.childNodes.indexOf(node);\n      if (indexOfChild === -1)\n        throw new Error(\n          \"Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(indexOfChild, 1);\n      node.parentElement = null;\n      node.parentNode = null;\n      return node;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    public attachShadow(_init: ShadowRootInit): IRRElement {\n      const shadowRoot = this.ownerDocument.createElement('SHADOWROOT');\n      this.shadowRoot = shadowRoot;\n      return shadowRoot;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    public dispatchEvent(_event: Event) {\n      return true;\n    }\n\n    toString() {\n      let attributeString = '';\n      for (const attribute in this.attributes) {\n        attributeString += `${attribute}=\"${this.attributes[attribute]}\" `;\n      }\n      return `${this.tagName} ${attributeString}`;\n    }\n  };\n}\n\nexport function BaseRRMediaElementImpl<\n  RRElement extends ConstrainedConstructor<IRRElement>\n>(RRElementClass: RRElement) {\n  return class BaseRRMediaElement extends RRElementClass {\n    public currentTime?: number;\n    public volume?: number;\n    public paused?: boolean;\n    public muted?: boolean;\n    public playbackRate?: number;\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    attachShadow(_init: ShadowRootInit): IRRElement {\n      throw new Error(\n        `RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow`,\n      );\n    }\n    public play() {\n      this.paused = false;\n    }\n    public pause() {\n      this.paused = true;\n    }\n  };\n}\n\nexport function BaseRRTextImpl<RRNode extends ConstrainedConstructor<IRRNode>>(\n  RRNodeClass: RRNode,\n) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRText extends RRNodeClass implements IRRText {\n    public readonly nodeType: number = NodeType.TEXT_NODE;\n    public readonly nodeName: '#text' = '#text';\n    public readonly RRNodeType = RRNodeType.Text;\n    public data: string;\n\n    constructor(data: string) {\n      super();\n      this.data = data;\n    }\n\n    public get textContent(): string {\n      return this.data;\n    }\n\n    public set textContent(textContent: string) {\n      this.data = textContent;\n    }\n\n    toString() {\n      return `RRText text=${JSON.stringify(this.data)}`;\n    }\n  };\n}\n\nexport function BaseRRCommentImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRComment extends RRNodeClass implements IRRComment {\n    public readonly nodeType: number = NodeType.COMMENT_NODE;\n    public readonly nodeName: '#comment' = '#comment';\n    public readonly RRNodeType = RRNodeType.Comment;\n    public data: string;\n\n    constructor(data: string) {\n      super();\n      this.data = data;\n    }\n\n    public get textContent(): string {\n      return this.data;\n    }\n\n    public set textContent(textContent: string) {\n      this.data = textContent;\n    }\n\n    toString() {\n      return `RRComment text=${JSON.stringify(this.data)}`;\n    }\n  };\n}\n\nexport function BaseRRCDATASectionImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRCDATASection\n    extends RRNodeClass\n    implements IRRCDATASection {\n    public readonly nodeName: '#cdata-section' = '#cdata-section';\n    public readonly nodeType: number = NodeType.CDATA_SECTION_NODE;\n    public readonly RRNodeType = RRNodeType.CDATA;\n    public data: string;\n\n    constructor(data: string) {\n      super();\n      this.data = data;\n    }\n\n    public get textContent(): string {\n      return this.data;\n    }\n\n    public set textContent(textContent: string) {\n      this.data = textContent;\n    }\n\n    toString() {\n      return `RRCDATASection data=${JSON.stringify(this.data)}`;\n    }\n  };\n}\n\nexport class ClassList {\n  private onChange: ((newClassText: string) => void) | undefined;\n  classes: string[] = [];\n\n  constructor(\n    classText?: string,\n    onChange?: ((newClassText: string) => void) | undefined,\n  ) {\n    if (classText) {\n      const classes = classText.trim().split(/\\s+/);\n      this.classes.push(...classes);\n    }\n    this.onChange = onChange;\n  }\n\n  add = (...classNames: string[]) => {\n    for (const item of classNames) {\n      const className = String(item);\n      if (this.classes.indexOf(className) >= 0) continue;\n      this.classes.push(className);\n    }\n    this.onChange && this.onChange(this.classes.join(' '));\n  };\n\n  remove = (...classNames: string[]) => {\n    this.classes = this.classes.filter(\n      (item) => classNames.indexOf(item) === -1,\n    );\n    this.onChange && this.onChange(this.classes.join(' '));\n  };\n}\n\nexport type CSSStyleDeclaration = Record<string, string> & {\n  setProperty: (\n    name: string,\n    value: string | null,\n    priority?: string | null,\n  ) => void;\n  removeProperty: (name: string) => string;\n};\n\n// Enumerate nodeType value of standard HTML Node.\nexport enum NodeType {\n  PLACEHOLDER, // This isn't a node type. Enum type value starts from zero but NodeType value starts from 1.\n  ELEMENT_NODE,\n  ATTRIBUTE_NODE,\n  TEXT_NODE,\n  CDATA_SECTION_NODE,\n  ENTITY_REFERENCE_NODE,\n  ENTITY_NODE,\n  PROCESSING_INSTRUCTION_NODE,\n  COMMENT_NODE,\n  DOCUMENT_NODE,\n  DOCUMENT_TYPE_NODE,\n  DOCUMENT_FRAGMENT_NODE,\n}\n", "import { NodeType as RRNodeType, Mirror as NodeMirror } from 'rrweb-snapshot';\nimport type {\n  canvasMutationData,\n  canvasEventWithTime,\n  inputData,\n  scrollData,\n  styleDeclarationData,\n  styleSheetRuleData,\n} from '@rrweb/types';\nimport type {\n  IRRCDATASection,\n  IRRComment,\n  IRRDocument,\n  IRRDocumentType,\n  IRRElement,\n  IRRNode,\n  IRRText,\n} from './document';\nimport type {\n  RRCanvasElement,\n  RRElement,\n  RRIFrameElement,\n  RRMediaElement,\n  RRStyleElement,\n  RRDocument,\n  Mirror,\n} from '.';\n\nconst NAMESPACES: Record<string, string> = {\n  svg: 'http://www.w3.org/2000/svg',\n  'xlink:href': 'http://www.w3.org/1999/xlink',\n  xmlns: 'http://www.w3.org/2000/xmlns/',\n};\n\n// camel case svg element tag names\nconst SVGTagMap: Record<string, string> = {\n  altglyph: 'altGlyph',\n  altglyphdef: 'altGlyphDef',\n  altglyphitem: 'altGlyphItem',\n  animatecolor: 'animateColor',\n  animatemotion: 'animateMotion',\n  animatetransform: 'animateTransform',\n  clippath: 'clipPath',\n  feblend: 'feBlend',\n  fecolormatrix: 'feColorMatrix',\n  fecomponenttransfer: 'feComponentTransfer',\n  fecomposite: 'feComposite',\n  feconvolvematrix: 'feConvolveMatrix',\n  fediffuselighting: 'feDiffuseLighting',\n  fedisplacementmap: 'feDisplacementMap',\n  fedistantlight: 'feDistantLight',\n  fedropshadow: 'feDropShadow',\n  feflood: 'feFlood',\n  fefunca: 'feFuncA',\n  fefuncb: 'feFuncB',\n  fefuncg: 'feFuncG',\n  fefuncr: 'feFuncR',\n  fegaussianblur: 'feGaussianBlur',\n  feimage: 'feImage',\n  femerge: 'feMerge',\n  femergenode: 'feMergeNode',\n  femorphology: 'feMorphology',\n  feoffset: 'feOffset',\n  fepointlight: 'fePointLight',\n  fespecularlighting: 'feSpecularLighting',\n  fespotlight: 'feSpotLight',\n  fetile: 'feTile',\n  feturbulence: 'feTurbulence',\n  foreignobject: 'foreignObject',\n  glyphref: 'glyphRef',\n  lineargradient: 'linearGradient',\n  radialgradient: 'radialGradient',\n};\n\nexport type ReplayerHandler = {\n  mirror: NodeMirror;\n  applyCanvas: (\n    canvasEvent: canvasEventWithTime,\n    canvasMutationData: canvasMutationData,\n    target: HTMLCanvasElement,\n  ) => void;\n  applyInput: (data: inputData) => void;\n  applyScroll: (data: scrollData, isSync: boolean) => void;\n  applyStyleSheetMutation: (\n    data: styleDeclarationData | styleSheetRuleData,\n    styleSheet: CSSStyleSheet,\n  ) => void;\n};\n\nexport function diff(\n  oldTree: Node,\n  newTree: IRRNode,\n  replayer: ReplayerHandler,\n  rrnodeMirror?: Mirror,\n) {\n  const oldChildren = oldTree.childNodes;\n  const newChildren = newTree.childNodes;\n  rrnodeMirror =\n    rrnodeMirror ||\n    (newTree as RRDocument).mirror ||\n    (newTree.ownerDocument as RRDocument).mirror;\n\n  if (oldChildren.length > 0 || newChildren.length > 0) {\n    diffChildren(\n      Array.from(oldChildren),\n      newChildren,\n      oldTree,\n      replayer,\n      rrnodeMirror,\n    );\n  }\n\n  let inputDataToApply = null,\n    scrollDataToApply = null;\n  switch (newTree.RRNodeType) {\n    case RRNodeType.Document: {\n      const newRRDocument = newTree as IRRDocument;\n      scrollDataToApply = (newRRDocument as RRDocument).scrollData;\n      break;\n    }\n    case RRNodeType.Element: {\n      const oldElement = oldTree as HTMLElement;\n      const newRRElement = newTree as IRRElement;\n      diffProps(oldElement, newRRElement, rrnodeMirror);\n      scrollDataToApply = (newRRElement as RRElement).scrollData;\n      inputDataToApply = (newRRElement as RRElement).inputData;\n      switch (newRRElement.tagName) {\n        case 'AUDIO':\n        case 'VIDEO': {\n          const oldMediaElement = oldTree as HTMLMediaElement;\n          const newMediaRRElement = newRRElement as RRMediaElement;\n          if (newMediaRRElement.paused !== undefined)\n            newMediaRRElement.paused\n              ? void oldMediaElement.pause()\n              : void oldMediaElement.play();\n          if (newMediaRRElement.muted !== undefined)\n            oldMediaElement.muted = newMediaRRElement.muted;\n          if (newMediaRRElement.volume !== undefined)\n            oldMediaElement.volume = newMediaRRElement.volume;\n          if (newMediaRRElement.currentTime !== undefined)\n            oldMediaElement.currentTime = newMediaRRElement.currentTime;\n          if (newMediaRRElement.playbackRate !== undefined)\n            oldMediaElement.playbackRate = newMediaRRElement.playbackRate;\n          break;\n        }\n        case 'CANVAS':\n          {\n            const rrCanvasElement = newTree as RRCanvasElement;\n            // This canvas element is created with initial data in an iframe element. https://github.com/rrweb-io/rrweb/pull/944\n            if (rrCanvasElement.rr_dataURL !== null) {\n              const image = document.createElement('img');\n              image.onload = () => {\n                const ctx = (oldElement as HTMLCanvasElement).getContext('2d');\n                if (ctx) {\n                  ctx.drawImage(image, 0, 0, image.width, image.height);\n                }\n              };\n              image.src = rrCanvasElement.rr_dataURL;\n            }\n            rrCanvasElement.canvasMutations.forEach((canvasMutation) =>\n              replayer.applyCanvas(\n                canvasMutation.event,\n                canvasMutation.mutation,\n                oldTree as HTMLCanvasElement,\n              ),\n            );\n          }\n          break;\n        case 'STYLE':\n          {\n            const styleSheet = (oldElement as HTMLStyleElement).sheet;\n            styleSheet &&\n              (newTree as RRStyleElement).rules.forEach((data) =>\n                replayer.applyStyleSheetMutation(data, styleSheet),\n              );\n          }\n          break;\n      }\n      if (newRRElement.shadowRoot) {\n        if (!oldElement.shadowRoot) oldElement.attachShadow({ mode: 'open' });\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const oldChildren = oldElement.shadowRoot!.childNodes;\n        const newChildren = newRRElement.shadowRoot.childNodes;\n        if (oldChildren.length > 0 || newChildren.length > 0)\n          diffChildren(\n            Array.from(oldChildren),\n            newChildren,\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            oldElement.shadowRoot!,\n            replayer,\n            rrnodeMirror,\n          );\n      }\n      break;\n    }\n    case RRNodeType.Text:\n    case RRNodeType.Comment:\n    case RRNodeType.CDATA:\n      if (\n        oldTree.textContent !==\n        (newTree as IRRText | IRRComment | IRRCDATASection).data\n      )\n        oldTree.textContent = (newTree as\n          | IRRText\n          | IRRComment\n          | IRRCDATASection).data;\n      break;\n    default:\n  }\n\n  scrollDataToApply && replayer.applyScroll(scrollDataToApply, true);\n  /**\n   * Input data need to get applied after all children of this node are updated.\n   * Otherwise when we set a value for a select element whose options are empty, the value won't actually update.\n   */\n  inputDataToApply && replayer.applyInput(inputDataToApply);\n\n  // IFrame element doesn't have child nodes.\n  if (newTree.nodeName === 'IFRAME') {\n    const oldContentDocument = (oldTree as HTMLIFrameElement).contentDocument;\n    const newIFrameElement = newTree as RRIFrameElement;\n    // If the iframe is cross-origin, the contentDocument will be null.\n    if (oldContentDocument) {\n      const sn = rrnodeMirror.getMeta(newIFrameElement.contentDocument);\n      if (sn) {\n        replayer.mirror.add(oldContentDocument, { ...sn });\n      }\n      diff(\n        oldContentDocument,\n        newIFrameElement.contentDocument,\n        replayer,\n        rrnodeMirror,\n      );\n    }\n  }\n}\n\nfunction diffProps(\n  oldTree: HTMLElement,\n  newTree: IRRElement,\n  rrnodeMirror: Mirror,\n) {\n  const oldAttributes = oldTree.attributes;\n  const newAttributes = newTree.attributes;\n\n  for (const name in newAttributes) {\n    const newValue = newAttributes[name];\n    const sn = rrnodeMirror.getMeta(newTree);\n    if (sn && 'isSVG' in sn && sn.isSVG && NAMESPACES[name])\n      oldTree.setAttributeNS(NAMESPACES[name], name, newValue);\n    else if (newTree.tagName === 'CANVAS' && name === 'rr_dataURL') {\n      const image = document.createElement('img');\n      image.src = newValue;\n      image.onload = () => {\n        const ctx = (oldTree as HTMLCanvasElement).getContext('2d');\n        if (ctx) {\n          ctx.drawImage(image, 0, 0, image.width, image.height);\n        }\n      };\n    } else oldTree.setAttribute(name, newValue);\n  }\n\n  for (const { name } of Array.from(oldAttributes))\n    if (!(name in newAttributes)) oldTree.removeAttribute(name);\n\n  newTree.scrollLeft && (oldTree.scrollLeft = newTree.scrollLeft);\n  newTree.scrollTop && (oldTree.scrollTop = newTree.scrollTop);\n}\n\nfunction diffChildren(\n  oldChildren: (Node | undefined)[],\n  newChildren: IRRNode[],\n  parentNode: Node,\n  replayer: ReplayerHandler,\n  rrnodeMirror: Mirror,\n) {\n  let oldStartIndex = 0,\n    oldEndIndex = oldChildren.length - 1,\n    newStartIndex = 0,\n    newEndIndex = newChildren.length - 1;\n  let oldStartNode = oldChildren[oldStartIndex],\n    oldEndNode = oldChildren[oldEndIndex],\n    newStartNode = newChildren[newStartIndex],\n    newEndNode = newChildren[newEndIndex];\n  let oldIdToIndex: Record<number, number> | undefined = undefined,\n    indexInOld;\n  while (oldStartIndex <= oldEndIndex && newStartIndex <= newEndIndex) {\n    const oldStartId = replayer.mirror.getId(oldStartNode);\n    const oldEndId = replayer.mirror.getId(oldEndNode);\n    const newStartId = rrnodeMirror.getId(newStartNode);\n    const newEndId = rrnodeMirror.getId(newEndNode);\n\n    // rrdom contains elements with negative ids, we don't want to accidentally match those to a mirror mismatch (-1) id.\n    // Negative oldStartId happen when nodes are not in the mirror, but are in the DOM.\n    // eg.iframes come with a document, html, head and body nodes.\n    // thats why below we always check if an id is negative.\n\n    if (oldStartNode === undefined) {\n      oldStartNode = oldChildren[++oldStartIndex];\n    } else if (oldEndNode === undefined) {\n      oldEndNode = oldChildren[--oldEndIndex];\n    } else if (\n      oldStartId !== -1 &&\n      // same first element?\n      oldStartId === newStartId\n    ) {\n      diff(oldStartNode, newStartNode, replayer, rrnodeMirror);\n      oldStartNode = oldChildren[++oldStartIndex];\n      newStartNode = newChildren[++newStartIndex];\n    } else if (\n      oldEndId !== -1 &&\n      // same last element?\n      oldEndId === newEndId\n    ) {\n      diff(oldEndNode, newEndNode, replayer, rrnodeMirror);\n      oldEndNode = oldChildren[--oldEndIndex];\n      newEndNode = newChildren[--newEndIndex];\n    } else if (\n      oldStartId !== -1 &&\n      // is the first old element the same as the last new element?\n      oldStartId === newEndId\n    ) {\n      parentNode.insertBefore(oldStartNode, oldEndNode.nextSibling);\n      diff(oldStartNode, newEndNode, replayer, rrnodeMirror);\n      oldStartNode = oldChildren[++oldStartIndex];\n      newEndNode = newChildren[--newEndIndex];\n    } else if (\n      oldEndId !== -1 &&\n      // is the last old element the same as the first new element?\n      oldEndId === newStartId\n    ) {\n      parentNode.insertBefore(oldEndNode, oldStartNode);\n      diff(oldEndNode, newStartNode, replayer, rrnodeMirror);\n      oldEndNode = oldChildren[--oldEndIndex];\n      newStartNode = newChildren[++newStartIndex];\n    } else {\n      // none of the elements matched\n\n      if (!oldIdToIndex) {\n        oldIdToIndex = {};\n        for (let i = oldStartIndex; i <= oldEndIndex; i++) {\n          const oldChild = oldChildren[i];\n          if (oldChild && replayer.mirror.hasNode(oldChild))\n            oldIdToIndex[replayer.mirror.getId(oldChild)] = i;\n        }\n      }\n      indexInOld = oldIdToIndex[rrnodeMirror.getId(newStartNode)];\n      if (indexInOld) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const nodeToMove = oldChildren[indexInOld]!;\n        parentNode.insertBefore(nodeToMove, oldStartNode);\n        diff(nodeToMove, newStartNode, replayer, rrnodeMirror);\n        oldChildren[indexInOld] = undefined;\n      } else {\n        const newNode = createOrGetNode(\n          newStartNode,\n          replayer.mirror,\n          rrnodeMirror,\n        );\n\n        /**\n         * A mounted iframe element has an automatically created HTML element.\n         * We should delete it before insert a serialized one. Otherwise, an error 'Only one element on document allowed' will be thrown.\n         */\n        if (\n          parentNode.nodeName === '#document' &&\n          replayer.mirror.getMeta(newNode)?.type === RRNodeType.Element &&\n          (parentNode as Document).documentElement\n        ) {\n          parentNode.removeChild((parentNode as Document).documentElement);\n          oldChildren[oldStartIndex] = undefined;\n          oldStartNode = undefined;\n        }\n        parentNode.insertBefore(newNode, oldStartNode || null);\n        diff(newNode, newStartNode, replayer, rrnodeMirror);\n      }\n      newStartNode = newChildren[++newStartIndex];\n    }\n  }\n  if (oldStartIndex > oldEndIndex) {\n    const referenceRRNode = newChildren[newEndIndex + 1];\n    let referenceNode = null;\n    if (referenceRRNode)\n      parentNode.childNodes.forEach((child) => {\n        if (\n          replayer.mirror.getId(child) === rrnodeMirror.getId(referenceRRNode)\n        )\n          referenceNode = child;\n      });\n    for (; newStartIndex <= newEndIndex; ++newStartIndex) {\n      const newNode = createOrGetNode(\n        newChildren[newStartIndex],\n        replayer.mirror,\n        rrnodeMirror,\n      );\n      parentNode.insertBefore(newNode, referenceNode);\n      diff(newNode, newChildren[newStartIndex], replayer, rrnodeMirror);\n    }\n  } else if (newStartIndex > newEndIndex) {\n    for (; oldStartIndex <= oldEndIndex; oldStartIndex++) {\n      const node = oldChildren[oldStartIndex];\n      if (node) {\n        parentNode.removeChild(node);\n        replayer.mirror.removeNodeFromMap(node);\n      }\n    }\n  }\n}\n\nexport function createOrGetNode(\n  rrNode: IRRNode,\n  domMirror: NodeMirror,\n  rrnodeMirror: Mirror,\n): Node {\n  const nodeId = rrnodeMirror.getId(rrNode);\n  const sn = rrnodeMirror.getMeta(rrNode);\n  let node: Node | null = null;\n  // negative ids shouldn't be compared accross mirrors\n  if (nodeId > -1) node = domMirror.getNode(nodeId);\n  if (node !== null) return node;\n  switch (rrNode.RRNodeType) {\n    case RRNodeType.Document:\n      node = new Document();\n      break;\n    case RRNodeType.DocumentType:\n      node = document.implementation.createDocumentType(\n        (rrNode as IRRDocumentType).name,\n        (rrNode as IRRDocumentType).publicId,\n        (rrNode as IRRDocumentType).systemId,\n      );\n      break;\n    case RRNodeType.Element: {\n      let tagName = (rrNode as IRRElement).tagName.toLowerCase();\n      tagName = SVGTagMap[tagName] || tagName;\n      if (sn && 'isSVG' in sn && sn?.isSVG) {\n        node = document.createElementNS(NAMESPACES['svg'], tagName);\n      } else node = document.createElement((rrNode as IRRElement).tagName);\n      break;\n    }\n    case RRNodeType.Text:\n      node = document.createTextNode((rrNode as IRRText).data);\n      break;\n    case RRNodeType.Comment:\n      node = document.createComment((rrNode as IRRComment).data);\n      break;\n    case RRNodeType.CDATA:\n      node = document.createCDATASection((rrNode as IRRCDATASection).data);\n      break;\n  }\n\n  if (sn) domMirror.add(node, { ...sn });\n  return node;\n}\n", "import {\n  NodeType as RRNodeType,\n  create<PERSON><PERSON><PERSON>r as createNode<PERSON>irror,\n} from 'rrweb-snapshot';\nimport type {\n  Mirror as NodeMirror,\n  IMir<PERSON>r,\n  serializedNodeWithId,\n} from 'rrweb-snapshot';\nimport type {\n  canvasMutationData,\n  canvasEventWithTime,\n  inputData,\n  scrollData,\n  styleSheetRuleData,\n  styleDeclarationData,\n} from '@rrweb/types';\nimport {\n  BaseRRNode as RRNode,\n  BaseRRCDATASectionImpl,\n  BaseRRCommentImpl,\n  BaseRRDocumentImpl,\n  BaseRRDocumentTypeImpl,\n  BaseRRElementImpl,\n  BaseRRMediaElementImpl,\n  BaseRRTextImpl,\n  IRRDocument,\n  IRRElement,\n  IRRNode,\n  NodeType,\n  IRRDocumentType,\n  IRRText,\n  IRRComment,\n} from './document';\n\nexport class RRDocument extends BaseRRDocumentImpl(RRNode) {\n  private UNSERIALIZED_STARTING_ID = -2;\n  // In the rrweb replayer, there are some unserialized nodes like the element that stores the injected style rules.\n  // These unserialized nodes may interfere the execution of the diff algorithm.\n  // The id of serialized node is larger than 0. So this value less than 0 is used as id for these unserialized nodes.\n  private _unserializedId = this.UNSERIALIZED_STARTING_ID;\n\n  /**\n   * Every time the id is used, it will minus 1 automatically to avoid collisions.\n   */\n  public get unserializedId(): number {\n    return this._unserializedId--;\n  }\n\n  public mirror: Mirror = createMirror();\n\n  public scrollData: scrollData | null = null;\n\n  constructor(mirror?: Mirror) {\n    super();\n    if (mirror) {\n      this.mirror = mirror;\n    }\n  }\n\n  createDocument(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _namespace: string | null,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _qualifiedName: string | null,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _doctype?: DocumentType | null,\n  ) {\n    return new RRDocument();\n  }\n\n  createDocumentType(\n    qualifiedName: string,\n    publicId: string,\n    systemId: string,\n  ) {\n    const documentTypeNode = new RRDocumentType(\n      qualifiedName,\n      publicId,\n      systemId,\n    );\n    documentTypeNode.ownerDocument = this;\n    return documentTypeNode;\n  }\n\n  createElement<K extends keyof HTMLElementTagNameMap>(\n    tagName: K,\n  ): RRElementType<K>;\n  createElement(tagName: string): RRElement;\n  createElement(tagName: string) {\n    const upperTagName = tagName.toUpperCase();\n    let element;\n    switch (upperTagName) {\n      case 'AUDIO':\n      case 'VIDEO':\n        element = new RRMediaElement(upperTagName);\n        break;\n      case 'IFRAME':\n        element = new RRIFrameElement(upperTagName, this.mirror);\n        break;\n      case 'CANVAS':\n        element = new RRCanvasElement(upperTagName);\n        break;\n      case 'STYLE':\n        element = new RRStyleElement(upperTagName);\n        break;\n      default:\n        element = new RRElement(upperTagName);\n        break;\n    }\n    element.ownerDocument = this;\n    return element;\n  }\n\n  createComment(data: string) {\n    const commentNode = new RRComment(data);\n    commentNode.ownerDocument = this;\n    return commentNode;\n  }\n\n  createCDATASection(data: string) {\n    const sectionNode = new RRCDATASection(data);\n    sectionNode.ownerDocument = this;\n    return sectionNode;\n  }\n\n  createTextNode(data: string) {\n    const textNode = new RRText(data);\n    textNode.ownerDocument = this;\n    return textNode;\n  }\n\n  destroyTree() {\n    this.childNodes = [];\n    this.mirror.reset();\n  }\n\n  open() {\n    super.open();\n    this._unserializedId = this.UNSERIALIZED_STARTING_ID;\n  }\n}\n\nexport const RRDocumentType = BaseRRDocumentTypeImpl(RRNode);\n\nexport class RRElement extends BaseRRElementImpl(RRNode) {\n  inputData: inputData | null = null;\n  scrollData: scrollData | null = null;\n}\n\nexport class RRMediaElement extends BaseRRMediaElementImpl(RRElement) {}\n\nexport class RRCanvasElement extends RRElement implements IRRElement {\n  public rr_dataURL: string | null = null;\n  public canvasMutations: {\n    event: canvasEventWithTime;\n    mutation: canvasMutationData;\n  }[] = [];\n  /**\n   * This is a dummy implementation to distinguish RRCanvasElement from real HTMLCanvasElement.\n   */\n  getContext(): RenderingContext | null {\n    return null;\n  }\n}\n\nexport class RRStyleElement extends RRElement {\n  public rules: (styleSheetRuleData | styleDeclarationData)[] = [];\n}\n\nexport class RRIFrameElement extends RRElement {\n  contentDocument: RRDocument = new RRDocument();\n  constructor(upperTagName: string, mirror: Mirror) {\n    super(upperTagName);\n    this.contentDocument.mirror = mirror;\n  }\n}\n\nexport const RRText = BaseRRTextImpl(RRNode);\nexport type RRText = typeof RRText;\n\nexport const RRComment = BaseRRCommentImpl(RRNode);\nexport type RRComment = typeof RRComment;\n\nexport const RRCDATASection = BaseRRCDATASectionImpl(RRNode);\nexport type RRCDATASection = typeof RRCDATASection;\n\ninterface RRElementTagNameMap {\n  audio: RRMediaElement;\n  canvas: RRCanvasElement;\n  iframe: RRIFrameElement;\n  style: RRStyleElement;\n  video: RRMediaElement;\n}\n\ntype RRElementType<\n  K extends keyof HTMLElementTagNameMap\n> = K extends keyof RRElementTagNameMap ? RRElementTagNameMap[K] : RRElement;\n\nfunction getValidTagName(element: HTMLElement): string {\n  // https://github.com/rrweb-io/rrweb-snapshot/issues/56\n  if (element instanceof HTMLFormElement) {\n    return 'FORM';\n  }\n  return element.tagName.toUpperCase();\n}\n\n/**\n * Build a RRNode from a real Node.\n * @param node - the real Node\n * @param rrdom - the RRDocument\n * @param domMirror - the NodeMirror that records the real document tree\n * @returns the built RRNode\n */\nexport function buildFromNode(\n  node: Node,\n  rrdom: IRRDocument,\n  domMirror: NodeMirror,\n  parentRRNode?: IRRNode | null,\n): IRRNode | null {\n  let rrNode: IRRNode;\n\n  switch (node.nodeType) {\n    case NodeType.DOCUMENT_NODE:\n      if (parentRRNode && parentRRNode.nodeName === 'IFRAME')\n        rrNode = (parentRRNode as RRIFrameElement).contentDocument;\n      else {\n        rrNode = rrdom;\n        (rrNode as IRRDocument).compatMode = (node as Document).compatMode as\n          | 'BackCompat'\n          | 'CSS1Compat';\n      }\n      break;\n    case NodeType.DOCUMENT_TYPE_NODE: {\n      const documentType = node as DocumentType;\n      rrNode = rrdom.createDocumentType(\n        documentType.name,\n        documentType.publicId,\n        documentType.systemId,\n      );\n      break;\n    }\n    case NodeType.ELEMENT_NODE: {\n      const elementNode = node as HTMLElement;\n      const tagName = getValidTagName(elementNode);\n      rrNode = rrdom.createElement(tagName);\n      const rrElement = rrNode as IRRElement;\n      for (const { name, value } of Array.from(elementNode.attributes)) {\n        rrElement.attributes[name] = value;\n      }\n      elementNode.scrollLeft && (rrElement.scrollLeft = elementNode.scrollLeft);\n      elementNode.scrollTop && (rrElement.scrollTop = elementNode.scrollTop);\n      /**\n       * We don't have to record special values of input elements at the beginning.\n       * Because if these values are changed later, the mutation will be applied through the batched input events on its RRElement after the diff algorithm is executed.\n       */\n      break;\n    }\n    case NodeType.TEXT_NODE:\n      rrNode = rrdom.createTextNode((node as Text).textContent || '');\n      break;\n    case NodeType.CDATA_SECTION_NODE:\n      rrNode = rrdom.createCDATASection((node as CDATASection).data);\n      break;\n    case NodeType.COMMENT_NODE:\n      rrNode = rrdom.createComment((node as Comment).textContent || '');\n      break;\n    // if node is a shadow root\n    case NodeType.DOCUMENT_FRAGMENT_NODE:\n      rrNode = (parentRRNode as IRRElement).attachShadow({ mode: 'open' });\n      break;\n    default:\n      return null;\n  }\n\n  let sn: serializedNodeWithId | null = domMirror.getMeta(node);\n\n  if (rrdom instanceof RRDocument) {\n    if (!sn) {\n      sn = getDefaultSN(rrNode, rrdom.unserializedId);\n      domMirror.add(node, sn);\n    }\n    rrdom.mirror.add(rrNode, { ...sn });\n  }\n\n  return rrNode;\n}\n\n/**\n * Build a RRDocument from a real document tree.\n * @param dom - the real document tree\n * @param domMirror - the NodeMirror that records the real document tree\n * @param rrdom - the rrdom object to be constructed\n * @returns the build rrdom\n */\nexport function buildFromDom(\n  dom: Document,\n  domMirror: NodeMirror = createNodeMirror(),\n  rrdom: IRRDocument = new RRDocument(),\n) {\n  function walk(node: Node, parentRRNode: IRRNode | null) {\n    const rrNode = buildFromNode(node, rrdom, domMirror, parentRRNode);\n    if (rrNode === null) return;\n    if (\n      // if the parentRRNode isn't a RRIFrameElement\n      parentRRNode?.nodeName !== 'IFRAME' &&\n      // if node isn't a shadow root\n      node.nodeType !== NodeType.DOCUMENT_FRAGMENT_NODE\n    ) {\n      parentRRNode?.appendChild(rrNode);\n      rrNode.parentNode = parentRRNode;\n      rrNode.parentElement = parentRRNode as RRElement;\n    }\n\n    if (node.nodeName === 'IFRAME') {\n      const iframeDoc = (node as HTMLIFrameElement).contentDocument;\n      iframeDoc && walk(iframeDoc, rrNode);\n    } else if (\n      node.nodeType === NodeType.DOCUMENT_NODE ||\n      node.nodeType === NodeType.ELEMENT_NODE ||\n      node.nodeType === NodeType.DOCUMENT_FRAGMENT_NODE\n    ) {\n      // if the node is a shadow dom\n      if (\n        node.nodeType === NodeType.ELEMENT_NODE &&\n        (node as HTMLElement).shadowRoot\n      )\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        walk((node as HTMLElement).shadowRoot!, rrNode);\n      node.childNodes.forEach((childNode) => walk(childNode, rrNode));\n    }\n  }\n  walk(dom, null);\n  return rrdom;\n}\n\nexport function createMirror(): Mirror {\n  return new Mirror();\n}\n\n// based on Mirror from rrweb-snapshots\nexport class Mirror implements IMirror<RRNode> {\n  private idNodeMap: Map<number, RRNode> = new Map();\n  private nodeMetaMap: WeakMap<RRNode, serializedNodeWithId> = new WeakMap();\n\n  getId(n: RRNode | undefined | null): number {\n    if (!n) return -1;\n\n    const id = this.getMeta(n)?.id;\n\n    // if n is not a serialized Node, use -1 as its id.\n    return id ?? -1;\n  }\n\n  getNode(id: number): RRNode | null {\n    return this.idNodeMap.get(id) || null;\n  }\n\n  getIds(): number[] {\n    return Array.from(this.idNodeMap.keys());\n  }\n\n  getMeta(n: RRNode): serializedNodeWithId | null {\n    return this.nodeMetaMap.get(n) || null;\n  }\n\n  // removes the node from idNodeMap\n  // doesn't remove the node from nodeMetaMap\n  removeNodeFromMap(n: RRNode) {\n    const id = this.getId(n);\n    this.idNodeMap.delete(id);\n\n    if (n.childNodes) {\n      n.childNodes.forEach((childNode) => this.removeNodeFromMap(childNode));\n    }\n  }\n  has(id: number): boolean {\n    return this.idNodeMap.has(id);\n  }\n\n  hasNode(node: RRNode): boolean {\n    return this.nodeMetaMap.has(node);\n  }\n\n  add(n: RRNode, meta: serializedNodeWithId) {\n    const id = meta.id;\n    this.idNodeMap.set(id, n);\n    this.nodeMetaMap.set(n, meta);\n  }\n\n  replace(id: number, n: RRNode) {\n    const oldNode = this.getNode(id);\n    if (oldNode) {\n      const meta = this.nodeMetaMap.get(oldNode);\n      if (meta) this.nodeMetaMap.set(n, meta);\n    }\n    this.idNodeMap.set(id, n);\n  }\n\n  reset() {\n    this.idNodeMap = new Map();\n    this.nodeMetaMap = new WeakMap();\n  }\n}\n\n/**\n * Get a default serializedNodeWithId value for a RRNode.\n * @param id - the serialized id to assign\n */\nexport function getDefaultSN(node: IRRNode, id: number): serializedNodeWithId {\n  switch (node.RRNodeType) {\n    case RRNodeType.Document:\n      return {\n        id,\n        type: node.RRNodeType,\n        childNodes: [],\n      };\n    case RRNodeType.DocumentType: {\n      const doctype = node as IRRDocumentType;\n      return {\n        id,\n        type: node.RRNodeType,\n        name: doctype.name,\n        publicId: doctype.publicId,\n        systemId: doctype.systemId,\n      };\n    }\n    case RRNodeType.Element:\n      return {\n        id,\n        type: node.RRNodeType,\n        tagName: (node as IRRElement).tagName.toLowerCase(), // In rrweb data, all tagNames are lowercase.\n        attributes: {},\n        childNodes: [],\n      };\n    case RRNodeType.Text:\n      return {\n        id,\n        type: node.RRNodeType,\n        textContent: (node as IRRText).textContent || '',\n      };\n    case RRNodeType.Comment:\n      return {\n        id,\n        type: node.RRNodeType,\n        textContent: (node as IRRComment).textContent || '',\n      };\n    case RRNodeType.CDATA:\n      return {\n        id,\n        type: node.RRNodeType,\n        textContent: '',\n      };\n  }\n}\n\n/**\n * Print the RRDom as a string.\n * @param rootNode - the root node of the RRDom tree\n * @param mirror - a rrweb or rrdom Mirror\n * @returns printed string\n */\nexport function printRRDom(rootNode: IRRNode, mirror: IMirror<IRRNode>) {\n  return walk(rootNode, mirror, '');\n}\nfunction walk(node: IRRNode, mirror: IMirror<IRRNode>, blankSpace: string) {\n  let printText = `${blankSpace}${mirror.getId(node)} ${node.toString()}\\n`;\n  if (node.RRNodeType === RRNodeType.Element) {\n    const element = node as IRRElement;\n    if (element.shadowRoot)\n      printText += walk(element.shadowRoot, mirror, blankSpace + '  ');\n  }\n  for (const child of node.childNodes)\n    printText += walk(child, mirror, blankSpace + '  ');\n  if (node.nodeName === 'IFRAME')\n    printText += walk(\n      (node as RRIFrameElement).contentDocument,\n      mirror,\n      blankSpace + '  ',\n    );\n  return printText;\n}\n\nexport { RRNode };\n\nexport { diff, createOrGetNode, ReplayerHandler } from './diff';\nexport * from './document';\n"], "names": ["NodeType", "Mirror", "this", "idNodeMap", "Map", "nodeMetaMap", "WeakMap", "prototype", "getId", "n", "_a", "id", "getMeta", "getNode", "get", "getIds", "Array", "from", "keys", "removeNodeFromMap", "_this", "childNodes", "for<PERSON>ach", "childNode", "has", "hasNode", "node", "add", "meta", "set", "replace", "oldNode", "reset", "toCSSText", "style", "properties", "name", "value", "normalizedName", "hyphenate", "push", "join", "camelizeRE", "CUSTOM_PROPERTY_REGEX", "camelize", "str", "test", "_", "c", "toUpperCase", "hyphenateRE", "toLowerCase", "BaseRRNode", "constructor", "_args", "ELEMENT_NODE", "TEXT_NODE", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "nextS<PERSON>ling", "parentNode", "siblings", "index", "indexOf", "contains", "child", "append<PERSON><PERSON><PERSON>", "_new<PERSON><PERSON>d", "Error", "insertBefore", "_refChild", "<PERSON><PERSON><PERSON><PERSON>", "_node", "toString", "BaseRRDocumentImpl", "RRNodeClass", "BaseRRDocument", "DOCUMENT_NODE", "RRNodeType", "Document", "documentElement", "find", "Element", "tagName", "body", "head", "implementation", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "DocumentType", "some", "s", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "childIndex", "splice", "indexOfChild", "open", "close", "write", "content", "publicId", "doctype", "createDocumentType", "createDocument", "_namespace", "_qualifiedName", "_doctype", "qualifiedName", "systemId", "BaseRRDocumentTypeImpl", "ownerDocument", "createElement", "element", "BaseRRElementImpl", "createElementNS", "_namespaceURI", "createTextNode", "data", "text", "BaseRRTextImpl", "createComment", "comment", "BaseRRCommentImpl", "createCDATASection", "CDATASection", "BaseRRCDATASectionImpl", "super", "DOCUMENT_TYPE_NODE", "nodeName", "textContent", "result", "classList", "ClassList", "attributes", "class", "newClassName", "className", "cssText", "res", "propertyDelimiter", "split", "item", "tmp", "trim", "parseCSSText", "setProperty", "priority", "removeProperty", "getAttribute", "setAttribute", "attribute", "setAttributeNS", "removeAttribute", "attachShadow", "_init", "shadowRoot", "dispatchEvent", "_event", "attributeString", "BaseRRMediaElementImpl", "RRElementClass", "play", "paused", "pause", "Text", "JSON", "stringify", "COMMENT_NODE", "Comment", "CDATA_SECTION_NODE", "CDATA", "classText", "onChange", "classNames", "String", "classes", "filter", "NAMESPACES", "svg", "xmlns", "SVGTagMap", "altglyph", "altglyphdef", "altglyphitem", "animatecolor", "animatemotion", "animatetransform", "clippath", "feblend", "fecolormatrix", "fecomponenttransfer", "fecomposite", "feconvolvematrix", "fediffuselighting", "fedisplacementmap", "fedistantlight", "fedropshadow", "feflood", "fefunca", "fefuncb", "fefuncg", "fefuncr", "feg<PERSON><PERSON><PERSON><PERSON><PERSON>", "feimage", "femerge", "femergenode", "femorphology", "feoffset", "fepointlight", "fespecularlighting", "fespotlight", "fetile", "feturbulence", "foreignobject", "glyphref", "lineargradient", "radialgradient", "diff", "oldTree", "newTree", "replayer", "rrnodeMirror", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirror", "diff<PERSON><PERSON><PERSON><PERSON>", "inputDataToApply", "scrollDataToApply", "scrollData", "oldElement", "newRRElement", "oldAttributes", "newAttributes", "newValue", "sn", "isSVG", "image", "document", "src", "onload", "ctx", "getContext", "drawImage", "width", "height", "scrollLeft", "scrollTop", "diffProps", "inputData", "oldMediaElement", "newMediaRRElement", "undefined", "muted", "volume", "currentTime", "playbackRate", "rrCanvasElement", "rr_dataURL", "canvasMutations", "canvasMutation", "applyCanvas", "event", "mutation", "styleSheet", "sheet", "rules", "applyStyleSheetMutation", "mode", "applyScroll", "applyInput", "oldContentDocument", "contentDocument", "newIFrameElement", "oldIdToIndex", "indexInOld", "oldStartIndex", "oldEndIndex", "newStartIndex", "newEndIndex", "oldStartNode", "oldEndNode", "newStartNode", "newEndNode", "oldStartId", "oldEndId", "newStartId", "newEndId", "i", "<PERSON><PERSON><PERSON><PERSON>", "nodeToMove", "newNode", "createOrGetNode", "type", "referenceRRNode", "referenceNode", "rrNode", "dom<PERSON><PERSON><PERSON>r", "nodeId", "RRDocument", "RRNode", "UNSERIALIZED_STARTING_ID", "createMirror", "unserializedId", "_unserializedId", "documentTypeNode", "RRDocumentType", "upperTagName", "RRMediaElement", "RRIFrameElement", "RRCanvasElement", "RRStyleElement", "<PERSON><PERSON><PERSON>", "commentNode", "RRComment", "sectionNode", "RRCDATASection", "textNode", "RRText", "destroyTree", "buildFromNode", "rrdom", "parentRRNode", "compatMode", "documentType", "elementNode", "HTMLFormElement", "rr<PERSON><PERSON>", "DOCUMENT_FRAGMENT_NODE", "getDefaultSN", "delete", "walk", "blankSpace", "printText", "dom", "createNodeMirror", "iframeDoc", "rootNode"], "mappings": "mCAAA,IAAIA,GACJ,SAAWA,GACPA,EAASA,EAAmB,SAAI,GAAK,WACrCA,EAASA,EAAuB,aAAI,GAAK,eACzCA,EAASA,EAAkB,QAAI,GAAK,UACpCA,EAASA,EAAe,KAAI,GAAK,OACjCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAkB,QAAI,GAAK,SACvC,CAPD,CAOGA,IAAaA,EAAW,KA4C3B,IAAIC,EAAU,WACV,SAASA,IACLC,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,YAAc,IAAIC,QAoD3B,OAlDAL,EAAOM,UAAUC,MAAQ,SAAUC,GAC/B,IAAIC,EACJ,IAAKD,EACD,OAAQ,EACZ,IAAIE,EAAgC,QAA1BD,EAAKR,KAAKU,QAAQH,UAAuB,IAAPC,OAAgB,EAASA,EAAGC,GACxE,OAAOA,QAA+BA,GAAM,GAEhDV,EAAOM,UAAUM,QAAU,SAAUF,GACjC,OAAOT,KAAKC,UAAUW,IAAIH,IAAO,MAErCV,EAAOM,UAAUQ,OAAS,WACtB,OAAOC,MAAMC,KAAKf,KAAKC,UAAUe,SAErCjB,EAAOM,UAAUK,QAAU,SAAUH,GACjC,OAAOP,KAAKG,YAAYS,IAAIL,IAAM,MAEtCR,EAAOM,UAAUY,kBAAoB,SAAUV,GAC3C,IAAIW,EAAQlB,KACRS,EAAKT,KAAKM,MAAMC,GACpBP,KAAKC,UAAkB,OAAEQ,GACrBF,EAAEY,YACFZ,EAAEY,WAAWC,SAAQ,SAAUC,GAC3B,OAAOH,EAAMD,kBAAkBI,OAI3CtB,EAAOM,UAAUiB,IAAM,SAAUb,GAC7B,OAAOT,KAAKC,UAAUqB,IAAIb,IAE9BV,EAAOM,UAAUkB,QAAU,SAAUC,GACjC,OAAOxB,KAAKG,YAAYmB,IAAIE,IAEhCzB,EAAOM,UAAUoB,IAAM,SAAUlB,EAAGmB,GAChC,IAAIjB,EAAKiB,EAAKjB,GACdT,KAAKC,UAAU0B,IAAIlB,EAAIF,GACvBP,KAAKG,YAAYwB,IAAIpB,EAAGmB,IAE5B3B,EAAOM,UAAUuB,QAAU,SAAUnB,EAAIF,GACrC,IAAIsB,EAAU7B,KAAKW,QAAQF,GAC3B,GAAIoB,EAAS,CACT,IAAIH,EAAO1B,KAAKG,YAAYS,IAAIiB,GAC5BH,GACA1B,KAAKG,YAAYwB,IAAIpB,EAAGmB,GAEhC1B,KAAKC,UAAU0B,IAAIlB,EAAIF,IAE3BR,EAAOM,UAAUyB,MAAQ,WACrB9B,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,YAAc,IAAIC,SAEpBL,CACX,aC3FgBgC,EAAUC,GACxB,MAAMC,EAAa,GACnB,IAAK,MAAMC,KAAQF,EAAO,CACxB,MAAMG,EAAQH,EAAME,GACpB,GAAqB,iBAAVC,EAAoB,SAC/B,MAAMC,EAAiBC,EAAUH,GACjCD,EAAWK,KAAK,GAAGF,MAAmBD,MAExC,OAAOF,EAAWM,KAAK,IACzB,CAKA,MAAMC,EAAa,YACbC,EAAwB,oBACjBC,EAAYC,GACnBF,EAAsBG,KAAKD,GAAaA,EACrCA,EAAIf,QAAQY,GAAY,CAACK,EAAGC,IAAeA,EAAIA,EAAEC,cAAgB,KAMpEC,EAAc,aACPX,EAAaM,GACjBA,EAAIf,QAAQoB,EAAa,OAAOC,oBCqF5BC,EAcXC,eAAeC,GAbRpD,gBAAwB,GACxBA,mBAAgC,KAChCA,gBAA6B,KAGpBA,kBAAuBF,WAASuD,aAChCrD,eAAoBF,WAASwD,UAWlCC,iBACT,OAAOvD,KAAKmB,WAAW,IAAM,KAGpBqC,gBACT,OAAOxD,KAAKmB,WAAWnB,KAAKmB,WAAWsC,OAAS,IAAM,KAG7CC,kBACT,MAAMC,EAAa3D,KAAK2D,WACxB,IAAKA,EAAY,OAAO,KACxB,MAAMC,EAAWD,EAAWxC,WACtB0C,EAAQD,EAASE,QAAQ9D,MAC/B,OAAO4D,EAASC,EAAQ,IAAM,KAGzBE,SAASvC,GACd,GAAIA,IAASxB,KAAM,OAAO,EAC1B,IAAK,MAAMgE,KAAShE,KAAKmB,WACvB,GAAI6C,EAAMD,SAASvC,GAAO,OAAO,EAEnC,OAAO,EAIFyC,YAAYC,GACjB,MAAM,IAAIC,MACR,+GAKGC,aAAaF,EAAoBG,GACtC,MAAM,IAAIF,MACR,gHAKGG,YAAYC,GACjB,MAAM,IAAIJ,MACR,+GAIGK,WACL,MAAO,mBAIKC,EAEdC,GACA,OAAO,MAAMC,UAAuBD,EAA7BvB,kCACWnD,cAAmBF,WAAS8E,cAC5B5E,cAAwB,YACxBA,gBAA0C,aAC1CA,gBAAa6E,EAAWC,SACjC9E,iBAA6B,KAEzB+E,sBACT,OACG/E,KAAKmB,WAAW6D,MACdxD,GACCA,EAAKqD,aAAeA,EAAWI,SACE,SAAhCzD,EAAoB0D,WACL,KAIbC,iBACT,iBACGnF,KAAK+E,sCAAiB5D,WAAW6D,MAC/BxD,GACCA,EAAKqD,aAAeA,EAAWI,SACE,SAAhCzD,EAAoB0D,YACL,KAIbE,iBACT,iBACGpF,KAAK+E,sCAAiB5D,WAAW6D,MAC/BxD,GACCA,EAAKqD,aAAeA,EAAWI,SACE,SAAhCzD,EAAoB0D,YACL,KAIbG,qBACT,OAAOrF,KAGEsF,wBACT,OAAOtF,KAAK+E,gBAGPd,YAAY5C,GACjB,MAAMkE,EAAWlE,EAAUwD,WAC3B,IACEU,IAAaV,EAAWI,SACxBM,IAAaV,EAAWW,eAEpBxF,KAAKmB,WAAWsE,MAAMC,GAAMA,EAAEb,aAAeU,IAC/C,MAAM,IAAIpB,MACR,yEACEoB,IAAaV,EAAWI,QAAU,YAAc,sCAQxD,OAHA5D,EAAUsE,cAAgB,KAC1BtE,EAAUsC,WAAa3D,KACvBA,KAAKmB,WAAWmB,KAAKjB,GACdA,EAGF+C,aAAawB,EAAmBC,GACrC,MAAMN,EAAWK,EAASf,WAC1B,IACEU,IAAaV,EAAWI,SACxBM,IAAaV,EAAWW,eAEpBxF,KAAKmB,WAAWsE,MAAMC,GAAMA,EAAEb,aAAeU,IAC/C,MAAM,IAAIpB,MACR,0EACEoB,IAAaV,EAAWI,QAAU,YAAc,sCAKxD,GAAiB,OAAbY,EAAmB,OAAO7F,KAAKiE,YAAY2B,GAC/C,MAAME,EAAa9F,KAAKmB,WAAW2C,QAAQ+B,GAC3C,IAAmB,GAAfC,EACF,MAAM,IAAI3B,MACR,uIAKJ,OAHAnE,KAAKmB,WAAW4E,OAAOD,EAAY,EAAGF,GACtCA,EAASD,cAAgB,KACzBC,EAASjC,WAAa3D,KACf4F,EAGFtB,YAAY9C,GACjB,MAAMwE,EAAehG,KAAKmB,WAAW2C,QAAQtC,GAC7C,IAAsB,IAAlBwE,EACF,MAAM,IAAI7B,MACR,4GAKJ,OAHAnE,KAAKmB,WAAW4E,OAAOC,EAAc,GACrCxE,EAAKmE,cAAgB,KACrBnE,EAAKmC,WAAa,KACXnC,EAGFyE,OACLjG,KAAKmB,WAAa,GAGb+E,SAUAC,MAAMC,GACX,IAAIC,EAWJ,GARE,uEADAD,EAGAC,EAAW,yCAGX,sEADAD,IAGAC,EAAW,yCACTA,EAAU,CACZ,MAAMC,EAAUtG,KAAKuG,mBAAmB,OAAQF,EAAU,IAC1DrG,KAAKiG,OACLjG,KAAKiE,YAAYqC,IAIrBE,eAEEC,EAEAC,EAEAC,GAEA,OAAO,IAAIhC,EAGb4B,mBACEK,EACAP,EACAQ,GAEA,MAAMP,EAAU,IAAKQ,EAAuB5D,GAA5B,CACd0D,EACAP,EACAQ,GAGF,OADAP,EAAQS,cAAgB/G,KACjBsG,EAGTU,cAAc9B,GACZ,MAAM+B,EAAU,IAAKC,EAAkBhE,GAAvB,CAAoCgC,GAEpD,OADA+B,EAAQF,cAAgB/G,KACjBiH,EAGTE,gBAAgBC,EAAuBR,GACrC,OAAO5G,KAAKgH,cAAcJ,GAG5BS,eAAeC,GACb,MAAMC,EAAO,IAAKC,EAAetE,GAApB,CAAiCoE,GAE9C,OADAC,EAAKR,cAAgB/G,KACduH,EAGTE,cAAcH,GACZ,MAAMI,EAAU,IAAKC,EAAkBzE,GAAvB,CAAoCoE,GAEpD,OADAI,EAAQX,cAAgB/G,KACjB0H,EAGTE,mBAAmBN,GACjB,MAAMO,EAAe,IAAKC,EAAuB5E,GAA5B,CAAyCoE,GAE9D,OADAO,EAAad,cAAgB/G,KACtB6H,EAGTrD,WACE,MAAO,cAGb,UAEgBsC,EAEdpC,GAGA,OAAO,cACGA,EAURvB,YAAYyD,EAAuBP,EAAkBQ,GACnDkB,QATc/H,cAAmBF,WAASkI,mBAC5BhI,gBAAa6E,EAAWW,aAKjCxF,iBAA6B,KAIlCA,KAAKkC,KAAO0E,EACZ5G,KAAKqG,SAAWA,EAChBrG,KAAK6G,SAAWA,EAChB7G,KAAKiI,SAAWrB,EAGlBpC,WACE,MAAO,kBAGb,UAEgB0C,EAEdxC,GAGA,OAAO,cAA4BA,EAUjCvB,YAAY+B,GACV6C,QAVc/H,cAAmBF,WAASuD,aAC5BrD,gBAAa6E,EAAWI,QAGjCjF,gBAAqC,GACrCA,gBAAgC,KAMrCA,KAAKkF,QAAUA,EAAQnC,cACvB/C,KAAKiI,SAAW/C,EAAQnC,cAGfmF,kBACT,IAAIC,EAAS,GAEb,OADAnI,KAAKmB,WAAWC,SAASI,GAAU2G,GAAU3G,EAAK0G,cAC3CC,EAGED,gBAAYA,GACrBlI,KAAKmB,WAAa,CAACnB,KAAK+G,cAAcM,eAAea,IAG5CE,gBACT,OAAO,IAAIC,EACTrI,KAAKsI,WAAWC,OACfC,IACCxI,KAAKsI,WAAWC,MAAQC,CAAY,IAK/B/H,SACT,OAAOT,KAAKsI,WAAW7H,IAAM,GAGpBgI,gBACT,OAAOzI,KAAKsI,WAAWC,OAAS,GAGvBvG,YACT,MAAMA,EAAShC,KAAKsI,WAAWtG,eDzdR0G,GAC3B,MAAMC,EAA8B,GAE9BC,EAAoB,QAW1B,OATAF,EACG9G,QAFa,eAEI,IACjBiH,MALmB,iBAMnBzH,SAAQ,SAAU0H,GACjB,GAAIA,EAAM,CACR,MAAMC,EAAMD,EAAKD,MAAMD,GACvBG,EAAItF,OAAS,IAAMkF,EAAIjG,EAASqG,EAAI,GAAGC,SAAWD,EAAI,GAAGC,YAGxDL,CACT,CC2cUM,CAAajJ,KAAKsI,WAAWtG,OAC7B,GACEgB,EAAc,aAqBpB,OApBAhB,EAAMkH,YAAc,CAClBhH,EACAC,EACAgH,KAEA,GAAInG,EAAYJ,KAAKV,GAAO,OAC5B,MAAME,EAAiBM,EAASR,GAC3BC,EACAH,EAAMI,GAAkBD,SADVH,EAAMI,GAER,cAAb+G,IAA0BnH,EAAMI,IAAmB,eACvDpC,KAAKsI,WAAWtG,MAAQD,EAAUC,EAAM,EAE1CA,EAAMoH,eAAkBlH,IACtB,GAAIc,EAAYJ,KAAKV,GAAO,MAAO,GACnC,MAAME,EAAiBM,EAASR,GAC1BC,EAAQH,EAAMI,IAAmB,GAGvC,cAFOJ,EAAMI,GACbpC,KAAKsI,WAAWtG,MAAQD,EAAUC,GAC3BG,CAAK,EAEPH,EAGFqH,aAAanH,GAClB,OAAOlC,KAAKsI,WAAWpG,IAAS,KAG3BoH,aAAapH,EAAcqH,GAChCvJ,KAAKsI,WAAWpG,GAAQqH,EAGnBC,eACL/C,EACAG,EACAzE,GAEAnC,KAAKsJ,aAAa1C,EAAezE,GAG5BsH,gBAAgBvH,UACdlC,KAAKsI,WAAWpG,GAGlB+B,YAAY2B,GAIjB,OAHA5F,KAAKmB,WAAWmB,KAAKsD,GACrBA,EAASjC,WAAa3D,KACtB4F,EAASD,cAAgB3F,KAClB4F,EAGFxB,aAAawB,EAAmBC,GACrC,GAAiB,OAAbA,EAAmB,OAAO7F,KAAKiE,YAAY2B,GAC/C,MAAME,EAAa9F,KAAKmB,WAAW2C,QAAQ+B,GAC3C,IAAmB,GAAfC,EACF,MAAM,IAAI3B,MACR,uIAKJ,OAHAnE,KAAKmB,WAAW4E,OAAOD,EAAY,EAAGF,GACtCA,EAASD,cAAgB3F,KACzB4F,EAASjC,WAAa3D,KACf4F,EAGFtB,YAAY9C,GACjB,MAAMwE,EAAehG,KAAKmB,WAAW2C,QAAQtC,GAC7C,IAAsB,IAAlBwE,EACF,MAAM,IAAI7B,MACR,2GAKJ,OAHAnE,KAAKmB,WAAW4E,OAAOC,EAAc,GACrCxE,EAAKmE,cAAgB,KACrBnE,EAAKmC,WAAa,KACXnC,EAIFkI,aAAaC,GAClB,MAAMC,EAAa5J,KAAK+G,cAAcC,cAAc,cAEpD,OADAhH,KAAK4J,WAAaA,EACXA,EAIFC,cAAcC,GACnB,OAAO,EAGTtF,WACE,IAAIuF,EAAkB,GACtB,IAAK,MAAMR,KAAavJ,KAAKsI,WAC3ByB,GAAmB,GAAGR,MAAcvJ,KAAKsI,WAAWiB,OAEtD,MAAO,GAAGvJ,KAAKkF,WAAW6E,KAGhC,UAEgBC,EAEdC,GACA,OAAO,cAAiCA,EAOtCP,aAAaC,GACX,MAAM,IAAIxF,MACR,iHAGG+F,OACLlK,KAAKmK,QAAS,EAETC,QACLpK,KAAKmK,QAAS,GAGpB,UAEgB3C,EACd9C,GAIA,OAAO,cAAyBA,EAM9BvB,YAAYmE,GACVS,QANc/H,cAAmBF,WAASwD,UAC5BtD,cAAoB,QACpBA,gBAAa6E,EAAWwF,KAKtCrK,KAAKsH,KAAOA,EAGHY,kBACT,OAAOlI,KAAKsH,KAGHY,gBAAYA,GACrBlI,KAAKsH,KAAOY,EAGd1D,WACE,MAAO,eAAe8F,KAAKC,UAAUvK,KAAKsH,SAGhD,UAEgBK,EAEdjD,GAGA,OAAO,cAA4BA,EAMjCvB,YAAYmE,GACVS,QANc/H,cAAmBF,WAAS0K,aAC5BxK,cAAuB,WACvBA,gBAAa6E,EAAW4F,QAKtCzK,KAAKsH,KAAOA,EAGHY,kBACT,OAAOlI,KAAKsH,KAGHY,gBAAYA,GACrBlI,KAAKsH,KAAOY,EAGd1D,WACE,MAAO,kBAAkB8F,KAAKC,UAAUvK,KAAKsH,SAGnD,UAEgBQ,EAEdpD,GAGA,OAAO,cACGA,EAORvB,YAAYmE,GACVS,QANc/H,cAA6B,iBAC7BA,cAAmBF,WAAS4K,mBAC5B1K,gBAAa6E,EAAW8F,MAKtC3K,KAAKsH,KAAOA,EAGHY,kBACT,OAAOlI,KAAKsH,KAGHY,gBAAYA,GACrBlI,KAAKsH,KAAOY,EAGd1D,WACE,MAAO,uBAAuB8F,KAAKC,UAAUvK,KAAKsH,SAGxD,OAEae,EAIXlF,YACEyH,EACAC,GAEA,GANF7K,aAAoB,GAapBA,SAAM,IAAI8K,KACR,IAAK,MAAMhC,KAAQgC,EAAY,CAC7B,MAAMrC,EAAYsC,OAAOjC,GACrB9I,KAAKgL,QAAQlH,QAAQ2E,IAAc,GACvCzI,KAAKgL,QAAQ1I,KAAKmG,GAEpBzI,KAAK6K,UAAY7K,KAAK6K,SAAS7K,KAAKgL,QAAQzI,KAAK,KAAK,EAGxDvC,YAAS,IAAI8K,KACX9K,KAAKgL,QAAUhL,KAAKgL,QAAQC,QACzBnC,IAAuC,IAA9BgC,EAAWhH,QAAQgF,KAE/B9I,KAAK6K,UAAY7K,KAAK6K,SAAS7K,KAAKgL,QAAQzI,KAAK,KAAK,EApBlDqI,EAAW,CACb,MAAMI,EAAUJ,EAAU5B,OAAOH,MAAM,OACvC7I,KAAKgL,QAAQ1I,QAAQ0I,GAEvBhL,KAAK6K,SAAWA,qBA8BpB,SAAY/K,GACVA,iCACAA,mCACAA,uCACAA,6BACAA,+CACAA,qDACAA,iCACAA,iEACAA,mCACAA,qCACAA,gDACAA,uDACD,CAbD,CAAYA,aAAAA,gBChsBZ,MAAMoL,EAAqC,CACzCC,IAAK,6BACL,aAAc,+BACdC,MAAO,iCAIHC,EAAoC,CACxCC,SAAU,WACVC,YAAa,cACbC,aAAc,eACdC,aAAc,eACdC,cAAe,gBACfC,iBAAkB,mBAClBC,SAAU,WACVC,QAAS,UACTC,cAAe,gBACfC,oBAAqB,sBACrBC,YAAa,cACbC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,kBAAmB,oBACnBC,eAAgB,iBAChBC,aAAc,eACdC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,eAAgB,iBAChBC,QAAS,UACTC,QAAS,UACTC,YAAa,cACbC,aAAc,eACdC,SAAU,WACVC,aAAc,eACdC,mBAAoB,qBACpBC,YAAa,cACbC,OAAQ,SACRC,aAAc,eACdC,cAAe,gBACfC,SAAU,WACVC,eAAgB,iBAChBC,eAAgB,2BAkBFC,EACdC,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAcJ,EAAQxM,WACtB6M,EAAcJ,EAAQzM,WAC5B2M,EACEA,GACCF,EAAuBK,QACvBL,EAAQ7G,cAA6BkH,QAEpCF,EAAYtK,OAAS,GAAKuK,EAAYvK,OAAS,IACjDyK,EACEpN,MAAMC,KAAKgN,GACXC,EACAL,EACAE,EACAC,GAIJ,IAAIK,EAAmB,KACrBC,EAAoB,KACtB,OAAQR,EAAQ/I,YACd,KAAKA,EAAWC,SAEdsJ,EADsBR,EAC4BS,WAClD,MAEF,KAAKxJ,EAAWI,QAAS,CACvB,MAAMqJ,EAAaX,EACbY,EAAeX,EAIrB,OA+GN,SACED,EACAC,EACAE,GAEA,MAAMU,EAAgBb,EAAQrF,WACxBmG,EAAgBb,EAAQtF,WAE9B,IAAK,MAAMpG,KAAQuM,EAAe,CAChC,MAAMC,EAAWD,EAAcvM,GACzByM,EAAKb,EAAapN,QAAQkN,GAChC,GAAIe,GAAM,UAAWA,GAAMA,EAAGC,OAAS1D,EAAWhJ,GAChDyL,EAAQnE,eAAe0B,EAAWhJ,GAAOA,EAAMwM,QAC5C,GAAwB,WAApBd,EAAQ1I,SAAiC,eAAThD,EAAuB,CAC9D,MAAM2M,EAAQC,SAAS9H,cAAc,OACrC6H,EAAME,IAAML,EACZG,EAAMG,OAAS,KACb,MAAMC,EAAOtB,EAA8BuB,WAAW,MAClDD,GACFA,EAAIE,UAAUN,EAAO,EAAG,EAAGA,EAAMO,MAAOP,EAAMQ,cAG7C1B,EAAQrE,aAAapH,EAAMwM,GAGpC,IAAK,MAAMxM,KAAEA,KAAUpB,MAAMC,KAAKyN,GAC1BtM,KAAQuM,GAAgBd,EAAQlE,gBAAgBvH,GAExD0L,EAAQ0B,aAAe3B,EAAQ2B,WAAa1B,EAAQ0B,YACpD1B,EAAQ2B,YAAc5B,EAAQ4B,UAAY3B,EAAQ2B,UACpD,CAhJMC,CAAUlB,EAAYC,EAAcT,GACpCM,EAAqBG,EAA2BF,WAChDF,EAAoBI,EAA2BkB,UACvClB,EAAarJ,SACnB,IAAK,QACL,IAAK,QAAS,CACZ,MAAMwK,EAAkB/B,EAClBgC,EAAoBpB,OACOqB,IAA7BD,EAAkBxF,SACpBwF,EAAkBxF,OACTuF,EAAgBtF,QAChBsF,EAAgBxF,aACK0F,IAA5BD,EAAkBE,QACpBH,EAAgBG,MAAQF,EAAkBE,YACXD,IAA7BD,EAAkBG,SACpBJ,EAAgBI,OAASH,EAAkBG,aACPF,IAAlCD,EAAkBI,cACpBL,EAAgBK,YAAcJ,EAAkBI,kBACXH,IAAnCD,EAAkBK,eACpBN,EAAgBM,aAAeL,EAAkBK,cACnD,MAEF,IAAK,SACH,CACE,MAAMC,EAAkBrC,EAExB,GAAmC,OAA/BqC,EAAgBC,WAAqB,CACvC,MAAMrB,EAAQC,SAAS9H,cAAc,OACrC6H,EAAMG,OAAS,KACb,MAAMC,EAAOX,EAAiCY,WAAW,MACrDD,GACFA,EAAIE,UAAUN,EAAO,EAAG,EAAGA,EAAMO,MAAOP,EAAMQ,SAGlDR,EAAME,IAAMkB,EAAgBC,WAE9BD,EAAgBE,gBAAgB/O,SAASgP,GACvCvC,EAASwC,YACPD,EAAeE,MACfF,EAAeG,SACf5C,KAIN,MACF,IAAK,QACH,CACE,MAAM6C,EAAclC,EAAgCmC,MACpDD,GACG5C,EAA2B8C,MAAMtP,SAASkG,GACzCuG,EAAS8C,wBAAwBrJ,EAAMkJ,MAKjD,GAAIjC,EAAa3E,WAAY,CACtB0E,EAAW1E,YAAY0E,EAAW5E,aAAa,CAAEkH,KAAM,SAE5D,MAAM7C,EAAcO,EAAW1E,WAAYzI,WACrC6M,EAAcO,EAAa3E,WAAWzI,YACxC4M,EAAYtK,OAAS,GAAKuK,EAAYvK,OAAS,IACjDyK,EACEpN,MAAMC,KAAKgN,GACXC,EAEAM,EAAW1E,WACXiE,EACAC,GAGN,MAEF,KAAKjJ,EAAWwF,KAChB,KAAKxF,EAAW4F,QAChB,KAAK5F,EAAW8F,MAEZgD,EAAQzF,cACP0F,EAAmDtG,OAEpDqG,EAAQzF,YAAe0F,EAGFtG,MAa3B,GARA8G,GAAqBP,EAASgD,YAAYzC,GAAmB,GAK7DD,GAAoBN,EAASiD,WAAW3C,GAGf,WAArBP,EAAQ3F,SAAuB,CACjC,MAAM8I,EAAsBpD,EAA8BqD,gBACpDC,EAAmBrD,EAEzB,GAAImD,EAAoB,CACtB,MAAMpC,EAAKb,EAAapN,QAAQuQ,EAAiBD,iBAC7CrC,GACFd,EAASI,OAAOxM,IAAIsP,mBAAyBpC,IAE/CjB,EACEqD,EACAE,EAAiBD,gBACjBnD,EACAC,IAIR,CAkCA,SAASI,EACPH,EACAC,EACArK,EACAkK,EACAC,SAEA,IAQIoD,EACFC,EATEC,EAAgB,EAClBC,EAActD,EAAYtK,OAAS,EACnC6N,EAAgB,EAChBC,EAAcvD,EAAYvK,OAAS,EACjC+N,EAAezD,EAAYqD,GAC7BK,EAAa1D,EAAYsD,GACzBK,EAAe1D,EAAYsD,GAC3BK,EAAa3D,EAAYuD,GAG3B,KAAOH,GAAiBC,GAAeC,GAAiBC,GAAa,CACnE,MAAMK,EAAa/D,EAASI,OAAO3N,MAAMkR,GACnCK,EAAWhE,EAASI,OAAO3N,MAAMmR,GACjCK,EAAahE,EAAaxN,MAAMoR,GAChCK,EAAWjE,EAAaxN,MAAMqR,GAOpC,QAAqB/B,IAAjB4B,EACFA,EAAezD,IAAcqD,QACxB,QAAmBxB,IAAf6B,EACTA,EAAa1D,IAAcsD,QACtB,IACW,IAAhBO,GAEAA,IAAeE,EAEfpE,EAAK8D,EAAcE,EAAc7D,EAAUC,GAC3C0D,EAAezD,IAAcqD,GAC7BM,EAAe1D,IAAcsD,QACxB,IACS,IAAdO,GAEAA,IAAaE,EAEbrE,EAAK+D,EAAYE,EAAY9D,EAAUC,GACvC2D,EAAa1D,IAAcsD,GAC3BM,EAAa3D,IAAcuD,QACtB,IACW,IAAhBK,GAEAA,IAAeG,EAEfpO,EAAWS,aAAaoN,EAAcC,EAAW/N,aACjDgK,EAAK8D,EAAcG,EAAY9D,EAAUC,GACzC0D,EAAezD,IAAcqD,GAC7BO,EAAa3D,IAAcuD,QACtB,IACS,IAAdM,GAEAA,IAAaC,EAEbnO,EAAWS,aAAaqN,EAAYD,GACpC9D,EAAK+D,EAAYC,EAAc7D,EAAUC,GACzC2D,EAAa1D,IAAcsD,GAC3BK,EAAe1D,IAAcsD,OACxB,CAGL,IAAKJ,EAAc,CACjBA,EAAe,GACf,IAAK,IAAIc,EAAIZ,EAAeY,GAAKX,EAAaW,IAAK,CACjD,MAAMC,EAAWlE,EAAYiE,GACzBC,GAAYpE,EAASI,OAAO1M,QAAQ0Q,KACtCf,EAAarD,EAASI,OAAO3N,MAAM2R,IAAaD,IAItD,GADAb,EAAaD,EAAapD,EAAaxN,MAAMoR,IACzCP,EAAY,CAEd,MAAMe,EAAanE,EAAYoD,GAC/BxN,EAAWS,aAAa8N,EAAYV,GACpC9D,EAAKwE,EAAYR,EAAc7D,EAAUC,GACzCC,EAAYoD,QAAcvB,MACrB,CACL,MAAMuC,EAAUC,EACdV,EACA7D,EAASI,OACTH,GAQwB,cAAxBnK,EAAWsE,qBACX4F,EAASI,OAAOvN,QAAQyR,yBAAUE,QAASxN,EAAWI,SACrDtB,EAAwBoB,kBAEzBpB,EAAWW,YAAaX,EAAwBoB,iBAChDgJ,EAAYqD,QAAiBxB,EAC7B4B,OAAe5B,GAEjBjM,EAAWS,aAAa+N,EAASX,GAAgB,MACjD9D,EAAKyE,EAAST,EAAc7D,EAAUC,GAExC4D,EAAe1D,IAAcsD,IAGjC,GAAIF,EAAgBC,EAAa,CAC/B,MAAMiB,EAAkBtE,EAAYuD,EAAc,GAClD,IAAIgB,EAAgB,KAQpB,IAPID,GACF3O,EAAWxC,WAAWC,SAAS4C,IAE3B6J,EAASI,OAAO3N,MAAM0D,KAAW8J,EAAaxN,MAAMgS,KAEpDC,EAAgBvO,EAAK,IAEpBsN,GAAiBC,IAAeD,EAAe,CACpD,MAAMa,EAAUC,EACdpE,EAAYsD,GACZzD,EAASI,OACTH,GAEFnK,EAAWS,aAAa+N,EAASI,GACjC7E,EAAKyE,EAASnE,EAAYsD,GAAgBzD,EAAUC,SAEjD,GAAIwD,EAAgBC,EACzB,KAAOH,GAAiBC,EAAaD,IAAiB,CACpD,MAAM5P,EAAOuM,EAAYqD,GACrB5P,IACFmC,EAAWW,YAAY9C,GACvBqM,EAASI,OAAOhN,kBAAkBO,IAI1C,UAEgB4Q,EACdI,EACAC,EACA3E,GAEA,MAAM4E,EAAS5E,EAAaxN,MAAMkS,GAC5B7D,EAAKb,EAAapN,QAAQ8R,GAChC,IAAIhR,EAAoB,KAGxB,GADIkR,GAAU,IAAGlR,EAAOiR,EAAU9R,QAAQ+R,IAC7B,OAATlR,EAAe,OAAOA,EAC1B,OAAQgR,EAAO3N,YACb,KAAKA,EAAWC,SACdtD,EAAO,IAAIsD,SACX,MACF,KAAKD,EAAWW,aACdhE,EAAOsN,SAASzJ,eAAekB,mBAC5BiM,EAA2BtQ,KAC3BsQ,EAA2BnM,SAC3BmM,EAA2B3L,UAE9B,MACF,KAAKhC,EAAWI,QAAS,CACvB,IAAIC,EAAWsN,EAAsBtN,QAAQjC,cAC7CiC,EAAUmG,EAAUnG,IAAYA,EAE9B1D,EADEmN,GAAM,UAAWA,IAAMA,eAAAA,EAAIC,OACtBE,SAAS3H,gBAAgB+D,EAAgB,IAAGhG,GACvC4J,SAAS9H,cAAewL,EAAsBtN,SAC5D,MAEF,KAAKL,EAAWwF,KACd7I,EAAOsN,SAASzH,eAAgBmL,EAAmBlL,MACnD,MACF,KAAKzC,EAAW4F,QACdjJ,EAAOsN,SAASrH,cAAe+K,EAAsBlL,MACrD,MACF,KAAKzC,EAAW8F,MACdnJ,EAAOsN,SAASlH,mBAAoB4K,EAA2BlL,MAKnE,OADIqH,GAAI8D,EAAUhR,IAAID,mBAAWmN,IAC1BnN,CACT,OCjaamR,UAAmBlO,EAAmBmO,IAkBjDzP,YAAY8K,GACVlG,QAlBM/H,+BAA4B,EAI5BA,qBAAkBA,KAAK6S,yBASxB7S,YAAiB8S,IAEjB9S,gBAAgC,KAIjCiO,IACFjO,KAAKiO,OAASA,GAXP8E,qBACT,OAAO/S,KAAKgT,kBAcdxM,eAEEC,EAEAC,EAEAC,GAEA,OAAO,IAAIgM,EAGbpM,mBACEK,EACAP,EACAQ,GAEA,MAAMoM,EAAmB,IAAIC,EAC3BtM,EACAP,EACAQ,GAGF,OADAoM,EAAiBlM,cAAgB/G,KAC1BiT,EAOTjM,cAAc9B,GACZ,MAAMiO,EAAejO,EAAQnC,cAC7B,IAAIkE,EACJ,OAAQkM,GACN,IAAK,QACL,IAAK,QACHlM,EAAU,IAAImM,EAAeD,GAC7B,MACF,IAAK,SACHlM,EAAU,IAAIoM,EAAgBF,EAAcnT,KAAKiO,QACjD,MACF,IAAK,SACHhH,EAAU,IAAIqM,EAAgBH,GAC9B,MACF,IAAK,QACHlM,EAAU,IAAIsM,EAAeJ,GAC7B,MACF,QACElM,EAAU,IAAIuM,EAAUL,GAI5B,OADAlM,EAAQF,cAAgB/G,KACjBiH,EAGTQ,cAAcH,GACZ,MAAMmM,EAAc,IAAIC,EAAUpM,GAElC,OADAmM,EAAY1M,cAAgB/G,KACrByT,EAGT7L,mBAAmBN,GACjB,MAAMqM,EAAc,IAAIC,EAAetM,GAEvC,OADAqM,EAAY5M,cAAgB/G,KACrB2T,EAGTtM,eAAeC,GACb,MAAMuM,EAAW,IAAIC,EAAOxM,GAE5B,OADAuM,EAAS9M,cAAgB/G,KAClB6T,EAGTE,cACE/T,KAAKmB,WAAa,GAClBnB,KAAKiO,OAAOnM,QAGdmE,OACE8B,MAAM9B,OACNjG,KAAKgT,gBAAkBhT,KAAK6S,gCAInBK,EAAiBpM,EAAuB8L,SAExCY,UAAkBtM,EAAkB0L,IAAjDzP,kCACEnD,eAA8B,KAC9BA,gBAAgC,YAGrBoT,UAAuBpJ,EAAuBwJ,WAE9CF,UAAwBE,EAArCrQ,kCACSnD,gBAA4B,KAC5BA,qBAGD,GAINkP,aACE,OAAO,YAIEqE,UAAuBC,EAApCrQ,kCACSnD,WAAuD,UAGnDqT,UAAwBG,EAEnCrQ,YAAYgQ,EAAsBlF,GAChClG,MAAMoL,GAFRnT,qBAA8B,IAAI2S,EAGhC3S,KAAKgR,gBAAgB/C,OAASA,SAIrB6F,EAAStM,EAAeoL,GAGxBc,EAAY/L,EAAkBiL,GAG9BgB,EAAiB9L,EAAuB8K,YA8BrCoB,EACdxS,EACAyS,EACAxB,EACAyB,GAEA,IAAI1B,EAEJ,OAAQhR,EAAK+D,UACX,KAAKzF,WAAS8E,cACRsP,GAA0C,WAA1BA,EAAajM,SAC/BuK,EAAU0B,EAAiClD,iBAE3CwB,EAASyB,EACRzB,EAAuB2B,WAAc3S,EAAkB2S,YAI1D,MACF,KAAKrU,WAASkI,mBAAoB,CAChC,MAAMoM,EAAe5S,EACrBgR,EAASyB,EAAM1N,mBACb6N,EAAalS,KACbkS,EAAa/N,SACb+N,EAAavN,UAEf,MAEF,KAAK/G,WAASuD,aAAc,CAC1B,MAAMgR,EAAc7S,EACd0D,GA7Ca+B,EA6CaoN,aA3CbC,gBACd,OAEFrN,EAAQ/B,QAAQnC,cAyCnByP,EAASyB,EAAMjN,cAAc9B,GAC7B,MAAMqP,EAAY/B,EAClB,IAAK,MAAMtQ,KAAEA,EAAIC,MAAEA,KAAWrB,MAAMC,KAAKsT,EAAY/L,YACnDiM,EAAUjM,WAAWpG,GAAQC,EAE/BkS,EAAY/E,aAAeiF,EAAUjF,WAAa+E,EAAY/E,YAC9D+E,EAAY9E,YAAcgF,EAAUhF,UAAY8E,EAAY9E,WAK5D,MAEF,KAAKzP,WAASwD,UACZkP,EAASyB,EAAM5M,eAAgB7F,EAAc0G,aAAe,IAC5D,MACF,KAAKpI,WAAS4K,mBACZ8H,EAASyB,EAAMrM,mBAAoBpG,EAAsB8F,MACzD,MACF,KAAKxH,WAAS0K,aACZgI,EAASyB,EAAMxM,cAAejG,EAAiB0G,aAAe,IAC9D,MAEF,KAAKpI,WAAS0U,uBACZhC,EAAU0B,EAA4BxK,aAAa,CAAEkH,KAAM,SAC3D,MACF,QACE,OAAO,KAzEb,IAAyB3J,EA4EvB,IAAI0H,EAAkC8D,EAAU/R,QAAQc,GAUxD,OARIyS,aAAiBtB,IACdhE,IACHA,EAAK8F,EAAajC,EAAQyB,EAAMlB,gBAChCN,EAAUhR,IAAID,EAAMmN,IAEtBsF,EAAMhG,OAAOxM,IAAI+Q,mBAAa7D,KAGzB6D,CACT,UAkDgBM,IACd,OAAO,IAAI/S,CACb,OAGaA,EAAboD,cACUnD,eAAiC,IAAIE,IACrCF,iBAAqD,IAAII,QAEjEE,MAAMC,SACJ,IAAKA,EAAG,OAAQ,EAEhB,MAAME,YAAKT,KAAKU,QAAQH,yBAAIE,GAG5B,OAAOA,QAAAA,GAAO,EAGhBE,QAAQF,GACN,OAAOT,KAAKC,UAAUW,IAAIH,IAAO,KAGnCI,SACE,OAAOC,MAAMC,KAAKf,KAAKC,UAAUe,QAGnCN,QAAQH,GACN,OAAOP,KAAKG,YAAYS,IAAIL,IAAM,KAKpCU,kBAAkBV,GAChB,MAAME,EAAKT,KAAKM,MAAMC,GACtBP,KAAKC,UAAUyU,OAAOjU,GAElBF,EAAEY,YACJZ,EAAEY,WAAWC,SAASC,GAAcrB,KAAKiB,kBAAkBI,KAG/DC,IAAIb,GACF,OAAOT,KAAKC,UAAUqB,IAAIb,GAG5Bc,QAAQC,GACN,OAAOxB,KAAKG,YAAYmB,IAAIE,GAG9BC,IAAIlB,EAAWmB,GACb,MAAMjB,EAAKiB,EAAKjB,GAChBT,KAAKC,UAAU0B,IAAIlB,EAAIF,GACvBP,KAAKG,YAAYwB,IAAIpB,EAAGmB,GAG1BE,QAAQnB,EAAYF,GAClB,MAAMsB,EAAU7B,KAAKW,QAAQF,GAC7B,GAAIoB,EAAS,CACX,MAAMH,EAAO1B,KAAKG,YAAYS,IAAIiB,GAC9BH,GAAM1B,KAAKG,YAAYwB,IAAIpB,EAAGmB,GAEpC1B,KAAKC,UAAU0B,IAAIlB,EAAIF,GAGzBuB,QACE9B,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,YAAc,IAAIC,kBAQXqU,EAAajT,EAAef,GAC1C,OAAQe,EAAKqD,YACX,KAAKA,EAAWC,SACd,MAAO,CACLrE,KACA4R,KAAM7Q,EAAKqD,WACX1D,WAAY,IAEhB,KAAK0D,EAAWW,aAAc,CAC5B,MAAMc,EAAU9E,EAChB,MAAO,CACLf,KACA4R,KAAM7Q,EAAKqD,WACX3C,KAAMoE,EAAQpE,KACdmE,SAAUC,EAAQD,SAClBQ,SAAUP,EAAQO,UAGtB,KAAKhC,EAAWI,QACd,MAAO,CACLxE,KACA4R,KAAM7Q,EAAKqD,WACXK,QAAU1D,EAAoB0D,QAAQjC,cACtCqF,WAAY,GACZnH,WAAY,IAEhB,KAAK0D,EAAWwF,KAMhB,KAAKxF,EAAW4F,QACd,MAAO,CACLhK,KACA4R,KAAM7Q,EAAKqD,WACXqD,YAAc1G,EAAoB0G,aAAe,IAErD,KAAKrD,EAAW8F,MACd,MAAO,CACLlK,KACA4R,KAAM7Q,EAAKqD,WACXqD,YAAa,IAGrB,CAWA,SAASyM,EAAKnT,EAAeyM,EAA0B2G,GACrD,IAAIC,EAAY,GAAGD,IAAa3G,EAAO3N,MAAMkB,MAASA,EAAKgD,eAC3D,GAAIhD,EAAKqD,aAAeA,EAAWI,QAAS,CAC1C,MAAMgC,EAAUzF,EACZyF,EAAQ2C,aACViL,GAAaF,EAAK1N,EAAQ2C,WAAYqE,EAAQ2G,EAAa,OAE/D,IAAK,MAAM5Q,KAASxC,EAAKL,WACvB0T,GAAaF,EAAK3Q,EAAOiK,EAAQ2G,EAAa,MAOhD,MANsB,WAAlBpT,EAAKyG,WACP4M,GAAaF,EACVnT,EAAyBwP,gBAC1B/C,EACA2G,EAAa,OAEVC,CACT,oaAzLEC,EACArC,EJ5LF,WACI,OAAO,IAAI1S,CACf,CI0L0BgV,GACxBd,EAAqB,IAAItB,GAmCzB,OAjCA,SAASgC,EAAKnT,EAAY0S,GACxB,MAAM1B,EAASwB,EAAcxS,EAAMyS,EAAOxB,EAAWyB,GACrD,GAAe,OAAX1B,EAYJ,GAT6B,YAA3B0B,eAAAA,EAAcjM,WAEdzG,EAAK+D,WAAazF,WAAS0U,yBAE3BN,SAAAA,EAAcjQ,YAAYuO,GAC1BA,EAAO7O,WAAauQ,EACpB1B,EAAO7M,cAAgBuO,GAGH,WAAlB1S,EAAKyG,SAAuB,CAC9B,MAAM+M,EAAaxT,EAA2BwP,gBAC9CgE,GAAaL,EAAKK,EAAWxC,QAE7BhR,EAAK+D,WAAazF,WAAS8E,eAC3BpD,EAAK+D,WAAazF,WAASuD,cAC3B7B,EAAK+D,WAAazF,WAAS0U,yBAIzBhT,EAAK+D,WAAazF,WAASuD,cAC1B7B,EAAqBoI,YAGtB+K,EAAMnT,EAAqBoI,WAAa4I,GAC1ChR,EAAKL,WAAWC,SAASC,GAAcsT,EAAKtT,EAAWmR,MAG3DmC,CAAKG,EAAK,MACHb,CACT,yGAgI2BgB,EAAmBhH,GAC5C,OAAO0G,EAAKM,EAAUhH,EAAQ,GAChC"}