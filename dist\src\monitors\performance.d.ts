import type { EventData, MonitorOptions } from '../types';
/**
 * 性能监控器
 */
export declare class PerformanceMonitor {
    private options;
    private addEvent;
    private config;
    constructor(options: MonitorOptions, addEvent: (event: EventData) => void);
    /**
     * 初始化性能监控
     */
    init(): void;
    /**
     * 集成web-vitals核心指标
     */
    private initWebVitals;
    /**
     * 初始化基础性能监控（补充 web-vitals 未覆盖的指标）
     */
    private initBasicPerformanceMonitor;
    /**
     * 收集基础性能数据 补齐一下
     */
    private collectBasicPerformanceData;
    /**
     * 获取导航时间信息
     */
    private getNavigationTiming;
    /**
     * 获取导航类型
     */
    private getNavigationType;
    /**
     * 初始化资源性能监控
     */
    private initResourcePerformanceMonitor;
    /**
     * 手动记录性能指标
     * @param name 指标名称
     * @param value 指标值
     * @param extra 额外信息
     */
    recordMetric(name: string, value: number, extra?: Record<string, any>): void;
    /**
     * 检查是否启用服务器性能监控
     */
    isServerPerformanceEnabled(): boolean;
}
//# sourceMappingURL=performance.d.ts.map