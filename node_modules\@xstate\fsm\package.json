{"name": "@xstate/fsm", "version": "1.6.5", "description": "XState for finite state machines", "keywords": ["state", "machine", "scxml", "state", "machine", "finite", "state", "machine", "fsm", "automata"], "author": "<PERSON> <david<PERSON><PERSON><PERSON>@gmail.com>", "homepage": "https://github.com/statelyai/xstate/tree/main/packages/xstate-fsm#readme", "license": "MIT", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "es/index.js", "sideEffects": false, "files": ["dist", "lib/**/*.js", "lib/**/*.d.ts", "es/**/*.js", "es/**/*.d.ts"], "repository": {"type": "git", "url": "git+ssh://**************/statelyai/xstate.git"}, "scripts": {"clean": "rm -rf lib es dist tsconfig.tsbuildinfo", "build": "rollup -c", "test": "jest", "prepare": "npm run build"}, "bugs": {"url": "https://github.com/statelyai/xstate/issues"}, "devDependencies": {"jest": "^26.6.3", "lerna-alias": "3.0.3-0", "rollup": "^2.35.1", "rollup-plugin-filesize": "^6.2.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.2", "rollup-plugin-typescript2": "^0.30.0", "ts-jest": "^26.5.6", "typescript": "^4.5.2"}}