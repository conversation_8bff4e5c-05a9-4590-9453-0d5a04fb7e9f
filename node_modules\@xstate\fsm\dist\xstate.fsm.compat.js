var t,n;t=this,n=function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */function n(t,n){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var r,o,i=e.call(t),a=[];try{for(;(void 0===n||n-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(e=i.return)&&e.call(i)}finally{if(o)throw o.error}}return a}var e;(e=t.InterpreterStatus||(t.InterpreterStatus={}))[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped";var r={type:"xstate.init"};function o(t){return void 0===t?[]:[].concat(t)}function i(t,n){return"string"==typeof(t="string"==typeof t&&n&&n[t]?n[t]:t)?{type:t}:"function"==typeof t?{type:t.name,exec:t}:t}function a(t){return function(n){return t===n}}function u(t){return"string"==typeof t?{type:t}:t}function c(t,n){return{value:t,context:n,actions:[],changed:!1,matches:a(t)}}function f(t,n,e){var r=n,o=!1;return[t.filter((function(t){if("xstate.assign"===t.type){o=!0;var n=Object.assign({},r);return"function"==typeof t.assignment?n=t.assignment(r,e):Object.keys(t.assignment).forEach((function(o){n[o]="function"==typeof t.assignment[o]?t.assignment[o](r,e):t.assignment[o]})),r=n,!1}return!0})),r,o]}var s=function(t,n){return t.actions.forEach((function(e){var r=e.exec;return r&&r(t.context,n)}))};t.assign=function(t){return{type:"xstate.assign",assignment:t}},t.createMachine=function(t,e){void 0===e&&(e={});var s=n(f(o(t.states[t.initial].entry).map((function(t){return i(t,e.actions)})),t.context,r),2),l=s[0],p=s[1],y={config:t,_options:e,initialState:{value:t.initial,actions:l,context:p,matches:a(t.initial)},transition:function(e,r){var s,l,p="string"==typeof e?{value:e,context:t.context}:e,v=p.value,d=p.context,g=u(r),x=t.states[v];if(x.on){var h=o(x.on[g.type]);try{for(var m=function(t){var n="function"==typeof Symbol&&Symbol.iterator,e=n&&t[n],r=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}(h),S=m.next();!S.done;S=m.next()){var b=S.value;if(void 0===b)return c(v,d);var j="string"==typeof b?{target:b}:b,I=j.target,w=j.actions,_=void 0===w?[]:w,E=j.cond,O=void 0===E?function(){return!0}:E,R=void 0===I,M=null!=I?I:v,N=t.states[M];if(O(d,g)){var T=n(f((R?o(_):[].concat(x.exit,_,N.entry).filter((function(t){return t}))).map((function(t){return i(t,y._options.actions)})),d,g),3),k=T[0],F=T[1],P=T[2],X=null!=I?I:v;return{value:X,context:F,actions:k,changed:I!==v||k.length>0||P,matches:a(X)}}}}catch(t){s={error:t}}finally{try{S&&!S.done&&(l=m.return)&&l.call(m)}finally{if(s)throw s.error}}}return c(v,d)}};return y},t.interpret=function(n){var e=n.initialState,o=t.InterpreterStatus.NotStarted,i=new Set,c={_machine:n,send:function(r){o===t.InterpreterStatus.Running&&(e=n.transition(e,r),s(e,u(r)),i.forEach((function(t){return t(e)})))},subscribe:function(t){return i.add(t),t(e),{unsubscribe:function(){return i.delete(t)}}},start:function(i){if(i){var u="object"==typeof i?i:{context:n.config.context,value:i};e={value:u.value,actions:[],context:u.context,matches:a(u.value)}}return o=t.InterpreterStatus.Running,s(e,r),c},stop:function(){return o=t.InterpreterStatus.Stopped,i.clear(),c},get state(){return e},get status(){return o}};return c},Object.defineProperty(t,"__esModule",{value:!0})},"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).XStateFSM={});
