import type { EventData, PerformanceConfig, MonitorOptions } from '../types'
import { SEND_ID, EVENT_TYPES } from '../types'

import { onLCP, onCLS, onINP, onTTFB } from 'web-vitals'

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private config: PerformanceConfig

  constructor(
    private options: MonitorOptions,
    private addEvent: (event: EventData) => void
  ) {
    this.config = options.performance as PerformanceConfig
  }

  /**
   * 初始化性能监控
   */
  init(): void {
    if (this.config.enable) {
      this.initWebVitals()
      // 只收集 web-vitals 未覆盖的基础性能数据
      this.initBasicPerformanceMonitor()
    }

    if (this.config.firstResource) {
      this.initResourcePerformanceMonitor()
    }
  }

  /**
   * 集成web-vitals核心指标
   */
  private initWebVitals(): void {
    onLCP(metric => {
      this.addEvent({
        eventId: 'web-vitals-lcp',
        type: EVENT_TYPES.PERFORMANCE,
        data: metric,
        timestamp: Date.now()
      })
    })
    onCLS(metric => {
      this.addEvent({
        eventId: 'web-vitals-cls',
        type: EVENT_TYPES.PERFORMANCE,
        data: metric,
        timestamp: Date.now()
      })
    })
    onINP(metric => {
      this.addEvent({
        eventId: 'web-vitals-inp',
        type: EVENT_TYPES.PERFORMANCE,
        data: metric,
        timestamp: Date.now()
      })
    })
    onTTFB(metric => {
      this.addEvent({
        eventId: 'web-vitals-ttfb',
        type: EVENT_TYPES.PERFORMANCE,
        data: metric,
        timestamp: Date.now()
      })
    })
  }

  /**
   * 初始化基础性能监控（补充 web-vitals 未覆盖的指标）
   */
  private initBasicPerformanceMonitor(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const basicData = this.collectBasicPerformanceData()
        if (basicData) {
          this.addEvent({
            eventId: SEND_ID.PERFORMANCE,
            type: EVENT_TYPES.PERFORMANCE,
            data: basicData,
            timestamp: Date.now(),
            triggerTime: Date.now()
          })
        }
      }, 100)
    })
  }

  /**
   * 收集基础性能数据 补齐一下
   */
  private collectBasicPerformanceData() {
    if (!window.performance) return null

    // 优先使用现代 Navigation Timing
    const navigation = this.getNavigationTiming()
    if (!navigation) return null

    return {
      // 网络相关时间
      dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcpTime: navigation.connectEnd - navigation.connectStart,
      sslTime: navigation.secureConnectionStart > 0 
        ? navigation.connectEnd - navigation.secureConnectionStart 
        : 0,
      
      // 页面处理时间
      domParseTime: navigation.domInteractive - navigation.domLoading,
      resourceLoadTime: navigation.loadEventStart - navigation.domContentLoadedEventEnd,
      pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
      
      // 页面信息
      navigationType: this.getNavigationType(),
      redirectCount: navigation.redirectCount || 0,
      
      // 时间戳信息
      navigationStart: navigation.fetchStart,
      loadComplete: navigation.loadEventEnd
    }
  }

  /**
   * 获取导航时间信息
   */
  private getNavigationTiming(): PerformanceNavigationTiming | PerformanceTiming | null {
    if (window.performance.getEntriesByType) {
      const navEntries = window.performance.getEntriesByType('navigation')
      if (navEntries.length > 0) {
        return navEntries[0] as PerformanceNavigationTiming
      }
    }
    
    // 降级到旧 API
    return window.performance.timing || null
  }

  /**
   * 获取导航类型
   */
  private getNavigationType(): string {
    if (window.performance.getEntriesByType) {
      const navEntry = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navEntry) {
        return navEntry.type || 'navigate'
      }
    }
    
    // 降级到旧 API
    const navigation = window.performance.navigation
    if (navigation) {
      const types = ['navigate', 'reload', 'back_forward', 'prerender']
      return types[navigation.type] || 'navigate'
    }
    
    return 'navigate'
  }

  /**
   * 初始化资源性能监控
   */
  private initResourcePerformanceMonitor(): void {
    if (!('PerformanceObserver' in window)) {
      console.warn('[TinyMonitor] PerformanceObserver not supported')
      return
    }

    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming
            this.addEvent({
              eventId: SEND_ID.RESOURCE_PERF,
              type: EVENT_TYPES.PERFORMANCE,
              data: {
                name: resourceEntry.name,
                duration: resourceEntry.duration,
                transferSize: resourceEntry.transferSize,
                initiatorType: resourceEntry.initiatorType,
                startTime: resourceEntry.startTime,
                responseEnd: resourceEntry.responseEnd
              },
              timestamp: Date.now(),
              triggerTime: resourceEntry.startTime
            })
          }
        })
      })
      observer.observe({ entryTypes: ['resource'] })
    } catch (error) {
      console.warn('[TinyMonitor] PerformanceObserver init failed:', error)
    }
  }

  /**
   * 手动记录性能指标
   * @param name 指标名称
   * @param value 指标值
   * @param extra 额外信息
   */
  recordMetric(name: string, value: number, extra?: Record<string, any>): void {
    this.addEvent({
      eventId: SEND_ID.PERFORMANCE,
      type: EVENT_TYPES.PERFORMANCE,
      data: {
        metricName: name,
        metricValue: value,
        metricType: 'custom',
        extra
      },
      timestamp: Date.now(),
      triggerTime: Date.now()
    })
  }

  /**
   * 检查是否启用服务器性能监控
   */
  isServerPerformanceEnabled(): boolean {
    return this.config.server || false
  }
}


