{"version": 3, "file": "types.js", "sources": ["../src/index.ts"], "sourcesContent": ["export enum EventType {\n  Dom<PERSON><PERSON><PERSON>Loaded,\n  Load,\n  FullSnapshot,\n  IncrementalSnapshot,\n  Meta,\n  Custom,\n  Plugin,\n}\n\nexport type domContentLoadedEvent = {\n  type: EventType.DomContentLoaded;\n  data: unknown;\n};\n\nexport type loadedEvent = {\n  type: EventType.Load;\n  data: unknown;\n};\n\nexport type fullSnapshotEvent = {\n  type: EventType.FullSnapshot;\n  data: {\n    node: serializedNodeWithId;\n    initialOffset: {\n      top: number;\n      left: number;\n    };\n  };\n};\n\nexport type incrementalSnapshotEvent = {\n  type: EventType.IncrementalSnapshot;\n  data: incrementalData;\n};\n\nexport type metaEvent = {\n  type: EventType.Meta;\n  data: {\n    href: string;\n    width: number;\n    height: number;\n  };\n};\n\nexport type customEvent<T = unknown> = {\n  type: EventType.Custom;\n  data: {\n    tag: string;\n    payload: T;\n  };\n};\n\nexport type pluginEvent<T = unknown> = {\n  type: EventType.Plugin;\n  data: {\n    plugin: string;\n    payload: T;\n  };\n};\n\nexport enum IncrementalSource {\n  Mu<PERSON>,\n  <PERSON>Move,\n  MouseInteraction,\n  Scroll,\n  ViewportResize,\n  Input,\n  TouchMove,\n  MediaInteraction,\n  StyleSheetRule,\n  CanvasMutation,\n  Font,\n  Log,\n  Drag,\n  StyleDeclaration,\n  Selection,\n  AdoptedStyleSheet,\n  CustomElement,\n}\n\nexport type mutationData = {\n  source: IncrementalSource.Mutation;\n} & mutationCallbackParam;\n\nexport type mousemoveData = {\n  source:\n    | IncrementalSource.MouseMove\n    | IncrementalSource.TouchMove\n    | IncrementalSource.Drag;\n  positions: mousePosition[];\n};\n\nexport type mouseInteractionData = {\n  source: IncrementalSource.MouseInteraction;\n} & mouseInteractionParam;\n\nexport type scrollData = {\n  source: IncrementalSource.Scroll;\n} & scrollPosition;\n\nexport type viewportResizeData = {\n  source: IncrementalSource.ViewportResize;\n} & viewportResizeDimension;\n\nexport type inputData = {\n  source: IncrementalSource.Input;\n  id: number;\n} & inputValue;\n\nexport type mediaInteractionData = {\n  source: IncrementalSource.MediaInteraction;\n} & mediaInteractionParam;\n\nexport type styleSheetRuleData = {\n  source: IncrementalSource.StyleSheetRule;\n} & styleSheetRuleParam;\n\nexport type styleDeclarationData = {\n  source: IncrementalSource.StyleDeclaration;\n} & styleDeclarationParam;\n\nexport type canvasMutationData = {\n  source: IncrementalSource.CanvasMutation;\n} & canvasMutationParam;\n\nexport type fontData = {\n  source: IncrementalSource.Font;\n} & fontParam;\n\nexport type selectionData = {\n  source: IncrementalSource.Selection;\n} & selectionParam;\n\nexport type adoptedStyleSheetData = {\n  source: IncrementalSource.AdoptedStyleSheet;\n} & adoptedStyleSheetParam;\n\nexport type customElementData = {\n  source: IncrementalSource.CustomElement;\n} & customElementParam;\n\nexport type incrementalData =\n  | mutationData\n  | mousemoveData\n  | mouseInteractionData\n  | scrollData\n  | viewportResizeData\n  | inputData\n  | mediaInteractionData\n  | styleSheetRuleData\n  | canvasMutationData\n  | fontData\n  | selectionData\n  | styleDeclarationData\n  | adoptedStyleSheetData\n  | customElementData;\n\nexport type eventWithoutTime =\n  | domContentLoadedEvent\n  | loadedEvent\n  | fullSnapshotEvent\n  | incrementalSnapshotEvent\n  | metaEvent\n  | customEvent\n  | pluginEvent;\n\n/**\n * @deprecated intended for internal use\n * a synonym for eventWithoutTime\n */\nexport type event = eventWithoutTime;\n\nexport type eventWithTime = eventWithoutTime & {\n  timestamp: number;\n  delay?: number;\n};\n\nexport type canvasEventWithTime = eventWithTime & {\n  type: EventType.IncrementalSnapshot;\n  data: canvasMutationData;\n};\n\nexport type blockClass = string | RegExp;\n\nexport type maskTextClass = string | RegExp;\n\nexport type SamplingStrategy = Partial<{\n  /**\n   * false means not to record mouse/touch move events\n   * number is the throttle threshold of recording mouse/touch move\n   */\n  mousemove: boolean | number;\n  /**\n   * number is the throttle threshold of mouse/touch move callback\n   */\n  mousemoveCallback: number;\n  /**\n   * false means not to record mouse interaction events\n   * can also specify record some kinds of mouse interactions\n   */\n  mouseInteraction: boolean | Record<string, boolean | undefined>;\n  /**\n   * number is the throttle threshold of recording scroll\n   */\n  scroll: number;\n  /**\n   * number is the throttle threshold of recording media interactions\n   */\n  media: number;\n  /**\n   * 'all' will record all the input events\n   * 'last' will only record the last input value while input a sequence of chars\n   */\n  input: 'all' | 'last';\n  /**\n   * 'all' will record every single canvas call\n   * number between 1 and 60, will record an image snapshots in a web-worker a (maximum) number of times per second.\n   *                          Number only supported where [`OffscreenCanvas`](http://mdn.io/offscreencanvas) is supported.\n   */\n  canvas: 'all' | number;\n}>;\n\nexport interface ICrossOriginIframeMirror {\n  getId(\n    iframe: HTMLIFrameElement,\n    remoteId: number,\n    parentToRemoteMap?: Map<number, number>,\n    remoteToParentMap?: Map<number, number>,\n  ): number;\n  getIds(iframe: HTMLIFrameElement, remoteId: number[]): number[];\n  getRemoteId(\n    iframe: HTMLIFrameElement,\n    parentId: number,\n    map?: Map<number, number>,\n  ): number;\n  getRemoteIds(iframe: HTMLIFrameElement, parentId: number[]): number[];\n  reset(iframe?: HTMLIFrameElement): void;\n}\n\nexport type RecordPlugin<TOptions = unknown> = {\n  name: string;\n  observer?: (\n    cb: (...args: Array<unknown>) => void,\n    win: IWindow,\n    options: TOptions,\n  ) => listenerHandler;\n  eventProcessor?: <TExtend>(event: eventWithTime) => eventWithTime & TExtend;\n  getMirror?: (mirrors: {\n    nodeMirror: IMirror<Node>;\n    crossOriginIframeMirror: ICrossOriginIframeMirror;\n    crossOriginIframeStyleMirror: ICrossOriginIframeMirror;\n  }) => void;\n  options: TOptions;\n};\n\nexport type hooksParam = {\n  mutation?: mutationCallBack;\n  mousemove?: mousemoveCallBack;\n  mouseInteraction?: mouseInteractionCallBack;\n  scroll?: scrollCallback;\n  viewportResize?: viewportResizeCallback;\n  input?: inputCallback;\n  mediaInteaction?: mediaInteractionCallback;\n  styleSheetRule?: styleSheetRuleCallback;\n  styleDeclaration?: styleDeclarationCallback;\n  canvasMutation?: canvasMutationCallback;\n  font?: fontCallback;\n  selection?: selectionCallback;\n  customElement?: customElementCallback;\n};\n\n// https://dom.spec.whatwg.org/#interface-mutationrecord\nexport type mutationRecord = Readonly<{\n  type: string;\n  target: Node;\n  oldValue: string | null;\n  addedNodes: NodeList;\n  removedNodes: NodeList;\n  attributeName: string | null;\n}>;\n\nexport type textCursor = {\n  node: Node;\n  value: string | null;\n};\nexport type textMutation = {\n  id: number;\n  value: string | null;\n};\n\nexport type styleOMValue = {\n  [key: string]: styleValueWithPriority | string | false;\n};\n\nexport type styleValueWithPriority = [string, string];\n\nexport type attributeCursor = {\n  node: Node;\n  attributes: {\n    [key: string]: string | styleOMValue | null;\n  };\n  styleDiff: styleOMValue;\n  _unchangedStyles: styleOMValue;\n};\nexport type attributeMutation = {\n  id: number;\n  attributes: {\n    [key: string]: string | styleOMValue | null;\n  };\n};\n\nexport type removedNodeMutation = {\n  parentId: number;\n  id: number;\n  isShadow?: boolean;\n};\n\nexport type addedNodeMutation = {\n  parentId: number;\n  // Newly recorded mutations will not have previousId any more, just for compatibility\n  previousId?: number | null;\n  nextId: number | null;\n  node: serializedNodeWithId;\n};\n\nexport type mutationCallbackParam = {\n  texts: textMutation[];\n  attributes: attributeMutation[];\n  removes: removedNodeMutation[];\n  adds: addedNodeMutation[];\n  isAttachIframe?: true;\n};\n\nexport type mutationCallBack = (m: mutationCallbackParam) => void;\n\nexport type mousemoveCallBack = (\n  p: mousePosition[],\n  source:\n    | IncrementalSource.MouseMove\n    | IncrementalSource.TouchMove\n    | IncrementalSource.Drag,\n) => void;\n\nexport type mousePosition = {\n  x: number;\n  y: number;\n  id: number;\n  timeOffset: number;\n};\n\nexport type mouseMovePos = {\n  x: number;\n  y: number;\n  id: number;\n  debugData: incrementalData;\n};\n\nexport enum MouseInteractions {\n  MouseUp,\n  MouseDown,\n  Click,\n  ContextMenu,\n  DblClick,\n  Focus,\n  Blur,\n  TouchStart,\n  TouchMove_Departed, // we will start a separate observer for touch move event\n  TouchEnd,\n  TouchCancel,\n}\n\nexport enum PointerTypes {\n  Mouse,\n  Pen,\n  Touch,\n}\n\nexport enum CanvasContext {\n  '2D',\n  WebGL,\n  WebGL2,\n}\n\nexport type SerializedCanvasArg =\n  | {\n      rr_type: 'ArrayBuffer';\n      base64: string; // base64\n    }\n  | {\n      rr_type: 'Blob';\n      data: Array<CanvasArg>;\n      type?: string;\n    }\n  | {\n      rr_type: string;\n      src: string; // url of image\n    }\n  | {\n      rr_type: string;\n      args: Array<CanvasArg>;\n    }\n  | {\n      rr_type: string;\n      index: number;\n    };\n\nexport type CanvasArg =\n  | SerializedCanvasArg\n  | string\n  | number\n  | boolean\n  | null\n  | CanvasArg[];\n\ntype mouseInteractionParam = {\n  type: MouseInteractions;\n  id: number;\n  x?: number;\n  y?: number;\n  pointerType?: PointerTypes;\n};\n\nexport type mouseInteractionCallBack = (d: mouseInteractionParam) => void;\n\nexport type scrollPosition = {\n  id: number;\n  x: number;\n  y: number;\n};\n\nexport type scrollCallback = (p: scrollPosition) => void;\n\nexport type styleSheetAddRule = {\n  rule: string;\n  index?: number | number[];\n};\n\nexport type styleSheetDeleteRule = {\n  index: number | number[];\n};\n\nexport type styleSheetRuleParam = {\n  id?: number;\n  styleId?: number;\n  removes?: styleSheetDeleteRule[];\n  adds?: styleSheetAddRule[];\n  replace?: string;\n  replaceSync?: string;\n};\n\nexport type styleSheetRuleCallback = (s: styleSheetRuleParam) => void;\n\nexport type adoptedStyleSheetParam = {\n  // id indicates the node id of document or shadow DOMs' host element.\n  id: number;\n  // New CSSStyleSheets which have never appeared before.\n  styles?: {\n    styleId: number;\n    rules: styleSheetAddRule[];\n  }[];\n  // StyleSheet ids to be adopted.\n  styleIds: number[];\n};\n\nexport type adoptedStyleSheetCallback = (a: adoptedStyleSheetParam) => void;\n\nexport type styleDeclarationParam = {\n  id?: number;\n  styleId?: number;\n  index: number[];\n  set?: {\n    property: string;\n    value: string | null;\n    priority: string | undefined;\n  };\n  remove?: {\n    property: string;\n  };\n};\n\nexport type styleDeclarationCallback = (s: styleDeclarationParam) => void;\n\nexport type canvasMutationCommand = {\n  property: string;\n  args: Array<unknown>;\n  setter?: true;\n};\n\nexport type canvasMutationParam =\n  | {\n      id: number;\n      type: CanvasContext;\n      commands: canvasMutationCommand[];\n    }\n  | ({\n      id: number;\n      type: CanvasContext;\n    } & canvasMutationCommand);\n\nexport type canvasMutationWithType = {\n  type: CanvasContext;\n} & canvasMutationCommand;\n\nexport type canvasMutationCallback = (p: canvasMutationParam) => void;\n\nexport type canvasManagerMutationCallback = (\n  target: HTMLCanvasElement,\n  p: canvasMutationWithType,\n) => void;\n\nexport type ImageBitmapDataURLWorkerParams = {\n  id: number;\n  bitmap: ImageBitmap;\n  width: number;\n  height: number;\n  dataURLOptions: DataURLOptions;\n};\n\nexport type ImageBitmapDataURLWorkerResponse =\n  | {\n      id: number;\n    }\n  | {\n      id: number;\n      type: string;\n      base64: string;\n      width: number;\n      height: number;\n    };\n\nexport type fontParam = {\n  family: string;\n  fontSource: string;\n  buffer: boolean;\n  descriptors?: FontFaceDescriptors;\n};\n\nexport type fontCallback = (p: fontParam) => void;\n\nexport type viewportResizeDimension = {\n  width: number;\n  height: number;\n};\n\nexport type viewportResizeCallback = (d: viewportResizeDimension) => void;\n\nexport type inputValue = {\n  text: string;\n  isChecked: boolean;\n\n  // `userTriggered` indicates if this event was triggered directly by user (userTriggered: true)\n  // or was triggered indirectly (userTriggered: false)\n  // Example of `userTriggered` in action:\n  // User clicks on radio element (userTriggered: true) which triggers the other radio element to change (userTriggered: false)\n  userTriggered?: boolean;\n};\n\nexport type inputCallback = (v: inputValue & { id: number }) => void;\n\nexport enum MediaInteractions {\n  Play,\n  Pause,\n  Seeked,\n  VolumeChange,\n  RateChange,\n}\n\nexport type mediaInteractionParam = {\n  type: MediaInteractions;\n  id: number;\n  currentTime?: number;\n  volume?: number;\n  muted?: boolean;\n  loop?: boolean;\n  playbackRate?: number;\n};\n\nexport type mediaInteractionCallback = (p: mediaInteractionParam) => void;\n\nexport type DocumentDimension = {\n  x: number;\n  y: number;\n  // scale value relative to its parent iframe\n  relativeScale: number;\n  // scale value relative to the root iframe\n  absoluteScale: number;\n};\n\nexport type SelectionRange = {\n  start: number;\n  startOffset: number;\n  end: number;\n  endOffset: number;\n};\n\nexport type selectionParam = {\n  ranges: Array<SelectionRange>;\n};\n\nexport type selectionCallback = (p: selectionParam) => void;\n\nexport type customElementParam = {\n  define?: {\n    name: string;\n  };\n};\n\nexport type customElementCallback = (c: customElementParam) => void;\n\n/**\n *  @deprecated\n */\ninterface INode extends Node {\n  __sn: serializedNodeWithId;\n}\n\nexport type DeprecatedMirror = {\n  map: {\n    [key: number]: INode;\n  };\n  getId: (n: Node) => number;\n  getNode: (id: number) => INode | null;\n  removeNodeFromMap: (n: Node) => void;\n  has: (id: number) => boolean;\n  reset: () => void;\n};\n\nexport type throttleOptions = {\n  leading?: boolean;\n  trailing?: boolean;\n};\n\nexport type listenerHandler = () => void;\nexport type hookResetter = () => void;\n\nexport type playerMetaData = {\n  startTime: number;\n  endTime: number;\n  totalTime: number;\n};\n\nexport type actionWithDelay = {\n  doAction: () => void;\n  delay: number;\n};\n\nexport type Handler = (event?: unknown) => void;\n\nexport type Emitter = {\n  on(type: string, handler: Handler): void;\n  emit(type: string, event?: unknown): void;\n  off(type: string, handler: Handler): void;\n};\n\nexport type Arguments<T> = T extends (...payload: infer U) => unknown\n  ? U\n  : unknown;\n\nexport enum ReplayerEvents {\n  Start = 'start',\n  Pause = 'pause',\n  /**\n   * @deprecated use Play instead\n   */\n  Resume = 'resume',\n  Resize = 'resize',\n  Finish = 'finish',\n  FullsnapshotRebuilded = 'fullsnapshot-rebuilded',\n  LoadStylesheetStart = 'load-stylesheet-start',\n  LoadStylesheetEnd = 'load-stylesheet-end',\n  SkipStart = 'skip-start',\n  SkipEnd = 'skip-end',\n  MouseInteraction = 'mouse-interaction',\n  EventCast = 'event-cast',\n  CustomEvent = 'custom-event',\n  Flush = 'flush',\n  StateChange = 'state-change',\n  PlayBack = 'play-back',\n  Destroy = 'destroy',\n}\n\nexport type KeepIframeSrcFn = (src: string) => boolean;\n\ndeclare global {\n  interface Window {\n    FontFace: typeof FontFace;\n  }\n}\n\nexport type IWindow = Window & typeof globalThis;\n\nexport type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport type GetTypedKeys<Obj extends object, ValueType> = TakeTypeHelper<\n  Obj,\n  ValueType\n>[keyof TakeTypeHelper<Obj, ValueType>];\nexport type TakeTypeHelper<Obj extends object, ValueType> = {\n  [K in keyof Obj]: Obj[K] extends ValueType ? K : never;\n};\n\nexport type TakeTypedKeyValues<Obj extends object, Type> = Pick<\n  Obj,\n  TakeTypeHelper<Obj, Type>[keyof TakeTypeHelper<Obj, Type>]\n>;\n\nexport enum NodeType {\n  Document,\n  DocumentType,\n  Element,\n  Text,\n  CDATA,\n  Comment,\n}\n\nexport type documentNode = {\n  type: NodeType.Document;\n  childNodes: serializedNodeWithId[];\n  compatMode?: string;\n};\n\nexport type documentTypeNode = {\n  type: NodeType.DocumentType;\n  name: string;\n  publicId: string;\n  systemId: string;\n};\n\ntype cssTextKeyAttr = {\n  _cssText?: string;\n};\n\nexport type attributes = cssTextKeyAttr & {\n  [key: string]:\n    | string\n    | number // properties e.g. rr_scrollLeft or rr_mediaCurrentTime\n    | true // e.g. checked  on <input type=\"radio\">\n    | null; // an indication that an attribute was removed (during a mutation)\n};\n\nexport type legacyAttributes = {\n  /**\n   * @deprecated old bug in rrweb was causing these to always be set\n   * @see https://github.com/rrweb-io/rrweb/pull/651\n   */\n  selected: false;\n};\n\nexport type mediaAttributes = {\n  rr_mediaState: 'played' | 'paused';\n  rr_mediaCurrentTime: number;\n  /**\n   * for backwards compatibility this is optional but should always be set\n   */\n  rr_mediaPlaybackRate?: number;\n  /**\n   * for backwards compatibility this is optional but should always be set\n   */\n  rr_mediaMuted?: boolean;\n  /**\n   * for backwards compatibility this is optional but should always be set\n   */\n  rr_mediaLoop?: boolean;\n  /**\n   * for backwards compatibility this is optional but should always be set\n   */\n  rr_mediaVolume?: number;\n};\n\nexport type elementNode = {\n  type: NodeType.Element;\n  tagName: string;\n  attributes: attributes;\n  childNodes: serializedNodeWithId[];\n  isSVG?: true;\n  needBlock?: boolean;\n  // This is a custom element or not.\n  isCustom?: true;\n};\n\nexport type textNode = {\n  type: NodeType.Text;\n  textContent: string;\n  /**\n   * @deprecated styles are now always snapshotted against parent <style> element\n   * style mutations can still happen via an added textNode, but they don't need this attribute for correct replay\n   */\n  isStyle?: true;\n};\n\nexport type cdataNode = {\n  type: NodeType.CDATA;\n  textContent: '';\n};\n\nexport type commentNode = {\n  type: NodeType.Comment;\n  textContent: string;\n};\n\nexport type serializedNode = (\n  | documentNode\n  | documentTypeNode\n  | elementNode\n  | textNode\n  | cdataNode\n  | commentNode\n) & {\n  rootId?: number;\n  isShadowHost?: boolean;\n  isShadow?: boolean;\n};\n\nexport type serializedNodeWithId = serializedNode & { id: number };\n\nexport type serializedElementNodeWithId = Extract<\n  serializedNodeWithId,\n  Record<'type', NodeType.Element>\n>;\n\nexport interface IMirror<TNode> {\n  getId(n: TNode | undefined | null): number;\n\n  getNode(id: number): TNode | null;\n\n  getIds(): number[];\n\n  getMeta(n: TNode): serializedNodeWithId | null;\n\n  removeNodeFromMap(n: TNode): void;\n\n  has(id: number): boolean;\n\n  hasNode(node: TNode): boolean;\n\n  add(n: TNode, meta: serializedNodeWithId): void;\n\n  replace(id: number, n: TNode): void;\n\n  reset(): void;\n}\n\nexport type DataURLOptions = Partial<{\n  type: string;\n  quality: number;\n}>;\n\n// Types for @rrweb/packer\nexport type PackFn = (event: eventWithTime) => string;\nexport type UnpackFn = (raw: string) => eventWithTime;\n"], "names": ["EventType", "IncrementalSource", "MouseInteractions", "PointerTypes", "CanvasContext", "MediaInteractions", "ReplayerEvents", "NodeType"], "mappings": "AAAY,IAAA,8BAAAA,eAAL;AACLA,aAAA,WAAA,kBAAA,IAAA,CAAA,IAAA;AACAA,aAAA,WAAA,MAAA,IAAA,CAAA,IAAA;AACAA,aAAA,WAAA,cAAA,IAAA,CAAA,IAAA;AACAA,aAAA,WAAA,qBAAA,IAAA,CAAA,IAAA;AACAA,aAAA,WAAA,MAAA,IAAA,CAAA,IAAA;AACAA,aAAA,WAAA,QAAA,IAAA,CAAA,IAAA;AACAA,aAAA,WAAA,QAAA,IAAA,CAAA,IAAA;AAPUA,SAAAA;AAAA,GAAA,aAAA,CAAA,CAAA;AA6DA,IAAA,sCAAAC,uBAAL;AACLA,qBAAA,mBAAA,UAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,WAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,kBAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,QAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,gBAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,OAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,WAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,kBAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,gBAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,gBAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,MAAA,IAAA,EAAA,IAAA;AACAA,qBAAA,mBAAA,KAAA,IAAA,EAAA,IAAA;AACAA,qBAAA,mBAAA,MAAA,IAAA,EAAA,IAAA;AACAA,qBAAA,mBAAA,kBAAA,IAAA,EAAA,IAAA;AACAA,qBAAA,mBAAA,WAAA,IAAA,EAAA,IAAA;AACAA,qBAAA,mBAAA,mBAAA,IAAA,EAAA,IAAA;AACAA,qBAAA,mBAAA,eAAA,IAAA,EAAA,IAAA;AAjBUA,SAAAA;AAAA,GAAA,qBAAA,CAAA,CAAA;AAySA,IAAA,sCAAAC,uBAAL;AACLA,qBAAA,mBAAA,SAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,WAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,OAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,aAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,UAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,OAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,MAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,YAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,oBAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,UAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,aAAA,IAAA,EAAA,IAAA;AAXUA,SAAAA;AAAA,GAAA,qBAAA,CAAA,CAAA;AAcA,IAAA,iCAAAC,kBAAL;AACLA,gBAAA,cAAA,OAAA,IAAA,CAAA,IAAA;AACAA,gBAAA,cAAA,KAAA,IAAA,CAAA,IAAA;AACAA,gBAAA,cAAA,OAAA,IAAA,CAAA,IAAA;AAHUA,SAAAA;AAAA,GAAA,gBAAA,CAAA,CAAA;AAMA,IAAA,kCAAAC,mBAAL;AACLA,iBAAA,eAAA,IAAA,IAAA,CAAA,IAAA;AACAA,iBAAA,eAAA,OAAA,IAAA,CAAA,IAAA;AACAA,iBAAA,eAAA,QAAA,IAAA,CAAA,IAAA;AAHUA,SAAAA;AAAA,GAAA,iBAAA,CAAA,CAAA;AAsLA,IAAA,sCAAAC,uBAAL;AACLA,qBAAA,mBAAA,MAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,OAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,QAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,cAAA,IAAA,CAAA,IAAA;AACAA,qBAAA,mBAAA,YAAA,IAAA,CAAA,IAAA;AALUA,SAAAA;AAAA,GAAA,qBAAA,CAAA,CAAA;AAmGA,IAAA,mCAAAC,oBAAL;AACLA,kBAAA,OAAQ,IAAA;AACRA,kBAAA,OAAQ,IAAA;AAIRA,kBAAA,QAAS,IAAA;AACTA,kBAAA,QAAS,IAAA;AACTA,kBAAA,QAAS,IAAA;AACTA,kBAAA,uBAAwB,IAAA;AACxBA,kBAAA,qBAAsB,IAAA;AACtBA,kBAAA,mBAAoB,IAAA;AACpBA,kBAAA,WAAY,IAAA;AACZA,kBAAA,SAAU,IAAA;AACVA,kBAAA,kBAAmB,IAAA;AACnBA,kBAAA,WAAY,IAAA;AACZA,kBAAA,aAAc,IAAA;AACdA,kBAAA,OAAQ,IAAA;AACRA,kBAAA,aAAc,IAAA;AACdA,kBAAA,UAAW,IAAA;AACXA,kBAAA,SAAU,IAAA;AApBAA,SAAAA;AAAA,GAAA,kBAAA,CAAA,CAAA;AAgDA,IAAA,6BAAAC,cAAL;AACLA,YAAA,UAAA,UAAA,IAAA,CAAA,IAAA;AACAA,YAAA,UAAA,cAAA,IAAA,CAAA,IAAA;AACAA,YAAA,UAAA,SAAA,IAAA,CAAA,IAAA;AACAA,YAAA,UAAA,MAAA,IAAA,CAAA,IAAA;AACAA,YAAA,UAAA,OAAA,IAAA,CAAA,IAAA;AACAA,YAAA,UAAA,SAAA,IAAA,CAAA,IAAA;AANUA,SAAAA;AAAA,GAAA,YAAA,CAAA,CAAA;"}