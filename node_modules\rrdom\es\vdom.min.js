var e;!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(e||(e={}));var t=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){var t;if(!e)return-1;var n=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=n?n:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,n=this.getId(e);this.idNodeMap.delete(n),e.childNodes&&e.childNodes.forEach((function(e){return t.removeNodeFromMap(e)}))},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function n(e){const t=[];for(const n in e){const o=e[n];if("string"!=typeof o)continue;const s=a(n);t.push(`${s}: ${o};`)}return t.join(" ")}const o=/-([a-z])/g,s=/^--[a-zA-Z0-9-]+$/,r=e=>s.test(e)?e:e.replace(o,((e,t)=>t?t.toUpperCase():"")),i=/\B([A-Z])/g,a=e=>e.replace(i,"-$1").toLowerCase();class d{constructor(...e){this.childNodes=[],this.parentElement=null,this.parentNode=null,this.ELEMENT_NODE=T.ELEMENT_NODE,this.TEXT_NODE=T.TEXT_NODE}get firstChild(){return this.childNodes[0]||null}get lastChild(){return this.childNodes[this.childNodes.length-1]||null}get nextSibling(){const e=this.parentNode;if(!e)return null;const t=e.childNodes,n=t.indexOf(this);return t[n+1]||null}contains(e){if(e===this)return!0;for(const t of this.childNodes)if(t.contains(e))return!0;return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}function c(t){return class n extends t{constructor(){super(...arguments),this.nodeType=T.DOCUMENT_NODE,this.nodeName="#document",this.compatMode="CSS1Compat",this.RRNodeType=e.Document,this.textContent=null}get documentElement(){return this.childNodes.find((t=>t.RRNodeType===e.Element&&"HTML"===t.tagName))||null}get body(){var t;return(null===(t=this.documentElement)||void 0===t?void 0:t.childNodes.find((t=>t.RRNodeType===e.Element&&"BODY"===t.tagName)))||null}get head(){var t;return(null===(t=this.documentElement)||void 0===t?void 0:t.childNodes.find((t=>t.RRNodeType===e.Element&&"HEAD"===t.tagName)))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(t){const n=t.RRNodeType;if((n===e.Element||n===e.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===n)))throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${n===e.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);return t.parentElement=null,t.parentNode=this,this.childNodes.push(t),t}insertBefore(t,n){const o=t.RRNodeType;if((o===e.Element||o===e.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===o)))throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${o===e.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);if(null===n)return this.appendChild(t);const s=this.childNodes.indexOf(n);if(-1==s)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(s,0,t),t.parentElement=null,t.parentNode=this,t}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}open(){this.childNodes=[]}close(){}write(e){let t;if('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'===e?t="-//W3C//DTD XHTML 1.0 Transitional//EN":'<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">'===e&&(t="-//W3C//DTD HTML 4.0 Transitional//EN"),t){const e=this.createDocumentType("html",t,"");this.open(),this.appendChild(e)}}createDocument(e,t,o){return new n}createDocumentType(e,t,n){const o=new(l(d))(e,t,n);return o.ownerDocument=this,o}createElement(e){const t=new(u(d))(e);return t.ownerDocument=this,t}createElementNS(e,t){return this.createElement(t)}createTextNode(e){const t=new(p(d))(e);return t.ownerDocument=this,t}createComment(e){const t=new(m(d))(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new(N(d))(e);return t.ownerDocument=this,t}toString(){return"RRDocument"}}}function l(t){return class extends t{constructor(t,n,o){super(),this.nodeType=T.DOCUMENT_TYPE_NODE,this.RRNodeType=e.DocumentType,this.textContent=null,this.name=t,this.publicId=n,this.systemId=o,this.nodeName=t}toString(){return"RRDocumentType"}}}function u(t){return class extends t{constructor(t){super(),this.nodeType=T.ELEMENT_NODE,this.RRNodeType=e.Element,this.attributes={},this.shadowRoot=null,this.tagName=t.toUpperCase(),this.nodeName=t.toUpperCase()}get textContent(){let e="";return this.childNodes.forEach((t=>e+=t.textContent)),e}set textContent(e){this.childNodes=[this.ownerDocument.createTextNode(e)]}get classList(){return new E(this.attributes.class,(e=>{this.attributes.class=e}))}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const e=this.attributes.style?function(e){const t={},n=/:(.+)/;return e.replace(/\/\*.*?\*\//g,"").split(/;(?![^(]*\))/g).forEach((function(e){if(e){const o=e.split(n);o.length>1&&(t[r(o[0].trim())]=o[1].trim())}})),t}(this.attributes.style):{},t=/\B([A-Z])/g;return e.setProperty=(o,s,i)=>{if(t.test(o))return;const a=r(o);s?e[a]=s:delete e[a],"important"===i&&(e[a]+=" !important"),this.attributes.style=n(e)},e.removeProperty=o=>{if(t.test(o))return"";const s=r(o),i=e[s]||"";return delete e[s],this.attributes.style=n(e),i},e}getAttribute(e){return this.attributes[e]||null}setAttribute(e,t){this.attributes[e]=t}setAttributeNS(e,t,n){this.setAttribute(t,n)}removeAttribute(e){delete this.attributes[e]}appendChild(e){return this.childNodes.push(e),e.parentNode=this,e.parentElement=this,e}insertBefore(e,t){if(null===t)return this.appendChild(e);const n=this.childNodes.indexOf(t);if(-1==n)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(n,0,e),e.parentElement=this,e.parentNode=this,e}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}attachShadow(e){const t=this.ownerDocument.createElement("SHADOWROOT");return this.shadowRoot=t,t}dispatchEvent(e){return!0}toString(){let e="";for(const t in this.attributes)e+=`${t}="${this.attributes[t]}" `;return`${this.tagName} ${e}`}}}function h(e){return class extends e{attachShadow(e){throw new Error("RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow")}play(){this.paused=!1}pause(){this.paused=!0}}}function p(t){return class extends t{constructor(t){super(),this.nodeType=T.TEXT_NODE,this.nodeName="#text",this.RRNodeType=e.Text,this.data=t}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRText text=${JSON.stringify(this.data)}`}}}function m(t){return class extends t{constructor(t){super(),this.nodeType=T.COMMENT_NODE,this.nodeName="#comment",this.RRNodeType=e.Comment,this.data=t}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}}function N(t){return class extends t{constructor(t){super(),this.nodeName="#cdata-section",this.nodeType=T.CDATA_SECTION_NODE,this.RRNodeType=e.CDATA,this.data=t}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}}class E{constructor(e,t){if(this.classes=[],this.add=(...e)=>{for(const t of e){const e=String(t);this.classes.indexOf(e)>=0||this.classes.push(e)}this.onChange&&this.onChange(this.classes.join(" "))},this.remove=(...e)=>{this.classes=this.classes.filter((t=>-1===e.indexOf(t))),this.onChange&&this.onChange(this.classes.join(" "))},e){const t=e.trim().split(/\s+/);this.classes.push(...t)}this.onChange=t}}var T;!function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(T||(T={}));const R={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"};function f(t,n,o,s){const r=t.childNodes,i=n.childNodes;s=s||n.mirror||n.ownerDocument.mirror,(r.length>0||i.length>0)&&D(Array.from(r),i,t,o,s);let a=null,d=null;switch(n.RRNodeType){case e.Document:d=n.scrollData;break;case e.Element:{const e=t,r=n;switch(function(e,t,n){const o=e.attributes,s=t.attributes;for(const o in s){const r=s[o],i=n.getMeta(t);if(i&&"isSVG"in i&&i.isSVG&&R[o])e.setAttributeNS(R[o],o,r);else if("CANVAS"===t.tagName&&"rr_dataURL"===o){const t=document.createElement("img");t.src=r,t.onload=()=>{const n=e.getContext("2d");n&&n.drawImage(t,0,0,t.width,t.height)}}else e.setAttribute(o,r)}for(const{name:t}of Array.from(o))t in s||e.removeAttribute(t);t.scrollLeft&&(e.scrollLeft=t.scrollLeft),t.scrollTop&&(e.scrollTop=t.scrollTop)}(e,r,s),d=r.scrollData,a=r.inputData,r.tagName){case"AUDIO":case"VIDEO":{const e=t,n=r;void 0!==n.paused&&(n.paused?e.pause():e.play()),void 0!==n.muted&&(e.muted=n.muted),void 0!==n.volume&&(e.volume=n.volume),void 0!==n.currentTime&&(e.currentTime=n.currentTime);break}case"CANVAS":n.canvasMutations.forEach((e=>o.applyCanvas(e.event,e.mutation,t)));break;case"STYLE":!function(e,t){const n=e.sheet;t.forEach((e=>{if(e.type===g.Insert)try{if(Array.isArray(e.index)){const{positions:t,index:o}=M(e.index);C(n.cssRules,t).insertRule(e.cssText,o)}else n.insertRule(e.cssText,e.index)}catch(e){}else if(e.type===g.Remove)try{if(Array.isArray(e.index)){const{positions:t,index:o}=M(e.index);C(n.cssRules,t).deleteRule(o||0)}else n.deleteRule(e.index)}catch(e){}else if(e.type===g.SetProperty){C(n.cssRules,e.index).style.setProperty(e.property,e.value,e.priority)}else if(e.type===g.RemoveProperty){C(n.cssRules,e.index).style.removeProperty(e.property)}}))}(e,n.rules)}if(r.shadowRoot){e.shadowRoot||e.attachShadow({mode:"open"});const t=e.shadowRoot.childNodes,n=r.shadowRoot.childNodes;(t.length>0||n.length>0)&&D(Array.from(t),n,e.shadowRoot,o,s)}break}case e.Text:case e.Comment:case e.CDATA:t.textContent!==n.data&&(t.textContent=n.data)}if(d&&o.applyScroll(d,!0),a&&o.applyInput(a),"IFRAME"===n.nodeName){const e=t.contentDocument,r=n;if(e){const t=s.getMeta(r.contentDocument);t&&o.mirror.add(e,Object.assign({},t)),f(e,r.contentDocument,o,s)}}}function D(t,n,o,s,r){var i,a;let d,c,l=0,u=t.length-1,h=0,p=n.length-1,m=t[l],N=t[u],E=n[h],T=n[p];for(;l<=u&&h<=p;)if(void 0===m)m=t[++l];else if(void 0===N)N=t[--u];else if(s.mirror.getId(m)===r.getId(E))f(m,E,s,r),m=t[++l],E=n[++h];else if(s.mirror.getId(N)===r.getId(T))f(N,T,s,r),N=t[--u],T=n[--p];else if(s.mirror.getId(m)===r.getId(T))o.insertBefore(m,N.nextSibling),f(m,T,s,r),m=t[++l],T=n[--p];else if(s.mirror.getId(N)===r.getId(E))o.insertBefore(N,m),f(N,E,s,r),N=t[--u],E=n[++h];else{if(!d){d={};for(let e=l;e<=u;e++){const n=t[e];n&&s.mirror.hasNode(n)&&(d[s.mirror.getId(n)]=e)}}if(c=d[r.getId(E)],c){const e=t[c];o.insertBefore(e,m),f(e,E,s,r),t[c]=void 0}else{const n=y(E,s.mirror,r);(null===(i=s.mirror.getMeta(o))||void 0===i?void 0:i.type)===e.Document&&(null===(a=s.mirror.getMeta(n))||void 0===a?void 0:a.type)===e.Element&&o.documentElement&&(o.removeChild(o.documentElement),t[l]=void 0,m=void 0),o.insertBefore(n,m||null),f(n,E,s,r)}E=n[++h]}if(l>u){const e=n[p+1];let t=null;for(e&&o.childNodes.forEach((n=>{s.mirror.getId(n)===r.getId(e)&&(t=n)}));h<=p;++h){const e=y(n[h],s.mirror,r);o.insertBefore(e,t),f(e,n[h],s,r)}}else if(h>p)for(;l<=u;l++){const e=t[l];e&&(o.removeChild(e),s.mirror.removeNodeFromMap(e))}}function y(t,n,o){let s=n.getNode(o.getId(t));const r=o.getMeta(t);if(null!==s)return s;switch(t.RRNodeType){case e.Document:s=new Document;break;case e.DocumentType:s=document.implementation.createDocumentType(t.name,t.publicId,t.systemId);break;case e.Element:t.tagName.toLowerCase(),s=r&&"isSVG"in r&&(null==r?void 0:r.isSVG)?document.createElementNS(R.svg,t.tagName.toLowerCase()):document.createElement(t.tagName);break;case e.Text:s=document.createTextNode(t.data);break;case e.Comment:s=document.createComment(t.data);break;case e.CDATA:s=document.createCDATASection(t.data)}return r&&n.add(s,Object.assign({},r)),s}function C(e,t){const n=e[t[0]];return 1===t.length?n:C(n.cssRules[t[1]].cssRules,t.slice(2))}var g;function M(e){const t=[...e],n=t.pop();return{positions:t,index:n}}!function(e){e[e.Insert=0]="Insert",e[e.Remove=1]="Remove",e[e.Snapshot=2]="Snapshot",e[e.SetProperty=3]="SetProperty",e[e.RemoveProperty=4]="RemoveProperty"}(g||(g={}));class w extends(c(d)){constructor(e){super(),this._unserializedId=-1,this.mirror=U(),this.scrollData=null,e&&(this.mirror=e)}get unserializedId(){return this._unserializedId--}createDocument(e,t,n){return new w}createDocumentType(e,t,n){const o=new O(e,t,n);return o.ownerDocument=this,o}createElement(e){const t=e.toUpperCase();let n;switch(t){case"AUDIO":case"VIDEO":n=new v(t);break;case"IFRAME":n=new _(t,this.mirror);break;case"CANVAS":n=new A(t);break;case"STYLE":n=new I(t);break;default:n=new x(t)}return n.ownerDocument=this,n}createComment(e){const t=new S(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new L(e);return t.ownerDocument=this,t}createTextNode(e){const t=new b(e);return t.ownerDocument=this,t}destroyTree(){this.childNodes=[],this.mirror.reset()}open(){super.open(),this._unserializedId=-1}}const O=l(d);class x extends(u(d)){constructor(){super(...arguments),this.inputData=null,this.scrollData=null}}class v extends(h(x)){}class A extends x{constructor(){super(...arguments),this.canvasMutations=[]}getContext(){return null}}class I extends x{constructor(){super(...arguments),this.rules=[]}}class _ extends x{constructor(e,t){super(e),this.contentDocument=new w,this.contentDocument.mirror=t}}const b=p(d),S=m(d),L=N(d);function F(e,t,n,o){let s;switch(e.nodeType){case T.DOCUMENT_NODE:o&&"IFRAME"===o.nodeName?s=o.contentDocument:(s=t,s.compatMode=e.compatMode);break;case T.DOCUMENT_TYPE_NODE:const n=e;s=t.createDocumentType(n.name,n.publicId,n.systemId);break;case T.ELEMENT_NODE:const i=e,a=(r=i)instanceof HTMLFormElement?"FORM":r.tagName.toUpperCase();s=t.createElement(a);const d=s;for(const{name:e,value:t}of Array.from(i.attributes))d.attributes[e]=t;i.scrollLeft&&(d.scrollLeft=i.scrollLeft),i.scrollTop&&(d.scrollTop=i.scrollTop);break;case T.TEXT_NODE:s=t.createTextNode(e.textContent||"");break;case T.CDATA_SECTION_NODE:s=t.createCDATASection(e.data);break;case T.COMMENT_NODE:s=t.createComment(e.textContent||"");break;case T.DOCUMENT_FRAGMENT_NODE:s=o.attachShadow({mode:"open"});break;default:return null}var r;let i=n.getMeta(e);return t instanceof w&&(i||(i=B(s,t.unserializedId),n.add(e,i)),t.mirror.add(s,Object.assign({},i))),s}function k(e,n=function(){return new t}(),o=new w){return function e(t,s){const r=F(t,o,n,s);null!==r&&("IFRAME"!==(null==s?void 0:s.nodeName)&&t.nodeType!==T.DOCUMENT_FRAGMENT_NODE&&(null==s||s.appendChild(r),r.parentNode=s,r.parentElement=s),"IFRAME"===t.nodeName?e(t.contentDocument,r):t.nodeType!==T.DOCUMENT_NODE&&t.nodeType!==T.ELEMENT_NODE&&t.nodeType!==T.DOCUMENT_FRAGMENT_NODE||(t.nodeType===T.ELEMENT_NODE&&t.shadowRoot&&e(t.shadowRoot,r),t.childNodes.forEach((t=>e(t,r)))))}(e,null),o}function U(){return new P}class P{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){var t;if(!e)return-1;const n=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=n?n:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}replace(e,t){this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function B(t,n){switch(t.RRNodeType){case e.Document:return{id:n,type:t.RRNodeType,childNodes:[]};case e.DocumentType:const o=t;return{id:n,type:t.RRNodeType,name:o.name,publicId:o.publicId,systemId:o.systemId};case e.Element:return{id:n,type:t.RRNodeType,tagName:t.tagName.toLowerCase(),attributes:{},childNodes:[]};case e.Text:case e.Comment:return{id:n,type:t.RRNodeType,textContent:t.textContent||""};case e.CDATA:return{id:n,type:t.RRNodeType,textContent:""}}}export{N as BaseRRCDATASectionImpl,m as BaseRRCommentImpl,c as BaseRRDocumentImpl,l as BaseRRDocumentTypeImpl,u as BaseRRElementImpl,h as BaseRRMediaElementImpl,d as BaseRRNode,p as BaseRRTextImpl,E as ClassList,P as Mirror,T as NodeType,L as RRCDATASection,A as RRCanvasElement,S as RRComment,w as RRDocument,O as RRDocumentType,x as RRElement,_ as RRIFrameElement,v as RRMediaElement,I as RRStyleElement,b as RRText,g as StyleRuleType,k as buildFromDom,F as buildFromNode,U as createMirror,y as createOrGetNode,f as diff,B as getDefaultSN};
//# sourceMappingURL=vdom.min.js.map
