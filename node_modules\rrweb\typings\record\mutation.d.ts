import type { MutationBufferParam } from '../types';
import type { mutationRecord } from '@rrweb/types';
export default class MutationBuffer {
    private frozen;
    private locked;
    private texts;
    private attributes;
    private removes;
    private mapRemoves;
    private movedMap;
    private addedSet;
    private movedSet;
    private droppedSet;
    private mutationCb;
    private blockClass;
    private blockSelector;
    private maskTextClass;
    private maskTextSelector;
    private inlineStylesheet;
    private maskInputOptions;
    private maskTextFn;
    private maskInputFn;
    private keepIframeSrcFn;
    private recordCanvas;
    private inlineImages;
    private slimDOMOptions;
    private dataURLOptions;
    private doc;
    private mirror;
    private iframeManager;
    private stylesheetManager;
    private shadowDomManager;
    private canvasManager;
    init(options: MutationBufferParam): void;
    freeze(): void;
    unfreeze(): void;
    isFrozen(): boolean;
    lock(): void;
    unlock(): void;
    reset(): void;
    processMutations: (mutations: mutationRecord[]) => void;
    emit: () => void;
    private processMutation;
    private genAdds;
}
