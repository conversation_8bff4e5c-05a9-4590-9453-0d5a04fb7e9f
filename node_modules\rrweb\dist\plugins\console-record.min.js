var rrwebConsoleRecord=function(N){"use strict";var y;(function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"})(y||(y={}));const m=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`;let b={map:{},getId(){return console.error(m),-1},getNode(){return console.error(m),null},removeNodeFromMap(){console.error(m)},has(){return console.error(m),!1},reset(){console.error(m)}};typeof window<"u"&&window.Proxy&&window.Reflect&&(b=new Proxy(b,{get(e,r,n){return r==="map"&&console.error(m),Reflect.get(e,r,n)}}));function C(e,r,n){try{if(!(r in e))return()=>{};const t=e[r],o=n(t);return typeof o=="function"&&(o.prototype=o.prototype||{},Object.defineProperties(o,{__rrweb_original__:{enumerable:!1,value:t}})),e[r]=o,()=>{e[r]=t}}catch{return()=>{}}}class p{constructor(r){this.fileName=r.fileName||"",this.functionName=r.functionName||"",this.lineNumber=r.lineNumber,this.columnNumber=r.columnNumber}toString(){const r=this.lineNumber||"",n=this.columnNumber||"";return this.functionName?`${this.functionName} (${this.fileName}:${r}:${n})`:`${this.fileName}:${r}:${n}`}}const S=/(^|@)\S+:\d+/,L=/^\s*at .*(\S+:\d+|\(native\))/m,j=/^(eval@)?(\[native code])?$/,E={parse:function(e){if(!e)return[];if(typeof e.stacktrace<"u"||typeof e["opera#sourceloc"]<"u")return this.parseOpera(e);if(e.stack&&e.stack.match(L))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(e.indexOf(":")===-1)return[e];const r=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));if(!r)throw new Error(`Cannot parse given url: ${e}`);return[r[1],r[2]||void 0,r[3]||void 0]},parseV8OrIE:function(e){return e.stack.split(`
`).filter(function(r){return!!r.match(L)},this).map(function(r){r.indexOf("(eval ")>-1&&(r=r.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));let n=r.replace(/^\s+/,"").replace(/\(eval code/g,"(");const t=n.match(/ (\((.+):(\d+):(\d+)\)$)/);n=t?n.replace(t[0],""):n;const o=n.split(/\s+/).slice(1),c=this.extractLocation(t?t[1]:o.pop()),s=o.join(" ")||void 0,l=["eval","<anonymous>"].indexOf(c[0])>-1?void 0:c[0];return new p({functionName:s,fileName:l,lineNumber:c[1],columnNumber:c[2]})},this)},parseFFOrSafari:function(e){return e.stack.split(`
`).filter(function(r){return!r.match(j)},this).map(function(r){if(r.indexOf(" > eval")>-1&&(r=r.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),r.indexOf("@")===-1&&r.indexOf(":")===-1)return new p({functionName:r});{const n=/((.*".+"[^@]*)?[^@]*)(?:@)/,t=r.match(n),o=t&&t[1]?t[1]:void 0,c=this.extractLocation(r.replace(n,""));return new p({functionName:o,fileName:c[0],lineNumber:c[1],columnNumber:c[2]})}},this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf(`
`)>-1&&e.message.split(`
`).length>e.stacktrace.split(`
`).length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(e){const r=/Line (\d+).*script (?:in )?(\S+)/i,n=e.message.split(`
`),t=[];for(let o=2,c=n.length;o<c;o+=2){const s=r.exec(n[o]);s&&t.push(new p({fileName:s[2],lineNumber:parseFloat(s[1])}))}return t},parseOpera10:function(e){const r=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,n=e.stacktrace.split(`
`),t=[];for(let o=0,c=n.length;o<c;o+=2){const s=r.exec(n[o]);s&&t.push(new p({functionName:s[3]||void 0,fileName:s[2],lineNumber:parseFloat(s[1])}))}return t},parseOpera11:function(e){return e.stack.split(`
`).filter(function(r){return!!r.match(S)&&!r.match(/^Error created at/)},this).map(function(r){const n=r.split("@"),t=this.extractLocation(n.pop()),o=(n.shift()||"").replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;return new p({functionName:o,fileName:t[0],lineNumber:t[1],columnNumber:t[2]})},this)}};function T(e){if(!e||!e.outerHTML)return"";let r="";for(;e.parentElement;){let n=e.localName;if(!n)break;n=n.toLowerCase();const t=e.parentElement,o=[];if(t.children&&t.children.length>0)for(let c=0;c<t.children.length;c++){const s=t.children[c];s.localName&&s.localName.toLowerCase&&s.localName.toLowerCase()===n&&o.push(s)}o.length>1&&(n+=`:eq(${o.indexOf(e)})`),r=n+(r?">"+r:""),e=t}return r}function v(e){return Object.prototype.toString.call(e)==="[object Object]"}function k(e,r){if(r===0)return!0;const n=Object.keys(e);for(const t of n)if(v(e[t])&&k(e[t],r-1))return!0;return!1}function w(e,r){const n={numOfKeysLimit:50,depthOfLimit:4};Object.assign(n,r);const t=[],o=[];return JSON.stringify(e,function(l,i){if(t.length>0){const a=t.indexOf(this);~a?t.splice(a+1):t.push(this),~a?o.splice(a,1/0,l):o.push(l),~t.indexOf(i)&&(t[0]===i?i="[Circular ~]":i="[Circular ~."+o.slice(0,t.indexOf(i)).join(".")+"]")}else t.push(i);if(i===null)return i;if(i===void 0)return"undefined";if(c(i))return s(i);if(i instanceof Event){const a={};for(const u in i){const f=i[u];Array.isArray(f)?a[u]=T(f.length?f[0]:null):a[u]=f}return a}else{if(i instanceof Node)return i instanceof HTMLElement?i?i.outerHTML:"":i.nodeName;if(i instanceof Error)return i.stack?i.stack+`
End of stack for Error object`:i.name+": "+i.message}return i});function c(l){return!!(v(l)&&Object.keys(l).length>n.numOfKeysLimit||typeof l=="function"||v(l)&&k(l,n.depthOfLimit))}function s(l){let i=l.toString();return n.stringLengthLimit&&i.length>n.stringLengthLimit&&(i=`${i.slice(0,n.stringLengthLimit)}...`),i}}const x={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],lengthThreshold:1e3,logger:"console"};function P(e,r,n){const t=n?Object.assign({},x,n):x,o=t.logger;if(!o)return()=>{};let c;typeof o=="string"?c=r[o]:c=o;let s=0;const l=[];if(t.level.includes("error")&&window){const a=u=>{const f=u.message,d=u.error,h=E.parse(d).map(g=>g.toString()),O=[w(f,t.stringifyOptions)];e({level:"error",trace:h,payload:O})};window.addEventListener("error",a),l.push(()=>{window&&window.removeEventListener("error",a)})}for(const a of t.level)l.push(i(c,a));return()=>{l.forEach(a=>a())};function i(a,u){return a[u]?C(a,u,f=>(...d)=>{f.apply(this,d);try{const h=E.parse(new Error).map(g=>g.toString()).splice(1),O=d.map(g=>w(g,t.stringifyOptions));s++,s<t.lengthThreshold?e({level:u,trace:h,payload:O}):s===t.lengthThreshold&&e({level:"warn",trace:[],payload:[w("The number of log records reached the threshold.")]})}catch(h){f("rrweb logger error:",h,...d)}}):()=>{}}}const $="rrweb/console@1",_=e=>({name:$,observer:P,options:e});return N.PLUGIN_NAME=$,N.getRecordConsolePlugin=_,Object.defineProperty(N,"__esModule",{value:!0}),N}({});
//# sourceMappingURL=console-record.min.js.map
