<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyMonitor SDK 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 0;
            min-height: 600px;
        }
        
        .test-panel {
            padding: 30px;
            overflow-y: auto;
            max-height: 80vh;
        }
        
        .log-panel {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            font-family: 'Courier New', monospace;
            overflow-y: auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 8px;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status-info {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4facfe;
        }
        
        .status-info p {
            margin-bottom: 8px;
            color: #666;
        }
        
        .status-info span {
            font-weight: 600;
            color: #333;
        }
        
        .log-area {
            height: 400px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 4px;
            padding: 4px 8px;
            border-radius: 3px;
        }
        
        .log-entry.info {
            background: rgba(0, 255, 0, 0.1);
        }
        
        .log-entry.success {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff88;
        }
        
        .log-entry.warning {
            background: rgba(255, 255, 0, 0.1);
            color: #ffff00;
        }
        
        .log-entry.error {
            background: rgba(255, 0, 0, 0.1);
            color: #ff6666;
        }
        
        .log-controls {
            margin-bottom: 15px;
        }
        
        #imageTest {
            margin-top: 10px;
            min-height: 50px;
            border: 1px dashed #ccc;
            padding: 10px;
            border-radius: 4px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>� TinyMonitor SDK</h1>
            <p>前端监控 SDK 测试平台</p>
        </div>
        
        <div class="main-content">
            <div class="test-panel">
                <!-- SDK 初始化 -->
                <div class="test-section">
                    <h3>⚙️ SDK 初始化</h3>
                    <div class="input-group">
                        <label for="dsn">上报地址 (DSN):</label>
                        <!-- <input type="text" id="dsn" placeholder="https://your-api.com/monitor" value="https://httpbin.org/post"> -->
                        <input type="text" id="dsn" placeholder="https://your-api.com/monitor" value="https://localhost:3893/post">
                    </div>
                    <div class="button-group">
                        <button class="btn" onclick="initSDK()">初始化 SDK</button>
                        <button class="btn" onclick="getSDKStatus()">获取状态</button>
                        <button class="btn" onclick="destroySDK()">销毁 SDK</button>
                    </div>
                    <div class="status-info">
                        <p>SDK 状态: <span id="sdkStatus">未初始化</span></p>
                        <p>队列长度: <span id="queueLength">0</span></p>
                        <p>启用模块: <span id="enabledModules">-</span></p>
                        <p>用户 ID: <span id="userId">-</span></p>
                    </div>
                </div>

                <!-- 录屏测试 -->
                <div class="test-section">
                    <h3>📹 录屏测试</h3>
                    <div class="button-group">
                        <button class="btn" onclick="startRecording()">开始录屏</button>
                        <button class="btn" onclick="stopRecording()">停止录屏</button>
                        <button class="btn" onclick="getRecordingStatus()">获取录屏状态</button>
                        <button class="btn" onclick="startPreview()">预览录屏</button>
                        <button class="btn" onclick="playPreview()">播放</button>
                        <button class="btn" onclick="pausePreview()">暂停</button>
                        <button class="btn" onclick="closePreview()">关闭预览</button>
                        <button class="btn" onclick="debugRecordData()">调试录屏数据</button>
                        <button class="btn" onclick="triggerRecordingError()">触发错误录屏</button>
                        <button class="btn" onclick="simulateUserInteraction()">模拟用户交互</button>
                        <button class="btn" onclick="testRecordingWithError()">录屏+错误测试</button>
                    </div>
                    <div class="status-info">
                        <p>录屏状态: <span id="recordStatus">未初始化</span></p>
                        <p>录制时长: <span id="recordDuration">0ms</span></p>
                        <p>事件数量: <span id="recordEvents">0</span></p>
                    </div>
                </div>
                
                <!-- 错误监控测试 -->
                <div class="test-section">
                    <h3>❌ 错误监控测试</h3>
                    <div class="button-group">
                        <button class="btn" onclick="triggerJSError()">JS 错误</button>
                        <button class="btn" onclick="triggerPromiseError()">Promise 错误</button>
                        <button class="btn" onclick="triggerResourceError()">资源错误</button>
                        <button class="btn" onclick="triggerCustomError()">自定义错误</button>
                        <button class="btn" onclick="triggerNetworkError()">网络错误</button>
                    </div>
                    <div id="imageTest"></div>
                </div>
                
                <!-- 性能监控测试 -->
                <div class="test-section">
                    <h3>⚡ 性能监控测试</h3>
                    <div class="button-group">
                        <button class="btn" onclick="recordCustomMetric()">自定义指标</button>
                        <button class="btn" onclick="simulateSlowTask()">慢任务模拟</button>
                        <button class="btn" onclick="testLargeDataProcess()">大数据处理</button>
                        <button class="btn" onclick="showPerformanceInfo()">性能信息</button>
                    </div>
                </div>
                
                <!-- 事件监控测试 -->
                <div class="test-section">
                    <h3>🎯 事件监控测试</h3>
                    <div class="button-group">
                        <button class="btn" onclick="recordCustomEvent()">自定义事件</button>
                        <button class="btn" onclick="simulateUserJourney()">用户行为链</button>
                        <button class="btn" onclick="testFormInteraction()">表单交互</button>
                    </div>
                </div>
                
                <!-- HTTP 监控测试 -->
                <div class="test-section">
                    <h3>🌐 HTTP 监控测试</h3>
                    <div class="button-group">
                        <button class="btn" onclick="testSuccessAPI()">成功请求</button>
                        <button class="btn" onclick="test404API()">404 请求</button>
                        <button class="btn" onclick="test500API()">500 请求</button>
                        <button class="btn" onclick="testSlowAPI()">慢请求</button>
                        <button class="btn" onclick="testXHRRequest()">XHR 请求</button>
                    </div>
                </div>
                
                <!-- PV 监控测试 -->
                <div class="test-section">
                    <h3>📄 PV 监控测试</h3>
                    <div class="input-group">
                        <label for="newRoute">新路由:</label>
                        <input type="text" id="newRoute" placeholder="/new-page" value="/test-page">
                    </div>
                    <div class="button-group">
                        <button class="btn" onclick="simulateRouteChange()">路由变化</button>
                        <button class="btn" onclick="recordCustomPageView()">自定义 PV</button>
                        <button class="btn" onclick="changePageTitle()">修改标题</button>
                    </div>
                </div>
                
                <!-- 工具函数 -->
                <div class="test-section">
                    <h3>🛠️ 工具函数</h3>
                    <div class="button-group">
                        <button class="btn" onclick="flushData()">立即发送</button>
                        <button class="btn" onclick="clearLogs()">清空日志</button>
                        <button class="btn" onclick="exportLogs()">导出日志</button>
                    </div>
                </div>
            </div>
            
            <div class="log-panel">
                <div class="log-controls">
                    <button class="btn" onclick="clearLogs()">清空日志</button>
                    <button class="btn" onclick="exportLogs()">导出日志</button>
                </div>
                <div class="log-area" id="logArea"></div>
            </div>
        </div>
    </div>

    <!-- SDK 引入 -->
    <script src="../dist/index.umd.js"></script>
    
    <script>
        let monitor = null;
        let logs = [];
        
        // 日志记录函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push({ message: logEntry, type });
            
            const logArea = document.getElementById('logArea');
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry ${type}`;
            logDiv.textContent = logEntry;
            logArea.appendChild(logDiv);
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(`[TinyMonitor Test] ${message}`);
        }
        
        // 初始化 SDK
        function initSDK() {
            const dsn = document.getElementById('dsn').value;
            
            if (!dsn) {
                addLog('请先设置 DSN 地址', 'error');
                return;
            }
            
            try {
                monitor = TinyMonitor.init({
                    dsn: dsn,
                    appName: 'TinyMonitor-Test',
                    appCode: 'test-app',
                    appVersion: '1.0.0',
                    userUuid: 'test-user-' + Date.now(),
                    debug: true,
                    
                    // 开启所有监控功能
                    error: {
                        enable: true,
                        server: true
                    },
                    performance: {
                        enable: true,
                        firstResource: true,
                        server: true
                    },
                    event: { 
                        enable: true,
                        targetElements: ['.btn', '#header-nav', '[data-monitor]']
                    },
                    http: { enable: true },
                    pv: { enable: true },
                    
                    // 录屏配置
                    record: {
                        enable: true,
                        enablePreview: true, // 启用预览功能
                        maxDuration: 30000, // 30秒
                        compress: false, // 预览时不压缩，方便调试
                        maskAllInputs: true,
                        blockClass: 'rr-block'
                    },
                    
                    // 配置
                    maxEvents: 10,
                    flushInterval: 5000,
                    sampleRate: 1,
                    
                    // 扩展信息
                    ext: {
                        testMode: true,
                        browser: navigator.userAgent,
                        timestamp: Date.now()
                    },
                    
                    // 钩子函数
                    beforeSend: (data) => {
                        addLog(`准备发送 ${data.eventInfo.length} 个事件`, 'info');
                        return data;
                    },
                    afterSend: (data) => {
                        addLog(`成功发送 ${data.eventInfo.length} 个事件`, 'success');
                    }
                });
                
                addLog('SDK 初始化成功！', 'success');
                updateStatus();
                
            } catch (error) {
                addLog(`SDK 初始化失败: ${error.message}`, 'error');
            }
        }

        // 录屏测试函数
        function startRecording() {
            if (!monitor) {
                addLog('SDK 未初始化', 'error');
                return;
            }
            monitor.startRecord();
            addLog('手动开始录屏', 'info');
            updateRecordStatus();
        }

        function stopRecording() {
            if (!monitor) {
                addLog('SDK 未初始化', 'error');
                return;
            }
            monitor.stopRecord();
            addLog('手动停止录屏', 'info');
            
            // 延迟更新状态，确保录屏数据已处理
            setTimeout(() => {
                updateRecordStatus();
                const status = monitor.getRecordStatus();
                if (status.eventCount > 0) {
                    addLog(`录屏完成，共记录 ${status.eventCount} 个事件，可以预览了`, 'success');
                }
            }, 500);
        }

        function getRecordingStatus() {
            if (!monitor) {
                addLog('SDK 未初始化', 'error');
                return;
            }
            const status = monitor.getRecordStatus();
            addLog(`录屏状态: ${status.recording ? '录制中' : '未录制'}, 时长: ${status.duration}ms, 事件数: ${status.eventCount}`, 'info');
            updateRecordStatus();
        }

        function triggerRecordingError() {
            addLog('触发错误以自动开始录屏', 'warning');
            setTimeout(() => {
                throw new Error('这是一个会触发录屏的测试错误 - ' + new Date().toISOString());
            }, 100);
        }

        function simulateUserInteraction() {
            addLog('模拟用户交互（会被录屏记录）', 'info');
            
            // 模拟点击
            const testBtn = document.createElement('button');
            testBtn.textContent = '临时测试按钮';
            testBtn.className = 'btn';
            testBtn.style.margin = '10px';
            testBtn.onclick = () => {
                addLog('点击了临时按钮', 'info');
                document.body.removeChild(testBtn);
            };
            document.body.appendChild(testBtn);
            
            // 自动点击
            setTimeout(() => {
                testBtn.click();
            }, 1000);
        }

        // 新增预览函数
        function startPreview() {
            if (!monitor) {
                addLog('SDK 未初始化', 'error');
                return;
            }
            
            try {
                // 检查录屏数据
                const status = monitor.getRecordStatus();
                if (status.eventCount === 0) {
                    addLog('没有录屏数据，请先录制一些操作', 'warning');
                    return;
                }
                
                addLog(`准备预览 ${status.eventCount} 个录屏事件`, 'info');
                
                // 访问录屏监控器的预览方法
                if (monitor.recordMonitor && typeof monitor.recordMonitor.startPreview === 'function') {
                    monitor.recordMonitor.startPreview();
                    addLog('预览窗口已创建', 'success');
                } else {
                    addLog('预览功能不可用，recordMonitor 未找到', 'error');
                    console.log('monitor 对象:', monitor);
                    console.log('recordMonitor:', monitor.recordMonitor);
                }
            } catch (error) {
                addLog('预览失败: ' + error.message, 'error');
                console.error('预览错误详情:', error);
            }
        }

        function testRecordingWithError() {
            addLog('测试录屏 + 错误组合', 'warning');
            
            // 先开始录屏
            if (monitor) {
                monitor.startRecord();
                addLog('开始录屏', 'info');
                
                // 2秒后触发错误
                setTimeout(() => {
                    throw new Error('录屏过程中的测试错误');
                }, 2000);
                
                // 5秒后停止录屏并预览
                setTimeout(() => {
                    monitor.stopRecord();
                    addLog('停止录屏', 'info');
                    // 自动预览
                    setTimeout(() => {
                        startPreview();
                    }, 1000);
                }, 5000);
            }
        }

        function updateRecordStatus() {
            if (!monitor) {
                document.getElementById('recordStatus').textContent = '未初始化';
                document.getElementById('recordDuration').textContent = '0ms';
                document.getElementById('recordEvents').textContent = '0';
                return;
            }
            
            try {
                const status = monitor.getRecordStatus();
                document.getElementById('recordStatus').textContent = status.recording ? '录制中' : '未录制';
                document.getElementById('recordDuration').textContent = status.duration + 'ms';
                document.getElementById('recordEvents').textContent = status.eventCount;
            } catch (error) {
                addLog(`获取录屏状态失败: ${error.message}`, 'error');
            }
        }
        
        // 更新状态显示
        function updateStatus() {
            if (!monitor) {
                document.getElementById('sdkStatus').textContent = '未初始化';
                document.getElementById('queueLength').textContent = '0';
                document.getElementById('enabledModules').textContent = '-';
                document.getElementById('userId').textContent = '-';
                return;
            }
            
            try {
                document.getElementById('sdkStatus').textContent = '已初始化';
                
                const queueStatus = monitor.getQueueStatus();
                document.getElementById('queueLength').textContent = queueStatus ? queueStatus.length : '0';
                
                const enabledModules = monitor.getEnabledMonitors();
                document.getElementById('enabledModules').textContent = enabledModules.join(', ');
                
                const baseInfo = monitor.getBaseInfo();
                document.getElementById('userId').textContent = baseInfo.userUuid.slice(-8);
                
                // 更新录屏状态
                updateRecordStatus();
                
            } catch (error) {
                addLog(`获取状态失败: ${error.message}`, 'error');
            }
        }
        
        // 错误测试函数
        function triggerJSError() {
            addLog('触发 JS 错误', 'warning');
            setTimeout(() => {
                throw new Error('这是一个测试 JS 错误 - ' + new Date().toISOString());
            }, 100);
        }
        
        function triggerPromiseError() {
            addLog('触发 Promise 错误', 'warning');
            Promise.reject(new Error('这是一个测试 Promise 错误 - ' + new Date().toISOString()));
        }
        
        function triggerResourceError() {
            addLog('触发资源加载错误', 'warning');
            const img = document.createElement('img');
            img.src = 'https://nonexistent-domain-' + Date.now() + '.com/image.jpg';
            document.getElementById('imageTest').appendChild(img);
        }
        
        function triggerCustomError() {
            addLog('触发自定义错误', 'warning');
            if (monitor) {
                TinyMonitor.reportError(new Error('自定义测试错误'), {
                    context: 'manual-test',
                    timestamp: Date.now(),
                    userAction: 'click-custom-error-button'
                });
            }
        }
        
        function triggerNetworkError() {
            addLog('触发网络错误', 'warning');
            fetch('https://nonexistent-api-' + Date.now() + '.com/data')
                .catch(error => {
                    addLog('网络请求失败（已被监控）', 'error');
                });
        }
        
        // 性能测试函数
        function recordCustomMetric() {
            if (!monitor) return;
            const value = Math.random() * 1000;
            TinyMonitor.recordMetric('custom-load-time', value, {
                component: 'test-component',
                timestamp: Date.now()
            });
            addLog(`记录自定义指标: ${value.toFixed(2)}ms`, 'info');
        }
        
        function simulateSlowTask() {
            addLog('开始执行慢任务', 'warning');
            const start = performance.now();
            
            // 模拟耗时任务
            let sum = 0;
            for (let i = 0; i < 10000000; i++) {
                sum += Math.random();
            }
            
            const duration = performance.now() - start;
            if (monitor) {
                TinyMonitor.recordMetric('slow-task-duration', duration, {
                    taskType: 'calculation',
                    result: sum
                });
            }
            addLog(`慢任务完成，耗时: ${duration.toFixed(2)}ms`, 'success');
        }
        
        function testLargeDataProcess() {
            addLog('测试大数据处理', 'info');
            const start = performance.now();
            
            // 创建大数组并处理
            const largeArray = Array.from({length: 100000}, (_, i) => i);
            const processed = largeArray.map(x => x * 2).filter(x => x % 3 === 0);
            
            const duration = performance.now() - start;
            if (monitor) {
                TinyMonitor.recordMetric('large-data-process', duration, {
                    dataSize: largeArray.length,
                    resultSize: processed.length
                });
            }
            addLog(`大数据处理完成: ${duration.toFixed(2)}ms`, 'success');
        }
        
        function showPerformanceInfo() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                addLog(`页面加载时间: ${(perfData.loadEventEnd - perfData.navigationStart).toFixed(2)}ms`, 'info');
                addLog(`DOM 解析时间: ${(perfData.domContentLoadedEventEnd - perfData.navigationStart).toFixed(2)}ms`, 'info');
            }
        }
        
        // 事件测试函数
        function recordCustomEvent() {
            if (!monitor) return;
            TinyMonitor.recordEvent('custom-button-click', {
                buttonName: 'test-custom-event',
                timestamp: Date.now(),
                userAgent: navigator.userAgent
            });
            addLog('记录自定义事件', 'info');
        }
        
        function simulateUserJourney() {
            if (!monitor) return;
            const actions = ['page-enter', 'scroll-down', 'click-cta', 'form-focus'];
            actions.forEach((action, index) => {
                setTimeout(() => {
                    TinyMonitor.recordEvent(action, {
                        step: index + 1,
                        journey: 'test-user-flow'
                    });
                    addLog(`用户行为: ${action}`, 'info');
                }, index * 500);
            });
        }
        
        function testFormInteraction() {
            if (!monitor) return;
            TinyMonitor.recordEvent('form-interaction', {
                formType: 'test-form',
                action: 'input-focus',
                timestamp: Date.now()
            });
            addLog('记录表单交互事件', 'info');
        }
        
        // HTTP 测试函数
        function testSuccessAPI() {
            addLog('发送成功请求', 'info');
            fetch('https://jsonplaceholder.typicode.com/posts/1')
                .then(response => response.json())
                .then(data => {
                    addLog('API 请求成功', 'success');
                })
                .catch(error => {
                    addLog('API 请求失败', 'error');
                });
        }
        
        function test404API() {
            addLog('发送 404 请求', 'warning');
            fetch('https://jsonplaceholder.typicode.com/posts/99999')
                .then(response => {
                    if (!response.ok) {
                        addLog(`API 返回 ${response.status}`, 'warning');
                    }
                });
        }
        
        function test500API() {
            addLog('发送 500 请求', 'warning');
            fetch('https://httpstat.us/500')
                .catch(error => {
                    addLog('500 错误请求完成', 'warning');
                });
        }
        
        function testSlowAPI() {
            addLog('发送慢请求', 'info');
            fetch('https://httpstat.us/200?sleep=2000')
                .then(response => {
                    addLog('慢请求完成', 'success');
                });
        }
        
        function testXHRRequest() {
            addLog('发送 XHR 请求', 'info');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://jsonplaceholder.typicode.com/users');
            xhr.onload = function() {
                addLog('XHR 请求完成', 'success');
            };
            xhr.send();
        }
        
        // PV 测试函数
        function simulateRouteChange() {
            const newRoute = document.getElementById('newRoute').value;
            history.pushState({}, '', newRoute);
            addLog(`模拟路由变化: ${newRoute}`, 'info');
        }
        
        function recordCustomPageView() {
            if (!monitor) return;
            TinyMonitor.recordPageView({
                page: 'custom-test-page',
                timestamp: Date.now(),
                referrer: 'test-suite'
            });
            addLog('记录自定义页面访问', 'info');
        }
        
        function changePageTitle() {
            const newTitle = `TinyMonitor Test - ${new Date().toLocaleTimeString()}`;
            document.title = newTitle;
            addLog(`修改页面标题: ${newTitle}`, 'info');
        }
        
        // 工具函数
        function getSDKStatus() {
            updateStatus();
            addLog('状态已更新', 'info');
        }
        
        function flushData() {
            if (!monitor) {
                addLog('SDK 未初始化', 'error');
                return;
            }
            TinyMonitor.flush();
            addLog('手动发送数据', 'info');
            updateStatus();
        }
        
        function destroySDK() {
            if (!monitor) {
                addLog('SDK 未初始化', 'error');
                return;
            }
            TinyMonitor.destroy();
            monitor = null;
            addLog('SDK 已销毁', 'warning');
            updateStatus();
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('logArea').innerHTML = '';
            addLog('日志已清空', 'info');
        }
        
        function exportLogs() {
            const logText = logs.map(log => log.message).join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tiny-monitor-logs-${Date.now()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            addLog('日志已导出', 'success');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('测试页面加载完成', 'success');
            addLog('请先点击"初始化 SDK"开始测试', 'info');
            
            // 定期更新状态
            setInterval(updateStatus, 2000);
        });
        
        // 监听页面点击（测试事件监控）
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                addLog(`点击按钮: ${e.target.textContent}`, 'info');
            }
        });
    </script>
</body>
</html> 
