{"name": "rrdom", "version": "0.1.7", "homepage": "https://github.com/rrweb-io/rrweb/tree/main/packages/rrdom#readme", "license": "MIT", "main": "lib/rrdom.js", "module": "es/rrdom.js", "typings": "es", "unpkg": "dist/rrdom.js", "files": ["dist", "lib", "es", "typings"], "repository": {"type": "git", "url": "git+https://github.com/rrweb-io/rrweb.git"}, "scripts": {"dev": "rollup -c -w", "bundle": "rollup --config", "bundle:es-only": "cross-env ES_ONLY=true rollup --config", "check-types": "tsc -noEmit", "test": "jest", "prepublish": "npm run bundle", "lint": "yarn eslint src/**/*.ts"}, "bugs": {"url": "https://github.com/rrweb-io/rrweb/issues"}, "devDependencies": {"@rollup/plugin-commonjs": "^20.0.0", "@rrweb/types": "^2.0.0-alpha.4", "@types/jest": "^27.4.1", "@types/puppeteer": "^5.4.4", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "eslint": "^8.15.0", "jest": "^27.5.1", "puppeteer": "^9.1.1", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-web-worker-loader": "^1.6.1", "ts-jest": "^27.1.3", "typescript": "^4.7.3"}, "dependencies": {"rrweb-snapshot": "^2.0.0-alpha.4"}, "gitHead": "7bb68625e3ff39258a8cc8614f0691f265cb5bee"}