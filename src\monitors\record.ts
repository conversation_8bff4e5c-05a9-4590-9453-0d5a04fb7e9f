import type { EventData, MonitorOptions } from '../types'
import { SEND_ID, EVENT_TYPES } from '../types'
import { record, Replayer } from 'rrweb'
import type { eventWithTime } from 'rrweb/typings/types'
import { gzipSync, gunzipSync } from 'fflate'
import { Base64 } from 'js-base64'

/**
 * 录屏监控器配置
 */
export interface RecordConfig {
  enable: boolean
  maxDuration?: number // 最大录制时长(ms)，默认30秒
  sampleRate?: number // 采样率，默认1
  compress?: boolean // 是否压缩，默认true
  maskAllInputs?: boolean // 是否遮罩所有输入框
  blockClass?: string // 使用 blockClass 替代 blockSelector
  enablePreview?: boolean // 是否启用预览功能
}

/**
 * 录屏监控器
 */
export class RecordMonitor {
  private config: RecordConfig
  private stopRecord: (() => void) | null = null
  private events: any[] = []
  private startTime = 0
  private recordTimer: NodeJS.Timeout | null = null
  private replayer: Replayer | null = null
  private previewContainer: HTMLElement | null = null

  constructor(
    private options: Required<MonitorOptions>,
    private addEvent: (event: EventData) => void
  ) {
    this.config = {
      enable: false,
      maxDuration: 30000, // 30秒
      sampleRate: 1,
      compress: true,
      maskAllInputs: true,
      enablePreview: false,
      ...options.record
    } as RecordConfig
  }

  /**
   * 初始化录屏监控
   */
  init(): void {
    if (!this.config.enable) return
    
    // 监听错误事件，自动开始录屏
    this.initErrorTrigger()
  }

  /**
   * 监听错误事件触发录屏
   */
  private initErrorTrigger(): void {
    // 监听 JS 错误
    window.addEventListener('error', () => {
      this.startRecording('js_error')
    })

    // 监听 Promise 错误
    window.addEventListener('unhandledrejection', () => {
      this.startRecording('promise_error')
    })

    // 监听 HTTP 错误（需要配合 HttpMonitor）
    const originalAddEvent = this.addEvent
    this.addEvent = (event: EventData) => {
      if (event.type === EVENT_TYPES.HTTP && event.data.status >= 400) {
        this.startRecording('http_error')
      }
      originalAddEvent(event)
    }
  }

  /**
   * 修复 rrweb 的 node.matches 问题
   */
  private patchNodeMatches(): void {
    // 为所有节点类型添加 matches 方法
    if (!Node.prototype.matches) {
      Node.prototype.matches = function(selector: string) {
        if (this.nodeType !== Node.ELEMENT_NODE) {
          return false
        }
        const element = this as Element
        return element.matches ? element.matches(selector) : false
      }
    }

    // 确保非 Element 节点调用 matches 时返回 false
    const originalMatches = Element.prototype.matches
    Node.prototype.matches = function(selector: string) {
      if (this.nodeType !== Node.ELEMENT_NODE) {
        return false
      }
      return originalMatches.call(this, selector)
    }
  }

  /**
   * 开始录屏
   */
  startRecording(trigger: string = 'manual'): void {
    if (this.stopRecord) {
      console.warn('[RecordMonitor] 录屏已在进行中')
      return
    }

    // 修复 rrweb 的 matches 问题
    this.patchNodeMatches()

    this.events = []
    this.startTime = Date.now()

    const recordOptions = {
      emit: (event) => {
        this.events.push(event)
      },
      maskAllInputs: this.config.maskAllInputs,
      blockClass: this.config.blockClass || 'rr-block',
      sampling: {
        mousemove: this.config.sampleRate! < 1,
        mouseInteraction: true,
        scroll: 150, // 节流滚动事件
        input: 'last' // 只记录最后的输入值
      }
    }

    try {
      this.stopRecord = record(recordOptions)
      
      // 设置最大录制时长
      this.recordTimer = setTimeout(() => {
        this.stopRecording(trigger)
      }, this.config.maxDuration)

      console.log(`[RecordMonitor] 开始录屏，触发原因: ${trigger}`)
    } catch (error) {
      console.error('[RecordMonitor] 录屏启动失败:', error)
    }
  }

  /**
   * 停止录屏
   */
  stopRecording(trigger: string = 'manual'): void {
    if (!this.stopRecord) {
      console.warn('[RecordMonitor] 没有正在进行的录屏')
      return
    }

    // 停止录制
    this.stopRecord()
    this.stopRecord = null

    // 清除定时器
    if (this.recordTimer) {
      clearTimeout(this.recordTimer)
      this.recordTimer = null
    }

    const duration = Date.now() - this.startTime
    console.log(`[RecordMonitor] 录屏结束，时长: ${duration}ms，事件数: ${this.events.length}`)

    // 如果启用预览，自动显示预览
    if (this.config.enablePreview) {
      setTimeout(() => this.startPreview(), 500)
    }

    // 处理录屏数据
    this.processRecordData(trigger, duration)
  }

  /**
   * 创建预览容器
   */
  private createPreviewContainer(): HTMLElement {
    if (this.previewContainer) {
      return this.previewContainer
    }

    const container = document.createElement('div')
    container.id = 'rrweb-preview'
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 400px;
      height: 300px;
      background: #000;
      border: 2px solid #333;
      border-radius: 8px;
      z-index: 10000;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `

    // 添加控制栏
    const controls = document.createElement('div')
    controls.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 40px;
      background: #333;
      display: flex;
      align-items: center;
      padding: 0 10px;
      gap: 10px;
    `

    const playBtn = document.createElement('button')
    playBtn.textContent = '播放'
    playBtn.style.cssText = 'padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;'
    
    const pauseBtn = document.createElement('button')
    pauseBtn.textContent = '暂停'
    pauseBtn.style.cssText = 'padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;'
    
    const closeBtn = document.createElement('button')
    closeBtn.textContent = '关闭'
    closeBtn.style.cssText = 'padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: auto;'

    controls.appendChild(playBtn)
    controls.appendChild(pauseBtn)
    controls.appendChild(closeBtn)
    container.appendChild(controls)

    // 添加播放区域
    const playerArea = document.createElement('div')
    playerArea.id = 'rrweb-player'
    playerArea.style.cssText = `
      position: absolute;
      top: 40px;
      left: 0;
      right: 0;
      bottom: 0;
      background: #f5f5f5;
    `
    container.appendChild(playerArea)

    // 绑定事件
    playBtn.onclick = () => this.playPreview()
    pauseBtn.onclick = () => this.pausePreview()
    closeBtn.onclick = () => this.closePreview()

    document.body.appendChild(container)
    this.previewContainer = container
    return container
  }

  /**
   * 开始预览录屏
   */
  startPreview(): void {
    if (!this.config.enablePreview || this.events.length === 0) {
      console.warn('[RecordMonitor] 预览功能未启用或无录屏数据')
      return
    }

    const container = this.createPreviewContainer()
    const playerArea = container.querySelector('#rrweb-player') as HTMLElement

    if (this.replayer) {
      this.replayer.destroy()
    }

    try {
      this.replayer = new Replayer(this.events as eventWithTime[], {
        root: playerArea,
        width: 380,
        height: 240,
        autoPlay: false,
        speed: 1
      })

      console.log('[RecordMonitor] 预览器已创建')
    } catch (error) {
      console.error('[RecordMonitor] 预览器创建失败:', error)
    }
  }

  /**
   * 播放预览
   */
  private playPreview(): void {
    if (this.replayer) {
      this.replayer.play()
    }
  }

  /**
   * 暂停预览
   */
  private pausePreview(): void {
    if (this.replayer) {
      this.replayer.pause()
    }
  }

  /**
   * 关闭预览
   */
  private closePreview(): void {
    if (this.replayer) {
      this.replayer.destroy()
      this.replayer = null
    }
    if (this.previewContainer) {
      document.body.removeChild(this.previewContainer)
      this.previewContainer = null
    }
  }

  /**
   * 处理录屏数据
   */
  private processRecordData(trigger: string, duration: number): void {
    if (this.events.length === 0) return

    const recordData = {
      trigger,
      duration,
      startTime: this.startTime,
      endTime: Date.now(),
      events: this.events,
      url: location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }

    // 压缩数据
    const compressedData = this.config.compress 
      ? this.compressData(recordData)
      : recordData

    // 发送录屏数据
    this.addEvent({
      eventId: SEND_ID.RECORD,
      type: EVENT_TYPES.RECORD,
      data: {
        compressed: this.config.compress,
        size: this.config.compress ? compressedData.length : JSON.stringify(recordData).length,
        originalSize: JSON.stringify(recordData).length,
        ...compressedData
      },
      timestamp: Date.now(),
      triggerTime: this.startTime
    })

    // 清空事件数组
    this.events = []
  }

  /**
   * 压缩录屏数据
   */
  private compressData(data: any): string {
    try {
      const jsonStr = JSON.stringify(data)
      const encoder = new TextEncoder()
      const uint8Array = encoder.encode(jsonStr)
      const compressed = gzipSync(uint8Array)
      return Base64.fromUint8Array(compressed)
    } catch (error) {
      console.error('[RecordMonitor] 数据压缩失败:', error)
      return JSON.stringify(data)
    }
  }

  /**
   * 解压录屏数据（用于调试）
   */
  static decompressData(compressedData: string): any {
    try {
      const uint8Array = Base64.toUint8Array(compressedData)
      const decompressed = gunzipSync(uint8Array)
      const decoder = new TextDecoder()
      const jsonStr = decoder.decode(decompressed)
      return JSON.parse(jsonStr)
    } catch (error) {
      console.error('[RecordMonitor] 数据解压失败:', error)
      return null
    }
  }

  /**
   * 手动开始录屏
   */
  manualStart(): void {
    this.startRecording('manual')
  }

  /**
   * 手动停止录屏
   */
  manualStop(): void {
    this.stopRecording('manual')
  }

  /**
   * 获取录屏状态
   */
  getStatus(): { recording: boolean; duration: number; eventCount: number } {
    return {
      recording: !!this.stopRecord,
      duration: this.stopRecord ? Date.now() - this.startTime : 0,
      eventCount: this.events.length
    }
  }
}



