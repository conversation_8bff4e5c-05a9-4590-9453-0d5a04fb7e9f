var rrdom=function(e){"use strict";var t;!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(t||(t={}));var o=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){var t;if(!e)return-1;var o=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=o?o:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,o=this.getId(e);this.idNodeMap.delete(o),e.childNodes&&e.childNodes.forEach((function(e){return t.removeNodeFromMap(e)}))},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var o=t.id;this.idNodeMap.set(o,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){var o=this.getNode(e);if(o){var n=this.nodeMetaMap.get(o);n&&this.nodeMetaMap.set(t,n)}this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function n(e){const t=[];for(const o in e){const n=e[o];if("string"!=typeof n)continue;const r=d(o);t.push(`${r}: ${n};`)}return t.join(" ")}const r=/-([a-z])/g,s=/^--[a-zA-Z0-9-]+$/,i=e=>s.test(e)?e:e.replace(r,((e,t)=>t?t.toUpperCase():"")),a=/\B([A-Z])/g,d=e=>e.replace(a,"-$1").toLowerCase();class c{constructor(...t){this.childNodes=[],this.parentElement=null,this.parentNode=null,this.ELEMENT_NODE=e.NodeType.ELEMENT_NODE,this.TEXT_NODE=e.NodeType.TEXT_NODE}get firstChild(){return this.childNodes[0]||null}get lastChild(){return this.childNodes[this.childNodes.length-1]||null}get nextSibling(){const e=this.parentNode;if(!e)return null;const t=e.childNodes,o=t.indexOf(this);return t[o+1]||null}contains(e){if(e===this)return!0;for(const t of this.childNodes)if(t.contains(e))return!0;return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}function l(o){return class n extends o{constructor(){super(...arguments),this.nodeType=e.NodeType.DOCUMENT_NODE,this.nodeName="#document",this.compatMode="CSS1Compat",this.RRNodeType=t.Document,this.textContent=null}get documentElement(){return this.childNodes.find((e=>e.RRNodeType===t.Element&&"HTML"===e.tagName))||null}get body(){var e;return(null===(e=this.documentElement)||void 0===e?void 0:e.childNodes.find((e=>e.RRNodeType===t.Element&&"BODY"===e.tagName)))||null}get head(){var e;return(null===(e=this.documentElement)||void 0===e?void 0:e.childNodes.find((e=>e.RRNodeType===t.Element&&"HEAD"===e.tagName)))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(e){const o=e.RRNodeType;if((o===t.Element||o===t.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===o)))throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${o===t.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);return e.parentElement=null,e.parentNode=this,this.childNodes.push(e),e}insertBefore(e,o){const n=e.RRNodeType;if((n===t.Element||n===t.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===n)))throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${n===t.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);if(null===o)return this.appendChild(e);const r=this.childNodes.indexOf(o);if(-1==r)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(r,0,e),e.parentElement=null,e.parentNode=this,e}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}open(){this.childNodes=[]}close(){}write(e){let t;if('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'===e?t="-//W3C//DTD XHTML 1.0 Transitional//EN":'<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">'===e&&(t="-//W3C//DTD HTML 4.0 Transitional//EN"),t){const e=this.createDocumentType("html",t,"");this.open(),this.appendChild(e)}}createDocument(e,t,o){return new n}createDocumentType(e,t,o){const n=new(u(c))(e,t,o);return n.ownerDocument=this,n}createElement(e){const t=new(h(c))(e);return t.ownerDocument=this,t}createElementNS(e,t){return this.createElement(t)}createTextNode(e){const t=new(m(c))(e);return t.ownerDocument=this,t}createComment(e){const t=new(N(c))(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new(f(c))(e);return t.ownerDocument=this,t}toString(){return"RRDocument"}}}function u(o){return class extends o{constructor(o,n,r){super(),this.nodeType=e.NodeType.DOCUMENT_TYPE_NODE,this.RRNodeType=t.DocumentType,this.textContent=null,this.name=o,this.publicId=n,this.systemId=r,this.nodeName=o}toString(){return"RRDocumentType"}}}function h(o){return class extends o{constructor(o){super(),this.nodeType=e.NodeType.ELEMENT_NODE,this.RRNodeType=t.Element,this.attributes={},this.shadowRoot=null,this.tagName=o.toUpperCase(),this.nodeName=o.toUpperCase()}get textContent(){let e="";return this.childNodes.forEach((t=>e+=t.textContent)),e}set textContent(e){this.childNodes=[this.ownerDocument.createTextNode(e)]}get classList(){return new E(this.attributes.class,(e=>{this.attributes.class=e}))}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const e=this.attributes.style?function(e){const t={},o=/:(.+)/;return e.replace(/\/\*.*?\*\//g,"").split(/;(?![^(]*\))/g).forEach((function(e){if(e){const n=e.split(o);n.length>1&&(t[i(n[0].trim())]=n[1].trim())}})),t}(this.attributes.style):{},t=/\B([A-Z])/g;return e.setProperty=(o,r,s)=>{if(t.test(o))return;const a=i(o);r?e[a]=r:delete e[a],"important"===s&&(e[a]+=" !important"),this.attributes.style=n(e)},e.removeProperty=o=>{if(t.test(o))return"";const r=i(o),s=e[r]||"";return delete e[r],this.attributes.style=n(e),s},e}getAttribute(e){return this.attributes[e]||null}setAttribute(e,t){this.attributes[e]=t}setAttributeNS(e,t,o){this.setAttribute(t,o)}removeAttribute(e){delete this.attributes[e]}appendChild(e){return this.childNodes.push(e),e.parentNode=this,e.parentElement=this,e}insertBefore(e,t){if(null===t)return this.appendChild(e);const o=this.childNodes.indexOf(t);if(-1==o)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(o,0,e),e.parentElement=this,e.parentNode=this,e}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}attachShadow(e){const t=this.ownerDocument.createElement("SHADOWROOT");return this.shadowRoot=t,t}dispatchEvent(e){return!0}toString(){let e="";for(const t in this.attributes)e+=`${t}="${this.attributes[t]}" `;return`${this.tagName} ${e}`}}}function p(e){return class extends e{attachShadow(e){throw new Error("RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow")}play(){this.paused=!1}pause(){this.paused=!0}}}function m(o){return class extends o{constructor(o){super(),this.nodeType=e.NodeType.TEXT_NODE,this.nodeName="#text",this.RRNodeType=t.Text,this.data=o}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRText text=${JSON.stringify(this.data)}`}}}function N(o){return class extends o{constructor(o){super(),this.nodeType=e.NodeType.COMMENT_NODE,this.nodeName="#comment",this.RRNodeType=t.Comment,this.data=o}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}}function f(o){return class extends o{constructor(o){super(),this.nodeName="#cdata-section",this.nodeType=e.NodeType.CDATA_SECTION_NODE,this.RRNodeType=t.CDATA,this.data=o}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}}class E{constructor(e,t){if(this.classes=[],this.add=(...e)=>{for(const t of e){const e=String(t);this.classes.indexOf(e)>=0||this.classes.push(e)}this.onChange&&this.onChange(this.classes.join(" "))},this.remove=(...e)=>{this.classes=this.classes.filter((t=>-1===e.indexOf(t))),this.onChange&&this.onChange(this.classes.join(" "))},e){const t=e.trim().split(/\s+/);this.classes.push(...t)}this.onChange=t}}e.NodeType=void 0,function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(e.NodeType||(e.NodeType={}));const T={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"},R={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function D(e,o,n,r){const s=e.childNodes,i=o.childNodes;r=r||o.mirror||o.ownerDocument.mirror,(s.length>0||i.length>0)&&g(Array.from(s),i,e,n,r);let a=null,d=null;switch(o.RRNodeType){case t.Document:d=o.scrollData;break;case t.Element:{const t=e,s=o;switch(function(e,t,o){const n=e.attributes,r=t.attributes;for(const n in r){const s=r[n],i=o.getMeta(t);if(i&&"isSVG"in i&&i.isSVG&&T[n])e.setAttributeNS(T[n],n,s);else if("CANVAS"===t.tagName&&"rr_dataURL"===n){const t=document.createElement("img");t.src=s,t.onload=()=>{const o=e.getContext("2d");o&&o.drawImage(t,0,0,t.width,t.height)}}else e.setAttribute(n,s)}for(const{name:t}of Array.from(n))t in r||e.removeAttribute(t);t.scrollLeft&&(e.scrollLeft=t.scrollLeft),t.scrollTop&&(e.scrollTop=t.scrollTop)}(t,s,r),d=s.scrollData,a=s.inputData,s.tagName){case"AUDIO":case"VIDEO":{const t=e,o=s;void 0!==o.paused&&(o.paused?t.pause():t.play()),void 0!==o.muted&&(t.muted=o.muted),void 0!==o.volume&&(t.volume=o.volume),void 0!==o.currentTime&&(t.currentTime=o.currentTime),void 0!==o.playbackRate&&(t.playbackRate=o.playbackRate);break}case"CANVAS":{const r=o;if(null!==r.rr_dataURL){const e=document.createElement("img");e.onload=()=>{const o=t.getContext("2d");o&&o.drawImage(e,0,0,e.width,e.height)},e.src=r.rr_dataURL}r.canvasMutations.forEach((t=>n.applyCanvas(t.event,t.mutation,e)))}break;case"STYLE":{const e=t.sheet;e&&o.rules.forEach((t=>n.applyStyleSheetMutation(t,e)))}}if(s.shadowRoot){t.shadowRoot||t.attachShadow({mode:"open"});const e=t.shadowRoot.childNodes,o=s.shadowRoot.childNodes;(e.length>0||o.length>0)&&g(Array.from(e),o,t.shadowRoot,n,r)}break}case t.Text:case t.Comment:case t.CDATA:e.textContent!==o.data&&(e.textContent=o.data)}if(d&&n.applyScroll(d,!0),a&&n.applyInput(a),"IFRAME"===o.nodeName){const t=e.contentDocument,s=o;if(t){const e=r.getMeta(s.contentDocument);e&&n.mirror.add(t,Object.assign({},e)),D(t,s.contentDocument,n,r)}}}function g(e,o,n,r,s){var i;let a,d,c=0,l=e.length-1,u=0,h=o.length-1,p=e[c],m=e[l],N=o[u],f=o[h];for(;c<=l&&u<=h;){const E=r.mirror.getId(p),T=r.mirror.getId(m),R=s.getId(N),g=s.getId(f);if(void 0===p)p=e[++c];else if(void 0===m)m=e[--l];else if(-1!==E&&E===R)D(p,N,r,s),p=e[++c],N=o[++u];else if(-1!==T&&T===g)D(m,f,r,s),m=e[--l],f=o[--h];else if(-1!==E&&E===g)n.insertBefore(p,m.nextSibling),D(p,f,r,s),p=e[++c],f=o[--h];else if(-1!==T&&T===R)n.insertBefore(m,p),D(m,N,r,s),m=e[--l],N=o[++u];else{if(!a){a={};for(let t=c;t<=l;t++){const o=e[t];o&&r.mirror.hasNode(o)&&(a[r.mirror.getId(o)]=t)}}if(d=a[s.getId(N)],d){const t=e[d];n.insertBefore(t,p),D(t,N,r,s),e[d]=void 0}else{const o=y(N,r.mirror,s);"#document"===n.nodeName&&(null===(i=r.mirror.getMeta(o))||void 0===i?void 0:i.type)===t.Element&&n.documentElement&&(n.removeChild(n.documentElement),e[c]=void 0,p=void 0),n.insertBefore(o,p||null),D(o,N,r,s)}N=o[++u]}}if(c>l){const e=o[h+1];let t=null;for(e&&n.childNodes.forEach((o=>{r.mirror.getId(o)===s.getId(e)&&(t=o)}));u<=h;++u){const e=y(o[u],r.mirror,s);n.insertBefore(e,t),D(e,o[u],r,s)}}else if(u>h)for(;c<=l;c++){const t=e[c];t&&(n.removeChild(t),r.mirror.removeNodeFromMap(t))}}function y(e,o,n){const r=n.getId(e),s=n.getMeta(e);let i=null;if(r>-1&&(i=o.getNode(r)),null!==i)return i;switch(e.RRNodeType){case t.Document:i=new Document;break;case t.DocumentType:i=document.implementation.createDocumentType(e.name,e.publicId,e.systemId);break;case t.Element:{let t=e.tagName.toLowerCase();t=R[t]||t,i=s&&"isSVG"in s&&(null==s?void 0:s.isSVG)?document.createElementNS(T.svg,t):document.createElement(e.tagName);break}case t.Text:i=document.createTextNode(e.data);break;case t.Comment:i=document.createComment(e.data);break;case t.CDATA:i=document.createCDATASection(e.data)}return s&&o.add(i,Object.assign({},s)),i}class C extends(l(c)){constructor(e){super(),this.UNSERIALIZED_STARTING_ID=-2,this._unserializedId=this.UNSERIALIZED_STARTING_ID,this.mirror=L(),this.scrollData=null,e&&(this.mirror=e)}get unserializedId(){return this._unserializedId--}createDocument(e,t,o){return new C}createDocumentType(e,t,o){const n=new M(e,t,o);return n.ownerDocument=this,n}createElement(e){const t=e.toUpperCase();let o;switch(t){case"AUDIO":case"VIDEO":o=new O(t);break;case"IFRAME":o=new A(t,this.mirror);break;case"CANVAS":o=new x(t);break;case"STYLE":o=new I(t);break;default:o=new w(t)}return o.ownerDocument=this,o}createComment(e){const t=new _(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new v(e);return t.ownerDocument=this,t}createTextNode(e){const t=new b(e);return t.ownerDocument=this,t}destroyTree(){this.childNodes=[],this.mirror.reset()}open(){super.open(),this._unserializedId=this.UNSERIALIZED_STARTING_ID}}const M=u(c);class w extends(h(c)){constructor(){super(...arguments),this.inputData=null,this.scrollData=null}}class O extends(p(w)){}class x extends w{constructor(){super(...arguments),this.rr_dataURL=null,this.canvasMutations=[]}getContext(){return null}}class I extends w{constructor(){super(...arguments),this.rules=[]}}class A extends w{constructor(e,t){super(e),this.contentDocument=new C,this.contentDocument.mirror=t}}const b=m(c),_=N(c),v=f(c);function S(t,o,n,r){let s;switch(t.nodeType){case e.NodeType.DOCUMENT_NODE:r&&"IFRAME"===r.nodeName?s=r.contentDocument:(s=o,s.compatMode=t.compatMode);break;case e.NodeType.DOCUMENT_TYPE_NODE:{const e=t;s=o.createDocumentType(e.name,e.publicId,e.systemId);break}case e.NodeType.ELEMENT_NODE:{const e=t,n=(i=e)instanceof HTMLFormElement?"FORM":i.tagName.toUpperCase();s=o.createElement(n);const r=s;for(const{name:t,value:o}of Array.from(e.attributes))r.attributes[t]=o;e.scrollLeft&&(r.scrollLeft=e.scrollLeft),e.scrollTop&&(r.scrollTop=e.scrollTop);break}case e.NodeType.TEXT_NODE:s=o.createTextNode(t.textContent||"");break;case e.NodeType.CDATA_SECTION_NODE:s=o.createCDATASection(t.data);break;case e.NodeType.COMMENT_NODE:s=o.createComment(t.textContent||"");break;case e.NodeType.DOCUMENT_FRAGMENT_NODE:s=r.attachShadow({mode:"open"});break;default:return null}var i;let a=n.getMeta(t);return o instanceof C&&(a||(a=U(s,o.unserializedId),n.add(t,a)),o.mirror.add(s,Object.assign({},a))),s}function L(){return new F}class F{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){var t;if(!e)return-1;const o=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=o?o:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const o=t.id;this.idNodeMap.set(o,e),this.nodeMetaMap.set(e,t)}replace(e,t){const o=this.getNode(e);if(o){const e=this.nodeMetaMap.get(o);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function U(e,o){switch(e.RRNodeType){case t.Document:return{id:o,type:e.RRNodeType,childNodes:[]};case t.DocumentType:{const t=e;return{id:o,type:e.RRNodeType,name:t.name,publicId:t.publicId,systemId:t.systemId}}case t.Element:return{id:o,type:e.RRNodeType,tagName:e.tagName.toLowerCase(),attributes:{},childNodes:[]};case t.Text:case t.Comment:return{id:o,type:e.RRNodeType,textContent:e.textContent||""};case t.CDATA:return{id:o,type:e.RRNodeType,textContent:""}}}function k(e,o,n){let r=`${n}${o.getId(e)} ${e.toString()}\n`;if(e.RRNodeType===t.Element){const t=e;t.shadowRoot&&(r+=k(t.shadowRoot,o,n+"  "))}for(const t of e.childNodes)r+=k(t,o,n+"  ");return"IFRAME"===e.nodeName&&(r+=k(e.contentDocument,o,n+"  ")),r}return e.BaseRRCDATASectionImpl=f,e.BaseRRCommentImpl=N,e.BaseRRDocumentImpl=l,e.BaseRRDocumentTypeImpl=u,e.BaseRRElementImpl=h,e.BaseRRMediaElementImpl=p,e.BaseRRNode=c,e.BaseRRTextImpl=m,e.ClassList=E,e.Mirror=F,e.RRCDATASection=v,e.RRCanvasElement=x,e.RRComment=_,e.RRDocument=C,e.RRDocumentType=M,e.RRElement=w,e.RRIFrameElement=A,e.RRMediaElement=O,e.RRNode=c,e.RRStyleElement=I,e.RRText=b,e.buildFromDom=function(t,n=function(){return new o}(),r=new C){return function t(o,s){const i=S(o,r,n,s);if(null!==i)if("IFRAME"!==(null==s?void 0:s.nodeName)&&o.nodeType!==e.NodeType.DOCUMENT_FRAGMENT_NODE&&(null==s||s.appendChild(i),i.parentNode=s,i.parentElement=s),"IFRAME"===o.nodeName){const e=o.contentDocument;e&&t(e,i)}else o.nodeType!==e.NodeType.DOCUMENT_NODE&&o.nodeType!==e.NodeType.ELEMENT_NODE&&o.nodeType!==e.NodeType.DOCUMENT_FRAGMENT_NODE||(o.nodeType===e.NodeType.ELEMENT_NODE&&o.shadowRoot&&t(o.shadowRoot,i),o.childNodes.forEach((e=>t(e,i))))}(t,null),r},e.buildFromNode=S,e.createMirror=L,e.createOrGetNode=y,e.diff=D,e.getDefaultSN=U,e.printRRDom=function(e,t){return k(e,t,"")},Object.defineProperty(e,"__esModule",{value:!0}),e}({});
//# sourceMappingURL=rrdom.min.js.map
