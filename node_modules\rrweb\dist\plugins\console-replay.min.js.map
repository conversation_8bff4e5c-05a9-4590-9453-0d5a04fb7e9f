{"version": 3, "file": "console-replay.min.js", "sources": ["../../../rrweb-snapshot/es/rrweb-snapshot.js", "../../src/utils.ts", "../../src/plugins/console/record/index.ts", "../../../types/dist/types.js", "../../src/plugins/console/replay/index.ts"], "sourcesContent": ["var NodeType;\r\n(function (NodeType) {\r\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\r\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\r\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\r\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\r\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\r\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\r\n})(NodeType || (NodeType = {}));\n\nfunction isElement(n) {\r\n    return n.nodeType === n.ELEMENT_NODE;\r\n}\r\nfunction isShadowRoot(n) {\r\n    var host = n === null || n === void 0 ? void 0 : n.host;\r\n    return Boolean((host === null || host === void 0 ? void 0 : host.shadowRoot) === n);\r\n}\r\nfunction isNativeShadowDom(shadowRoot) {\r\n    return Object.prototype.toString.call(shadowRoot) === '[object ShadowRoot]';\r\n}\r\nfunction fixBrowserCompatibilityIssuesInCSS(cssText) {\r\n    if (cssText.includes(' background-clip: text;') &&\r\n        !cssText.includes(' -webkit-background-clip: text;')) {\r\n        cssText = cssText.replace(' background-clip: text;', ' -webkit-background-clip: text; background-clip: text;');\r\n    }\r\n    return cssText;\r\n}\r\nfunction getCssRulesString(s) {\r\n    try {\r\n        var rules = s.rules || s.cssRules;\r\n        return rules\r\n            ? fixBrowserCompatibilityIssuesInCSS(Array.from(rules).map(getCssRuleString).join(''))\r\n            : null;\r\n    }\r\n    catch (error) {\r\n        return null;\r\n    }\r\n}\r\nfunction getCssRuleString(rule) {\r\n    var cssStringified = rule.cssText;\r\n    if (isCSSImportRule(rule)) {\r\n        try {\r\n            cssStringified = getCssRulesString(rule.styleSheet) || cssStringified;\r\n        }\r\n        catch (_a) {\r\n        }\r\n    }\r\n    return cssStringified;\r\n}\r\nfunction isCSSImportRule(rule) {\r\n    return 'styleSheet' in rule;\r\n}\r\nvar Mirror = (function () {\r\n    function Mirror() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n    Mirror.prototype.getId = function (n) {\r\n        var _a;\r\n        if (!n)\r\n            return -1;\r\n        var id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\r\n        return id !== null && id !== void 0 ? id : -1;\r\n    };\r\n    Mirror.prototype.getNode = function (id) {\r\n        return this.idNodeMap.get(id) || null;\r\n    };\r\n    Mirror.prototype.getIds = function () {\r\n        return Array.from(this.idNodeMap.keys());\r\n    };\r\n    Mirror.prototype.getMeta = function (n) {\r\n        return this.nodeMetaMap.get(n) || null;\r\n    };\r\n    Mirror.prototype.removeNodeFromMap = function (n) {\r\n        var _this = this;\r\n        var id = this.getId(n);\r\n        this.idNodeMap[\"delete\"](id);\r\n        if (n.childNodes) {\r\n            n.childNodes.forEach(function (childNode) {\r\n                return _this.removeNodeFromMap(childNode);\r\n            });\r\n        }\r\n    };\r\n    Mirror.prototype.has = function (id) {\r\n        return this.idNodeMap.has(id);\r\n    };\r\n    Mirror.prototype.hasNode = function (node) {\r\n        return this.nodeMetaMap.has(node);\r\n    };\r\n    Mirror.prototype.add = function (n, meta) {\r\n        var id = meta.id;\r\n        this.idNodeMap.set(id, n);\r\n        this.nodeMetaMap.set(n, meta);\r\n    };\r\n    Mirror.prototype.replace = function (id, n) {\r\n        var oldNode = this.getNode(id);\r\n        if (oldNode) {\r\n            var meta = this.nodeMetaMap.get(oldNode);\r\n            if (meta)\r\n                this.nodeMetaMap.set(n, meta);\r\n        }\r\n        this.idNodeMap.set(id, n);\r\n    };\r\n    Mirror.prototype.reset = function () {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    };\r\n    return Mirror;\r\n}());\r\nfunction createMirror() {\r\n    return new Mirror();\r\n}\r\nfunction maskInputValue(_a) {\r\n    var maskInputOptions = _a.maskInputOptions, tagName = _a.tagName, type = _a.type, value = _a.value, maskInputFn = _a.maskInputFn;\r\n    var text = value || '';\r\n    if (maskInputOptions[tagName.toLowerCase()] ||\r\n        maskInputOptions[type]) {\r\n        if (maskInputFn) {\r\n            text = maskInputFn(text);\r\n        }\r\n        else {\r\n            text = '*'.repeat(text.length);\r\n        }\r\n    }\r\n    return text;\r\n}\r\nvar ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\r\nfunction is2DCanvasBlank(canvas) {\r\n    var ctx = canvas.getContext('2d');\r\n    if (!ctx)\r\n        return true;\r\n    var chunkSize = 50;\r\n    for (var x = 0; x < canvas.width; x += chunkSize) {\r\n        for (var y = 0; y < canvas.height; y += chunkSize) {\r\n            var getImageData = ctx.getImageData;\r\n            var originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData\r\n                ? getImageData[ORIGINAL_ATTRIBUTE_NAME]\r\n                : getImageData;\r\n            var pixelBuffer = new Uint32Array(originalGetImageData.call(ctx, x, y, Math.min(chunkSize, canvas.width - x), Math.min(chunkSize, canvas.height - y)).data.buffer);\r\n            if (pixelBuffer.some(function (pixel) { return pixel !== 0; }))\r\n                return false;\r\n        }\r\n    }\r\n    return true;\r\n}\n\nvar _id = 1;\r\nvar tagNameRegex = new RegExp('[^a-z0-9-_:]');\r\nvar IGNORED_NODE = -2;\r\nfunction genId() {\r\n    return _id++;\r\n}\r\nfunction getValidTagName(element) {\r\n    if (element instanceof HTMLFormElement) {\r\n        return 'form';\r\n    }\r\n    var processedTagName = element.tagName.toLowerCase().trim();\r\n    if (tagNameRegex.test(processedTagName)) {\r\n        return 'div';\r\n    }\r\n    return processedTagName;\r\n}\r\nfunction stringifyStyleSheet(sheet) {\r\n    return sheet.cssRules\r\n        ? Array.from(sheet.cssRules)\r\n            .map(function (rule) { return rule.cssText || ''; })\r\n            .join('')\r\n        : '';\r\n}\r\nfunction extractOrigin(url) {\r\n    var origin = '';\r\n    if (url.indexOf('//') > -1) {\r\n        origin = url.split('/').slice(0, 3).join('/');\r\n    }\r\n    else {\r\n        origin = url.split('/')[0];\r\n    }\r\n    origin = origin.split('?')[0];\r\n    return origin;\r\n}\r\nvar canvasService;\r\nvar canvasCtx;\r\nvar URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\r\nvar RELATIVE_PATH = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\\\|\\/\\/|#).*/;\r\nvar DATA_URI = /^(data:)([^,]*),(.*)/i;\r\nfunction absoluteToStylesheet(cssText, href) {\r\n    return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {\r\n        var filePath = path1 || path2 || path3;\r\n        var maybeQuote = quote1 || quote2 || '';\r\n        if (!filePath) {\r\n            return origin;\r\n        }\r\n        if (!RELATIVE_PATH.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (DATA_URI.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (filePath[0] === '/') {\r\n            return \"url(\".concat(maybeQuote).concat(extractOrigin(href) + filePath).concat(maybeQuote, \")\");\r\n        }\r\n        var stack = href.split('/');\r\n        var parts = filePath.split('/');\r\n        stack.pop();\r\n        for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\r\n            var part = parts_1[_i];\r\n            if (part === '.') {\r\n                continue;\r\n            }\r\n            else if (part === '..') {\r\n                stack.pop();\r\n            }\r\n            else {\r\n                stack.push(part);\r\n            }\r\n        }\r\n        return \"url(\".concat(maybeQuote).concat(stack.join('/')).concat(maybeQuote, \")\");\r\n    });\r\n}\r\nvar SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\r\nvar SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\r\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\r\n    if (attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var pos = 0;\r\n    function collectCharacters(regEx) {\r\n        var chars;\r\n        var match = regEx.exec(attributeValue.substring(pos));\r\n        if (match) {\r\n            chars = match[0];\r\n            pos += chars.length;\r\n            return chars;\r\n        }\r\n        return '';\r\n    }\r\n    var output = [];\r\n    while (true) {\r\n        collectCharacters(SRCSET_COMMAS_OR_SPACES);\r\n        if (pos >= attributeValue.length) {\r\n            break;\r\n        }\r\n        var url = collectCharacters(SRCSET_NOT_SPACES);\r\n        if (url.slice(-1) === ',') {\r\n            url = absoluteToDoc(doc, url.substring(0, url.length - 1));\r\n            output.push(url);\r\n        }\r\n        else {\r\n            var descriptorsStr = '';\r\n            url = absoluteToDoc(doc, url);\r\n            var inParens = false;\r\n            while (true) {\r\n                var c = attributeValue.charAt(pos);\r\n                if (c === '') {\r\n                    output.push((url + descriptorsStr).trim());\r\n                    break;\r\n                }\r\n                else if (!inParens) {\r\n                    if (c === ',') {\r\n                        pos += 1;\r\n                        output.push((url + descriptorsStr).trim());\r\n                        break;\r\n                    }\r\n                    else if (c === '(') {\r\n                        inParens = true;\r\n                    }\r\n                }\r\n                else {\r\n                    if (c === ')') {\r\n                        inParens = false;\r\n                    }\r\n                }\r\n                descriptorsStr += c;\r\n                pos += 1;\r\n            }\r\n        }\r\n    }\r\n    return output.join(', ');\r\n}\r\nfunction absoluteToDoc(doc, attributeValue) {\r\n    if (!attributeValue || attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var a = doc.createElement('a');\r\n    a.href = attributeValue;\r\n    return a.href;\r\n}\r\nfunction isSVGElement(el) {\r\n    return Boolean(el.tagName === 'svg' || el.ownerSVGElement);\r\n}\r\nfunction getHref() {\r\n    var a = document.createElement('a');\r\n    a.href = '';\r\n    return a.href;\r\n}\r\nfunction transformAttribute(doc, tagName, name, value) {\r\n    if (name === 'src' ||\r\n        (name === 'href' && value && !(tagName === 'use' && value[0] === '#'))) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'xlink:href' && value && value[0] !== '#') {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'background' &&\r\n        value &&\r\n        (tagName === 'table' || tagName === 'td' || tagName === 'th')) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'srcset' && value) {\r\n        return getAbsoluteSrcsetString(doc, value);\r\n    }\r\n    else if (name === 'style' && value) {\r\n        return absoluteToStylesheet(value, getHref());\r\n    }\r\n    else if (tagName === 'object' && name === 'data' && value) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else {\r\n        return value;\r\n    }\r\n}\r\nfunction _isBlockedElement(element, blockClass, blockSelector) {\r\n    if (typeof blockClass === 'string') {\r\n        if (element.classList.contains(blockClass)) {\r\n            return true;\r\n        }\r\n    }\r\n    else {\r\n        for (var eIndex = element.classList.length; eIndex--;) {\r\n            var className = element.classList[eIndex];\r\n            if (blockClass.test(className)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    if (blockSelector) {\r\n        return element.matches(blockSelector);\r\n    }\r\n    return false;\r\n}\r\nfunction classMatchesRegex(node, regex, checkAncestors) {\r\n    if (!node)\r\n        return false;\r\n    if (node.nodeType !== node.ELEMENT_NODE) {\r\n        if (!checkAncestors)\r\n            return false;\r\n        return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n    }\r\n    for (var eIndex = node.classList.length; eIndex--;) {\r\n        var className = node.classList[eIndex];\r\n        if (regex.test(className)) {\r\n            return true;\r\n        }\r\n    }\r\n    if (!checkAncestors)\r\n        return false;\r\n    return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n}\r\nfunction needMaskingText(node, maskTextClass, maskTextSelector) {\r\n    var el = node.nodeType === node.ELEMENT_NODE\r\n        ? node\r\n        : node.parentElement;\r\n    if (el === null)\r\n        return false;\r\n    if (typeof maskTextClass === 'string') {\r\n        if (el.classList.contains(maskTextClass))\r\n            return true;\r\n        if (el.closest(\".\".concat(maskTextClass)))\r\n            return true;\r\n    }\r\n    else {\r\n        if (classMatchesRegex(el, maskTextClass, true))\r\n            return true;\r\n    }\r\n    if (maskTextSelector) {\r\n        if (el.matches(maskTextSelector))\r\n            return true;\r\n        if (el.closest(maskTextSelector))\r\n            return true;\r\n    }\r\n    return false;\r\n}\r\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\r\n    var win = iframeEl.contentWindow;\r\n    if (!win) {\r\n        return;\r\n    }\r\n    var fired = false;\r\n    var readyState;\r\n    try {\r\n        readyState = win.document.readyState;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (readyState !== 'complete') {\r\n        var timer_1 = setTimeout(function () {\r\n            if (!fired) {\r\n                listener();\r\n                fired = true;\r\n            }\r\n        }, iframeLoadTimeout);\r\n        iframeEl.addEventListener('load', function () {\r\n            clearTimeout(timer_1);\r\n            fired = true;\r\n            listener();\r\n        });\r\n        return;\r\n    }\r\n    var blankUrl = 'about:blank';\r\n    if (win.location.href !== blankUrl ||\r\n        iframeEl.src === blankUrl ||\r\n        iframeEl.src === '') {\r\n        setTimeout(listener, 0);\r\n        return iframeEl.addEventListener('load', listener);\r\n    }\r\n    iframeEl.addEventListener('load', listener);\r\n}\r\nfunction onceStylesheetLoaded(link, listener, styleSheetLoadTimeout) {\r\n    var fired = false;\r\n    var styleSheetLoaded;\r\n    try {\r\n        styleSheetLoaded = link.sheet;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (styleSheetLoaded)\r\n        return;\r\n    var timer = setTimeout(function () {\r\n        if (!fired) {\r\n            listener();\r\n            fired = true;\r\n        }\r\n    }, styleSheetLoadTimeout);\r\n    link.addEventListener('load', function () {\r\n        clearTimeout(timer);\r\n        fired = true;\r\n        listener();\r\n    });\r\n}\r\nfunction serializeNode(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c;\r\n    var rootId = getRootId(doc, mirror);\r\n    switch (n.nodeType) {\r\n        case n.DOCUMENT_NODE:\r\n            if (n.compatMode !== 'CSS1Compat') {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: [],\r\n                    compatMode: n.compatMode\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: []\r\n                };\r\n            }\r\n        case n.DOCUMENT_TYPE_NODE:\r\n            return {\r\n                type: NodeType.DocumentType,\r\n                name: n.name,\r\n                publicId: n.publicId,\r\n                systemId: n.systemId,\r\n                rootId: rootId\r\n            };\r\n        case n.ELEMENT_NODE:\r\n            return serializeElementNode(n, {\r\n                doc: doc,\r\n                blockClass: blockClass,\r\n                blockSelector: blockSelector,\r\n                inlineStylesheet: inlineStylesheet,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn,\r\n                dataURLOptions: dataURLOptions,\r\n                inlineImages: inlineImages,\r\n                recordCanvas: recordCanvas,\r\n                keepIframeSrcFn: keepIframeSrcFn,\r\n                newlyAddedElement: newlyAddedElement,\r\n                rootId: rootId\r\n            });\r\n        case n.TEXT_NODE:\r\n            return serializeTextNode(n, {\r\n                maskTextClass: maskTextClass,\r\n                maskTextSelector: maskTextSelector,\r\n                maskTextFn: maskTextFn,\r\n                rootId: rootId\r\n            });\r\n        case n.CDATA_SECTION_NODE:\r\n            return {\r\n                type: NodeType.CDATA,\r\n                textContent: '',\r\n                rootId: rootId\r\n            };\r\n        case n.COMMENT_NODE:\r\n            return {\r\n                type: NodeType.Comment,\r\n                textContent: n.textContent || '',\r\n                rootId: rootId\r\n            };\r\n        default:\r\n            return false;\r\n    }\r\n}\r\nfunction getRootId(doc, mirror) {\r\n    if (!mirror.hasNode(doc))\r\n        return undefined;\r\n    var docId = mirror.getId(doc);\r\n    return docId === 1 ? undefined : docId;\r\n}\r\nfunction serializeTextNode(n, options) {\r\n    var _a;\r\n    var maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, maskTextFn = options.maskTextFn, rootId = options.rootId;\r\n    var parentTagName = n.parentNode && n.parentNode.tagName;\r\n    var textContent = n.textContent;\r\n    var isStyle = parentTagName === 'STYLE' ? true : undefined;\r\n    var isScript = parentTagName === 'SCRIPT' ? true : undefined;\r\n    if (isStyle && textContent) {\r\n        try {\r\n            if (n.nextSibling || n.previousSibling) {\r\n            }\r\n            else if ((_a = n.parentNode.sheet) === null || _a === void 0 ? void 0 : _a.cssRules) {\r\n                textContent = stringifyStyleSheet(n.parentNode.sheet);\r\n            }\r\n        }\r\n        catch (err) {\r\n            console.warn(\"Cannot get CSS styles from text's parentNode. Error: \".concat(err), n);\r\n        }\r\n        textContent = absoluteToStylesheet(textContent, getHref());\r\n    }\r\n    if (isScript) {\r\n        textContent = 'SCRIPT_PLACEHOLDER';\r\n    }\r\n    if (!isStyle &&\r\n        !isScript &&\r\n        textContent &&\r\n        needMaskingText(n, maskTextClass, maskTextSelector)) {\r\n        textContent = maskTextFn\r\n            ? maskTextFn(textContent)\r\n            : textContent.replace(/[\\S]/g, '*');\r\n    }\r\n    return {\r\n        type: NodeType.Text,\r\n        textContent: textContent || '',\r\n        isStyle: isStyle,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction serializeElementNode(n, options) {\r\n    var doc = options.doc, blockClass = options.blockClass, blockSelector = options.blockSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c, rootId = options.rootId;\r\n    var needBlock = _isBlockedElement(n, blockClass, blockSelector);\r\n    var tagName = getValidTagName(n);\r\n    var attributes = {};\r\n    var len = n.attributes.length;\r\n    for (var i = 0; i < len; i++) {\r\n        var attr = n.attributes[i];\r\n        attributes[attr.name] = transformAttribute(doc, tagName, attr.name, attr.value);\r\n    }\r\n    if (tagName === 'link' && inlineStylesheet) {\r\n        var stylesheet = Array.from(doc.styleSheets).find(function (s) {\r\n            return s.href === n.href;\r\n        });\r\n        var cssText = null;\r\n        if (stylesheet) {\r\n            cssText = getCssRulesString(stylesheet);\r\n        }\r\n        if (cssText) {\r\n            delete attributes.rel;\r\n            delete attributes.href;\r\n            attributes._cssText = absoluteToStylesheet(cssText, stylesheet.href);\r\n        }\r\n    }\r\n    if (tagName === 'style' &&\r\n        n.sheet &&\r\n        !(n.innerText || n.textContent || '').trim().length) {\r\n        var cssText = getCssRulesString(n.sheet);\r\n        if (cssText) {\r\n            attributes._cssText = absoluteToStylesheet(cssText, getHref());\r\n        }\r\n    }\r\n    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {\r\n        var value = n.value;\r\n        var checked = n.checked;\r\n        if (attributes.type !== 'radio' &&\r\n            attributes.type !== 'checkbox' &&\r\n            attributes.type !== 'submit' &&\r\n            attributes.type !== 'button' &&\r\n            value) {\r\n            attributes.value = maskInputValue({\r\n                type: attributes.type,\r\n                tagName: tagName,\r\n                value: value,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn\r\n            });\r\n        }\r\n        else if (checked) {\r\n            attributes.checked = checked;\r\n        }\r\n    }\r\n    if (tagName === 'option') {\r\n        if (n.selected && !maskInputOptions['select']) {\r\n            attributes.selected = true;\r\n        }\r\n        else {\r\n            delete attributes.selected;\r\n        }\r\n    }\r\n    if (tagName === 'canvas' && recordCanvas) {\r\n        if (n.__context === '2d') {\r\n            if (!is2DCanvasBlank(n)) {\r\n                attributes.rr_dataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n        }\r\n        else if (!('__context' in n)) {\r\n            var canvasDataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            var blankCanvas = document.createElement('canvas');\r\n            blankCanvas.width = n.width;\r\n            blankCanvas.height = n.height;\r\n            var blankCanvasDataURL = blankCanvas.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            if (canvasDataURL !== blankCanvasDataURL) {\r\n                attributes.rr_dataURL = canvasDataURL;\r\n            }\r\n        }\r\n    }\r\n    if (tagName === 'img' && inlineImages) {\r\n        if (!canvasService) {\r\n            canvasService = doc.createElement('canvas');\r\n            canvasCtx = canvasService.getContext('2d');\r\n        }\r\n        var image_1 = n;\r\n        var oldValue_1 = image_1.crossOrigin;\r\n        image_1.crossOrigin = 'anonymous';\r\n        var recordInlineImage = function () {\r\n            try {\r\n                canvasService.width = image_1.naturalWidth;\r\n                canvasService.height = image_1.naturalHeight;\r\n                canvasCtx.drawImage(image_1, 0, 0);\r\n                attributes.rr_dataURL = canvasService.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n            catch (err) {\r\n                console.warn(\"Cannot inline img src=\".concat(image_1.currentSrc, \"! Error: \").concat(err));\r\n            }\r\n            oldValue_1\r\n                ? (attributes.crossOrigin = oldValue_1)\r\n                : image_1.removeAttribute('crossorigin');\r\n        };\r\n        if (image_1.complete && image_1.naturalWidth !== 0)\r\n            recordInlineImage();\r\n        else\r\n            image_1.onload = recordInlineImage;\r\n    }\r\n    if (tagName === 'audio' || tagName === 'video') {\r\n        attributes.rr_mediaState = n.paused\r\n            ? 'paused'\r\n            : 'played';\r\n        attributes.rr_mediaCurrentTime = n.currentTime;\r\n    }\r\n    if (!newlyAddedElement) {\r\n        if (n.scrollLeft) {\r\n            attributes.rr_scrollLeft = n.scrollLeft;\r\n        }\r\n        if (n.scrollTop) {\r\n            attributes.rr_scrollTop = n.scrollTop;\r\n        }\r\n    }\r\n    if (needBlock) {\r\n        var _d = n.getBoundingClientRect(), width = _d.width, height = _d.height;\r\n        attributes = {\r\n            \"class\": attributes[\"class\"],\r\n            rr_width: \"\".concat(width, \"px\"),\r\n            rr_height: \"\".concat(height, \"px\")\r\n        };\r\n    }\r\n    if (tagName === 'iframe' && !keepIframeSrcFn(attributes.src)) {\r\n        if (!n.contentDocument) {\r\n            attributes.rr_src = attributes.src;\r\n        }\r\n        delete attributes.src;\r\n    }\r\n    return {\r\n        type: NodeType.Element,\r\n        tagName: tagName,\r\n        attributes: attributes,\r\n        childNodes: [],\r\n        isSVG: isSVGElement(n) || undefined,\r\n        needBlock: needBlock,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction lowerIfExists(maybeAttr) {\r\n    if (maybeAttr === undefined) {\r\n        return '';\r\n    }\r\n    else {\r\n        return maybeAttr.toLowerCase();\r\n    }\r\n}\r\nfunction slimDOMExcluded(sn, slimDOMOptions) {\r\n    if (slimDOMOptions.comment && sn.type === NodeType.Comment) {\r\n        return true;\r\n    }\r\n    else if (sn.type === NodeType.Element) {\r\n        if (slimDOMOptions.script &&\r\n            (sn.tagName === 'script' ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'preload' &&\r\n                    sn.attributes.as === 'script') ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'prefetch' &&\r\n                    typeof sn.attributes.href === 'string' &&\r\n                    sn.attributes.href.endsWith('.js')))) {\r\n            return true;\r\n        }\r\n        else if (slimDOMOptions.headFavicon &&\r\n            ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon') ||\r\n                (sn.tagName === 'meta' &&\r\n                    (lowerIfExists(sn.attributes.name).match(/^msapplication-tile(image|color)$/) ||\r\n                        lowerIfExists(sn.attributes.name) === 'application-name' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'apple-touch-icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'shortcut icon')))) {\r\n            return true;\r\n        }\r\n        else if (sn.tagName === 'meta') {\r\n            if (slimDOMOptions.headMetaDescKeywords &&\r\n                lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaSocial &&\r\n                (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) ||\r\n                    lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) ||\r\n                    lowerIfExists(sn.attributes.name) === 'pinterest')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaRobots &&\r\n                (lowerIfExists(sn.attributes.name) === 'robots' ||\r\n                    lowerIfExists(sn.attributes.name) === 'googlebot' ||\r\n                    lowerIfExists(sn.attributes.name) === 'bingbot')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaHttpEquiv &&\r\n                sn.attributes['http-equiv'] !== undefined) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaAuthorship &&\r\n                (lowerIfExists(sn.attributes.name) === 'author' ||\r\n                    lowerIfExists(sn.attributes.name) === 'generator' ||\r\n                    lowerIfExists(sn.attributes.name) === 'framework' ||\r\n                    lowerIfExists(sn.attributes.name) === 'publisher' ||\r\n                    lowerIfExists(sn.attributes.name) === 'progid' ||\r\n                    lowerIfExists(sn.attributes.property).match(/^article:/) ||\r\n                    lowerIfExists(sn.attributes.property).match(/^product:/))) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaVerification &&\r\n                (lowerIfExists(sn.attributes.name) === 'google-site-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'yandex-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'csrf-token' ||\r\n                    lowerIfExists(sn.attributes.name) === 'p:domain_verify' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verify-v1' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'shopify-checkout-api-token')) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction serializeNodeWithId(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.inlineStylesheet, inlineStylesheet = _b === void 0 ? true : _b, _c = options.maskInputOptions, maskInputOptions = _c === void 0 ? {} : _c, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, slimDOMOptions = options.slimDOMOptions, _d = options.dataURLOptions, dataURLOptions = _d === void 0 ? {} : _d, _e = options.inlineImages, inlineImages = _e === void 0 ? false : _e, _f = options.recordCanvas, recordCanvas = _f === void 0 ? false : _f, onSerialize = options.onSerialize, onIframeLoad = options.onIframeLoad, _g = options.iframeLoadTimeout, iframeLoadTimeout = _g === void 0 ? 5000 : _g, onStylesheetLoad = options.onStylesheetLoad, _h = options.stylesheetLoadTimeout, stylesheetLoadTimeout = _h === void 0 ? 5000 : _h, _j = options.keepIframeSrcFn, keepIframeSrcFn = _j === void 0 ? function () { return false; } : _j, _k = options.newlyAddedElement, newlyAddedElement = _k === void 0 ? false : _k;\r\n    var _l = options.preserveWhiteSpace, preserveWhiteSpace = _l === void 0 ? true : _l;\r\n    var _serializedNode = serializeNode(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: newlyAddedElement\r\n    });\r\n    if (!_serializedNode) {\r\n        console.warn(n, 'not serialized');\r\n        return null;\r\n    }\r\n    var id;\r\n    if (mirror.hasNode(n)) {\r\n        id = mirror.getId(n);\r\n    }\r\n    else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||\r\n        (!preserveWhiteSpace &&\r\n            _serializedNode.type === NodeType.Text &&\r\n            !_serializedNode.isStyle &&\r\n            !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, '').length)) {\r\n        id = IGNORED_NODE;\r\n    }\r\n    else {\r\n        id = genId();\r\n    }\r\n    var serializedNode = Object.assign(_serializedNode, { id: id });\r\n    mirror.add(n, serializedNode);\r\n    if (id === IGNORED_NODE) {\r\n        return null;\r\n    }\r\n    if (onSerialize) {\r\n        onSerialize(n);\r\n    }\r\n    var recordChild = !skipChild;\r\n    if (serializedNode.type === NodeType.Element) {\r\n        recordChild = recordChild && !serializedNode.needBlock;\r\n        delete serializedNode.needBlock;\r\n        var shadowRoot = n.shadowRoot;\r\n        if (shadowRoot && isNativeShadowDom(shadowRoot))\r\n            serializedNode.isShadowHost = true;\r\n    }\r\n    if ((serializedNode.type === NodeType.Document ||\r\n        serializedNode.type === NodeType.Element) &&\r\n        recordChild) {\r\n        if (slimDOMOptions.headWhitespace &&\r\n            serializedNode.type === NodeType.Element &&\r\n            serializedNode.tagName === 'head') {\r\n            preserveWhiteSpace = false;\r\n        }\r\n        var bypassOptions = {\r\n            doc: doc,\r\n            mirror: mirror,\r\n            blockClass: blockClass,\r\n            blockSelector: blockSelector,\r\n            maskTextClass: maskTextClass,\r\n            maskTextSelector: maskTextSelector,\r\n            skipChild: skipChild,\r\n            inlineStylesheet: inlineStylesheet,\r\n            maskInputOptions: maskInputOptions,\r\n            maskTextFn: maskTextFn,\r\n            maskInputFn: maskInputFn,\r\n            slimDOMOptions: slimDOMOptions,\r\n            dataURLOptions: dataURLOptions,\r\n            inlineImages: inlineImages,\r\n            recordCanvas: recordCanvas,\r\n            preserveWhiteSpace: preserveWhiteSpace,\r\n            onSerialize: onSerialize,\r\n            onIframeLoad: onIframeLoad,\r\n            iframeLoadTimeout: iframeLoadTimeout,\r\n            onStylesheetLoad: onStylesheetLoad,\r\n            stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n            keepIframeSrcFn: keepIframeSrcFn\r\n        };\r\n        for (var _i = 0, _m = Array.from(n.childNodes); _i < _m.length; _i++) {\r\n            var childN = _m[_i];\r\n            var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n            if (serializedChildNode) {\r\n                serializedNode.childNodes.push(serializedChildNode);\r\n            }\r\n        }\r\n        if (isElement(n) && n.shadowRoot) {\r\n            for (var _o = 0, _p = Array.from(n.shadowRoot.childNodes); _o < _p.length; _o++) {\r\n                var childN = _p[_o];\r\n                var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n                if (serializedChildNode) {\r\n                    isNativeShadowDom(n.shadowRoot) &&\r\n                        (serializedChildNode.isShadow = true);\r\n                    serializedNode.childNodes.push(serializedChildNode);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (n.parentNode &&\r\n        isShadowRoot(n.parentNode) &&\r\n        isNativeShadowDom(n.parentNode)) {\r\n        serializedNode.isShadow = true;\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'iframe') {\r\n        onceIframeLoaded(n, function () {\r\n            var iframeDoc = n.contentDocument;\r\n            if (iframeDoc && onIframeLoad) {\r\n                var serializedIframeNode = serializeNodeWithId(iframeDoc, {\r\n                    doc: iframeDoc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedIframeNode) {\r\n                    onIframeLoad(n, serializedIframeNode);\r\n                }\r\n            }\r\n        }, iframeLoadTimeout);\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'link' &&\r\n        serializedNode.attributes.rel === 'stylesheet') {\r\n        onceStylesheetLoaded(n, function () {\r\n            if (onStylesheetLoad) {\r\n                var serializedLinkNode = serializeNodeWithId(n, {\r\n                    doc: doc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedLinkNode) {\r\n                    onStylesheetLoad(n, serializedLinkNode);\r\n                }\r\n            }\r\n        }, stylesheetLoadTimeout);\r\n    }\r\n    return serializedNode;\r\n}\r\nfunction snapshot(n, options) {\r\n    var _a = options || {}, _b = _a.mirror, mirror = _b === void 0 ? new Mirror() : _b, _c = _a.blockClass, blockClass = _c === void 0 ? 'rr-block' : _c, _d = _a.blockSelector, blockSelector = _d === void 0 ? null : _d, _e = _a.maskTextClass, maskTextClass = _e === void 0 ? 'rr-mask' : _e, _f = _a.maskTextSelector, maskTextSelector = _f === void 0 ? null : _f, _g = _a.inlineStylesheet, inlineStylesheet = _g === void 0 ? true : _g, _h = _a.inlineImages, inlineImages = _h === void 0 ? false : _h, _j = _a.recordCanvas, recordCanvas = _j === void 0 ? false : _j, _k = _a.maskAllInputs, maskAllInputs = _k === void 0 ? false : _k, maskTextFn = _a.maskTextFn, maskInputFn = _a.maskInputFn, _l = _a.slimDOM, slimDOM = _l === void 0 ? false : _l, dataURLOptions = _a.dataURLOptions, preserveWhiteSpace = _a.preserveWhiteSpace, onSerialize = _a.onSerialize, onIframeLoad = _a.onIframeLoad, iframeLoadTimeout = _a.iframeLoadTimeout, onStylesheetLoad = _a.onStylesheetLoad, stylesheetLoadTimeout = _a.stylesheetLoadTimeout, _m = _a.keepIframeSrcFn, keepIframeSrcFn = _m === void 0 ? function () { return false; } : _m;\r\n    var maskInputOptions = maskAllInputs === true\r\n        ? {\r\n            color: true,\r\n            date: true,\r\n            'datetime-local': true,\r\n            email: true,\r\n            month: true,\r\n            number: true,\r\n            range: true,\r\n            search: true,\r\n            tel: true,\r\n            text: true,\r\n            time: true,\r\n            url: true,\r\n            week: true,\r\n            textarea: true,\r\n            select: true,\r\n            password: true\r\n        }\r\n        : maskAllInputs === false\r\n            ? {\r\n                password: true\r\n            }\r\n            : maskAllInputs;\r\n    var slimDOMOptions = slimDOM === true || slimDOM === 'all'\r\n        ?\r\n            {\r\n                script: true,\r\n                comment: true,\r\n                headFavicon: true,\r\n                headWhitespace: true,\r\n                headMetaDescKeywords: slimDOM === 'all',\r\n                headMetaSocial: true,\r\n                headMetaRobots: true,\r\n                headMetaHttpEquiv: true,\r\n                headMetaAuthorship: true,\r\n                headMetaVerification: true\r\n            }\r\n        : slimDOM === false\r\n            ? {}\r\n            : slimDOM;\r\n    return serializeNodeWithId(n, {\r\n        doc: n,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        skipChild: false,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        slimDOMOptions: slimDOMOptions,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        preserveWhiteSpace: preserveWhiteSpace,\r\n        onSerialize: onSerialize,\r\n        onIframeLoad: onIframeLoad,\r\n        iframeLoadTimeout: iframeLoadTimeout,\r\n        onStylesheetLoad: onStylesheetLoad,\r\n        stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: false\r\n    });\r\n}\r\nfunction visitSnapshot(node, onVisit) {\r\n    function walk(current) {\r\n        onVisit(current);\r\n        if (current.type === NodeType.Document ||\r\n            current.type === NodeType.Element) {\r\n            current.childNodes.forEach(walk);\r\n        }\r\n    }\r\n    walk(node);\r\n}\r\nfunction cleanupSnapshot() {\r\n    _id = 1;\r\n}\n\nvar commentre = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\r\nfunction parse(css, options) {\r\n    if (options === void 0) { options = {}; }\r\n    var lineno = 1;\r\n    var column = 1;\r\n    function updatePosition(str) {\r\n        var lines = str.match(/\\n/g);\r\n        if (lines) {\r\n            lineno += lines.length;\r\n        }\r\n        var i = str.lastIndexOf('\\n');\r\n        column = i === -1 ? column + str.length : str.length - i;\r\n    }\r\n    function position() {\r\n        var start = { line: lineno, column: column };\r\n        return function (node) {\r\n            node.position = new Position(start);\r\n            whitespace();\r\n            return node;\r\n        };\r\n    }\r\n    var Position = (function () {\r\n        function Position(start) {\r\n            this.start = start;\r\n            this.end = { line: lineno, column: column };\r\n            this.source = options.source;\r\n        }\r\n        return Position;\r\n    }());\r\n    Position.prototype.content = css;\r\n    var errorsList = [];\r\n    function error(msg) {\r\n        var err = new Error(\"\".concat(options.source || '', \":\").concat(lineno, \":\").concat(column, \": \").concat(msg));\r\n        err.reason = msg;\r\n        err.filename = options.source;\r\n        err.line = lineno;\r\n        err.column = column;\r\n        err.source = css;\r\n        if (options.silent) {\r\n            errorsList.push(err);\r\n        }\r\n        else {\r\n            throw err;\r\n        }\r\n    }\r\n    function stylesheet() {\r\n        var rulesList = rules();\r\n        return {\r\n            type: 'stylesheet',\r\n            stylesheet: {\r\n                source: options.source,\r\n                rules: rulesList,\r\n                parsingErrors: errorsList\r\n            }\r\n        };\r\n    }\r\n    function open() {\r\n        return match(/^{\\s*/);\r\n    }\r\n    function close() {\r\n        return match(/^}/);\r\n    }\r\n    function rules() {\r\n        var node;\r\n        var rules = [];\r\n        whitespace();\r\n        comments(rules);\r\n        while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\r\n            if (node !== false) {\r\n                rules.push(node);\r\n                comments(rules);\r\n            }\r\n        }\r\n        return rules;\r\n    }\r\n    function match(re) {\r\n        var m = re.exec(css);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var str = m[0];\r\n        updatePosition(str);\r\n        css = css.slice(str.length);\r\n        return m;\r\n    }\r\n    function whitespace() {\r\n        match(/^\\s*/);\r\n    }\r\n    function comments(rules) {\r\n        if (rules === void 0) { rules = []; }\r\n        var c;\r\n        while ((c = comment())) {\r\n            if (c !== false) {\r\n                rules.push(c);\r\n            }\r\n            c = comment();\r\n        }\r\n        return rules;\r\n    }\r\n    function comment() {\r\n        var pos = position();\r\n        if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\r\n            return;\r\n        }\r\n        var i = 2;\r\n        while ('' !== css.charAt(i) &&\r\n            ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {\r\n            ++i;\r\n        }\r\n        i += 2;\r\n        if ('' === css.charAt(i - 1)) {\r\n            return error('End of comment missing');\r\n        }\r\n        var str = css.slice(2, i - 2);\r\n        column += 2;\r\n        updatePosition(str);\r\n        css = css.slice(i);\r\n        column += 2;\r\n        return pos({\r\n            type: 'comment',\r\n            comment: str\r\n        });\r\n    }\r\n    function selector() {\r\n        var m = match(/^([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return trim(m[0])\r\n            .replace(/\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*\\/+/g, '')\r\n            .replace(/\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'/g, function (m) {\r\n            return m.replace(/,/g, '\\u200C');\r\n        })\r\n            .split(/\\s*(?![^(]*\\)),\\s*/)\r\n            .map(function (s) {\r\n            return s.replace(/\\u200C/g, ',');\r\n        });\r\n    }\r\n    function declaration() {\r\n        var pos = position();\r\n        var propMatch = match(/^(\\*?[-#\\/\\*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/);\r\n        if (!propMatch) {\r\n            return;\r\n        }\r\n        var prop = trim(propMatch[0]);\r\n        if (!match(/^:\\s*/)) {\r\n            return error(\"property missing ':'\");\r\n        }\r\n        var val = match(/^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^\\)]*?\\)|[^};])+)/);\r\n        var ret = pos({\r\n            type: 'declaration',\r\n            property: prop.replace(commentre, ''),\r\n            value: val ? trim(val[0]).replace(commentre, '') : ''\r\n        });\r\n        match(/^[;\\s]*/);\r\n        return ret;\r\n    }\r\n    function declarations() {\r\n        var decls = [];\r\n        if (!open()) {\r\n            return error(\"missing '{'\");\r\n        }\r\n        comments(decls);\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            if (decl !== false) {\r\n                decls.push(decl);\r\n                comments(decls);\r\n            }\r\n            decl = declaration();\r\n        }\r\n        if (!close()) {\r\n            return error(\"missing '}'\");\r\n        }\r\n        return decls;\r\n    }\r\n    function keyframe() {\r\n        var m;\r\n        var vals = [];\r\n        var pos = position();\r\n        while ((m = match(/^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/))) {\r\n            vals.push(m[1]);\r\n            match(/^,\\s*/);\r\n        }\r\n        if (!vals.length) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'keyframe',\r\n            values: vals,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    function atkeyframes() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?keyframes\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = m[1];\r\n        m = match(/^([-\\w]+)\\s*/);\r\n        if (!m) {\r\n            return error('@keyframes missing name');\r\n        }\r\n        var name = m[1];\r\n        if (!open()) {\r\n            return error(\"@keyframes missing '{'\");\r\n        }\r\n        var frame;\r\n        var frames = comments();\r\n        while ((frame = keyframe())) {\r\n            frames.push(frame);\r\n            frames = frames.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@keyframes missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'keyframes',\r\n            name: name,\r\n            vendor: vendor,\r\n            keyframes: frames\r\n        });\r\n    }\r\n    function atsupports() {\r\n        var pos = position();\r\n        var m = match(/^@supports *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var supports = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@supports missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@supports missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'supports',\r\n            supports: supports,\r\n            rules: style\r\n        });\r\n    }\r\n    function athost() {\r\n        var pos = position();\r\n        var m = match(/^@host\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@host missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@host missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'host',\r\n            rules: style\r\n        });\r\n    }\r\n    function atmedia() {\r\n        var pos = position();\r\n        var m = match(/^@media *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var media = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@media missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@media missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'media',\r\n            media: media,\r\n            rules: style\r\n        });\r\n    }\r\n    function atcustommedia() {\r\n        var pos = position();\r\n        var m = match(/^@custom-media\\s+(--[^\\s]+)\\s*([^{;]+);/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'custom-media',\r\n            name: trim(m[1]),\r\n            media: trim(m[2])\r\n        });\r\n    }\r\n    function atpage() {\r\n        var pos = position();\r\n        var m = match(/^@page */);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var sel = selector() || [];\r\n        if (!open()) {\r\n            return error(\"@page missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@page missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'page',\r\n            selectors: sel,\r\n            declarations: decls\r\n        });\r\n    }\r\n    function atdocument() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?document *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = trim(m[1]);\r\n        var doc = trim(m[2]);\r\n        if (!open()) {\r\n            return error(\"@document missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@document missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'document',\r\n            document: doc,\r\n            vendor: vendor,\r\n            rules: style\r\n        });\r\n    }\r\n    function atfontface() {\r\n        var pos = position();\r\n        var m = match(/^@font-face\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@font-face missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@font-face missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'font-face',\r\n            declarations: decls\r\n        });\r\n    }\r\n    var atimport = _compileAtrule('import');\r\n    var atcharset = _compileAtrule('charset');\r\n    var atnamespace = _compileAtrule('namespace');\r\n    function _compileAtrule(name) {\r\n        var re = new RegExp('^@' + name + '\\\\s*([^;]+);');\r\n        return function () {\r\n            var pos = position();\r\n            var m = match(re);\r\n            if (!m) {\r\n                return;\r\n            }\r\n            var ret = { type: name };\r\n            ret[name] = m[1].trim();\r\n            return pos(ret);\r\n        };\r\n    }\r\n    function atrule() {\r\n        if (css[0] !== '@') {\r\n            return;\r\n        }\r\n        return (atkeyframes() ||\r\n            atmedia() ||\r\n            atcustommedia() ||\r\n            atsupports() ||\r\n            atimport() ||\r\n            atcharset() ||\r\n            atnamespace() ||\r\n            atdocument() ||\r\n            atpage() ||\r\n            athost() ||\r\n            atfontface());\r\n    }\r\n    function rule() {\r\n        var pos = position();\r\n        var sel = selector();\r\n        if (!sel) {\r\n            return error('selector missing');\r\n        }\r\n        comments();\r\n        return pos({\r\n            type: 'rule',\r\n            selectors: sel,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    return addParent(stylesheet());\r\n}\r\nfunction trim(str) {\r\n    return str ? str.replace(/^\\s+|\\s+$/g, '') : '';\r\n}\r\nfunction addParent(obj, parent) {\r\n    var isNode = obj && typeof obj.type === 'string';\r\n    var childParent = isNode ? obj : parent;\r\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\r\n        var k = _a[_i];\r\n        var value = obj[k];\r\n        if (Array.isArray(value)) {\r\n            value.forEach(function (v) {\r\n                addParent(v, childParent);\r\n            });\r\n        }\r\n        else if (value && typeof value === 'object') {\r\n            addParent(value, childParent);\r\n        }\r\n    }\r\n    if (isNode) {\r\n        Object.defineProperty(obj, 'parent', {\r\n            configurable: true,\r\n            writable: true,\r\n            enumerable: false,\r\n            value: parent || null\r\n        });\r\n    }\r\n    return obj;\r\n}\n\nvar tagMap = {\r\n    script: 'noscript',\r\n    altglyph: 'altGlyph',\r\n    altglyphdef: 'altGlyphDef',\r\n    altglyphitem: 'altGlyphItem',\r\n    animatecolor: 'animateColor',\r\n    animatemotion: 'animateMotion',\r\n    animatetransform: 'animateTransform',\r\n    clippath: 'clipPath',\r\n    feblend: 'feBlend',\r\n    fecolormatrix: 'feColorMatrix',\r\n    fecomponenttransfer: 'feComponentTransfer',\r\n    fecomposite: 'feComposite',\r\n    feconvolvematrix: 'feConvolveMatrix',\r\n    fediffuselighting: 'feDiffuseLighting',\r\n    fedisplacementmap: 'feDisplacementMap',\r\n    fedistantlight: 'feDistantLight',\r\n    fedropshadow: 'feDropShadow',\r\n    feflood: 'feFlood',\r\n    fefunca: 'feFuncA',\r\n    fefuncb: 'feFuncB',\r\n    fefuncg: 'feFuncG',\r\n    fefuncr: 'feFuncR',\r\n    fegaussianblur: 'feGaussianBlur',\r\n    feimage: 'feImage',\r\n    femerge: 'feMerge',\r\n    femergenode: 'feMergeNode',\r\n    femorphology: 'feMorphology',\r\n    feoffset: 'feOffset',\r\n    fepointlight: 'fePointLight',\r\n    fespecularlighting: 'feSpecularLighting',\r\n    fespotlight: 'feSpotLight',\r\n    fetile: 'feTile',\r\n    feturbulence: 'feTurbulence',\r\n    foreignobject: 'foreignObject',\r\n    glyphref: 'glyphRef',\r\n    lineargradient: 'linearGradient',\r\n    radialgradient: 'radialGradient'\r\n};\r\nfunction getTagName(n) {\r\n    var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;\r\n    if (tagName === 'link' && n.attributes._cssText) {\r\n        tagName = 'style';\r\n    }\r\n    return tagName;\r\n}\r\nfunction escapeRegExp(str) {\r\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n}\r\nvar HOVER_SELECTOR = /([^\\\\]):hover/;\r\nvar HOVER_SELECTOR_GLOBAL = new RegExp(HOVER_SELECTOR.source, 'g');\r\nfunction addHoverClass(cssText, cache) {\r\n    var cachedStyle = cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.get(cssText);\r\n    if (cachedStyle)\r\n        return cachedStyle;\r\n    var ast = parse(cssText, {\r\n        silent: true\r\n    });\r\n    if (!ast.stylesheet) {\r\n        return cssText;\r\n    }\r\n    var selectors = [];\r\n    ast.stylesheet.rules.forEach(function (rule) {\r\n        if ('selectors' in rule) {\r\n            (rule.selectors || []).forEach(function (selector) {\r\n                if (HOVER_SELECTOR.test(selector)) {\r\n                    selectors.push(selector);\r\n                }\r\n            });\r\n        }\r\n    });\r\n    if (selectors.length === 0) {\r\n        return cssText;\r\n    }\r\n    var selectorMatcher = new RegExp(selectors\r\n        .filter(function (selector, index) { return selectors.indexOf(selector) === index; })\r\n        .sort(function (a, b) { return b.length - a.length; })\r\n        .map(function (selector) {\r\n        return escapeRegExp(selector);\r\n    })\r\n        .join('|'), 'g');\r\n    var result = cssText.replace(selectorMatcher, function (selector) {\r\n        var newSelector = selector.replace(HOVER_SELECTOR_GLOBAL, '$1.\\\\:hover');\r\n        return \"\".concat(selector, \", \").concat(newSelector);\r\n    });\r\n    cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.set(cssText, result);\r\n    return result;\r\n}\r\nfunction createCache() {\r\n    var stylesWithHoverClass = new Map();\r\n    return {\r\n        stylesWithHoverClass: stylesWithHoverClass\r\n    };\r\n}\r\nfunction buildNode(n, options) {\r\n    var doc = options.doc, hackCss = options.hackCss, cache = options.cache;\r\n    switch (n.type) {\r\n        case NodeType.Document:\r\n            return doc.implementation.createDocument(null, '', null);\r\n        case NodeType.DocumentType:\r\n            return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);\r\n        case NodeType.Element: {\r\n            var tagName = getTagName(n);\r\n            var node_1;\r\n            if (n.isSVG) {\r\n                node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);\r\n            }\r\n            else {\r\n                node_1 = doc.createElement(tagName);\r\n            }\r\n            var specialAttributes = {};\r\n            for (var name_1 in n.attributes) {\r\n                if (!Object.prototype.hasOwnProperty.call(n.attributes, name_1)) {\r\n                    continue;\r\n                }\r\n                var value = n.attributes[name_1];\r\n                if (tagName === 'option' &&\r\n                    name_1 === 'selected' &&\r\n                    value === false) {\r\n                    continue;\r\n                }\r\n                if (value === true)\r\n                    value = '';\r\n                if (name_1.startsWith('rr_')) {\r\n                    specialAttributes[name_1] = value;\r\n                    continue;\r\n                }\r\n                var isTextarea = tagName === 'textarea' && name_1 === 'value';\r\n                var isRemoteOrDynamicCss = tagName === 'style' && name_1 === '_cssText';\r\n                if (isRemoteOrDynamicCss && hackCss && typeof value === 'string') {\r\n                    value = addHoverClass(value, cache);\r\n                }\r\n                if ((isTextarea || isRemoteOrDynamicCss) && typeof value === 'string') {\r\n                    var child = doc.createTextNode(value);\r\n                    for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {\r\n                        var c = _a[_i];\r\n                        if (c.nodeType === node_1.TEXT_NODE) {\r\n                            node_1.removeChild(c);\r\n                        }\r\n                    }\r\n                    node_1.appendChild(child);\r\n                    continue;\r\n                }\r\n                try {\r\n                    if (n.isSVG && name_1 === 'xlink:href') {\r\n                        node_1.setAttributeNS('http://www.w3.org/1999/xlink', name_1, value.toString());\r\n                    }\r\n                    else if (name_1 === 'onload' ||\r\n                        name_1 === 'onclick' ||\r\n                        name_1.substring(0, 7) === 'onmouse') {\r\n                        node_1.setAttribute('_' + name_1, value.toString());\r\n                    }\r\n                    else if (tagName === 'meta' &&\r\n                        n.attributes['http-equiv'] === 'Content-Security-Policy' &&\r\n                        name_1 === 'content') {\r\n                        node_1.setAttribute('csp-content', value.toString());\r\n                        continue;\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'preload' &&\r\n                        n.attributes.as === 'script') {\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'prefetch' &&\r\n                        typeof n.attributes.href === 'string' &&\r\n                        n.attributes.href.endsWith('.js')) {\r\n                    }\r\n                    else if (tagName === 'img' &&\r\n                        n.attributes.srcset &&\r\n                        n.attributes.rr_dataURL) {\r\n                        node_1.setAttribute('rrweb-original-srcset', n.attributes.srcset);\r\n                    }\r\n                    else {\r\n                        node_1.setAttribute(name_1, value.toString());\r\n                    }\r\n                }\r\n                catch (error) {\r\n                }\r\n            }\r\n            var _loop_1 = function (name_2) {\r\n                var value = specialAttributes[name_2];\r\n                if (tagName === 'canvas' && name_2 === 'rr_dataURL') {\r\n                    var image_1 = document.createElement('img');\r\n                    image_1.onload = function () {\r\n                        var ctx = node_1.getContext('2d');\r\n                        if (ctx) {\r\n                            ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);\r\n                        }\r\n                    };\r\n                    image_1.src = value.toString();\r\n                    if (node_1.RRNodeType)\r\n                        node_1.rr_dataURL = value.toString();\r\n                }\r\n                else if (tagName === 'img' && name_2 === 'rr_dataURL') {\r\n                    var image = node_1;\r\n                    if (!image.currentSrc.startsWith('data:')) {\r\n                        image.setAttribute('rrweb-original-src', n.attributes.src);\r\n                        image.src = value.toString();\r\n                    }\r\n                }\r\n                if (name_2 === 'rr_width') {\r\n                    node_1.style.width = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_height') {\r\n                    node_1.style.height = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_mediaCurrentTime' &&\r\n                    typeof value === 'number') {\r\n                    node_1.currentTime = value;\r\n                }\r\n                else if (name_2 === 'rr_mediaState') {\r\n                    switch (value) {\r\n                        case 'played':\r\n                            node_1\r\n                                .play()[\"catch\"](function (e) { return console.warn('media playback error', e); });\r\n                            break;\r\n                        case 'paused':\r\n                            node_1.pause();\r\n                            break;\r\n                    }\r\n                }\r\n            };\r\n            for (var name_2 in specialAttributes) {\r\n                _loop_1(name_2);\r\n            }\r\n            if (n.isShadowHost) {\r\n                if (!node_1.shadowRoot) {\r\n                    node_1.attachShadow({ mode: 'open' });\r\n                }\r\n                else {\r\n                    while (node_1.shadowRoot.firstChild) {\r\n                        node_1.shadowRoot.removeChild(node_1.shadowRoot.firstChild);\r\n                    }\r\n                }\r\n            }\r\n            return node_1;\r\n        }\r\n        case NodeType.Text:\r\n            return doc.createTextNode(n.isStyle && hackCss\r\n                ? addHoverClass(n.textContent, cache)\r\n                : n.textContent);\r\n        case NodeType.CDATA:\r\n            return doc.createCDATASection(n.textContent);\r\n        case NodeType.Comment:\r\n            return doc.createComment(n.textContent);\r\n        default:\r\n            return null;\r\n    }\r\n}\r\nfunction buildNodeWithSN(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.hackCss, hackCss = _b === void 0 ? true : _b, afterAppend = options.afterAppend, cache = options.cache;\r\n    var node = buildNode(n, { doc: doc, hackCss: hackCss, cache: cache });\r\n    if (!node) {\r\n        return null;\r\n    }\r\n    if (n.rootId && mirror.getNode(n.rootId) !== doc) {\r\n        mirror.replace(n.rootId, doc);\r\n    }\r\n    if (n.type === NodeType.Document) {\r\n        doc.close();\r\n        doc.open();\r\n        if (n.compatMode === 'BackCompat' &&\r\n            n.childNodes &&\r\n            n.childNodes[0].type !== NodeType.DocumentType) {\r\n            if (n.childNodes[0].type === NodeType.Element &&\r\n                'xmlns' in n.childNodes[0].attributes &&\r\n                n.childNodes[0].attributes.xmlns === 'http://www.w3.org/1999/xhtml') {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">');\r\n            }\r\n            else {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">');\r\n            }\r\n        }\r\n        node = doc;\r\n    }\r\n    mirror.add(node, n);\r\n    if ((n.type === NodeType.Document || n.type === NodeType.Element) &&\r\n        !skipChild) {\r\n        for (var _i = 0, _c = n.childNodes; _i < _c.length; _i++) {\r\n            var childN = _c[_i];\r\n            var childNode = buildNodeWithSN(childN, {\r\n                doc: doc,\r\n                mirror: mirror,\r\n                skipChild: false,\r\n                hackCss: hackCss,\r\n                afterAppend: afterAppend,\r\n                cache: cache\r\n            });\r\n            if (!childNode) {\r\n                console.warn('Failed to rebuild', childN);\r\n                continue;\r\n            }\r\n            if (childN.isShadow && isElement(node) && node.shadowRoot) {\r\n                node.shadowRoot.appendChild(childNode);\r\n            }\r\n            else {\r\n                node.appendChild(childNode);\r\n            }\r\n            if (afterAppend) {\r\n                afterAppend(childNode, childN.id);\r\n            }\r\n        }\r\n    }\r\n    return node;\r\n}\r\nfunction visit(mirror, onVisit) {\r\n    function walk(node) {\r\n        onVisit(node);\r\n    }\r\n    for (var _i = 0, _a = mirror.getIds(); _i < _a.length; _i++) {\r\n        var id = _a[_i];\r\n        if (mirror.has(id)) {\r\n            walk(mirror.getNode(id));\r\n        }\r\n    }\r\n}\r\nfunction handleScroll(node, mirror) {\r\n    var n = mirror.getMeta(node);\r\n    if ((n === null || n === void 0 ? void 0 : n.type) !== NodeType.Element) {\r\n        return;\r\n    }\r\n    var el = node;\r\n    for (var name_3 in n.attributes) {\r\n        if (!(Object.prototype.hasOwnProperty.call(n.attributes, name_3) &&\r\n            name_3.startsWith('rr_'))) {\r\n            continue;\r\n        }\r\n        var value = n.attributes[name_3];\r\n        if (name_3 === 'rr_scrollLeft') {\r\n            el.scrollLeft = value;\r\n        }\r\n        if (name_3 === 'rr_scrollTop') {\r\n            el.scrollTop = value;\r\n        }\r\n    }\r\n}\r\nfunction rebuild(n, options) {\r\n    var doc = options.doc, onVisit = options.onVisit, _a = options.hackCss, hackCss = _a === void 0 ? true : _a, afterAppend = options.afterAppend, cache = options.cache, _b = options.mirror, mirror = _b === void 0 ? new Mirror() : _b;\r\n    var node = buildNodeWithSN(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        skipChild: false,\r\n        hackCss: hackCss,\r\n        afterAppend: afterAppend,\r\n        cache: cache\r\n    });\r\n    visit(mirror, function (visitedNode) {\r\n        if (onVisit) {\r\n            onVisit(visitedNode);\r\n        }\r\n        handleScroll(visitedNode, mirror);\r\n    });\r\n    return node;\r\n}\n\nexport { IGNORED_NODE, Mirror, NodeType, addHoverClass, buildNodeWithSN, classMatchesRegex, cleanupSnapshot, createCache, createMirror, genId, getCssRuleString, getCssRulesString, is2DCanvasBlank, isCSSImportRule, isElement, isNativeShadowDom, isShadowRoot, maskInputValue, needMaskingText, rebuild, serializeNodeWithId, snapshot, transformAttribute, visitSnapshot };\n", "import type {\n  throttleOptions,\n  listenerHandler,\n  hookR<PERSON>tter,\n  blockClass,\n  addedNodeMutation,\n  DocumentDimension,\n  IWindow,\n  DeprecatedMirror,\n  textMutation,\n} from '@rrweb/types';\nimport type { I<PERSON>ir<PERSON>r, Mirror } from 'rrweb-snapshot';\nimport { isShadowRoot, IGNORED_NODE, classMatchesRegex } from 'rrweb-snapshot';\nimport type { RRNode, RRIFrameElement } from 'rrdom';\n\nexport function on(\n  type: string,\n  fn: EventListenerOrEventListenerObject,\n  target: Document | IWindow = document,\n): listenerHandler {\n  const options = { capture: true, passive: true };\n  target.addEventListener(type, fn, options);\n  return () => target.removeEventListener(type, fn, options);\n}\n\n// https://github.com/rrweb-io/rrweb/pull/407\nconst DEPARTED_MIRROR_ACCESS_WARNING =\n  'Please stop import mirror directly. Instead of that,' +\n  '\\r\\n' +\n  'now you can use replayer.getMirror() to access the mirror instance of a replayer,' +\n  '\\r\\n' +\n  'or you can use record.mirror to access the mirror instance during recording.';\nexport let _mirror: DeprecatedMirror = {\n  map: {},\n  getId() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return -1;\n  },\n  getNode() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return null;\n  },\n  removeNodeFromMap() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n  has() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return false;\n  },\n  reset() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n};\nif (typeof window !== 'undefined' && window.Proxy && window.Reflect) {\n  _mirror = new Proxy(_mirror, {\n    get(target, prop, receiver) {\n      if (prop === 'map') {\n        console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n      }\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      return Reflect.get(target, prop, receiver);\n    },\n  });\n}\n\n// copy from underscore and modified\nexport function throttle<T>(\n  func: (arg: T) => void,\n  wait: number,\n  options: throttleOptions = {},\n) {\n  let timeout: ReturnType<typeof setTimeout> | null = null;\n  let previous = 0;\n  return function (...args: T[]) {\n    const now = Date.now();\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n    const remaining = wait - (now - previous);\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-this-alias\n    const context = this;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(() => {\n        previous = options.leading === false ? 0 : Date.now();\n        timeout = null;\n        func.apply(context, args);\n      }, remaining);\n    }\n  };\n}\n\nexport function hookSetter<T>(\n  target: T,\n  key: string | number | symbol,\n  d: PropertyDescriptor,\n  isRevoked?: boolean,\n  win = window,\n): hookResetter {\n  const original = win.Object.getOwnPropertyDescriptor(target, key);\n  win.Object.defineProperty(\n    target,\n    key,\n    isRevoked\n      ? d\n      : {\n          set(value) {\n            // put hooked setter into event loop to avoid of set latency\n            setTimeout(() => {\n              d.set!.call(this, value);\n            }, 0);\n            if (original && original.set) {\n              original.set.call(this, value);\n            }\n          },\n        },\n  );\n  return () => hookSetter(target, key, original || {}, true);\n}\n\n// copy from https://github.com/getsentry/sentry-javascript/blob/b2109071975af8bf0316d3b5b38f519bdaf5dc15/packages/utils/src/object.ts\nexport function patch(\n  source: { [key: string]: any },\n  name: string,\n  replacement: (...args: unknown[]) => unknown,\n): () => void {\n  try {\n    if (!(name in source)) {\n      return () => {\n        //\n      };\n    }\n\n    const original = source[name] as () => unknown;\n    const wrapped = replacement(original);\n\n    // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n    // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n    if (typeof wrapped === 'function') {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __rrweb_original__: {\n          enumerable: false,\n          value: original,\n        },\n      });\n    }\n\n    source[name] = wrapped;\n\n    return () => {\n      source[name] = original;\n    };\n  } catch {\n    return () => {\n      //\n    };\n    // This can throw if multiple fill happens on a global object like XMLHttpRequest\n    // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n  }\n}\n\nexport function getWindowHeight(): number {\n  return (\n    window.innerHeight ||\n    (document.documentElement && document.documentElement.clientHeight) ||\n    (document.body && document.body.clientHeight)\n  );\n}\n\nexport function getWindowWidth(): number {\n  return (\n    window.innerWidth ||\n    (document.documentElement && document.documentElement.clientWidth) ||\n    (document.body && document.body.clientWidth)\n  );\n}\n\n/**\n * Checks if the given element set to be blocked by rrweb\n * @param node - node to check\n * @param blockClass - class name to check\n * @param blockSelector - css selectors to check\n * @param checkAncestors - whether to search through parent nodes for the block class\n * @returns true/false if the node was blocked or not\n */\nexport function isBlocked(\n  node: Node | null,\n  blockClass: blockClass,\n  blockSelector: string | null,\n  checkAncestors: boolean,\n): boolean {\n  if (!node) {\n    return false;\n  }\n  const el: HTMLElement | null =\n    node.nodeType === node.ELEMENT_NODE\n      ? (node as HTMLElement)\n      : node.parentElement;\n  if (!el) return false;\n\n  if (typeof blockClass === 'string') {\n    if (el.classList.contains(blockClass)) return true;\n    if (checkAncestors && el.closest('.' + blockClass) !== null) return true;\n  } else {\n    if (classMatchesRegex(el, blockClass, checkAncestors)) return true;\n  }\n  if (blockSelector) {\n    if ((node as HTMLElement).matches(blockSelector)) return true;\n    if (checkAncestors && el.closest(blockSelector) !== null) return true;\n  }\n  return false;\n}\n\nexport function isSerialized(n: Node, mirror: Mirror): boolean {\n  return mirror.getId(n) !== -1;\n}\n\nexport function isIgnored(n: Node, mirror: Mirror): boolean {\n  // The main part of the slimDOM check happens in\n  // rrweb-snapshot::serializeNodeWithId\n  return mirror.getId(n) === IGNORED_NODE;\n}\n\nexport function isAncestorRemoved(target: Node, mirror: Mirror): boolean {\n  if (isShadowRoot(target)) {\n    return false;\n  }\n  const id = mirror.getId(target);\n  if (!mirror.has(id)) {\n    return true;\n  }\n  if (\n    target.parentNode &&\n    target.parentNode.nodeType === target.DOCUMENT_NODE\n  ) {\n    return false;\n  }\n  // if the root is not document, it means the node is not in the DOM tree anymore\n  if (!target.parentNode) {\n    return true;\n  }\n  return isAncestorRemoved(target.parentNode, mirror);\n}\n\nexport function isTouchEvent(\n  event: MouseEvent | TouchEvent,\n): event is TouchEvent {\n  return Boolean((event as TouchEvent).changedTouches);\n}\n\nexport function polyfill(win = window) {\n  if ('NodeList' in win && !win.NodeList.prototype.forEach) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    win.NodeList.prototype.forEach = (Array.prototype\n      .forEach as unknown) as NodeList['forEach'];\n  }\n\n  if ('DOMTokenList' in win && !win.DOMTokenList.prototype.forEach) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    win.DOMTokenList.prototype.forEach = (Array.prototype\n      .forEach as unknown) as DOMTokenList['forEach'];\n  }\n\n  // https://github.com/Financial-Times/polyfill-service/pull/183\n  if (!Node.prototype.contains) {\n    Node.prototype.contains = (...args: unknown[]) => {\n      let node = args[0] as Node | null;\n      if (!(0 in args)) {\n        throw new TypeError('1 argument is required');\n      }\n\n      do {\n        if (this === node) {\n          return true;\n        }\n      } while ((node = node && node.parentNode));\n\n      return false;\n    };\n  }\n}\n\ntype ResolveTree = {\n  value: addedNodeMutation;\n  children: ResolveTree[];\n  parent: ResolveTree | null;\n};\n\nexport function queueToResolveTrees(queue: addedNodeMutation[]): ResolveTree[] {\n  const queueNodeMap: Record<number, ResolveTree> = {};\n  const putIntoMap = (\n    m: addedNodeMutation,\n    parent: ResolveTree | null,\n  ): ResolveTree => {\n    const nodeInTree: ResolveTree = {\n      value: m,\n      parent,\n      children: [],\n    };\n    queueNodeMap[m.node.id] = nodeInTree;\n    return nodeInTree;\n  };\n\n  const queueNodeTrees: ResolveTree[] = [];\n  for (const mutation of queue) {\n    const { nextId, parentId } = mutation;\n    if (nextId && nextId in queueNodeMap) {\n      const nextInTree = queueNodeMap[nextId];\n      if (nextInTree.parent) {\n        const idx = nextInTree.parent.children.indexOf(nextInTree);\n        nextInTree.parent.children.splice(\n          idx,\n          0,\n          putIntoMap(mutation, nextInTree.parent),\n        );\n      } else {\n        const idx = queueNodeTrees.indexOf(nextInTree);\n        queueNodeTrees.splice(idx, 0, putIntoMap(mutation, null));\n      }\n      continue;\n    }\n    if (parentId in queueNodeMap) {\n      const parentInTree = queueNodeMap[parentId];\n      parentInTree.children.push(putIntoMap(mutation, parentInTree));\n      continue;\n    }\n    queueNodeTrees.push(putIntoMap(mutation, null));\n  }\n\n  return queueNodeTrees;\n}\n\nexport function iterateResolveTree(\n  tree: ResolveTree,\n  cb: (mutation: addedNodeMutation) => unknown,\n) {\n  cb(tree.value);\n  /**\n   * The resolve tree was designed to reflect the DOM layout,\n   * but we need append next sibling first, so we do a reverse\n   * loop here.\n   */\n  for (let i = tree.children.length - 1; i >= 0; i--) {\n    iterateResolveTree(tree.children[i], cb);\n  }\n}\n\nexport type AppendedIframe = {\n  mutationInQueue: addedNodeMutation;\n  builtNode: HTMLIFrameElement | RRIFrameElement;\n};\n\nexport function isSerializedIframe<TNode extends Node | RRNode>(\n  n: TNode,\n  mirror: IMirror<TNode>,\n): boolean {\n  return Boolean(n.nodeName === 'IFRAME' && mirror.getMeta(n));\n}\n\nexport function isSerializedStylesheet<TNode extends Node | RRNode>(\n  n: TNode,\n  mirror: IMirror<TNode>,\n): boolean {\n  return Boolean(\n    n.nodeName === 'LINK' &&\n      n.nodeType === n.ELEMENT_NODE &&\n      (n as HTMLElement).getAttribute &&\n      (n as HTMLElement).getAttribute('rel') === 'stylesheet' &&\n      mirror.getMeta(n),\n  );\n}\n\nexport function getBaseDimension(\n  node: Node,\n  rootIframe: Node,\n): DocumentDimension {\n  const frameElement = node.ownerDocument?.defaultView?.frameElement;\n  if (!frameElement || frameElement === rootIframe) {\n    return {\n      x: 0,\n      y: 0,\n      relativeScale: 1,\n      absoluteScale: 1,\n    };\n  }\n\n  const frameDimension = frameElement.getBoundingClientRect();\n  const frameBaseDimension = getBaseDimension(frameElement, rootIframe);\n  // the iframe element may have a scale transform\n  const relativeScale = frameDimension.height / frameElement.clientHeight;\n  return {\n    x:\n      frameDimension.x * frameBaseDimension.relativeScale +\n      frameBaseDimension.x,\n    y:\n      frameDimension.y * frameBaseDimension.relativeScale +\n      frameBaseDimension.y,\n    relativeScale,\n    absoluteScale: frameBaseDimension.absoluteScale * relativeScale,\n  };\n}\n\nexport function hasShadowRoot<T extends Node | RRNode>(\n  n: T,\n): n is T & { shadowRoot: ShadowRoot } {\n  return Boolean(((n as unknown) as Element)?.shadowRoot);\n}\n\nexport function getNestedRule(\n  rules: CSSRuleList,\n  position: number[],\n): CSSGroupingRule {\n  const rule = rules[position[0]] as CSSGroupingRule;\n  if (position.length === 1) {\n    return rule;\n  } else {\n    return getNestedRule(\n      (rule.cssRules[position[1]] as CSSGroupingRule).cssRules,\n      position.slice(2),\n    );\n  }\n}\n\nexport function getPositionsAndIndex(nestedIndex: number[]) {\n  const positions = [...nestedIndex];\n  const index = positions.pop();\n  return { positions, index };\n}\n\n/**\n * Returns the latest mutation in the queue for each node.\n * @param mutations - mutations The text mutations to filter.\n * @returns The filtered text mutations.\n */\nexport function uniqueTextMutations(mutations: textMutation[]): textMutation[] {\n  const idSet = new Set<number>();\n  const uniqueMutations: textMutation[] = [];\n\n  for (let i = mutations.length; i--; ) {\n    const mutation = mutations[i];\n    if (!idSet.has(mutation.id)) {\n      uniqueMutations.push(mutation);\n      idSet.add(mutation.id);\n    }\n  }\n\n  return uniqueMutations;\n}\n\nexport class StyleSheetMirror {\n  private id = 1;\n  private styleIDMap = new WeakMap<CSSStyleSheet, number>();\n  private idStyleMap = new Map<number, CSSStyleSheet>();\n\n  getId(stylesheet: CSSStyleSheet): number {\n    return this.styleIDMap.get(stylesheet) ?? -1;\n  }\n\n  has(stylesheet: CSSStyleSheet): boolean {\n    return this.styleIDMap.has(stylesheet);\n  }\n\n  /**\n   * @returns If the stylesheet is in the mirror, returns the id of the stylesheet. If not, return the new assigned id.\n   */\n  add(stylesheet: CSSStyleSheet, id?: number): number {\n    if (this.has(stylesheet)) return this.getId(stylesheet);\n    let newId: number;\n    if (id === undefined) {\n      newId = this.id++;\n    } else newId = id;\n    this.styleIDMap.set(stylesheet, newId);\n    this.idStyleMap.set(newId, stylesheet);\n    return newId;\n  }\n\n  getStyle(id: number): CSSStyleSheet | null {\n    return this.idStyleMap.get(id) || null;\n  }\n\n  reset(): void {\n    this.styleIDMap = new WeakMap();\n    this.idStyleMap = new Map();\n    this.id = 1;\n  }\n\n  generateId(): number {\n    return this.id++;\n  }\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON>, RecordPlugin, IWindow } from '@rrweb/types';\nimport { patch } from '../../../utils';\nimport { Error<PERSON>tack<PERSON>arser, StackFrame } from './error-stack-parser';\nimport { stringify } from './stringify';\n\nexport type StringifyOptions = {\n  // limit of string length\n  stringLengthLimit?: number;\n  /**\n   * limit of number of keys in an object\n   * if an object contains more keys than this limit, we would call its toString function directly\n   */\n  numOfKeysLimit: number;\n  /**\n   * limit number of depth in an object\n   * if an object is too deep, toString process may cause browser OOM\n   */\n  depthOfLimit: number;\n};\n\ntype LogRecordOptions = {\n  level?: LogLevel[];\n  lengthThreshold?: number;\n  stringifyOptions?: StringifyOptions;\n  logger?: Logger | 'console';\n};\n\nconst defaultLogOptions: LogRecordOptions = {\n  level: [\n    'assert',\n    'clear',\n    'count',\n    'countReset',\n    'debug',\n    'dir',\n    'dirxml',\n    'error',\n    'group',\n    'groupCollapsed',\n    'groupEnd',\n    'info',\n    'log',\n    'table',\n    'time',\n    'timeEnd',\n    'timeLog',\n    'trace',\n    'warn',\n  ],\n  lengthThreshold: 1000,\n  logger: 'console',\n};\n\nexport type LogData = {\n  level: LogLevel;\n  trace: string[];\n  payload: string[];\n};\n\ntype logCallback = (p: LogData) => void;\n\n/* fork from interface Console */\n// all kinds of console functions\nexport type Logger = {\n  assert?: typeof console.assert;\n  clear?: typeof console.clear;\n  count?: typeof console.count;\n  countReset?: typeof console.countReset;\n  debug?: typeof console.debug;\n  dir?: typeof console.dir;\n  dirxml?: typeof console.dirxml;\n  error?: typeof console.error;\n  group?: typeof console.group;\n  groupCollapsed?: typeof console.groupCollapsed;\n  groupEnd?: () => void;\n  info?: typeof console.info;\n  log?: typeof console.log;\n  table?: typeof console.table;\n  time?: typeof console.time;\n  timeEnd?: typeof console.timeEnd;\n  timeLog?: typeof console.timeLog;\n  trace?: typeof console.trace;\n  warn?: typeof console.warn;\n};\n\nexport type LogLevel = keyof Logger;\n\nfunction initLogObserver(\n  cb: logCallback,\n  win: IWindow, // top window or in an iframe\n  options: LogRecordOptions,\n): listenerHandler {\n  const logOptions = (options\n    ? Object.assign({}, defaultLogOptions, options)\n    : defaultLogOptions) as {\n    level: LogLevel[];\n    lengthThreshold: number;\n    stringifyOptions?: StringifyOptions;\n    logger: Logger | 'console';\n  };\n  const loggerType = logOptions.logger;\n  if (!loggerType) {\n    return () => {\n      //\n    };\n  }\n  let logger: Logger;\n  if (typeof loggerType === 'string') {\n    logger = win[loggerType];\n  } else {\n    logger = loggerType;\n  }\n  let logCount = 0;\n  const cancelHandlers: listenerHandler[] = [];\n  // add listener to thrown errors\n  if (logOptions.level.includes('error')) {\n    if (window) {\n      const errorHandler = (event: ErrorEvent) => {\n        const message = event.message,\n          error = event.error as Error;\n        const trace: string[] = ErrorStackParser.parse(\n          error,\n        ).map((stackFrame: StackFrame) => stackFrame.toString());\n        const payload = [stringify(message, logOptions.stringifyOptions)];\n        cb({\n          level: 'error',\n          trace,\n          payload,\n        });\n      };\n      window.addEventListener('error', errorHandler);\n      cancelHandlers.push(() => {\n        if (window) window.removeEventListener('error', errorHandler);\n      });\n    }\n  }\n  for (const levelType of logOptions.level) {\n    cancelHandlers.push(replace(logger, levelType));\n  }\n  return () => {\n    cancelHandlers.forEach((h) => h());\n  };\n\n  /**\n   * replace the original console function and record logs\n   * @param logger - the logger object such as Console\n   * @param level - the name of log function to be replaced\n   */\n  function replace(_logger: Logger, level: LogLevel) {\n    if (!_logger[level]) {\n      return () => {\n        //\n      };\n    }\n    // replace the logger.{level}. return a restore function\n    return patch(\n      _logger,\n      level,\n      (original: (...args: Array<unknown>) => void) => {\n        return (...args: Array<unknown>) => {\n          original.apply(this, args);\n          try {\n            const trace = ErrorStackParser.parse(new Error())\n              .map((stackFrame: StackFrame) => stackFrame.toString())\n              .splice(1); // splice(1) to omit the hijacked log function\n            const payload = args.map((s) =>\n              stringify(s, logOptions.stringifyOptions),\n            );\n            logCount++;\n            if (logCount < logOptions.lengthThreshold) {\n              cb({\n                level,\n                trace,\n                payload,\n              });\n            } else if (logCount === logOptions.lengthThreshold) {\n              // notify the user\n              cb({\n                level: 'warn',\n                trace: [],\n                payload: [\n                  stringify('The number of log records reached the threshold.'),\n                ],\n              });\n            }\n          } catch (error) {\n            original('rrweb logger error:', error, ...args);\n          }\n        };\n      },\n    );\n  }\n}\n\nexport const PLUGIN_NAME = 'rrweb/console@1';\n\nexport const getRecordConsolePlugin: (\n  options?: LogRecordOptions,\n) => RecordPlugin = (options) => ({\n  name: PLUGIN_NAME,\n  observer: initLogObserver,\n  options: options,\n});\n", "var EventType = /* @__PURE__ */ ((EventType2) => {\n  EventType2[EventType2[\"DomContentLoaded\"] = 0] = \"DomContentLoaded\";\n  EventType2[EventType2[\"Load\"] = 1] = \"Load\";\n  EventType2[EventType2[\"FullSnapshot\"] = 2] = \"FullSnapshot\";\n  EventType2[EventType2[\"IncrementalSnapshot\"] = 3] = \"IncrementalSnapshot\";\n  EventType2[EventType2[\"Meta\"] = 4] = \"Meta\";\n  EventType2[EventType2[\"Custom\"] = 5] = \"Custom\";\n  EventType2[EventType2[\"Plugin\"] = 6] = \"Plugin\";\n  return EventType2;\n})(EventType || {});\nvar IncrementalSource = /* @__PURE__ */ ((IncrementalSource2) => {\n  IncrementalSource2[IncrementalSource2[\"Mutation\"] = 0] = \"Mutation\";\n  IncrementalSource2[IncrementalSource2[\"MouseMove\"] = 1] = \"MouseMove\";\n  IncrementalSource2[IncrementalSource2[\"MouseInteraction\"] = 2] = \"MouseInteraction\";\n  IncrementalSource2[IncrementalSource2[\"Scroll\"] = 3] = \"Scroll\";\n  IncrementalSource2[IncrementalSource2[\"ViewportResize\"] = 4] = \"ViewportResize\";\n  IncrementalSource2[IncrementalSource2[\"Input\"] = 5] = \"Input\";\n  IncrementalSource2[IncrementalSource2[\"TouchMove\"] = 6] = \"TouchMove\";\n  IncrementalSource2[IncrementalSource2[\"MediaInteraction\"] = 7] = \"MediaInteraction\";\n  IncrementalSource2[IncrementalSource2[\"StyleSheetRule\"] = 8] = \"StyleSheetRule\";\n  IncrementalSource2[IncrementalSource2[\"CanvasMutation\"] = 9] = \"CanvasMutation\";\n  IncrementalSource2[IncrementalSource2[\"Font\"] = 10] = \"Font\";\n  IncrementalSource2[IncrementalSource2[\"Log\"] = 11] = \"Log\";\n  IncrementalSource2[IncrementalSource2[\"Drag\"] = 12] = \"Drag\";\n  IncrementalSource2[IncrementalSource2[\"StyleDeclaration\"] = 13] = \"StyleDeclaration\";\n  IncrementalSource2[IncrementalSource2[\"Selection\"] = 14] = \"Selection\";\n  IncrementalSource2[IncrementalSource2[\"AdoptedStyleSheet\"] = 15] = \"AdoptedStyleSheet\";\n  return IncrementalSource2;\n})(IncrementalSource || {});\nvar MouseInteractions = /* @__PURE__ */ ((MouseInteractions2) => {\n  MouseInteractions2[MouseInteractions2[\"MouseUp\"] = 0] = \"MouseUp\";\n  MouseInteractions2[MouseInteractions2[\"MouseDown\"] = 1] = \"MouseDown\";\n  MouseInteractions2[MouseInteractions2[\"Click\"] = 2] = \"Click\";\n  MouseInteractions2[MouseInteractions2[\"ContextMenu\"] = 3] = \"ContextMenu\";\n  MouseInteractions2[MouseInteractions2[\"DblClick\"] = 4] = \"DblClick\";\n  MouseInteractions2[MouseInteractions2[\"Focus\"] = 5] = \"Focus\";\n  MouseInteractions2[MouseInteractions2[\"Blur\"] = 6] = \"Blur\";\n  MouseInteractions2[MouseInteractions2[\"TouchStart\"] = 7] = \"TouchStart\";\n  MouseInteractions2[MouseInteractions2[\"TouchMove_Departed\"] = 8] = \"TouchMove_Departed\";\n  MouseInteractions2[MouseInteractions2[\"TouchEnd\"] = 9] = \"TouchEnd\";\n  MouseInteractions2[MouseInteractions2[\"TouchCancel\"] = 10] = \"TouchCancel\";\n  return MouseInteractions2;\n})(MouseInteractions || {});\nvar CanvasContext = /* @__PURE__ */ ((CanvasContext2) => {\n  CanvasContext2[CanvasContext2[\"2D\"] = 0] = \"2D\";\n  CanvasContext2[CanvasContext2[\"WebGL\"] = 1] = \"WebGL\";\n  CanvasContext2[CanvasContext2[\"WebGL2\"] = 2] = \"WebGL2\";\n  return CanvasContext2;\n})(CanvasContext || {});\nvar MediaInteractions = /* @__PURE__ */ ((MediaInteractions2) => {\n  MediaInteractions2[MediaInteractions2[\"Play\"] = 0] = \"Play\";\n  MediaInteractions2[MediaInteractions2[\"Pause\"] = 1] = \"Pause\";\n  MediaInteractions2[MediaInteractions2[\"Seeked\"] = 2] = \"Seeked\";\n  MediaInteractions2[MediaInteractions2[\"VolumeChange\"] = 3] = \"VolumeChange\";\n  MediaInteractions2[MediaInteractions2[\"RateChange\"] = 4] = \"RateChange\";\n  return MediaInteractions2;\n})(MediaInteractions || {});\nvar ReplayerEvents = /* @__PURE__ */ ((ReplayerEvents2) => {\n  ReplayerEvents2[\"Start\"] = \"start\";\n  ReplayerEvents2[\"Pause\"] = \"pause\";\n  ReplayerEvents2[\"Resume\"] = \"resume\";\n  ReplayerEvents2[\"Resize\"] = \"resize\";\n  ReplayerEvents2[\"Finish\"] = \"finish\";\n  ReplayerEvents2[\"FullsnapshotRebuilded\"] = \"fullsnapshot-rebuilded\";\n  ReplayerEvents2[\"LoadStylesheetStart\"] = \"load-stylesheet-start\";\n  ReplayerEvents2[\"LoadStylesheetEnd\"] = \"load-stylesheet-end\";\n  ReplayerEvents2[\"SkipStart\"] = \"skip-start\";\n  ReplayerEvents2[\"SkipEnd\"] = \"skip-end\";\n  ReplayerEvents2[\"MouseInteraction\"] = \"mouse-interaction\";\n  ReplayerEvents2[\"EventCast\"] = \"event-cast\";\n  ReplayerEvents2[\"CustomEvent\"] = \"custom-event\";\n  ReplayerEvents2[\"Flush\"] = \"flush\";\n  ReplayerEvents2[\"StateChange\"] = \"state-change\";\n  ReplayerEvents2[\"PlayBack\"] = \"play-back\";\n  ReplayerEvents2[\"Destroy\"] = \"destroy\";\n  return ReplayerEvents2;\n})(ReplayerEvents || {});\nexport {\n  CanvasContext,\n  EventType,\n  IncrementalSource,\n  MediaInteractions,\n  MouseInteractions,\n  ReplayerEvents\n};\n//# sourceMappingURL=types.js.map\n", "import { LogLevel, LogData, PLUGIN_NAME } from '../record';\nimport type { eventWithTime } from '@rrweb/types';\nimport { EventType, IncrementalSource } from '@rrweb/types';\nimport type { ReplayPlugin } from '../../../types';\n\n/**\n * define an interface to replay log records\n * (data: logData) => void> function to display the log data\n */\ntype ReplayLogger = Partial<Record<LogLevel, (data: LogData) => void>>;\n\ntype LogReplayConfig = {\n  level?: LogLevel[];\n  replayLogger?: ReplayLogger;\n};\n\nconst ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\ntype PatchedConsoleLog = {\n  [ORIGINAL_ATTRIBUTE_NAME]: typeof console.log;\n};\n\nconst defaultLogConfig: LogReplayConfig = {\n  level: [\n    'assert',\n    'clear',\n    'count',\n    'countReset',\n    'debug',\n    'dir',\n    'dirxml',\n    'error',\n    'group',\n    'groupCollapsed',\n    'groupEnd',\n    'info',\n    'log',\n    'table',\n    'time',\n    'timeEnd',\n    'timeLog',\n    'trace',\n    'warn',\n  ],\n  replayLogger: undefined,\n};\n\nclass LogReplayPlugin {\n  private config: LogReplayConfig;\n\n  constructor(config?: LogReplayConfig) {\n    this.config = Object.assign(defaultLogConfig, config);\n  }\n\n  /**\n   * generate a console log replayer which implement the interface ReplayLogger\n   */\n  public getConsoleLogger(): ReplayLogger {\n    const replayLogger: ReplayLogger = {};\n    for (const level of this.config.level!) {\n      if (level === 'trace') {\n        replayLogger[level] = (data: LogData) => {\n          const logger = ((console.log as unknown) as PatchedConsoleLog)[\n            ORIGINAL_ATTRIBUTE_NAME\n          ]\n            ? ((console.log as unknown) as PatchedConsoleLog)[\n                ORIGINAL_ATTRIBUTE_NAME\n              ]\n            : console.log;\n          logger(\n            ...data.payload.map((s) => JSON.parse(s) as object),\n            this.formatMessage(data),\n          );\n        };\n      } else {\n        replayLogger[level] = (data: LogData) => {\n          const logger = ((console[level] as unknown) as PatchedConsoleLog)[\n            ORIGINAL_ATTRIBUTE_NAME\n          ]\n            ? ((console[level] as unknown) as PatchedConsoleLog)[\n                ORIGINAL_ATTRIBUTE_NAME\n              ]\n            : console[level];\n          logger(\n            ...data.payload.map((s) => JSON.parse(s) as object),\n            this.formatMessage(data),\n          );\n        };\n      }\n    }\n    return replayLogger;\n  }\n\n  /**\n   * format the trace data to a string\n   * @param data - the log data\n   */\n  private formatMessage(data: LogData): string {\n    if (data.trace.length === 0) {\n      return '';\n    }\n    const stackPrefix = '\\n\\tat ';\n    let result = stackPrefix;\n    result += data.trace.join(stackPrefix);\n    return result;\n  }\n}\n\nexport const getReplayConsolePlugin: (\n  options?: LogReplayConfig,\n) => ReplayPlugin = (options) => {\n  const replayLogger =\n    options?.replayLogger || new LogReplayPlugin(options).getConsoleLogger();\n\n  return {\n    handler(event: eventWithTime, _isSync, context) {\n      let logData: LogData | null = null;\n      if (\n        event.type === EventType.IncrementalSnapshot &&\n        event.data.source === (IncrementalSource.Log as IncrementalSource)\n      ) {\n        logData = (event.data as unknown) as LogData;\n      } else if (\n        event.type === EventType.Plugin &&\n        event.data.plugin === PLUGIN_NAME\n      ) {\n        logData = event.data.payload as LogData;\n      }\n      if (logData) {\n        try {\n          if (typeof replayLogger[logData.level] === 'function') {\n            replayLogger[logData.level]!(logData);\n          }\n        } catch (error) {\n          if (context.replayer.config.showWarning) {\n            console.warn(error);\n          }\n        }\n      }\n    },\n  };\n};\n"], "names": ["k", "L", "n", "i", "c"], "mappings": ";;;IAAA,IAAIA,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAS,CAAC,EAAEA,GAAC,GAAGA,GAAC,CAAC,EAAE,CAAC,CAAC;;ICAqC,MAAM,CAAC,CAAC,CAAC;AACrO;AACA,4EAA4E,CAAC,CAAQ,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,OAAO,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;ACF2xB,UAAC,WAAW,CAAC,iBAAiB;;ICA3uC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;ICAnpD,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,OAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7uB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAa,MAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,GAAGC,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAGC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAGD,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAGE,WAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;"}