var rrwebReplay=function(ae){"use strict";var I;(function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"})(I||(I={}));function dt(e){return e.nodeType===e.ELEMENT_NODE}var De=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(t){var n;if(!t)return-1;var o=(n=this.getMeta(t))===null||n===void 0?void 0:n.id;return o??-1},e.prototype.getNode=function(t){return this.idNodeMap.get(t)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(t){return this.nodeMetaMap.get(t)||null},e.prototype.removeNodeFromMap=function(t){var n=this,o=this.getId(t);this.idNodeMap.delete(o),t.childNodes&&t.childNodes.forEach(function(i){return n.removeNodeFromMap(i)})},e.prototype.has=function(t){return this.idNodeMap.has(t)},e.prototype.hasNode=function(t){return this.nodeMetaMap.has(t)},e.prototype.add=function(t,n){var o=n.id;this.idNodeMap.set(o,t),this.nodeMetaMap.set(t,n)},e.prototype.replace=function(t,n){var o=this.getNode(t);if(o){var i=this.nodeMetaMap.get(o);i&&this.nodeMetaMap.set(n,i)}this.idNodeMap.set(t,n)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function ht(){return new De}var Me=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g;function pt(e,t){t===void 0&&(t={});var n=1,o=1;function i(g){var h=g.match(/\n/g);h&&(n+=h.length);var b=g.lastIndexOf(`
`);o=b===-1?o+g.length:g.length-b}function l(){var g={line:n,column:o};return function(h){return h.position=new r(g),N(),h}}var r=function(){function g(h){this.start=h,this.end={line:n,column:o},this.source=t.source}return g}();r.prototype.content=e;var s=[];function a(g){var h=new Error("".concat(t.source||"",":").concat(n,":").concat(o,": ").concat(g));if(h.reason=g,h.filename=t.source,h.line=n,h.column=o,h.source=e,t.silent)s.push(h);else throw h}function c(){var g=m();return{type:"stylesheet",stylesheet:{source:t.source,rules:g,parsingErrors:s}}}function u(){return f(/^{\s*/)}function d(){return f(/^}/)}function m(){var g,h=[];for(N(),E(h);e.length&&e.charAt(0)!=="}"&&(g=k()||J());)g!==!1&&(h.push(g),E(h));return h}function f(g){var h=g.exec(e);if(h){var b=h[0];return i(b),e=e.slice(b.length),h}}function N(){f(/^\s*/)}function E(g){g===void 0&&(g=[]);for(var h;h=v();)h!==!1&&g.push(h),h=v();return g}function v(){var g=l();if(!(e.charAt(0)!=="/"||e.charAt(1)!=="*")){for(var h=2;e.charAt(h)!==""&&(e.charAt(h)!=="*"||e.charAt(h+1)!=="/");)++h;if(h+=2,e.charAt(h-1)==="")return a("End of comment missing");var b=e.slice(2,h-2);return o+=2,i(b),e=e.slice(h),o+=2,g({type:"comment",comment:b})}}function p(){var g=f(/^([^{]+)/);if(g)return B(g[0]).replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g,"").replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,function(h){return h.replace(/,/g,"\u200C")}).split(/\s*(?![^(]*\)),\s*/).map(function(h){return h.replace(/\u200C/g,",")})}function y(){var g=l(),h=f(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);if(h){var b=B(h[0]);if(!f(/^:\s*/))return a("property missing ':'");var x=f(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/),_=g({type:"declaration",property:b.replace(Me,""),value:x?B(x[0]).replace(Me,""):""});return f(/^[;\s]*/),_}}function T(){var g=[];if(!u())return a("missing '{'");E(g);for(var h;h=y();)h!==!1&&(g.push(h),E(g)),h=y();return d()?g:a("missing '}'")}function S(){for(var g,h=[],b=l();g=f(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)h.push(g[1]),f(/^,\s*/);if(h.length)return b({type:"keyframe",values:h,declarations:T()})}function A(){var g=l(),h=f(/^@([-\w]+)?keyframes\s*/);if(h){var b=h[1];if(h=f(/^([-\w]+)\s*/),!h)return a("@keyframes missing name");var x=h[1];if(!u())return a("@keyframes missing '{'");for(var _,se=E();_=S();)se.push(_),se=se.concat(E());return d()?g({type:"keyframes",name:x,vendor:b,keyframes:se}):a("@keyframes missing '}'")}}function O(){var g=l(),h=f(/^@supports *([^{]+)/);if(h){var b=B(h[1]);if(!u())return a("@supports missing '{'");var x=E().concat(m());return d()?g({type:"supports",supports:b,rules:x}):a("@supports missing '}'")}}function $(){var g=l(),h=f(/^@host\s*/);if(h){if(!u())return a("@host missing '{'");var b=E().concat(m());return d()?g({type:"host",rules:b}):a("@host missing '}'")}}function q(){var g=l(),h=f(/^@media *([^{]+)/);if(h){var b=B(h[1]);if(!u())return a("@media missing '{'");var x=E().concat(m());return d()?g({type:"media",media:b,rules:x}):a("@media missing '}'")}}function Ee(){var g=l(),h=f(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);if(h)return g({type:"custom-media",name:B(h[1]),media:B(h[2])})}function Te(){var g=l(),h=f(/^@page */);if(h){var b=p()||[];if(!u())return a("@page missing '{'");for(var x=E(),_;_=y();)x.push(_),x=x.concat(E());return d()?g({type:"page",selectors:b,declarations:x}):a("@page missing '}'")}}function Se(){var g=l(),h=f(/^@([-\w]+)?document *([^{]+)/);if(h){var b=B(h[1]),x=B(h[2]);if(!u())return a("@document missing '{'");var _=E().concat(m());return d()?g({type:"document",document:x,vendor:b,rules:_}):a("@document missing '}'")}}function Z(){var g=l(),h=f(/^@font-face\s*/);if(h){if(!u())return a("@font-face missing '{'");for(var b=E(),x;x=y();)b.push(x),b=b.concat(E());return d()?g({type:"font-face",declarations:b}):a("@font-face missing '}'")}}var ie=Y("import"),we=Y("charset"),be=Y("namespace");function Y(g){var h=new RegExp("^@"+g+"\\s*([^;]+);");return function(){var b=l(),x=f(h);if(x){var _={type:g};return _[g]=x[1].trim(),b(_)}}}function k(){if(e[0]==="@")return A()||q()||Ee()||O()||ie()||we()||be()||Se()||Te()||$()||Z()}function J(){var g=l(),h=p();return h?(E(),g({type:"rule",selectors:h,declarations:T()})):a("selector missing")}return le(c())}function B(e){return e?e.replace(/^\s+|\s+$/g,""):""}function le(e,t){for(var n=e&&typeof e.type=="string",o=n?e:t,i=0,l=Object.keys(e);i<l.length;i++){var r=l[i],s=e[r];Array.isArray(s)?s.forEach(function(a){le(a,o)}):s&&typeof s=="object"&&le(s,o)}return n&&Object.defineProperty(e,"parent",{configurable:!0,writable:!0,enumerable:!1,value:t||null}),e}var Ce={script:"noscript",altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function mt(e){var t=Ce[e.tagName]?Ce[e.tagName]:e.tagName;return t==="link"&&e.attributes._cssText&&(t="style"),t}function ft(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}var Re=/([^\\]):hover/,gt=new RegExp(Re.source,"g");function xe(e,t){var n=t?.stylesWithHoverClass.get(e);if(n)return n;var o=pt(e,{silent:!0});if(!o.stylesheet)return e;var i=[];if(o.stylesheet.rules.forEach(function(s){"selectors"in s&&(s.selectors||[]).forEach(function(a){Re.test(a)&&i.push(a)})}),i.length===0)return e;var l=new RegExp(i.filter(function(s,a){return i.indexOf(s)===a}).sort(function(s,a){return a.length-s.length}).map(function(s){return ft(s)}).join("|"),"g"),r=e.replace(l,function(s){var a=s.replace(gt,"$1.\\:hover");return"".concat(s,", ").concat(a)});return t?.stylesWithHoverClass.set(e,r),r}function Ae(){var e=new Map;return{stylesWithHoverClass:e}}function yt(e,t){var n=t.doc,o=t.hackCss,i=t.cache;switch(e.type){case I.Document:return n.implementation.createDocument(null,"",null);case I.DocumentType:return n.implementation.createDocumentType(e.name||"html",e.publicId,e.systemId);case I.Element:{var l=mt(e),r;e.isSVG?r=n.createElementNS("http://www.w3.org/2000/svg",l):r=n.createElement(l);var s={};for(var a in e.attributes)if(Object.prototype.hasOwnProperty.call(e.attributes,a)){var c=e.attributes[a];if(!(l==="option"&&a==="selected"&&c===!1)){if(c===!0&&(c=""),a.startsWith("rr_")){s[a]=c;continue}var u=l==="textarea"&&a==="value",d=l==="style"&&a==="_cssText";if(d&&o&&typeof c=="string"&&(c=xe(c,i)),(u||d)&&typeof c=="string"){for(var m=n.createTextNode(c),f=0,N=Array.from(r.childNodes);f<N.length;f++){var E=N[f];E.nodeType===r.TEXT_NODE&&r.removeChild(E)}r.appendChild(m);continue}try{if(e.isSVG&&a==="xlink:href")r.setAttributeNS("http://www.w3.org/1999/xlink",a,c.toString());else if(a==="onload"||a==="onclick"||a.substring(0,7)==="onmouse")r.setAttribute("_"+a,c.toString());else if(l==="meta"&&e.attributes["http-equiv"]==="Content-Security-Policy"&&a==="content"){r.setAttribute("csp-content",c.toString());continue}else l==="link"&&e.attributes.rel==="preload"&&e.attributes.as==="script"||l==="link"&&e.attributes.rel==="prefetch"&&typeof e.attributes.href=="string"&&e.attributes.href.endsWith(".js")||(l==="img"&&e.attributes.srcset&&e.attributes.rr_dataURL?r.setAttribute("rrweb-original-srcset",e.attributes.srcset):r.setAttribute(a,c.toString()))}catch{}}}var v=function(y){var T=s[y];if(l==="canvas"&&y==="rr_dataURL"){var S=document.createElement("img");S.onload=function(){var O=r.getContext("2d");O&&O.drawImage(S,0,0,S.width,S.height)},S.src=T.toString(),r.RRNodeType&&(r.rr_dataURL=T.toString())}else if(l==="img"&&y==="rr_dataURL"){var A=r;A.currentSrc.startsWith("data:")||(A.setAttribute("rrweb-original-src",e.attributes.src),A.src=T.toString())}if(y==="rr_width")r.style.width=T.toString();else if(y==="rr_height")r.style.height=T.toString();else if(y==="rr_mediaCurrentTime"&&typeof T=="number")r.currentTime=T;else if(y==="rr_mediaState")switch(T){case"played":r.play().catch(function(O){return console.warn("media playback error",O)});break;case"paused":r.pause();break}};for(var p in s)v(p);if(e.isShadowHost)if(!r.shadowRoot)r.attachShadow({mode:"open"});else for(;r.shadowRoot.firstChild;)r.shadowRoot.removeChild(r.shadowRoot.firstChild);return r}case I.Text:return n.createTextNode(e.isStyle&&o?xe(e.textContent,i):e.textContent);case I.CDATA:return n.createCDATASection(e.textContent);case I.Comment:return n.createComment(e.textContent);default:return null}}function H(e,t){var n=t.doc,o=t.mirror,i=t.skipChild,l=i===void 0?!1:i,r=t.hackCss,s=r===void 0?!0:r,a=t.afterAppend,c=t.cache,u=yt(e,{doc:n,hackCss:s,cache:c});if(!u)return null;if(e.rootId&&o.getNode(e.rootId)!==n&&o.replace(e.rootId,n),e.type===I.Document&&(n.close(),n.open(),e.compatMode==="BackCompat"&&e.childNodes&&e.childNodes[0].type!==I.DocumentType&&(e.childNodes[0].type===I.Element&&"xmlns"in e.childNodes[0].attributes&&e.childNodes[0].attributes.xmlns==="http://www.w3.org/1999/xhtml"?n.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'):n.write('<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">')),u=n),o.add(u,e),(e.type===I.Document||e.type===I.Element)&&!l)for(var d=0,m=e.childNodes;d<m.length;d++){var f=m[d],N=H(f,{doc:n,mirror:o,skipChild:!1,hackCss:s,afterAppend:a,cache:c});if(!N){console.warn("Failed to rebuild",f);continue}f.isShadow&&dt(u)&&u.shadowRoot?u.shadowRoot.appendChild(N):u.appendChild(N),a&&a(N,f.id)}return u}function vt(e,t){function n(r){t(r)}for(var o=0,i=e.getIds();o<i.length;o++){var l=i[o];e.has(l)&&n(e.getNode(l))}}function Nt(e,t){var n=t.getMeta(e);if(n?.type===I.Element){var o=e;for(var i in n.attributes)if(Object.prototype.hasOwnProperty.call(n.attributes,i)&&i.startsWith("rr_")){var l=n.attributes[i];i==="rr_scrollLeft"&&(o.scrollLeft=l),i==="rr_scrollTop"&&(o.scrollTop=l)}}}function Et(e,t){var n=t.doc,o=t.onVisit,i=t.hackCss,l=i===void 0?!0:i,r=t.afterAppend,s=t.cache,a=t.mirror,c=a===void 0?new De:a,u=H(e,{doc:n,mirror:c,skipChild:!1,hackCss:l,afterAppend:r,cache:s});return vt(c,function(d){o&&o(d),Nt(d,c)}),u}var w;(function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"})(w||(w={}));var Tt=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(t){var n;if(!t)return-1;var o=(n=this.getMeta(t))===null||n===void 0?void 0:n.id;return o??-1},e.prototype.getNode=function(t){return this.idNodeMap.get(t)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(t){return this.nodeMetaMap.get(t)||null},e.prototype.removeNodeFromMap=function(t){var n=this,o=this.getId(t);this.idNodeMap.delete(o),t.childNodes&&t.childNodes.forEach(function(i){return n.removeNodeFromMap(i)})},e.prototype.has=function(t){return this.idNodeMap.has(t)},e.prototype.hasNode=function(t){return this.nodeMetaMap.has(t)},e.prototype.add=function(t,n){var o=n.id;this.idNodeMap.set(o,t),this.nodeMetaMap.set(t,n)},e.prototype.replace=function(t,n){var o=this.getNode(t);if(o){var i=this.nodeMetaMap.get(o);i&&this.nodeMetaMap.set(n,i)}this.idNodeMap.set(t,n)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function St(){return new Tt}function wt(e){const t={},n=/;(?![^(]*\))/g,o=/:(.+)/,i=/\/\*.*?\*\//g;return e.replace(i,"").split(n).forEach(function(l){if(l){const r=l.split(o);r.length>1&&(t[ce(r[0].trim())]=r[1].trim())}}),t}function Ie(e){const t=[];for(const n in e){const o=e[n];if(typeof o!="string")continue;const i=Ct(n);t.push(`${i}: ${o};`)}return t.join(" ")}const bt=/-([a-z])/g,Dt=/^--[a-zA-Z0-9-]+$/,ce=e=>Dt.test(e)?e:e.replace(bt,(t,n)=>n?n.toUpperCase():""),Mt=/\B([A-Z])/g,Ct=e=>e.replace(Mt,"-$1").toLowerCase();class P{constructor(...t){this.childNodes=[],this.parentElement=null,this.parentNode=null,this.ELEMENT_NODE=R.ELEMENT_NODE,this.TEXT_NODE=R.TEXT_NODE}get firstChild(){return this.childNodes[0]||null}get lastChild(){return this.childNodes[this.childNodes.length-1]||null}get nextSibling(){const t=this.parentNode;if(!t)return null;const n=t.childNodes,o=n.indexOf(this);return n[o+1]||null}contains(t){if(t===this)return!0;for(const n of this.childNodes)if(n.contains(t))return!0;return!1}appendChild(t){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(t,n){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(t){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}function Rt(e){return class ut extends e{constructor(){super(...arguments),this.nodeType=R.DOCUMENT_NODE,this.nodeName="#document",this.compatMode="CSS1Compat",this.RRNodeType=w.Document,this.textContent=null}get documentElement(){return this.childNodes.find(n=>n.RRNodeType===w.Element&&n.tagName==="HTML")||null}get body(){var n;return((n=this.documentElement)===null||n===void 0?void 0:n.childNodes.find(o=>o.RRNodeType===w.Element&&o.tagName==="BODY"))||null}get head(){var n;return((n=this.documentElement)===null||n===void 0?void 0:n.childNodes.find(o=>o.RRNodeType===w.Element&&o.tagName==="HEAD"))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(n){const o=n.RRNodeType;if((o===w.Element||o===w.DocumentType)&&this.childNodes.some(i=>i.RRNodeType===o))throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${o===w.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);return n.parentElement=null,n.parentNode=this,this.childNodes.push(n),n}insertBefore(n,o){const i=n.RRNodeType;if((i===w.Element||i===w.DocumentType)&&this.childNodes.some(r=>r.RRNodeType===i))throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${i===w.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);if(o===null)return this.appendChild(n);const l=this.childNodes.indexOf(o);if(l==-1)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(l,0,n),n.parentElement=null,n.parentNode=this,n}removeChild(n){const o=this.childNodes.indexOf(n);if(o===-1)throw new Error("Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(o,1),n.parentElement=null,n.parentNode=null,n}open(){this.childNodes=[]}close(){}write(n){let o;if(n==='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'?o="-//W3C//DTD XHTML 1.0 Transitional//EN":n==='<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">'&&(o="-//W3C//DTD HTML 4.0 Transitional//EN"),o){const i=this.createDocumentType("html",o,"");this.open(),this.appendChild(i)}}createDocument(n,o,i){return new ut}createDocumentType(n,o,i){const l=new(Oe(P))(n,o,i);return l.ownerDocument=this,l}createElement(n){const o=new(ke(P))(n);return o.ownerDocument=this,o}createElementNS(n,o){return this.createElement(o)}createTextNode(n){const o=new(Le(P))(n);return o.ownerDocument=this,o}createComment(n){const o=new(_e(P))(n);return o.ownerDocument=this,o}createCDATASection(n){const o=new(Pe(P))(n);return o.ownerDocument=this,o}toString(){return"RRDocument"}}}function Oe(e){return class extends e{constructor(t,n,o){super(),this.nodeType=R.DOCUMENT_TYPE_NODE,this.RRNodeType=w.DocumentType,this.textContent=null,this.name=t,this.publicId=n,this.systemId=o,this.nodeName=t}toString(){return"RRDocumentType"}}}function ke(e){return class extends e{constructor(t){super(),this.nodeType=R.ELEMENT_NODE,this.RRNodeType=w.Element,this.attributes={},this.shadowRoot=null,this.tagName=t.toUpperCase(),this.nodeName=t.toUpperCase()}get textContent(){let t="";return this.childNodes.forEach(n=>t+=n.textContent),t}set textContent(t){this.childNodes=[this.ownerDocument.createTextNode(t)]}get classList(){return new At(this.attributes.class,t=>{this.attributes.class=t})}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const t=this.attributes.style?wt(this.attributes.style):{},n=/\B([A-Z])/g;return t.setProperty=(o,i,l)=>{if(n.test(o))return;const r=ce(o);i?t[r]=i:delete t[r],l==="important"&&(t[r]+=" !important"),this.attributes.style=Ie(t)},t.removeProperty=o=>{if(n.test(o))return"";const i=ce(o),l=t[i]||"";return delete t[i],this.attributes.style=Ie(t),l},t}getAttribute(t){return this.attributes[t]||null}setAttribute(t,n){this.attributes[t]=n}setAttributeNS(t,n,o){this.setAttribute(n,o)}removeAttribute(t){delete this.attributes[t]}appendChild(t){return this.childNodes.push(t),t.parentNode=this,t.parentElement=this,t}insertBefore(t,n){if(n===null)return this.appendChild(t);const o=this.childNodes.indexOf(n);if(o==-1)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(o,0,t),t.parentElement=this,t.parentNode=this,t}removeChild(t){const n=this.childNodes.indexOf(t);if(n===-1)throw new Error("Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(n,1),t.parentElement=null,t.parentNode=null,t}attachShadow(t){const n=this.ownerDocument.createElement("SHADOWROOT");return this.shadowRoot=n,n}dispatchEvent(t){return!0}toString(){let t="";for(const n in this.attributes)t+=`${n}="${this.attributes[n]}" `;return`${this.tagName} ${t}`}}}function xt(e){return class extends e{attachShadow(t){throw new Error("RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow")}play(){this.paused=!1}pause(){this.paused=!0}}}function Le(e){return class extends e{constructor(t){super(),this.nodeType=R.TEXT_NODE,this.nodeName="#text",this.RRNodeType=w.Text,this.data=t}get textContent(){return this.data}set textContent(t){this.data=t}toString(){return`RRText text=${JSON.stringify(this.data)}`}}}function _e(e){return class extends e{constructor(t){super(),this.nodeType=R.COMMENT_NODE,this.nodeName="#comment",this.RRNodeType=w.Comment,this.data=t}get textContent(){return this.data}set textContent(t){this.data=t}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}}function Pe(e){return class extends e{constructor(t){super(),this.nodeName="#cdata-section",this.nodeType=R.CDATA_SECTION_NODE,this.RRNodeType=w.CDATA,this.data=t}get textContent(){return this.data}set textContent(t){this.data=t}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}}class At{constructor(t,n){if(this.classes=[],this.add=(...o)=>{for(const i of o){const l=String(i);this.classes.indexOf(l)>=0||this.classes.push(l)}this.onChange&&this.onChange(this.classes.join(" "))},this.remove=(...o)=>{this.classes=this.classes.filter(i=>o.indexOf(i)===-1),this.onChange&&this.onChange(this.classes.join(" "))},t){const o=t.trim().split(/\s+/);this.classes.push(...o)}this.onChange=n}}var R;(function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"})(R||(R={}));const ue={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"},It={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function F(e,t,n,o){const i=e.childNodes,l=t.childNodes;o=o||t.mirror||t.ownerDocument.mirror,(i.length>0||l.length>0)&&Fe(Array.from(i),l,e,n,o);let r=null,s=null;switch(t.RRNodeType){case w.Document:{s=t.scrollData;break}case w.Element:{const a=e,c=t;switch(Ot(a,c,o),s=c.scrollData,r=c.inputData,c.tagName){case"AUDIO":case"VIDEO":{const u=e,d=c;d.paused!==void 0&&(d.paused?u.pause():u.play()),d.muted!==void 0&&(u.muted=d.muted),d.volume!==void 0&&(u.volume=d.volume),d.currentTime!==void 0&&(u.currentTime=d.currentTime),d.playbackRate!==void 0&&(u.playbackRate=d.playbackRate);break}case"CANVAS":{const u=t;if(u.rr_dataURL!==null){const d=document.createElement("img");d.onload=()=>{const m=a.getContext("2d");m&&m.drawImage(d,0,0,d.width,d.height)},d.src=u.rr_dataURL}u.canvasMutations.forEach(d=>n.applyCanvas(d.event,d.mutation,e))}break;case"STYLE":{const u=a.sheet;u&&t.rules.forEach(d=>n.applyStyleSheetMutation(d,u))}break}if(c.shadowRoot){a.shadowRoot||a.attachShadow({mode:"open"});const u=a.shadowRoot.childNodes,d=c.shadowRoot.childNodes;(u.length>0||d.length>0)&&Fe(Array.from(u),d,a.shadowRoot,n,o)}break}case w.Text:case w.Comment:case w.CDATA:e.textContent!==t.data&&(e.textContent=t.data);break}if(s&&n.applyScroll(s,!0),r&&n.applyInput(r),t.nodeName==="IFRAME"){const a=e.contentDocument,c=t;if(a){const u=o.getMeta(c.contentDocument);u&&n.mirror.add(a,Object.assign({},u)),F(a,c.contentDocument,n,o)}}}function Ot(e,t,n){const o=e.attributes,i=t.attributes;for(const l in i){const r=i[l],s=n.getMeta(t);if(s&&"isSVG"in s&&s.isSVG&&ue[l])e.setAttributeNS(ue[l],l,r);else if(t.tagName==="CANVAS"&&l==="rr_dataURL"){const a=document.createElement("img");a.src=r,a.onload=()=>{const c=e.getContext("2d");c&&c.drawImage(a,0,0,a.width,a.height)}}else e.setAttribute(l,r)}for(const{name:l}of Array.from(o))l in i||e.removeAttribute(l);t.scrollLeft&&(e.scrollLeft=t.scrollLeft),t.scrollTop&&(e.scrollTop=t.scrollTop)}function Fe(e,t,n,o,i){var l;let r=0,s=e.length-1,a=0,c=t.length-1,u=e[r],d=e[s],m=t[a],f=t[c],N,E;for(;r<=s&&a<=c;){const v=o.mirror.getId(u),p=o.mirror.getId(d),y=i.getId(m),T=i.getId(f);if(u===void 0)u=e[++r];else if(d===void 0)d=e[--s];else if(v!==-1&&v===y)F(u,m,o,i),u=e[++r],m=t[++a];else if(p!==-1&&p===T)F(d,f,o,i),d=e[--s],f=t[--c];else if(v!==-1&&v===T)n.insertBefore(u,d.nextSibling),F(u,f,o,i),u=e[++r],f=t[--c];else if(p!==-1&&p===y)n.insertBefore(d,u),F(d,m,o,i),d=e[--s],m=t[++a];else{if(!N){N={};for(let S=r;S<=s;S++){const A=e[S];A&&o.mirror.hasNode(A)&&(N[o.mirror.getId(A)]=S)}}if(E=N[i.getId(m)],E){const S=e[E];n.insertBefore(S,u),F(S,m,o,i),e[E]=void 0}else{const S=de(m,o.mirror,i);n.nodeName==="#document"&&((l=o.mirror.getMeta(S))===null||l===void 0?void 0:l.type)===w.Element&&n.documentElement&&(n.removeChild(n.documentElement),e[r]=void 0,u=void 0),n.insertBefore(S,u||null),F(S,m,o,i)}m=t[++a]}}if(r>s){const v=t[c+1];let p=null;for(v&&n.childNodes.forEach(y=>{o.mirror.getId(y)===i.getId(v)&&(p=y)});a<=c;++a){const y=de(t[a],o.mirror,i);n.insertBefore(y,p),F(y,t[a],o,i)}}else if(a>c)for(;r<=s;r++){const v=e[r];v&&(n.removeChild(v),o.mirror.removeNodeFromMap(v))}}function de(e,t,n){const o=n.getId(e),i=n.getMeta(e);let l=null;if(o>-1&&(l=t.getNode(o)),l!==null)return l;switch(e.RRNodeType){case w.Document:l=new Document;break;case w.DocumentType:l=document.implementation.createDocumentType(e.name,e.publicId,e.systemId);break;case w.Element:{let r=e.tagName.toLowerCase();r=It[r]||r,i&&"isSVG"in i&&i?.isSVG?l=document.createElementNS(ue.svg,r):l=document.createElement(e.tagName);break}case w.Text:l=document.createTextNode(e.data);break;case w.Comment:l=document.createComment(e.data);break;case w.CDATA:l=document.createCDATASection(e.data);break}return i&&t.add(l,Object.assign({},i)),l}class W extends Rt(P){constructor(t){super(),this.UNSERIALIZED_STARTING_ID=-2,this._unserializedId=this.UNSERIALIZED_STARTING_ID,this.mirror=Gt(),this.scrollData=null,t&&(this.mirror=t)}get unserializedId(){return this._unserializedId--}createDocument(t,n,o){return new W}createDocumentType(t,n,o){const i=new kt(t,n,o);return i.ownerDocument=this,i}createElement(t){const n=t.toUpperCase();let o;switch(n){case"AUDIO":case"VIDEO":o=new Lt(n);break;case"IFRAME":o=new Ft(n,this.mirror);break;case"CANVAS":o=new _t(n);break;case"STYLE":o=new Pt(n);break;default:o=new z(n);break}return o.ownerDocument=this,o}createComment(t){const n=new Ut(t);return n.ownerDocument=this,n}createCDATASection(t){const n=new $t(t);return n.ownerDocument=this,n}createTextNode(t){const n=new Bt(t);return n.ownerDocument=this,n}destroyTree(){this.childNodes=[],this.mirror.reset()}open(){super.open(),this._unserializedId=this.UNSERIALIZED_STARTING_ID}}const kt=Oe(P);class z extends ke(P){constructor(){super(...arguments),this.inputData=null,this.scrollData=null}}class Lt extends xt(z){}class _t extends z{constructor(){super(...arguments),this.rr_dataURL=null,this.canvasMutations=[]}getContext(){return null}}class Pt extends z{constructor(){super(...arguments),this.rules=[]}}class Ft extends z{constructor(t,n){super(t),this.contentDocument=new W,this.contentDocument.mirror=n}}const Bt=Le(P),Ut=_e(P),$t=Pe(P);function Wt(e){return e instanceof HTMLFormElement?"FORM":e.tagName.toUpperCase()}function Be(e,t,n,o){let i;switch(e.nodeType){case R.DOCUMENT_NODE:o&&o.nodeName==="IFRAME"?i=o.contentDocument:(i=t,i.compatMode=e.compatMode);break;case R.DOCUMENT_TYPE_NODE:{const r=e;i=t.createDocumentType(r.name,r.publicId,r.systemId);break}case R.ELEMENT_NODE:{const r=e,s=Wt(r);i=t.createElement(s);const a=i;for(const{name:c,value:u}of Array.from(r.attributes))a.attributes[c]=u;r.scrollLeft&&(a.scrollLeft=r.scrollLeft),r.scrollTop&&(a.scrollTop=r.scrollTop);break}case R.TEXT_NODE:i=t.createTextNode(e.textContent||"");break;case R.CDATA_SECTION_NODE:i=t.createCDATASection(e.data);break;case R.COMMENT_NODE:i=t.createComment(e.textContent||"");break;case R.DOCUMENT_FRAGMENT_NODE:i=o.attachShadow({mode:"open"});break;default:return null}let l=n.getMeta(e);return t instanceof W&&(l||(l=Ue(i,t.unserializedId),n.add(e,l)),t.mirror.add(i,Object.assign({},l))),i}function Vt(e,t=St(),n=new W){function o(i,l){const r=Be(i,n,t,l);if(r!==null)if(l?.nodeName!=="IFRAME"&&i.nodeType!==R.DOCUMENT_FRAGMENT_NODE&&(l?.appendChild(r),r.parentNode=l,r.parentElement=l),i.nodeName==="IFRAME"){const s=i.contentDocument;s&&o(s,r)}else(i.nodeType===R.DOCUMENT_NODE||i.nodeType===R.ELEMENT_NODE||i.nodeType===R.DOCUMENT_FRAGMENT_NODE)&&(i.nodeType===R.ELEMENT_NODE&&i.shadowRoot&&o(i.shadowRoot,r),i.childNodes.forEach(s=>o(s,r)))}return o(e,null),n}function Gt(){return new jt}class jt{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(t){var n;if(!t)return-1;const o=(n=this.getMeta(t))===null||n===void 0?void 0:n.id;return o??-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const n=this.getId(t);this.idNodeMap.delete(n),t.childNodes&&t.childNodes.forEach(o=>this.removeNodeFromMap(o))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,n){const o=n.id;this.idNodeMap.set(o,t),this.nodeMetaMap.set(t,n)}replace(t,n){const o=this.getNode(t);if(o){const i=this.nodeMetaMap.get(o);i&&this.nodeMetaMap.set(n,i)}this.idNodeMap.set(t,n)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function Ue(e,t){switch(e.RRNodeType){case w.Document:return{id:t,type:e.RRNodeType,childNodes:[]};case w.DocumentType:{const n=e;return{id:t,type:e.RRNodeType,name:n.name,publicId:n.publicId,systemId:n.systemId}}case w.Element:return{id:t,type:e.RRNodeType,tagName:e.tagName.toLowerCase(),attributes:{},childNodes:[]};case w.Text:return{id:t,type:e.RRNodeType,textContent:e.textContent||""};case w.Comment:return{id:t,type:e.RRNodeType,textContent:e.textContent||""};case w.CDATA:return{id:t,type:e.RRNodeType,textContent:""}}}function $e(e){return{all:e=e||new Map,on:function(t,n){var o=e.get(t);o?o.push(n):e.set(t,[n])},off:function(t,n){var o=e.get(t);o&&(n?o.splice(o.indexOf(n)>>>0,1):e.set(t,[]))},emit:function(t,n){var o=e.get(t);o&&o.slice().map(function(i){i(n)}),(o=e.get("*"))&&o.slice().map(function(i){i(t,n)})}}}var Yt=Object.freeze({__proto__:null,default:$e});function Ht(e=window,t=document){if("scrollBehavior"in t.documentElement.style&&e.__forceSmoothScrollPolyfill__!==!0)return;const n=e.HTMLElement||e.Element,o=468,i={scroll:e.scroll||e.scrollTo,scrollBy:e.scrollBy,elementScroll:n.prototype.scroll||a,scrollIntoView:n.prototype.scrollIntoView},l=e.performance&&e.performance.now?e.performance.now.bind(e.performance):Date.now;function r(p){const y=["MSIE ","Trident/","Edge/"];return new RegExp(y.join("|")).test(p)}const s=r(e.navigator.userAgent)?1:0;function a(p,y){this.scrollLeft=p,this.scrollTop=y}function c(p){return .5*(1-Math.cos(Math.PI*p))}function u(p){if(p===null||typeof p!="object"||p.behavior===void 0||p.behavior==="auto"||p.behavior==="instant")return!0;if(typeof p=="object"&&p.behavior==="smooth")return!1;throw new TypeError("behavior member of ScrollOptions "+p.behavior+" is not a valid value for enumeration ScrollBehavior.")}function d(p,y){if(y==="Y")return p.clientHeight+s<p.scrollHeight;if(y==="X")return p.clientWidth+s<p.scrollWidth}function m(p,y){const T=e.getComputedStyle(p,null)["overflow"+y];return T==="auto"||T==="scroll"}function f(p){const y=d(p,"Y")&&m(p,"Y"),T=d(p,"X")&&m(p,"X");return y||T}function N(p){for(;p!==t.body&&f(p)===!1;)p=p.parentNode||p.host;return p}function E(p){const y=l();let T,S,A,O=(y-p.startTime)/o;O=O>1?1:O,T=c(O),S=p.startX+(p.x-p.startX)*T,A=p.startY+(p.y-p.startY)*T,p.method.call(p.scrollable,S,A),(S!==p.x||A!==p.y)&&e.requestAnimationFrame(E.bind(e,p))}function v(p,y,T){let S,A,O,$;const q=l();p===t.body?(S=e,A=e.scrollX||e.pageXOffset,O=e.scrollY||e.pageYOffset,$=i.scroll):(S=p,A=p.scrollLeft,O=p.scrollTop,$=a),E({scrollable:S,method:$,startTime:q,startX:A,startY:O,x:y,y:T})}e.scroll=e.scrollTo=function(){if(arguments[0]!==void 0){if(u(arguments[0])===!0){i.scroll.call(e,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!="object"?arguments[0]:e.scrollX||e.pageXOffset,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:e.scrollY||e.pageYOffset);return}v.call(e,t.body,arguments[0].left!==void 0?~~arguments[0].left:e.scrollX||e.pageXOffset,arguments[0].top!==void 0?~~arguments[0].top:e.scrollY||e.pageYOffset)}},e.scrollBy=function(){if(arguments[0]!==void 0){if(u(arguments[0])){i.scrollBy.call(e,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!="object"?arguments[0]:0,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:0);return}v.call(e,t.body,~~arguments[0].left+(e.scrollX||e.pageXOffset),~~arguments[0].top+(e.scrollY||e.pageYOffset))}},n.prototype.scroll=n.prototype.scrollTo=function(){if(arguments[0]===void 0)return;if(u(arguments[0])===!0){if(typeof arguments[0]=="number"&&arguments[1]===void 0)throw new SyntaxError("Value could not be converted");i.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left:typeof arguments[0]!="object"?~~arguments[0]:this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top:arguments[1]!==void 0?~~arguments[1]:this.scrollTop);return}const p=arguments[0].left,y=arguments[0].top;v.call(this,this,typeof p>"u"?this.scrollLeft:~~p,typeof y>"u"?this.scrollTop:~~y)},n.prototype.scrollBy=function(){if(arguments[0]!==void 0){if(u(arguments[0])===!0){i.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop);return}this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior})}},n.prototype.scrollIntoView=function(){if(u(arguments[0])===!0){i.scrollIntoView.call(this,arguments[0]===void 0?!0:arguments[0]);return}const p=N(this),y=p.getBoundingClientRect(),T=this.getBoundingClientRect();p!==t.body?(v.call(this,p,p.scrollLeft+T.left-y.left,p.scrollTop+T.top-y.top),e.getComputedStyle(p).position!=="fixed"&&e.scrollBy({left:y.left,top:y.top,behavior:"smooth"})):e.scrollBy({left:T.left,top:T.top,behavior:"smooth"})}}var C=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(C||{}),D=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e))(D||{}),L=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(L||{}),K=(e=>(e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e))(K||{}),ee=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(ee||{}),M=(e=>(e.Start="start",e.Pause="pause",e.Resume="resume",e.Resize="resize",e.Finish="finish",e.FullsnapshotRebuilded="fullsnapshot-rebuilded",e.LoadStylesheetStart="load-stylesheet-start",e.LoadStylesheetEnd="load-stylesheet-end",e.SkipStart="skip-start",e.SkipEnd="skip-end",e.MouseInteraction="mouse-interaction",e.EventCast="event-cast",e.CustomEvent="custom-event",e.Flush="flush",e.StateChange="state-change",e.PlayBack="play-back",e.Destroy="destroy",e))(M||{});class zt{constructor(t=[],n){this.timeOffset=0,this.raf=null,this.actions=t,this.speed=n.speed,this.liveMode=n.liveMode}addAction(t){if(!this.actions.length||this.actions[this.actions.length-1].delay<=t.delay){this.actions.push(t);return}const n=this.findActionIndex(t);this.actions.splice(n,0,t)}start(){this.timeOffset=0;let t=performance.now();const n=()=>{const o=performance.now();for(this.timeOffset+=(o-t)*this.speed,t=o;this.actions.length;){const i=this.actions[0];if(this.timeOffset>=i.delay)this.actions.shift(),i.doAction();else break}(this.actions.length>0||this.liveMode)&&(this.raf=requestAnimationFrame(n))};this.raf=requestAnimationFrame(n)}clear(){this.raf&&(cancelAnimationFrame(this.raf),this.raf=null),this.actions.length=0}setSpeed(t){this.speed=t}toggleLiveMode(t){this.liveMode=t}isActive(){return this.raf!==null}findActionIndex(t){let n=0,o=this.actions.length-1;for(;n<=o;){const i=Math.floor((n+o)/2);if(this.actions[i].delay<t.delay)n=i+1;else if(this.actions[i].delay>t.delay)o=i-1;else return i+1}return n}}function We(e,t){if(e.type===C.IncrementalSnapshot&&e.data.source===D.MouseMove&&e.data.positions&&e.data.positions.length){const n=e.data.positions[0].timeOffset,o=e.timestamp+n;return e.delay=o-t,o-t}return e.delay=e.timestamp-t,e.delay}/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */function Ve(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var o,i,l=n.call(e),r=[];try{for(;(t===void 0||t-- >0)&&!(o=l.next()).done;)r.push(o.value)}catch(s){i={error:s}}finally{try{o&&!o.done&&(n=l.return)&&n.call(l)}finally{if(i)throw i.error}}return r}var V;(function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"})(V||(V={}));var Ge={type:"xstate.init"};function he(e){return e===void 0?[]:[].concat(e)}function G(e){return{type:"xstate.assign",assignment:e}}function je(e,t){return typeof(e=typeof e=="string"&&t&&t[e]?t[e]:e)=="string"?{type:e}:typeof e=="function"?{type:e.name,exec:e}:e}function te(e){return function(t){return e===t}}function Ye(e){return typeof e=="string"?{type:e}:e}function He(e,t){return{value:e,context:t,actions:[],changed:!1,matches:te(e)}}function ze(e,t,n){var o=t,i=!1;return[e.filter(function(l){if(l.type==="xstate.assign"){i=!0;var r=Object.assign({},o);return typeof l.assignment=="function"?r=l.assignment(o,n):Object.keys(l.assignment).forEach(function(s){r[s]=typeof l.assignment[s]=="function"?l.assignment[s](o,n):l.assignment[s]}),o=r,!1}return!0}),o,i]}function Xe(e,t){t===void 0&&(t={});var n=Ve(ze(he(e.states[e.initial].entry).map(function(r){return je(r,t.actions)}),e.context,Ge),2),o=n[0],i=n[1],l={config:e,_options:t,initialState:{value:e.initial,actions:o,context:i,matches:te(e.initial)},transition:function(r,s){var a,c,u=typeof r=="string"?{value:r,context:e.context}:r,d=u.value,m=u.context,f=Ye(s),N=e.states[d];if(N.on){var E=he(N.on[f.type]);try{for(var v=function(k){var J=typeof Symbol=="function"&&Symbol.iterator,g=J&&k[J],h=0;if(g)return g.call(k);if(k&&typeof k.length=="number")return{next:function(){return k&&h>=k.length&&(k=void 0),{value:k&&k[h++],done:!k}}};throw new TypeError(J?"Object is not iterable.":"Symbol.iterator is not defined.")}(E),p=v.next();!p.done;p=v.next()){var y=p.value;if(y===void 0)return He(d,m);var T=typeof y=="string"?{target:y}:y,S=T.target,A=T.actions,O=A===void 0?[]:A,$=T.cond,q=$===void 0?function(){return!0}:$,Ee=S===void 0,Te=S??d,Se=e.states[Te];if(q(m,f)){var Z=Ve(ze((Ee?he(O):[].concat(N.exit,O,Se.entry).filter(function(k){return k})).map(function(k){return je(k,l._options.actions)}),m,f),3),ie=Z[0],we=Z[1],be=Z[2],Y=S??d;return{value:Y,context:we,actions:ie,changed:S!==d||ie.length>0||be,matches:te(Y)}}}}catch(k){a={error:k}}finally{try{p&&!p.done&&(c=v.return)&&c.call(v)}finally{if(a)throw a.error}}}return He(d,m)}};return l}var Qe=function(e,t){return e.actions.forEach(function(n){var o=n.exec;return o&&o(e.context,t)})};function qe(e){var t=e.initialState,n=V.NotStarted,o=new Set,i={_machine:e,send:function(l){n===V.Running&&(t=e.transition(t,l),Qe(t,Ye(l)),o.forEach(function(r){return r(t)}))},subscribe:function(l){return o.add(l),l(t),{unsubscribe:function(){return o.delete(l)}}},start:function(l){if(l){var r=typeof l=="object"?l:{context:e.config.context,value:l};t={value:r.value,actions:[],context:r.context,matches:te(r.value)}}return n=V.Running,Qe(t,Ge),i},stop:function(){return n=V.Stopped,o.clear(),i},get state(){return t},get status(){return n}};return i}var Xt=Object.defineProperty,Qt=Object.defineProperties,qt=Object.getOwnPropertyDescriptors,Ze=Object.getOwnPropertySymbols,Zt=Object.prototype.hasOwnProperty,Jt=Object.prototype.propertyIsEnumerable,Je=(e,t,n)=>t in e?Xt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,pe=(e,t)=>{for(var n in t||(t={}))Zt.call(t,n)&&Je(e,n,t[n]);if(Ze)for(var n of Ze(t))Jt.call(t,n)&&Je(e,n,t[n]);return e},me=(e,t)=>Qt(e,qt(t));function Kt(e,t){for(let n=e.length-1;n>=0;n--){const o=e[n];if(o.type===C.Meta&&o.timestamp<=t)return e.slice(n)}return e}function en(e,{getCastFn:t,applyEventsSynchronously:n,emitter:o}){const i=Xe({id:"player",context:e,initial:"paused",states:{playing:{on:{PAUSE:{target:"paused",actions:["pause"]},CAST_EVENT:{target:"playing",actions:"castEvent"},END:{target:"paused",actions:["resetLastPlayedEvent","pause"]},ADD_EVENT:{target:"playing",actions:["addEvent"]}}},paused:{on:{PLAY:{target:"playing",actions:["recordTimeOffset","play"]},CAST_EVENT:{target:"paused",actions:"castEvent"},TO_LIVE:{target:"live",actions:["startLive"]},ADD_EVENT:{target:"paused",actions:["addEvent"]}}},live:{on:{ADD_EVENT:{target:"live",actions:["addEvent"]},CAST_EVENT:{target:"live",actions:["castEvent"]}}}}},{actions:{castEvent:G({lastPlayedEvent:(l,r)=>r.type==="CAST_EVENT"?r.payload.event:l.lastPlayedEvent}),recordTimeOffset:G((l,r)=>{let s=l.timeOffset;return"payload"in r&&"timeOffset"in r.payload&&(s=r.payload.timeOffset),me(pe({},l),{timeOffset:s,baselineTime:l.events[0].timestamp+s})}),play(l){var r;const{timer:s,events:a,baselineTime:c,lastPlayedEvent:u}=l;s.clear();for(const N of a)We(N,c);const d=Kt(a,c);let m=u?.timestamp;u?.type===C.IncrementalSnapshot&&u.data.source===D.MouseMove&&(m=u.timestamp+((r=u.data.positions[0])==null?void 0:r.timeOffset)),c<(m||0)&&o.emit(M.PlayBack);const f=new Array;for(const N of d)if(!(m&&m<c&&(N.timestamp<=m||N===u)))if(N.timestamp<c)f.push(N);else{const E=t(N,!1);s.addAction({doAction:()=>{E()},delay:N.delay})}n(f),o.emit(M.Flush),s.start()},pause(l){l.timer.clear()},resetLastPlayedEvent:G(l=>me(pe({},l),{lastPlayedEvent:null})),startLive:G({baselineTime:(l,r)=>(l.timer.toggleLiveMode(!0),l.timer.start(),r.type==="TO_LIVE"&&r.payload.baselineTime?r.payload.baselineTime:Date.now())}),addEvent:G((l,r)=>{const{baselineTime:s,timer:a,events:c}=l;if(r.type==="ADD_EVENT"){const{event:u}=r.payload;We(u,s);let d=c.length-1;if(!c[d]||c[d].timestamp<=u.timestamp)c.push(u);else{let N=-1,E=0;for(;E<=d;){const v=Math.floor((E+d)/2);c[v].timestamp<=u.timestamp?E=v+1:d=v-1}N===-1&&(N=E),c.splice(N,0,u)}const m=u.timestamp<s,f=t(u,m);m?f():a.isActive()&&a.addAction({doAction:()=>{f()},delay:u.delay})}return me(pe({},l),{events:c})})}});return qe(i)}function tn(e){const t=Xe({id:"speed",context:e,initial:"normal",states:{normal:{on:{FAST_FORWARD:{target:"skipping",actions:["recordSpeed","setSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}},skipping:{on:{BACK_TO_NORMAL:{target:"normal",actions:["restoreSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}}}},{actions:{setSpeed:(n,o)=>{"payload"in o&&n.timer.setSpeed(o.payload.speed)},recordSpeed:G({normalSpeed:n=>n.timer.speed}),restoreSpeed:n=>{n.timer.setSpeed(n.normalSpeed)}}});return qe(t)}const j=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`;let Ke={map:{},getId(){return console.error(j),-1},getNode(){return console.error(j),null},removeNodeFromMap(){console.error(j)},has(){return console.error(j),!1},reset(){console.error(j)}};typeof window<"u"&&window.Proxy&&window.Reflect&&(Ke=new Proxy(Ke,{get(e,t,n){return t==="map"&&console.error(j),Reflect.get(e,t,n)}}));function nn(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...t)=>{let n=t[0];if(!(0 in t))throw new TypeError("1 argument is required");do if(this===n)return!0;while(n=n&&n.parentNode);return!1})}function on(e){const t={},n=(i,l)=>{const r={value:i,parent:l,children:[]};return t[i.node.id]=r,r},o=[];for(const i of e){const{nextId:l,parentId:r}=i;if(l&&l in t){const s=t[l];if(s.parent){const a=s.parent.children.indexOf(s);s.parent.children.splice(a,0,n(i,s.parent))}else{const a=o.indexOf(s);o.splice(a,0,n(i,null))}continue}if(r in t){const s=t[r];s.children.push(n(i,s));continue}o.push(n(i,null))}return o}function et(e,t){t(e.value);for(let n=e.children.length-1;n>=0;n--)et(e.children[n],t)}function fe(e,t){return Boolean(e.nodeName==="IFRAME"&&t.getMeta(e))}function tt(e,t){var n,o;const i=(o=(n=e.ownerDocument)==null?void 0:n.defaultView)==null?void 0:o.frameElement;if(!i||i===t)return{x:0,y:0,relativeScale:1,absoluteScale:1};const l=i.getBoundingClientRect(),r=tt(i,t),s=l.height/i.clientHeight;return{x:l.x*r.relativeScale+r.x,y:l.y*r.relativeScale+r.y,relativeScale:s,absoluteScale:r.absoluteScale*s}}function ne(e){return Boolean(e?.shadowRoot)}function X(e,t){const n=e[t[0]];return t.length===1?n:X(n.cssRules[t[1]].cssRules,t.slice(2))}function nt(e){const t=[...e],n=t.pop();return{positions:t,index:n}}function rn(e){const t=new Set,n=[];for(let o=e.length;o--;){const i=e[o];t.has(i.id)||(n.push(i),t.add(i.id))}return n}class sn{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(t){var n;return(n=this.styleIDMap.get(t))!=null?n:-1}has(t){return this.styleIDMap.has(t)}add(t,n){if(this.has(t))return this.getId(t);let o;return n===void 0?o=this.id++:o=n,this.styleIDMap.set(t,o),this.idStyleMap.set(o,t),o}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}const an=e=>[`.${e} { background: currentColor }`,"noscript { display: none !important; }"];for(var ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Q=typeof Uint8Array>"u"?[]:new Uint8Array(256),oe=0;oe<ot.length;oe++)Q[ot.charCodeAt(oe)]=oe;var ln=function(e){var t=e.length*.75,n=e.length,o,i=0,l,r,s,a;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);var c=new ArrayBuffer(t),u=new Uint8Array(c);for(o=0;o<n;o+=4)l=Q[e.charCodeAt(o)],r=Q[e.charCodeAt(o+1)],s=Q[e.charCodeAt(o+2)],a=Q[e.charCodeAt(o+3)],u[i++]=l<<2|r>>4,u[i++]=(r&15)<<4|s>>2,u[i++]=(s&3)<<6|a&63;return c},cn=(e,t,n)=>new Promise((o,i)=>{var l=a=>{try{s(n.next(a))}catch(c){i(c)}},r=a=>{try{s(n.throw(a))}catch(c){i(c)}},s=a=>a.done?o(a.value):Promise.resolve(a.value).then(l,r);s((n=n.apply(e,t)).next())});const rt=new Map;function it(e,t){let n=rt.get(e);return n||(n=new Map,rt.set(e,n)),n.has(t)||n.set(t,[]),n.get(t)}function U(e,t,n){return o=>cn(this,null,function*(){if(o&&typeof o=="object"&&"rr_type"in o)if(n&&(n.isUnchanged=!1),o.rr_type==="ImageBitmap"&&"args"in o){const i=yield U(e,t,n)(o.args);return yield createImageBitmap.apply(null,i)}else if("index"in o){if(n||t===null)return o;const{rr_type:i,index:l}=o;return it(t,i)[l]}else if("args"in o){const{rr_type:i,args:l}=o,r=window[i];return new r(...yield Promise.all(l.map(U(e,t,n))))}else{if("base64"in o)return ln(o.base64);if("src"in o){const i=e.get(o.src);if(i)return i;{const l=new Image;return l.src=o.src,e.set(o.src,l),l}}else if("data"in o&&o.rr_type==="Blob"){const i=yield Promise.all(o.data.map(U(e,t,n)));return new Blob(i,{type:o.type})}}else if(Array.isArray(o))return yield Promise.all(o.map(U(e,t,n)));return o})}var un=(e,t,n)=>new Promise((o,i)=>{var l=a=>{try{s(n.next(a))}catch(c){i(c)}},r=a=>{try{s(n.throw(a))}catch(c){i(c)}},s=a=>a.done?o(a.value):Promise.resolve(a.value).then(l,r);s((n=n.apply(e,t)).next())});function dn(e,t){try{return t===K.WebGL?e.getContext("webgl")||e.getContext("experimental-webgl"):e.getContext("webgl2")}catch{return null}}const hn=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject"];function pn(e,t){if(!(t!=null&&t.constructor))return;const{name:n}=t.constructor;if(!hn.includes(n))return;const o=it(e,n);o.includes(t)||o.push(t)}function mn(e){return un(this,arguments,function*({mutation:t,target:n,type:o,imageMap:i,errorHandler:l}){try{const r=dn(n,o);if(!r)return;if(t.setter){r[t.property]=t.args[0];return}const s=r[t.property],a=yield Promise.all(t.args.map(U(i,r))),c=s.apply(r,a);pn(r,c)}catch(r){l(t,r)}})}var fn=(e,t,n)=>new Promise((o,i)=>{var l=a=>{try{s(n.next(a))}catch(c){i(c)}},r=a=>{try{s(n.throw(a))}catch(c){i(c)}},s=a=>a.done?o(a.value):Promise.resolve(a.value).then(l,r);s((n=n.apply(e,t)).next())});function gn(e){return fn(this,arguments,function*({event:t,mutation:n,target:o,imageMap:i,errorHandler:l}){try{const r=o.getContext("2d");if(n.setter){r[n.property]=n.args[0];return}const s=r[n.property];if(n.property==="drawImage"&&typeof n.args[0]=="string")i.get(t),s.apply(r,n.args);else{const a=yield Promise.all(n.args.map(U(i,r)));s.apply(r,a)}}catch(r){l(n,r)}})}var yn=(e,t,n)=>new Promise((o,i)=>{var l=a=>{try{s(n.next(a))}catch(c){i(c)}},r=a=>{try{s(n.throw(a))}catch(c){i(c)}},s=a=>a.done?o(a.value):Promise.resolve(a.value).then(l,r);s((n=n.apply(e,t)).next())});function st(e){return yn(this,arguments,function*({event:t,mutation:n,target:o,imageMap:i,canvasEventMap:l,errorHandler:r}){try{const s=l.get(t)||n,a="commands"in s?s.commands:[s];if([K.WebGL,K.WebGL2].includes(n.type)){for(let c=0;c<a.length;c++){const u=a[c];yield mn({mutation:u,type:n.type,target:o,imageMap:i,errorHandler:r})}return}for(let c=0;c<a.length;c++){const u=a[c];yield gn({event:t,mutation:u,target:o,imageMap:i,errorHandler:r})}}catch(s){r(n,s)}})}var vn=Object.defineProperty,Nn=Object.defineProperties,En=Object.getOwnPropertyDescriptors,at=Object.getOwnPropertySymbols,Tn=Object.prototype.hasOwnProperty,Sn=Object.prototype.propertyIsEnumerable,lt=(e,t,n)=>t in e?vn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,re=(e,t)=>{for(var n in t||(t={}))Tn.call(t,n)&&lt(e,n,t[n]);if(at)for(var n of at(t))Sn.call(t,n)&&lt(e,n,t[n]);return e},ge=(e,t)=>Nn(e,En(t)),ye=(e,t,n)=>new Promise((o,i)=>{var l=a=>{try{s(n.next(a))}catch(c){i(c)}},r=a=>{try{s(n.throw(a))}catch(c){i(c)}},s=a=>a.done?o(a.value):Promise.resolve(a.value).then(l,r);s((n=n.apply(e,t)).next())});const wn=10*1e3,bn=5*1e3,Dn=$e||Yt,ve="[replayer]",Ne={duration:500,lineCap:"round",lineWidth:3,strokeStyle:"red"};function ct(e){return e.type==C.IncrementalSnapshot&&(e.data.source==D.TouchMove||e.data.source==D.MouseInteraction&&e.data.type==L.TouchStart)}class Mn{constructor(t,n){if(this.usingVirtualDom=!1,this.virtualDom=new W,this.mouseTail=null,this.tailPositions=[],this.emitter=Dn(),this.legacy_missingNodeRetryMap={},this.cache=Ae(),this.imageMap=new Map,this.canvasEventMap=new Map,this.mirror=ht(),this.styleMirror=new sn,this.firstFullSnapshot=null,this.newDocumentQueue=[],this.mousePos=null,this.touchActive=null,this.lastSelectionData=null,this.constructedStyleMutations=[],this.adoptedStyleSheets=[],this.handleResize=s=>{this.iframe.style.display="inherit";for(const a of[this.mouseTail,this.iframe])!a||(a.setAttribute("width",String(s.width)),a.setAttribute("height",String(s.height)))},this.applyEventsSynchronously=s=>{for(const a of s){switch(a.type){case C.DomContentLoaded:case C.Load:case C.Custom:continue;case C.FullSnapshot:case C.Meta:case C.Plugin:case C.IncrementalSnapshot:break}this.getCastFn(a,!0)()}this.touchActive===!0?this.mouse.classList.add("touch-active"):this.touchActive===!1&&this.mouse.classList.remove("touch-active"),this.touchActive=null},this.getCastFn=(s,a=!1)=>{let c;switch(s.type){case C.DomContentLoaded:case C.Load:break;case C.Custom:c=()=>{this.emitter.emit(M.CustomEvent,s)};break;case C.Meta:c=()=>this.emitter.emit(M.Resize,{width:s.data.width,height:s.data.height});break;case C.FullSnapshot:c=()=>{var u;if(this.firstFullSnapshot){if(this.firstFullSnapshot===s){this.firstFullSnapshot=!0;return}}else this.firstFullSnapshot=!0;this.rebuildFullSnapshot(s,a),(u=this.iframe.contentWindow)==null||u.scrollTo(s.data.initialOffset),this.styleMirror.reset()};break;case C.IncrementalSnapshot:c=()=>{if(this.applyIncremental(s,a),!a&&(s===this.nextUserInteractionEvent&&(this.nextUserInteractionEvent=null,this.backToNormal()),this.config.skipInactive&&!this.nextUserInteractionEvent)){for(const u of this.service.state.context.events)if(!(u.timestamp<=s.timestamp)&&this.isUserInteraction(u)){u.delay-s.delay>wn*this.speedService.state.context.timer.speed&&(this.nextUserInteractionEvent=u);break}if(this.nextUserInteractionEvent){const u=this.nextUserInteractionEvent.delay-s.delay,d={speed:Math.min(Math.round(u/bn),this.config.maxSpeed)};this.speedService.send({type:"FAST_FORWARD",payload:d}),this.emitter.emit(M.SkipStart,d)}}};break}return()=>{c&&c();for(const d of this.config.plugins||[])d.handler&&d.handler(s,a,{replayer:this});this.service.send({type:"CAST_EVENT",payload:{event:s}});const u=this.service.state.context.events.length-1;if(s===this.service.state.context.events[u]){const d=()=>{u<this.service.state.context.events.length-1||(this.backToNormal(),this.service.send("END"),this.emitter.emit(M.Finish))};s.type===C.IncrementalSnapshot&&s.data.source===D.MouseMove&&s.data.positions.length?setTimeout(()=>{d()},Math.max(0,-s.data.positions[0].timeOffset+50)):d()}this.emitter.emit(M.EventCast,s)}},!(n!=null&&n.liveMode)&&t.length<2)throw new Error("Replayer need at least 2 events.");const o={speed:1,maxSpeed:360,root:document.body,loadTimeout:0,skipInactive:!1,showWarning:!0,showDebug:!1,blockClass:"rr-block",liveMode:!1,insertStyleRules:[],triggerFocus:!0,UNSAFE_replayCanvas:!1,pauseAnimation:!0,mouseTail:Ne,useVirtualDom:!0};this.config=Object.assign({},o,n),this.handleResize=this.handleResize.bind(this),this.getCastFn=this.getCastFn.bind(this),this.applyEventsSynchronously=this.applyEventsSynchronously.bind(this),this.emitter.on(M.Resize,this.handleResize),this.setupDom();for(const s of this.config.plugins||[])s.getMirror&&s.getMirror({nodeMirror:this.mirror});this.emitter.on(M.Flush,()=>{if(this.usingVirtualDom){const s={mirror:this.mirror,applyCanvas:(a,c,u)=>{st({event:a,mutation:c,target:u,imageMap:this.imageMap,canvasEventMap:this.canvasEventMap,errorHandler:this.warnCanvasMutationFailed.bind(this)})},applyInput:this.applyInput.bind(this),applyScroll:this.applyScroll.bind(this),applyStyleSheetMutation:(a,c)=>{a.source===D.StyleSheetRule?this.applyStyleSheetRule(a,c):a.source===D.StyleDeclaration&&this.applyStyleDeclaration(a,c)}};if(this.iframe.contentDocument&&F(this.iframe.contentDocument,this.virtualDom,s,this.virtualDom.mirror),this.virtualDom.destroyTree(),this.usingVirtualDom=!1,Object.keys(this.legacy_missingNodeRetryMap).length)for(const a in this.legacy_missingNodeRetryMap)try{const c=this.legacy_missingNodeRetryMap[a],u=de(c.node,this.mirror,this.virtualDom.mirror);F(u,c.node,s,this.virtualDom.mirror),c.node=u}catch(c){this.config.showWarning&&console.warn(c)}this.constructedStyleMutations.forEach(a=>{this.applyStyleSheetMutation(a)}),this.constructedStyleMutations=[],this.adoptedStyleSheets.forEach(a=>{this.applyAdoptedStyleSheet(a)}),this.adoptedStyleSheets=[]}this.mousePos&&(this.moveAndHover(this.mousePos.x,this.mousePos.y,this.mousePos.id,!0,this.mousePos.debugData),this.mousePos=null),this.lastSelectionData&&(this.applySelection(this.lastSelectionData),this.lastSelectionData=null)}),this.emitter.on(M.PlayBack,()=>{this.firstFullSnapshot=null,this.mirror.reset(),this.styleMirror.reset()});const i=new zt([],{speed:this.config.speed,liveMode:this.config.liveMode});this.service=en({events:t.map(s=>n&&n.unpackFn?n.unpackFn(s):s).sort((s,a)=>s.timestamp-a.timestamp),timer:i,timeOffset:0,baselineTime:0,lastPlayedEvent:null},{getCastFn:this.getCastFn,applyEventsSynchronously:this.applyEventsSynchronously,emitter:this.emitter}),this.service.start(),this.service.subscribe(s=>{this.emitter.emit(M.StateChange,{player:s})}),this.speedService=tn({normalSpeed:-1,timer:i}),this.speedService.start(),this.speedService.subscribe(s=>{this.emitter.emit(M.StateChange,{speed:s})});const l=this.service.state.context.events.find(s=>s.type===C.Meta),r=this.service.state.context.events.find(s=>s.type===C.FullSnapshot);if(l){const{width:s,height:a}=l.data;setTimeout(()=>{this.emitter.emit(M.Resize,{width:s,height:a})},0)}r&&setTimeout(()=>{var s;this.firstFullSnapshot||(this.firstFullSnapshot=r,this.rebuildFullSnapshot(r),(s=this.iframe.contentWindow)==null||s.scrollTo(r.data.initialOffset))},1),this.service.state.context.events.find(ct)&&this.mouse.classList.add("touch-device")}get timer(){return this.service.state.context.timer}on(t,n){return this.emitter.on(t,n),this}off(t,n){return this.emitter.off(t,n),this}setConfig(t){Object.keys(t).forEach(n=>{t[n],this.config[n]=t[n]}),this.config.skipInactive||this.backToNormal(),typeof t.speed<"u"&&this.speedService.send({type:"SET_SPEED",payload:{speed:t.speed}}),typeof t.mouseTail<"u"&&(t.mouseTail===!1?this.mouseTail&&(this.mouseTail.style.display="none"):(this.mouseTail||(this.mouseTail=document.createElement("canvas"),this.mouseTail.width=Number.parseFloat(this.iframe.width),this.mouseTail.height=Number.parseFloat(this.iframe.height),this.mouseTail.classList.add("replayer-mouse-tail"),this.wrapper.insertBefore(this.mouseTail,this.iframe)),this.mouseTail.style.display="inherit"))}getMetaData(){const t=this.service.state.context.events[0],n=this.service.state.context.events[this.service.state.context.events.length-1];return{startTime:t.timestamp,endTime:n.timestamp,totalTime:n.timestamp-t.timestamp}}getCurrentTime(){return this.timer.timeOffset+this.getTimeOffset()}getTimeOffset(){const{baselineTime:t,events:n}=this.service.state.context;return t-n[0].timestamp}getMirror(){return this.mirror}play(t=0){var n,o;this.service.state.matches("paused")?this.service.send({type:"PLAY",payload:{timeOffset:t}}):(this.service.send({type:"PAUSE"}),this.service.send({type:"PLAY",payload:{timeOffset:t}})),(o=(n=this.iframe.contentDocument)==null?void 0:n.getElementsByTagName("html")[0])==null||o.classList.remove("rrweb-paused"),this.emitter.emit(M.Start)}pause(t){var n,o;t===void 0&&this.service.state.matches("playing")&&this.service.send({type:"PAUSE"}),typeof t=="number"&&(this.play(t),this.service.send({type:"PAUSE"})),(o=(n=this.iframe.contentDocument)==null?void 0:n.getElementsByTagName("html")[0])==null||o.classList.add("rrweb-paused"),this.emitter.emit(M.Pause)}resume(t=0){console.warn("The 'resume' was deprecated in 1.0. Please use 'play' method which has the same interface."),this.play(t),this.emitter.emit(M.Resume)}destroy(){this.pause(),this.config.root.removeChild(this.wrapper),this.emitter.emit(M.Destroy)}startLive(t){this.service.send({type:"TO_LIVE",payload:{baselineTime:t}})}addEvent(t){const n=this.config.unpackFn?this.config.unpackFn(t):t;ct(n)&&this.mouse.classList.add("touch-device"),Promise.resolve().then(()=>this.service.send({type:"ADD_EVENT",payload:{event:n}}))}enableInteract(){this.iframe.setAttribute("scrolling","auto"),this.iframe.style.pointerEvents="auto"}disableInteract(){this.iframe.setAttribute("scrolling","no"),this.iframe.style.pointerEvents="none"}resetCache(){this.cache=Ae()}setupDom(){this.wrapper=document.createElement("div"),this.wrapper.classList.add("replayer-wrapper"),this.config.root.appendChild(this.wrapper),this.mouse=document.createElement("div"),this.mouse.classList.add("replayer-mouse"),this.wrapper.appendChild(this.mouse),this.config.mouseTail!==!1&&(this.mouseTail=document.createElement("canvas"),this.mouseTail.classList.add("replayer-mouse-tail"),this.mouseTail.style.display="inherit",this.wrapper.appendChild(this.mouseTail)),this.iframe=document.createElement("iframe");const t=["allow-same-origin"];this.config.UNSAFE_replayCanvas&&t.push("allow-scripts"),this.iframe.style.display="none",this.iframe.setAttribute("sandbox",t.join(" ")),this.disableInteract(),this.wrapper.appendChild(this.iframe),this.iframe.contentWindow&&this.iframe.contentDocument&&(Ht(this.iframe.contentWindow,this.iframe.contentDocument),nn(this.iframe.contentWindow))}rebuildFullSnapshot(t,n=!1){if(!this.iframe.contentDocument)return console.warn("Looks like your replayer has been destroyed.");Object.keys(this.legacy_missingNodeRetryMap).length&&console.warn("Found unresolved missing node map",this.legacy_missingNodeRetryMap),this.legacy_missingNodeRetryMap={};const o=[],i=(s,a)=>{this.collectIframeAndAttachDocument(o,s);for(const c of this.config.plugins||[])c.onBuild&&c.onBuild(s,{id:a,replayer:this})};Et(t.data.node,{doc:this.iframe.contentDocument,afterAppend:i,cache:this.cache,mirror:this.mirror}),i(this.iframe.contentDocument,t.data.node.id);for(const{mutationInQueue:s,builtNode:a}of o)this.attachDocumentToIframe(s,a),this.newDocumentQueue=this.newDocumentQueue.filter(c=>c!==s);const{documentElement:l,head:r}=this.iframe.contentDocument;this.insertStyleRules(l,r),this.service.state.matches("playing")||this.iframe.contentDocument.getElementsByTagName("html")[0].classList.add("rrweb-paused"),this.emitter.emit(M.FullsnapshotRebuilded,t),n||this.waitForStylesheetLoad(),this.config.UNSAFE_replayCanvas&&this.preloadAllImages()}insertStyleRules(t,n){var o;const i=an(this.config.blockClass).concat(this.config.insertStyleRules);if(this.config.pauseAnimation&&i.push("html.rrweb-paused *, html.rrweb-paused *:before, html.rrweb-paused *:after { animation-play-state: paused !important; }"),this.usingVirtualDom){const l=this.virtualDom.createElement("style");this.virtualDom.mirror.add(l,Ue(l,this.virtualDom.unserializedId)),t.insertBefore(l,n),l.rules.push({source:D.StyleSheetRule,adds:i.map((r,s)=>({rule:r,index:s}))})}else{const l=document.createElement("style");t.insertBefore(l,n);for(let r=0;r<i.length;r++)(o=l.sheet)==null||o.insertRule(i[r],r)}}attachDocumentToIframe(t,n){const o=this.usingVirtualDom?this.virtualDom.mirror:this.mirror,i=[],l=(r,s)=>{this.collectIframeAndAttachDocument(i,r);const a=o.getMeta(r);if(a?.type===I.Element&&a?.tagName.toUpperCase()==="HTML"){const{documentElement:c,head:u}=n.contentDocument;this.insertStyleRules(c,u)}for(const c of this.config.plugins||[])c.onBuild&&c.onBuild(r,{id:s,replayer:this})};H(t.node,{doc:n.contentDocument,mirror:o,hackCss:!0,skipChild:!1,afterAppend:l,cache:this.cache}),l(n.contentDocument,t.node.id);for(const{mutationInQueue:r,builtNode:s}of i)this.attachDocumentToIframe(r,s),this.newDocumentQueue=this.newDocumentQueue.filter(a=>a!==r)}collectIframeAndAttachDocument(t,n){if(fe(n,this.mirror)){const o=this.newDocumentQueue.find(i=>i.parentId===this.mirror.getId(n));o&&t.push({mutationInQueue:o,builtNode:n})}}waitForStylesheetLoad(){var t;const n=(t=this.iframe.contentDocument)==null?void 0:t.head;if(n){const o=new Set;let i,l=this.service.state;const r=()=>{l=this.service.state};this.emitter.on(M.Start,r),this.emitter.on(M.Pause,r);const s=()=>{this.emitter.off(M.Start,r),this.emitter.off(M.Pause,r)};n.querySelectorAll('link[rel="stylesheet"]').forEach(a=>{a.sheet||(o.add(a),a.addEventListener("load",()=>{o.delete(a),o.size===0&&i!==-1&&(l.matches("playing")&&this.play(this.getCurrentTime()),this.emitter.emit(M.LoadStylesheetEnd),i&&clearTimeout(i),s())}))}),o.size>0&&(this.service.send({type:"PAUSE"}),this.emitter.emit(M.LoadStylesheetStart),i=setTimeout(()=>{l.matches("playing")&&this.play(this.getCurrentTime()),i=-1,s()},this.config.loadTimeout))}}preloadAllImages(){return ye(this,null,function*(){this.service.state;const t=()=>{this.service.state};this.emitter.on(M.Start,t),this.emitter.on(M.Pause,t);const n=[];for(const o of this.service.state.context.events)o.type===C.IncrementalSnapshot&&o.data.source===D.CanvasMutation&&(n.push(this.deserializeAndPreloadCanvasEvents(o.data,o)),("commands"in o.data?o.data.commands:[o.data]).forEach(i=>{this.preloadImages(i,o)}));return Promise.all(n)})}preloadImages(t,n){if(t.property==="drawImage"&&typeof t.args[0]=="string"&&!this.imageMap.has(n)){const o=document.createElement("canvas"),i=o.getContext("2d"),l=i?.createImageData(o.width,o.height);l?.data,JSON.parse(t.args[0]),i?.putImageData(l,0,0)}}deserializeAndPreloadCanvasEvents(t,n){return ye(this,null,function*(){if(!this.canvasEventMap.has(n)){const o={isUnchanged:!0};if("commands"in t){const i=yield Promise.all(t.commands.map(l=>ye(this,null,function*(){const r=yield Promise.all(l.args.map(U(this.imageMap,null,o)));return ge(re({},l),{args:r})})));o.isUnchanged===!1&&this.canvasEventMap.set(n,ge(re({},t),{commands:i}))}else{const i=yield Promise.all(t.args.map(U(this.imageMap,null,o)));o.isUnchanged===!1&&this.canvasEventMap.set(n,ge(re({},t),{args:i}))}}})}applyIncremental(t,n){var o,i,l;const{data:r}=t;switch(r.source){case D.Mutation:{try{this.applyMutation(r,n)}catch(s){this.warn(`Exception in mutation ${s.message||s}`,r)}break}case D.Drag:case D.TouchMove:case D.MouseMove:if(n){const s=r.positions[r.positions.length-1];this.mousePos={x:s.x,y:s.y,id:s.id,debugData:r}}else r.positions.forEach(s=>{const a={doAction:()=>{this.moveAndHover(s.x,s.y,s.id,n,r)},delay:s.timeOffset+t.timestamp-this.service.state.context.baselineTime};this.timer.addAction(a)}),this.timer.addAction({doAction(){},delay:t.delay-((o=r.positions[0])==null?void 0:o.timeOffset)});break;case D.MouseInteraction:{if(r.id===-1||n)break;const s=new Event(L[r.type].toLowerCase()),a=this.mirror.getNode(r.id);if(!a)return this.debugNodeNotFound(r,r.id);this.emitter.emit(M.MouseInteraction,{type:r.type,target:a});const{triggerFocus:c}=this.config;switch(r.type){case L.Blur:"blur"in a&&a.blur();break;case L.Focus:c&&a.focus&&a.focus({preventScroll:!0});break;case L.Click:case L.TouchStart:case L.TouchEnd:n?(r.type===L.TouchStart?this.touchActive=!0:r.type===L.TouchEnd&&(this.touchActive=!1),this.mousePos={x:r.x,y:r.y,id:r.id,debugData:r}):(r.type===L.TouchStart&&(this.tailPositions.length=0),this.moveAndHover(r.x,r.y,r.id,n,r),r.type===L.Click?(this.mouse.classList.remove("active"),this.mouse.offsetWidth,this.mouse.classList.add("active")):r.type===L.TouchStart?(this.mouse.offsetWidth,this.mouse.classList.add("touch-active")):r.type===L.TouchEnd&&this.mouse.classList.remove("touch-active"));break;case L.TouchCancel:n?this.touchActive=!1:this.mouse.classList.remove("touch-active");break;default:a.dispatchEvent(s)}break}case D.Scroll:{if(r.id===-1)break;if(this.usingVirtualDom){const s=this.virtualDom.mirror.getNode(r.id);if(!s)return this.debugNodeNotFound(r,r.id);s.scrollData=r;break}this.applyScroll(r,n);break}case D.ViewportResize:this.emitter.emit(M.Resize,{width:r.width,height:r.height});break;case D.Input:{if(r.id===-1)break;if(this.usingVirtualDom){const s=this.virtualDom.mirror.getNode(r.id);if(!s)return this.debugNodeNotFound(r,r.id);s.inputData=r;break}this.applyInput(r);break}case D.MediaInteraction:{const s=this.usingVirtualDom?this.virtualDom.mirror.getNode(r.id):this.mirror.getNode(r.id);if(!s)return this.debugNodeNotFound(r,r.id);const a=s;try{r.currentTime&&(a.currentTime=r.currentTime),r.volume&&(a.volume=r.volume),r.muted&&(a.muted=r.muted),r.type===ee.Pause&&a.pause(),r.type===ee.Play&&a.play(),r.type===ee.RateChange&&(a.playbackRate=r.playbackRate)}catch(c){this.config.showWarning&&console.warn(`Failed to replay media interactions: ${c.message||c}`)}break}case D.StyleSheetRule:case D.StyleDeclaration:{this.usingVirtualDom?r.styleId?this.constructedStyleMutations.push(r):r.id&&((i=this.virtualDom.mirror.getNode(r.id))==null||i.rules.push(r)):this.applyStyleSheetMutation(r);break}case D.CanvasMutation:{if(!this.config.UNSAFE_replayCanvas)return;if(this.usingVirtualDom){const s=this.virtualDom.mirror.getNode(r.id);if(!s)return this.debugNodeNotFound(r,r.id);s.canvasMutations.push({event:t,mutation:r})}else{const s=this.mirror.getNode(r.id);if(!s)return this.debugNodeNotFound(r,r.id);st({event:t,mutation:r,target:s,imageMap:this.imageMap,canvasEventMap:this.canvasEventMap,errorHandler:this.warnCanvasMutationFailed.bind(this)})}break}case D.Font:{try{const s=new FontFace(r.family,r.buffer?new Uint8Array(JSON.parse(r.fontSource)):r.fontSource,r.descriptors);(l=this.iframe.contentDocument)==null||l.fonts.add(s)}catch(s){this.config.showWarning&&console.warn(s)}break}case D.Selection:{if(n){this.lastSelectionData=r;break}this.applySelection(r);break}case D.AdoptedStyleSheet:{this.usingVirtualDom?this.adoptedStyleSheets.push(r):this.applyAdoptedStyleSheet(r);break}}}applyMutation(t,n){if(this.config.useVirtualDom&&!this.usingVirtualDom&&n&&(this.usingVirtualDom=!0,Vt(this.iframe.contentDocument,this.mirror,this.virtualDom),Object.keys(this.legacy_missingNodeRetryMap).length))for(const c in this.legacy_missingNodeRetryMap)try{const u=this.legacy_missingNodeRetryMap[c],d=Be(u.node,this.virtualDom,this.mirror);d&&(u.node=d)}catch(u){this.config.showWarning&&console.warn(u)}const o=this.usingVirtualDom?this.virtualDom.mirror:this.mirror;t.removes.forEach(c=>{var u;const d=o.getNode(c.id);if(!d)return t.removes.find(f=>f.id===c.parentId)?void 0:this.warnNodeNotFound(t,c.id);let m=o.getNode(c.parentId);if(!m)return this.warnNodeNotFound(t,c.parentId);if(c.isShadow&&ne(m)&&(m=m.shadowRoot),o.removeNodeFromMap(d),m)try{m.removeChild(d),this.usingVirtualDom&&d.nodeName==="#text"&&m.nodeName==="STYLE"&&((u=m.rules)==null?void 0:u.length)>0&&(m.rules=[])}catch(f){if(f instanceof DOMException)this.warn("parent could not remove child in mutation",m,d,t);else throw f}});const i=re({},this.legacy_missingNodeRetryMap),l=[],r=c=>{let u=null;return c.nextId&&(u=o.getNode(c.nextId)),c.nextId!==null&&c.nextId!==void 0&&c.nextId!==-1&&!u},s=c=>{var u;if(!this.iframe.contentDocument)return console.warn("Looks like your replayer has been destroyed.");let d=o.getNode(c.parentId);if(!d)return c.node.type===I.Document?this.newDocumentQueue.push(c):l.push(c);c.node.isShadow&&(ne(d)||d.attachShadow({mode:"open"}),d=d.shadowRoot);let m=null,f=null;if(c.previousId&&(m=o.getNode(c.previousId)),c.nextId&&(f=o.getNode(c.nextId)),r(c))return l.push(c);if(c.node.rootId&&!o.getNode(c.node.rootId))return;const N=c.node.rootId?o.getNode(c.node.rootId):this.usingVirtualDom?this.virtualDom:this.iframe.contentDocument;if(fe(d,o)){this.attachDocumentToIframe(c,d);return}const E=(y,T)=>{for(const S of this.config.plugins||[])S.onBuild&&S.onBuild(y,{id:T,replayer:this})},v=H(c.node,{doc:N,mirror:o,skipChild:!0,hackCss:!0,cache:this.cache,afterAppend:E});if(c.previousId===-1||c.nextId===-1){i[c.node.id]={node:v,mutation:c};return}const p=o.getMeta(d);if(p&&p.type===I.Element&&p.tagName==="textarea"&&c.node.type===I.Text){const y=Array.isArray(d.childNodes)?d.childNodes:Array.from(d.childNodes);for(const T of y)T.nodeType===d.TEXT_NODE&&d.removeChild(T)}if(m&&m.nextSibling&&m.nextSibling.parentNode)d.insertBefore(v,m.nextSibling);else if(f&&f.parentNode)d.contains(f)?d.insertBefore(v,f):d.insertBefore(v,null);else{if(d===N)for(;N.firstChild;)N.removeChild(N.firstChild);d.appendChild(v)}if(E(v,c.node.id),this.usingVirtualDom&&v.nodeName==="#text"&&d.nodeName==="STYLE"&&((u=d.rules)==null?void 0:u.length)>0&&(d.rules=[]),fe(v,this.mirror)){const y=this.mirror.getId(v),T=this.newDocumentQueue.find(S=>S.parentId===y);T&&(this.attachDocumentToIframe(T,v),this.newDocumentQueue=this.newDocumentQueue.filter(S=>S!==T))}(c.previousId||c.nextId)&&this.legacy_resolveMissingNode(i,d,v,c)};t.adds.forEach(c=>{s(c)});const a=Date.now();for(;l.length;){const c=on(l);if(l.length=0,Date.now()-a>500){this.warn("Timeout in the loop, please check the resolve tree data:",c);break}for(const u of c)o.getNode(u.value.parentId)?et(u,d=>{s(d)}):this.debug("Drop resolve tree since there is no parent for the root node.",u)}Object.keys(i).length&&Object.assign(this.legacy_missingNodeRetryMap,i),rn(t.texts).forEach(c=>{var u;const d=o.getNode(c.id);if(!d)return t.removes.find(m=>m.id===c.id)?void 0:this.warnNodeNotFound(t,c.id);if(d.textContent=c.value,this.usingVirtualDom){const m=d.parentNode;((u=m?.rules)==null?void 0:u.length)>0&&(m.rules=[])}}),t.attributes.forEach(c=>{const u=o.getNode(c.id);if(!u)return t.removes.find(d=>d.id===c.id)?void 0:this.warnNodeNotFound(t,c.id);for(const d in c.attributes)if(typeof d=="string"){const m=c.attributes[d];if(m===null)u.removeAttribute(d);else if(typeof m=="string")try{if(d==="_cssText"&&(u.nodeName==="LINK"||u.nodeName==="STYLE"))try{const f=o.getMeta(u);Object.assign(f.attributes,c.attributes);const N=H(f,{doc:u.ownerDocument,mirror:o,skipChild:!0,hackCss:!0,cache:this.cache}),E=u.nextSibling,v=u.parentNode;if(N&&v){v.removeChild(u),v.insertBefore(N,E),o.replace(c.id,N);break}}catch{}u.setAttribute(d,m)}catch(f){this.config.showWarning&&console.warn("An error occurred may due to the checkout feature.",f)}else if(d==="style"){const f=m,N=u;for(const E in f)if(f[E]===!1)N.style.removeProperty(E);else if(f[E]instanceof Array){const v=f[E];N.style.setProperty(E,v[0],v[1])}else{const v=f[E];N.style.setProperty(E,v)}}}})}applyScroll(t,n){var o,i;const l=this.mirror.getNode(t.id);if(!l)return this.debugNodeNotFound(t,t.id);const r=this.mirror.getMeta(l);if(l===this.iframe.contentDocument)(o=this.iframe.contentWindow)==null||o.scrollTo({top:t.y,left:t.x,behavior:n?"auto":"smooth"});else if(r?.type===I.Document)(i=l.defaultView)==null||i.scrollTo({top:t.y,left:t.x,behavior:n?"auto":"smooth"});else try{l.scrollTo({top:t.y,left:t.x,behavior:n?"auto":"smooth"})}catch{}}applyInput(t){const n=this.mirror.getNode(t.id);if(!n)return this.debugNodeNotFound(t,t.id);try{n.checked=t.isChecked,n.value=t.text}catch{}}applySelection(t){try{const n=new Set,o=t.ranges.map(({start:i,startOffset:l,end:r,endOffset:s})=>{const a=this.mirror.getNode(i),c=this.mirror.getNode(r);if(!a||!c)return;const u=new Range;u.setStart(a,l),u.setEnd(c,s);const d=a.ownerDocument,m=d?.getSelection();return m&&n.add(m),{range:u,selection:m}});n.forEach(i=>i.removeAllRanges()),o.forEach(i=>{var l;return i&&((l=i.selection)==null?void 0:l.addRange(i.range))})}catch{}}applyStyleSheetMutation(t){var n;let o=null;t.styleId?o=this.styleMirror.getStyle(t.styleId):t.id&&(o=((n=this.mirror.getNode(t.id))==null?void 0:n.sheet)||null),o&&(t.source===D.StyleSheetRule?this.applyStyleSheetRule(t,o):t.source===D.StyleDeclaration&&this.applyStyleDeclaration(t,o))}applyStyleSheetRule(t,n){var o,i,l,r;if((o=t.adds)==null||o.forEach(({rule:s,index:a})=>{try{if(Array.isArray(a)){const{positions:c,index:u}=nt(a);X(n.cssRules,c).insertRule(s,u)}else{const c=a===void 0?void 0:Math.min(a,n.cssRules.length);n?.insertRule(s,c)}}catch{}}),(i=t.removes)==null||i.forEach(({index:s})=>{try{if(Array.isArray(s)){const{positions:a,index:c}=nt(s);X(n.cssRules,a).deleteRule(c||0)}else n?.deleteRule(s)}catch{}}),t.replace)try{(l=n.replace)==null||l.call(n,t.replace)}catch{}if(t.replaceSync)try{(r=n.replaceSync)==null||r.call(n,t.replaceSync)}catch{}}applyStyleDeclaration(t,n){t.set&&X(n.rules,t.index).style.setProperty(t.set.property,t.set.value,t.set.priority),t.remove&&X(n.rules,t.index).style.removeProperty(t.remove.property)}applyAdoptedStyleSheet(t){var n;const o=this.mirror.getNode(t.id);if(!o)return;(n=t.styles)==null||n.forEach(s=>{var a;let c=null,u=null;if(ne(o)?u=((a=o.ownerDocument)==null?void 0:a.defaultView)||null:o.nodeName==="#document"&&(u=o.defaultView),!!u)try{c=new u.CSSStyleSheet,this.styleMirror.add(c,s.styleId),this.applyStyleSheetRule({source:D.StyleSheetRule,adds:s.rules},c)}catch{}});const i=10;let l=0;const r=(s,a)=>{const c=a.map(u=>this.styleMirror.getStyle(u)).filter(u=>u!==null);ne(s)?s.shadowRoot.adoptedStyleSheets=c:s.nodeName==="#document"&&(s.adoptedStyleSheets=c),c.length!==a.length&&l<i&&(setTimeout(()=>r(s,a),0+100*l),l++)};r(o,t.styleIds)}legacy_resolveMissingNode(t,n,o,i){const{previousId:l,nextId:r}=i,s=l&&t[l],a=r&&t[r];if(s){const{node:c,mutation:u}=s;n.insertBefore(c,o),delete t[u.node.id],delete this.legacy_missingNodeRetryMap[u.node.id],(u.previousId||u.nextId)&&this.legacy_resolveMissingNode(t,n,c,u)}if(a){const{node:c,mutation:u}=a;n.insertBefore(c,o.nextSibling),delete t[u.node.id],delete this.legacy_missingNodeRetryMap[u.node.id],(u.previousId||u.nextId)&&this.legacy_resolveMissingNode(t,n,c,u)}}moveAndHover(t,n,o,i,l){const r=this.mirror.getNode(o);if(!r)return this.debugNodeNotFound(l,o);const s=tt(r,this.iframe),a=t*s.absoluteScale+s.x,c=n*s.absoluteScale+s.y;this.mouse.style.left=`${a}px`,this.mouse.style.top=`${c}px`,i||this.drawMouseTail({x:a,y:c}),this.hoverElements(r)}drawMouseTail(t){if(!this.mouseTail)return;const{lineCap:n,lineWidth:o,strokeStyle:i,duration:l}=this.config.mouseTail===!0?Ne:Object.assign({},Ne,this.config.mouseTail),r=()=>{if(!this.mouseTail)return;const s=this.mouseTail.getContext("2d");!s||!this.tailPositions.length||(s.clearRect(0,0,this.mouseTail.width,this.mouseTail.height),s.beginPath(),s.lineWidth=o,s.lineCap=n,s.strokeStyle=i,s.moveTo(this.tailPositions[0].x,this.tailPositions[0].y),this.tailPositions.forEach(a=>s.lineTo(a.x,a.y)),s.stroke())};this.tailPositions.push(t),r(),setTimeout(()=>{this.tailPositions=this.tailPositions.filter(s=>s!==t),r()},l/this.speedService.state.context.timer.speed)}hoverElements(t){var n;(n=this.iframe.contentDocument)==null||n.querySelectorAll(".\\:hover").forEach(i=>{i.classList.remove(":hover")});let o=t;for(;o;)o.classList&&o.classList.add(":hover"),o=o.parentElement}isUserInteraction(t){return t.type!==C.IncrementalSnapshot?!1:t.data.source>D.Mutation&&t.data.source<=D.Input}backToNormal(){this.nextUserInteractionEvent=null,!this.speedService.state.matches("normal")&&(this.speedService.send({type:"BACK_TO_NORMAL"}),this.emitter.emit(M.SkipEnd,{speed:this.speedService.state.context.normalSpeed}))}warnNodeNotFound(t,n){this.warn(`Node with id '${n}' not found. `,t)}warnCanvasMutationFailed(t,n){this.warn("Has error on canvas update",n,"canvas mutation:",t)}debugNodeNotFound(t,n){this.debug(ve,`Node with id '${n}' not found. `,t)}warn(...t){!this.config.showWarning||console.warn(ve,...t)}debug(...t){!this.config.showDebug||console.log(ve,...t)}}return ae.Replayer=Mn,Object.defineProperty(ae,"__esModule",{value:!0}),ae}({});
//# sourceMappingURL=rrweb-replay.min.js.map
