{"version": 3, "file": "vdom.min.js", "sources": ["../../rrweb-snapshot/es/rrweb-snapshot.js", "../src/style.ts", "../src/document.ts", "../src/diff.ts", "../src/index.ts"], "sourcesContent": ["var NodeType;\n(function (NodeType) {\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\n})(NodeType || (NodeType = {}));\n\nfunction isElement(n) {\n    return n.nodeType === n.ELEMENT_NODE;\n}\nfunction isShadowRoot(n) {\n    var _a;\n    var host = (_a = n) === null || _a === void 0 ? void 0 : _a.host;\n    return Boolean((host === null || host === void 0 ? void 0 : host.shadowRoot) === n);\n}\nvar Mirror = (function () {\n    function Mirror() {\n        this.idNodeMap = new Map();\n        this.nodeMetaMap = new WeakMap();\n    }\n    Mirror.prototype.getId = function (n) {\n        var _a;\n        if (!n)\n            return -1;\n        var id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\n        return id !== null && id !== void 0 ? id : -1;\n    };\n    Mirror.prototype.getNode = function (id) {\n        return this.idNodeMap.get(id) || null;\n    };\n    Mirror.prototype.getIds = function () {\n        return Array.from(this.idNodeMap.keys());\n    };\n    Mirror.prototype.getMeta = function (n) {\n        return this.nodeMetaMap.get(n) || null;\n    };\n    Mirror.prototype.removeNodeFromMap = function (n) {\n        var _this = this;\n        var id = this.getId(n);\n        this.idNodeMap[\"delete\"](id);\n        if (n.childNodes) {\n            n.childNodes.forEach(function (childNode) {\n                return _this.removeNodeFromMap(childNode);\n            });\n        }\n    };\n    Mirror.prototype.has = function (id) {\n        return this.idNodeMap.has(id);\n    };\n    Mirror.prototype.hasNode = function (node) {\n        return this.nodeMetaMap.has(node);\n    };\n    Mirror.prototype.add = function (n, meta) {\n        var id = meta.id;\n        this.idNodeMap.set(id, n);\n        this.nodeMetaMap.set(n, meta);\n    };\n    Mirror.prototype.replace = function (id, n) {\n        this.idNodeMap.set(id, n);\n    };\n    Mirror.prototype.reset = function () {\n        this.idNodeMap = new Map();\n        this.nodeMetaMap = new WeakMap();\n    };\n    return Mirror;\n}());\nfunction createMirror() {\n    return new Mirror();\n}\nfunction maskInputValue(_a) {\n    var maskInputOptions = _a.maskInputOptions, tagName = _a.tagName, type = _a.type, value = _a.value, maskInputFn = _a.maskInputFn;\n    var text = value || '';\n    if (maskInputOptions[tagName.toLowerCase()] ||\n        maskInputOptions[type]) {\n        if (maskInputFn) {\n            text = maskInputFn(text);\n        }\n        else {\n            text = '*'.repeat(text.length);\n        }\n    }\n    return text;\n}\nvar ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\nfunction is2DCanvasBlank(canvas) {\n    var ctx = canvas.getContext('2d');\n    if (!ctx)\n        return true;\n    var chunkSize = 50;\n    for (var x = 0; x < canvas.width; x += chunkSize) {\n        for (var y = 0; y < canvas.height; y += chunkSize) {\n            var getImageData = ctx.getImageData;\n            var originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData\n                ? getImageData[ORIGINAL_ATTRIBUTE_NAME]\n                : getImageData;\n            var pixelBuffer = new Uint32Array(originalGetImageData.call(ctx, x, y, Math.min(chunkSize, canvas.width - x), Math.min(chunkSize, canvas.height - y)).data.buffer);\n            if (pixelBuffer.some(function (pixel) { return pixel !== 0; }))\n                return false;\n        }\n    }\n    return true;\n}\n\nvar _id = 1;\nvar tagNameRegex = new RegExp('[^a-z0-9-_:]');\nvar IGNORED_NODE = -2;\nfunction genId() {\n    return _id++;\n}\nfunction getValidTagName(element) {\n    if (element instanceof HTMLFormElement) {\n        return 'form';\n    }\n    var processedTagName = element.tagName.toLowerCase().trim();\n    if (tagNameRegex.test(processedTagName)) {\n        return 'div';\n    }\n    return processedTagName;\n}\nfunction getCssRulesString(s) {\n    try {\n        var rules = s.rules || s.cssRules;\n        return rules ? Array.from(rules).map(getCssRuleString).join('') : null;\n    }\n    catch (error) {\n        return null;\n    }\n}\nfunction getCssRuleString(rule) {\n    var cssStringified = rule.cssText;\n    if (isCSSImportRule(rule)) {\n        try {\n            cssStringified = getCssRulesString(rule.styleSheet) || cssStringified;\n        }\n        catch (_a) {\n        }\n    }\n    return cssStringified;\n}\nfunction isCSSImportRule(rule) {\n    return 'styleSheet' in rule;\n}\nfunction stringifyStyleSheet(sheet) {\n    return sheet.cssRules\n        ? Array.from(sheet.cssRules)\n            .map(function (rule) { return rule.cssText || ''; })\n            .join('')\n        : '';\n}\nfunction extractOrigin(url) {\n    var origin = '';\n    if (url.indexOf('//') > -1) {\n        origin = url.split('/').slice(0, 3).join('/');\n    }\n    else {\n        origin = url.split('/')[0];\n    }\n    origin = origin.split('?')[0];\n    return origin;\n}\nvar canvasService;\nvar canvasCtx;\nvar URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\nvar RELATIVE_PATH = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\\\|\\/\\/|#).*/;\nvar DATA_URI = /^(data:)([^,]*),(.*)/i;\nfunction absoluteToStylesheet(cssText, href) {\n    return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {\n        var filePath = path1 || path2 || path3;\n        var maybeQuote = quote1 || quote2 || '';\n        if (!filePath) {\n            return origin;\n        }\n        if (!RELATIVE_PATH.test(filePath)) {\n            return \"url(\" + maybeQuote + filePath + maybeQuote + \")\";\n        }\n        if (DATA_URI.test(filePath)) {\n            return \"url(\" + maybeQuote + filePath + maybeQuote + \")\";\n        }\n        if (filePath[0] === '/') {\n            return \"url(\" + maybeQuote + (extractOrigin(href) + filePath) + maybeQuote + \")\";\n        }\n        var stack = href.split('/');\n        var parts = filePath.split('/');\n        stack.pop();\n        for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\n            var part = parts_1[_i];\n            if (part === '.') {\n                continue;\n            }\n            else if (part === '..') {\n                stack.pop();\n            }\n            else {\n                stack.push(part);\n            }\n        }\n        return \"url(\" + maybeQuote + stack.join('/') + maybeQuote + \")\";\n    });\n}\nvar SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\nvar SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\n    if (attributeValue.trim() === '') {\n        return attributeValue;\n    }\n    var pos = 0;\n    function collectCharacters(regEx) {\n        var chars;\n        var match = regEx.exec(attributeValue.substring(pos));\n        if (match) {\n            chars = match[0];\n            pos += chars.length;\n            return chars;\n        }\n        return '';\n    }\n    var output = [];\n    while (true) {\n        collectCharacters(SRCSET_COMMAS_OR_SPACES);\n        if (pos >= attributeValue.length) {\n            break;\n        }\n        var url = collectCharacters(SRCSET_NOT_SPACES);\n        if (url.slice(-1) === ',') {\n            url = absoluteToDoc(doc, url.substring(0, url.length - 1));\n            output.push(url);\n        }\n        else {\n            var descriptorsStr = '';\n            url = absoluteToDoc(doc, url);\n            var inParens = false;\n            while (true) {\n                var c = attributeValue.charAt(pos);\n                if (c === '') {\n                    output.push((url + descriptorsStr).trim());\n                    break;\n                }\n                else if (!inParens) {\n                    if (c === ',') {\n                        pos += 1;\n                        output.push((url + descriptorsStr).trim());\n                        break;\n                    }\n                    else if (c === '(') {\n                        inParens = true;\n                    }\n                }\n                else {\n                    if (c === ')') {\n                        inParens = false;\n                    }\n                }\n                descriptorsStr += c;\n                pos += 1;\n            }\n        }\n    }\n    return output.join(', ');\n}\nfunction absoluteToDoc(doc, attributeValue) {\n    if (!attributeValue || attributeValue.trim() === '') {\n        return attributeValue;\n    }\n    var a = doc.createElement('a');\n    a.href = attributeValue;\n    return a.href;\n}\nfunction isSVGElement(el) {\n    return Boolean(el.tagName === 'svg' || el.ownerSVGElement);\n}\nfunction getHref() {\n    var a = document.createElement('a');\n    a.href = '';\n    return a.href;\n}\nfunction transformAttribute(doc, tagName, name, value) {\n    if (name === 'src' || (name === 'href' && value)) {\n        return absoluteToDoc(doc, value);\n    }\n    else if (name === 'xlink:href' && value && value[0] !== '#') {\n        return absoluteToDoc(doc, value);\n    }\n    else if (name === 'background' &&\n        value &&\n        (tagName === 'table' || tagName === 'td' || tagName === 'th')) {\n        return absoluteToDoc(doc, value);\n    }\n    else if (name === 'srcset' && value) {\n        return getAbsoluteSrcsetString(doc, value);\n    }\n    else if (name === 'style' && value) {\n        return absoluteToStylesheet(value, getHref());\n    }\n    else if (tagName === 'object' && name === 'data' && value) {\n        return absoluteToDoc(doc, value);\n    }\n    else {\n        return value;\n    }\n}\nfunction _isBlockedElement(element, blockClass, blockSelector) {\n    if (typeof blockClass === 'string') {\n        if (element.classList.contains(blockClass)) {\n            return true;\n        }\n    }\n    else {\n        for (var eIndex = element.classList.length; eIndex--;) {\n            var className = element.classList[eIndex];\n            if (blockClass.test(className)) {\n                return true;\n            }\n        }\n    }\n    if (blockSelector) {\n        return element.matches(blockSelector);\n    }\n    return false;\n}\nfunction classMatchesRegex(node, regex, checkAncestors) {\n    if (!node)\n        return false;\n    if (node.nodeType !== node.ELEMENT_NODE) {\n        if (!checkAncestors)\n            return false;\n        return classMatchesRegex(node.parentNode, regex, checkAncestors);\n    }\n    for (var eIndex = node.classList.length; eIndex--;) {\n        var className = node.classList[eIndex];\n        if (regex.test(className)) {\n            return true;\n        }\n    }\n    if (!checkAncestors)\n        return false;\n    return classMatchesRegex(node.parentNode, regex, checkAncestors);\n}\nfunction needMaskingText(node, maskTextClass, maskTextSelector) {\n    var el = node.nodeType === node.ELEMENT_NODE\n        ? node\n        : node.parentElement;\n    if (el === null)\n        return false;\n    if (typeof maskTextClass === 'string') {\n        if (el.classList.contains(maskTextClass))\n            return true;\n        if (el.closest(\".\" + maskTextClass))\n            return true;\n    }\n    else {\n        if (classMatchesRegex(el, maskTextClass, true))\n            return true;\n    }\n    if (maskTextSelector) {\n        if (el.matches(maskTextSelector))\n            return true;\n        if (el.closest(maskTextSelector))\n            return true;\n    }\n    return false;\n}\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\n    var win = iframeEl.contentWindow;\n    if (!win) {\n        return;\n    }\n    var fired = false;\n    var readyState;\n    try {\n        readyState = win.document.readyState;\n    }\n    catch (error) {\n        return;\n    }\n    if (readyState !== 'complete') {\n        var timer_1 = setTimeout(function () {\n            if (!fired) {\n                listener();\n                fired = true;\n            }\n        }, iframeLoadTimeout);\n        iframeEl.addEventListener('load', function () {\n            clearTimeout(timer_1);\n            fired = true;\n            listener();\n        });\n        return;\n    }\n    var blankUrl = 'about:blank';\n    if (win.location.href !== blankUrl ||\n        iframeEl.src === blankUrl ||\n        iframeEl.src === '') {\n        setTimeout(listener, 0);\n        return;\n    }\n    iframeEl.addEventListener('load', listener);\n}\nfunction serializeNode(n, options) {\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c;\n    var rootId = getRootId(doc, mirror);\n    switch (n.nodeType) {\n        case n.DOCUMENT_NODE:\n            if (n.compatMode !== 'CSS1Compat') {\n                return {\n                    type: NodeType.Document,\n                    childNodes: [],\n                    compatMode: n.compatMode,\n                    rootId: rootId\n                };\n            }\n            else {\n                return {\n                    type: NodeType.Document,\n                    childNodes: [],\n                    rootId: rootId\n                };\n            }\n        case n.DOCUMENT_TYPE_NODE:\n            return {\n                type: NodeType.DocumentType,\n                name: n.name,\n                publicId: n.publicId,\n                systemId: n.systemId,\n                rootId: rootId\n            };\n        case n.ELEMENT_NODE:\n            return serializeElementNode(n, {\n                doc: doc,\n                blockClass: blockClass,\n                blockSelector: blockSelector,\n                inlineStylesheet: inlineStylesheet,\n                maskInputOptions: maskInputOptions,\n                maskInputFn: maskInputFn,\n                dataURLOptions: dataURLOptions,\n                inlineImages: inlineImages,\n                recordCanvas: recordCanvas,\n                keepIframeSrcFn: keepIframeSrcFn,\n                newlyAddedElement: newlyAddedElement,\n                rootId: rootId\n            });\n        case n.TEXT_NODE:\n            return serializeTextNode(n, {\n                maskTextClass: maskTextClass,\n                maskTextSelector: maskTextSelector,\n                maskTextFn: maskTextFn,\n                rootId: rootId\n            });\n        case n.CDATA_SECTION_NODE:\n            return {\n                type: NodeType.CDATA,\n                textContent: '',\n                rootId: rootId\n            };\n        case n.COMMENT_NODE:\n            return {\n                type: NodeType.Comment,\n                textContent: n.textContent || '',\n                rootId: rootId\n            };\n        default:\n            return false;\n    }\n}\nfunction getRootId(doc, mirror) {\n    if (!mirror.hasNode(doc))\n        return undefined;\n    var docId = mirror.getId(doc);\n    return docId === 1 ? undefined : docId;\n}\nfunction serializeTextNode(n, options) {\n    var _a;\n    var maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, maskTextFn = options.maskTextFn, rootId = options.rootId;\n    var parentTagName = n.parentNode && n.parentNode.tagName;\n    var textContent = n.textContent;\n    var isStyle = parentTagName === 'STYLE' ? true : undefined;\n    var isScript = parentTagName === 'SCRIPT' ? true : undefined;\n    if (isStyle && textContent) {\n        try {\n            if (n.nextSibling || n.previousSibling) {\n            }\n            else if ((_a = n.parentNode.sheet) === null || _a === void 0 ? void 0 : _a.cssRules) {\n                textContent = stringifyStyleSheet(n.parentNode.sheet);\n            }\n        }\n        catch (err) {\n            console.warn(\"Cannot get CSS styles from text's parentNode. Error: \" + err, n);\n        }\n        textContent = absoluteToStylesheet(textContent, getHref());\n    }\n    if (isScript) {\n        textContent = 'SCRIPT_PLACEHOLDER';\n    }\n    if (!isStyle &&\n        !isScript &&\n        textContent &&\n        needMaskingText(n, maskTextClass, maskTextSelector)) {\n        textContent = maskTextFn\n            ? maskTextFn(textContent)\n            : textContent.replace(/[\\S]/g, '*');\n    }\n    return {\n        type: NodeType.Text,\n        textContent: textContent || '',\n        isStyle: isStyle,\n        rootId: rootId\n    };\n}\nfunction serializeElementNode(n, options) {\n    var doc = options.doc, blockClass = options.blockClass, blockSelector = options.blockSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c, rootId = options.rootId;\n    var needBlock = _isBlockedElement(n, blockClass, blockSelector);\n    var tagName = getValidTagName(n);\n    var attributes = {};\n    var len = n.attributes.length;\n    for (var i = 0; i < len; i++) {\n        var attr = n.attributes[i];\n        attributes[attr.name] = transformAttribute(doc, tagName, attr.name, attr.value);\n    }\n    if (tagName === 'link' && inlineStylesheet) {\n        var stylesheet = Array.from(doc.styleSheets).find(function (s) {\n            return s.href === n.href;\n        });\n        var cssText = null;\n        if (stylesheet) {\n            cssText = getCssRulesString(stylesheet);\n        }\n        if (cssText) {\n            delete attributes.rel;\n            delete attributes.href;\n            attributes._cssText = absoluteToStylesheet(cssText, stylesheet.href);\n        }\n    }\n    if (tagName === 'style' &&\n        n.sheet &&\n        !(n.innerText || n.textContent || '').trim().length) {\n        var cssText = getCssRulesString(n.sheet);\n        if (cssText) {\n            attributes._cssText = absoluteToStylesheet(cssText, getHref());\n        }\n    }\n    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {\n        var value = n.value;\n        if (attributes.type !== 'radio' &&\n            attributes.type !== 'checkbox' &&\n            attributes.type !== 'submit' &&\n            attributes.type !== 'button' &&\n            value) {\n            attributes.value = maskInputValue({\n                type: attributes.type,\n                tagName: tagName,\n                value: value,\n                maskInputOptions: maskInputOptions,\n                maskInputFn: maskInputFn\n            });\n        }\n        else if (n.checked) {\n            attributes.checked = n.checked;\n        }\n    }\n    if (tagName === 'option') {\n        if (n.selected && !maskInputOptions['select']) {\n            attributes.selected = true;\n        }\n        else {\n            delete attributes.selected;\n        }\n    }\n    if (tagName === 'canvas' && recordCanvas) {\n        if (n.__context === '2d') {\n            if (!is2DCanvasBlank(n)) {\n                attributes.rr_dataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n            }\n        }\n        else if (!('__context' in n)) {\n            var canvasDataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n            var blankCanvas = document.createElement('canvas');\n            blankCanvas.width = n.width;\n            blankCanvas.height = n.height;\n            var blankCanvasDataURL = blankCanvas.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n            if (canvasDataURL !== blankCanvasDataURL) {\n                attributes.rr_dataURL = canvasDataURL;\n            }\n        }\n    }\n    if (tagName === 'img' && inlineImages) {\n        if (!canvasService) {\n            canvasService = doc.createElement('canvas');\n            canvasCtx = canvasService.getContext('2d');\n        }\n        var image_1 = n;\n        var oldValue_1 = image_1.crossOrigin;\n        image_1.crossOrigin = 'anonymous';\n        var recordInlineImage = function () {\n            try {\n                canvasService.width = image_1.naturalWidth;\n                canvasService.height = image_1.naturalHeight;\n                canvasCtx.drawImage(image_1, 0, 0);\n                attributes.rr_dataURL = canvasService.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n            }\n            catch (err) {\n                console.warn(\"Cannot inline img src=\" + image_1.currentSrc + \"! Error: \" + err);\n            }\n            oldValue_1\n                ? (attributes.crossOrigin = oldValue_1)\n                : image_1.removeAttribute('crossorigin');\n        };\n        if (image_1.complete && image_1.naturalWidth !== 0)\n            recordInlineImage();\n        else\n            image_1.onload = recordInlineImage;\n    }\n    if (tagName === 'audio' || tagName === 'video') {\n        attributes.rr_mediaState = n.paused\n            ? 'paused'\n            : 'played';\n        attributes.rr_mediaCurrentTime = n.currentTime;\n    }\n    if (!newlyAddedElement) {\n        if (n.scrollLeft) {\n            attributes.rr_scrollLeft = n.scrollLeft;\n        }\n        if (n.scrollTop) {\n            attributes.rr_scrollTop = n.scrollTop;\n        }\n    }\n    if (needBlock) {\n        var _d = n.getBoundingClientRect(), width = _d.width, height = _d.height;\n        attributes = {\n            \"class\": attributes[\"class\"],\n            rr_width: width + \"px\",\n            rr_height: height + \"px\"\n        };\n    }\n    if (tagName === 'iframe' && !keepIframeSrcFn(attributes.src)) {\n        if (!n.contentDocument) {\n            attributes.rr_src = attributes.src;\n        }\n        delete attributes.src;\n    }\n    return {\n        type: NodeType.Element,\n        tagName: tagName,\n        attributes: attributes,\n        childNodes: [],\n        isSVG: isSVGElement(n) || undefined,\n        needBlock: needBlock,\n        rootId: rootId\n    };\n}\nfunction lowerIfExists(maybeAttr) {\n    if (maybeAttr === undefined) {\n        return '';\n    }\n    else {\n        return maybeAttr.toLowerCase();\n    }\n}\nfunction slimDOMExcluded(sn, slimDOMOptions) {\n    if (slimDOMOptions.comment && sn.type === NodeType.Comment) {\n        return true;\n    }\n    else if (sn.type === NodeType.Element) {\n        if (slimDOMOptions.script &&\n            (sn.tagName === 'script' ||\n                (sn.tagName === 'link' &&\n                    sn.attributes.rel === 'preload' &&\n                    sn.attributes.as === 'script') ||\n                (sn.tagName === 'link' &&\n                    sn.attributes.rel === 'prefetch' &&\n                    typeof sn.attributes.href === 'string' &&\n                    sn.attributes.href.endsWith('.js')))) {\n            return true;\n        }\n        else if (slimDOMOptions.headFavicon &&\n            ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon') ||\n                (sn.tagName === 'meta' &&\n                    (lowerIfExists(sn.attributes.name).match(/^msapplication-tile(image|color)$/) ||\n                        lowerIfExists(sn.attributes.name) === 'application-name' ||\n                        lowerIfExists(sn.attributes.rel) === 'icon' ||\n                        lowerIfExists(sn.attributes.rel) === 'apple-touch-icon' ||\n                        lowerIfExists(sn.attributes.rel) === 'shortcut icon')))) {\n            return true;\n        }\n        else if (sn.tagName === 'meta') {\n            if (slimDOMOptions.headMetaDescKeywords &&\n                lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaSocial &&\n                (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) ||\n                    lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) ||\n                    lowerIfExists(sn.attributes.name) === 'pinterest')) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaRobots &&\n                (lowerIfExists(sn.attributes.name) === 'robots' ||\n                    lowerIfExists(sn.attributes.name) === 'googlebot' ||\n                    lowerIfExists(sn.attributes.name) === 'bingbot')) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaHttpEquiv &&\n                sn.attributes['http-equiv'] !== undefined) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaAuthorship &&\n                (lowerIfExists(sn.attributes.name) === 'author' ||\n                    lowerIfExists(sn.attributes.name) === 'generator' ||\n                    lowerIfExists(sn.attributes.name) === 'framework' ||\n                    lowerIfExists(sn.attributes.name) === 'publisher' ||\n                    lowerIfExists(sn.attributes.name) === 'progid' ||\n                    lowerIfExists(sn.attributes.property).match(/^article:/) ||\n                    lowerIfExists(sn.attributes.property).match(/^product:/))) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaVerification &&\n                (lowerIfExists(sn.attributes.name) === 'google-site-verification' ||\n                    lowerIfExists(sn.attributes.name) === 'yandex-verification' ||\n                    lowerIfExists(sn.attributes.name) === 'csrf-token' ||\n                    lowerIfExists(sn.attributes.name) === 'p:domain_verify' ||\n                    lowerIfExists(sn.attributes.name) === 'verify-v1' ||\n                    lowerIfExists(sn.attributes.name) === 'verification' ||\n                    lowerIfExists(sn.attributes.name) === 'shopify-checkout-api-token')) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nfunction serializeNodeWithId(n, options) {\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.inlineStylesheet, inlineStylesheet = _b === void 0 ? true : _b, _c = options.maskInputOptions, maskInputOptions = _c === void 0 ? {} : _c, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, slimDOMOptions = options.slimDOMOptions, _d = options.dataURLOptions, dataURLOptions = _d === void 0 ? {} : _d, _e = options.inlineImages, inlineImages = _e === void 0 ? false : _e, _f = options.recordCanvas, recordCanvas = _f === void 0 ? false : _f, onSerialize = options.onSerialize, onIframeLoad = options.onIframeLoad, _g = options.iframeLoadTimeout, iframeLoadTimeout = _g === void 0 ? 5000 : _g, _h = options.keepIframeSrcFn, keepIframeSrcFn = _h === void 0 ? function () { return false; } : _h, _j = options.newlyAddedElement, newlyAddedElement = _j === void 0 ? false : _j;\n    var _k = options.preserveWhiteSpace, preserveWhiteSpace = _k === void 0 ? true : _k;\n    var _serializedNode = serializeNode(n, {\n        doc: doc,\n        mirror: mirror,\n        blockClass: blockClass,\n        blockSelector: blockSelector,\n        maskTextClass: maskTextClass,\n        maskTextSelector: maskTextSelector,\n        inlineStylesheet: inlineStylesheet,\n        maskInputOptions: maskInputOptions,\n        maskTextFn: maskTextFn,\n        maskInputFn: maskInputFn,\n        dataURLOptions: dataURLOptions,\n        inlineImages: inlineImages,\n        recordCanvas: recordCanvas,\n        keepIframeSrcFn: keepIframeSrcFn,\n        newlyAddedElement: newlyAddedElement\n    });\n    if (!_serializedNode) {\n        console.warn(n, 'not serialized');\n        return null;\n    }\n    var id;\n    if (mirror.hasNode(n)) {\n        id = mirror.getId(n);\n    }\n    else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||\n        (!preserveWhiteSpace &&\n            _serializedNode.type === NodeType.Text &&\n            !_serializedNode.isStyle &&\n            !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, '').length)) {\n        id = IGNORED_NODE;\n    }\n    else {\n        id = genId();\n    }\n    if (id === IGNORED_NODE) {\n        return null;\n    }\n    var serializedNode = Object.assign(_serializedNode, { id: id });\n    mirror.add(n, serializedNode);\n    if (onSerialize) {\n        onSerialize(n);\n    }\n    var recordChild = !skipChild;\n    if (serializedNode.type === NodeType.Element) {\n        recordChild = recordChild && !serializedNode.needBlock;\n        delete serializedNode.needBlock;\n        if (n.shadowRoot)\n            serializedNode.isShadowHost = true;\n    }\n    if ((serializedNode.type === NodeType.Document ||\n        serializedNode.type === NodeType.Element) &&\n        recordChild) {\n        if (slimDOMOptions.headWhitespace &&\n            serializedNode.type === NodeType.Element &&\n            serializedNode.tagName === 'head') {\n            preserveWhiteSpace = false;\n        }\n        var bypassOptions = {\n            doc: doc,\n            mirror: mirror,\n            blockClass: blockClass,\n            blockSelector: blockSelector,\n            maskTextClass: maskTextClass,\n            maskTextSelector: maskTextSelector,\n            skipChild: skipChild,\n            inlineStylesheet: inlineStylesheet,\n            maskInputOptions: maskInputOptions,\n            maskTextFn: maskTextFn,\n            maskInputFn: maskInputFn,\n            slimDOMOptions: slimDOMOptions,\n            dataURLOptions: dataURLOptions,\n            inlineImages: inlineImages,\n            recordCanvas: recordCanvas,\n            preserveWhiteSpace: preserveWhiteSpace,\n            onSerialize: onSerialize,\n            onIframeLoad: onIframeLoad,\n            iframeLoadTimeout: iframeLoadTimeout,\n            keepIframeSrcFn: keepIframeSrcFn\n        };\n        for (var _i = 0, _l = Array.from(n.childNodes); _i < _l.length; _i++) {\n            var childN = _l[_i];\n            var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\n            if (serializedChildNode) {\n                serializedNode.childNodes.push(serializedChildNode);\n            }\n        }\n        if (isElement(n) && n.shadowRoot) {\n            for (var _m = 0, _o = Array.from(n.shadowRoot.childNodes); _m < _o.length; _m++) {\n                var childN = _o[_m];\n                var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\n                if (serializedChildNode) {\n                    serializedChildNode.isShadow = true;\n                    serializedNode.childNodes.push(serializedChildNode);\n                }\n            }\n        }\n    }\n    if (n.parentNode && isShadowRoot(n.parentNode)) {\n        serializedNode.isShadow = true;\n    }\n    if (serializedNode.type === NodeType.Element &&\n        serializedNode.tagName === 'iframe') {\n        onceIframeLoaded(n, function () {\n            var iframeDoc = n.contentDocument;\n            if (iframeDoc && onIframeLoad) {\n                var serializedIframeNode = serializeNodeWithId(iframeDoc, {\n                    doc: iframeDoc,\n                    mirror: mirror,\n                    blockClass: blockClass,\n                    blockSelector: blockSelector,\n                    maskTextClass: maskTextClass,\n                    maskTextSelector: maskTextSelector,\n                    skipChild: false,\n                    inlineStylesheet: inlineStylesheet,\n                    maskInputOptions: maskInputOptions,\n                    maskTextFn: maskTextFn,\n                    maskInputFn: maskInputFn,\n                    slimDOMOptions: slimDOMOptions,\n                    dataURLOptions: dataURLOptions,\n                    inlineImages: inlineImages,\n                    recordCanvas: recordCanvas,\n                    preserveWhiteSpace: preserveWhiteSpace,\n                    onSerialize: onSerialize,\n                    onIframeLoad: onIframeLoad,\n                    iframeLoadTimeout: iframeLoadTimeout,\n                    keepIframeSrcFn: keepIframeSrcFn\n                });\n                if (serializedIframeNode) {\n                    onIframeLoad(n, serializedIframeNode);\n                }\n            }\n        }, iframeLoadTimeout);\n    }\n    return serializedNode;\n}\nfunction snapshot(n, options) {\n    var _a = options || {}, _b = _a.mirror, mirror = _b === void 0 ? new Mirror() : _b, _c = _a.blockClass, blockClass = _c === void 0 ? 'rr-block' : _c, _d = _a.blockSelector, blockSelector = _d === void 0 ? null : _d, _e = _a.maskTextClass, maskTextClass = _e === void 0 ? 'rr-mask' : _e, _f = _a.maskTextSelector, maskTextSelector = _f === void 0 ? null : _f, _g = _a.inlineStylesheet, inlineStylesheet = _g === void 0 ? true : _g, _h = _a.inlineImages, inlineImages = _h === void 0 ? false : _h, _j = _a.recordCanvas, recordCanvas = _j === void 0 ? false : _j, _k = _a.maskAllInputs, maskAllInputs = _k === void 0 ? false : _k, maskTextFn = _a.maskTextFn, maskInputFn = _a.maskInputFn, _l = _a.slimDOM, slimDOM = _l === void 0 ? false : _l, dataURLOptions = _a.dataURLOptions, preserveWhiteSpace = _a.preserveWhiteSpace, onSerialize = _a.onSerialize, onIframeLoad = _a.onIframeLoad, iframeLoadTimeout = _a.iframeLoadTimeout, _m = _a.keepIframeSrcFn, keepIframeSrcFn = _m === void 0 ? function () { return false; } : _m;\n    var maskInputOptions = maskAllInputs === true\n        ? {\n            color: true,\n            date: true,\n            'datetime-local': true,\n            email: true,\n            month: true,\n            number: true,\n            range: true,\n            search: true,\n            tel: true,\n            text: true,\n            time: true,\n            url: true,\n            week: true,\n            textarea: true,\n            select: true,\n            password: true\n        }\n        : maskAllInputs === false\n            ? {\n                password: true\n            }\n            : maskAllInputs;\n    var slimDOMOptions = slimDOM === true || slimDOM === 'all'\n        ?\n            {\n                script: true,\n                comment: true,\n                headFavicon: true,\n                headWhitespace: true,\n                headMetaDescKeywords: slimDOM === 'all',\n                headMetaSocial: true,\n                headMetaRobots: true,\n                headMetaHttpEquiv: true,\n                headMetaAuthorship: true,\n                headMetaVerification: true\n            }\n        : slimDOM === false\n            ? {}\n            : slimDOM;\n    return serializeNodeWithId(n, {\n        doc: n,\n        mirror: mirror,\n        blockClass: blockClass,\n        blockSelector: blockSelector,\n        maskTextClass: maskTextClass,\n        maskTextSelector: maskTextSelector,\n        skipChild: false,\n        inlineStylesheet: inlineStylesheet,\n        maskInputOptions: maskInputOptions,\n        maskTextFn: maskTextFn,\n        maskInputFn: maskInputFn,\n        slimDOMOptions: slimDOMOptions,\n        dataURLOptions: dataURLOptions,\n        inlineImages: inlineImages,\n        recordCanvas: recordCanvas,\n        preserveWhiteSpace: preserveWhiteSpace,\n        onSerialize: onSerialize,\n        onIframeLoad: onIframeLoad,\n        iframeLoadTimeout: iframeLoadTimeout,\n        keepIframeSrcFn: keepIframeSrcFn,\n        newlyAddedElement: false\n    });\n}\nfunction visitSnapshot(node, onVisit) {\n    function walk(current) {\n        onVisit(current);\n        if (current.type === NodeType.Document ||\n            current.type === NodeType.Element) {\n            current.childNodes.forEach(walk);\n        }\n    }\n    walk(node);\n}\nfunction cleanupSnapshot() {\n    _id = 1;\n}\n\nvar commentre = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\nfunction parse(css, options) {\n    if (options === void 0) { options = {}; }\n    var lineno = 1;\n    var column = 1;\n    function updatePosition(str) {\n        var lines = str.match(/\\n/g);\n        if (lines) {\n            lineno += lines.length;\n        }\n        var i = str.lastIndexOf('\\n');\n        column = i === -1 ? column + str.length : str.length - i;\n    }\n    function position() {\n        var start = { line: lineno, column: column };\n        return function (node) {\n            node.position = new Position(start);\n            whitespace();\n            return node;\n        };\n    }\n    var Position = (function () {\n        function Position(start) {\n            this.start = start;\n            this.end = { line: lineno, column: column };\n            this.source = options.source;\n        }\n        return Position;\n    }());\n    Position.prototype.content = css;\n    var errorsList = [];\n    function error(msg) {\n        var err = new Error(options.source + ':' + lineno + ':' + column + ': ' + msg);\n        err.reason = msg;\n        err.filename = options.source;\n        err.line = lineno;\n        err.column = column;\n        err.source = css;\n        if (options.silent) {\n            errorsList.push(err);\n        }\n        else {\n            throw err;\n        }\n    }\n    function stylesheet() {\n        var rulesList = rules();\n        return {\n            type: 'stylesheet',\n            stylesheet: {\n                source: options.source,\n                rules: rulesList,\n                parsingErrors: errorsList\n            }\n        };\n    }\n    function open() {\n        return match(/^{\\s*/);\n    }\n    function close() {\n        return match(/^}/);\n    }\n    function rules() {\n        var node;\n        var rules = [];\n        whitespace();\n        comments(rules);\n        while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\n            if (node !== false) {\n                rules.push(node);\n                comments(rules);\n            }\n        }\n        return rules;\n    }\n    function match(re) {\n        var m = re.exec(css);\n        if (!m) {\n            return;\n        }\n        var str = m[0];\n        updatePosition(str);\n        css = css.slice(str.length);\n        return m;\n    }\n    function whitespace() {\n        match(/^\\s*/);\n    }\n    function comments(rules) {\n        if (rules === void 0) { rules = []; }\n        var c;\n        while ((c = comment())) {\n            if (c !== false) {\n                rules.push(c);\n            }\n            c = comment();\n        }\n        return rules;\n    }\n    function comment() {\n        var pos = position();\n        if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\n            return;\n        }\n        var i = 2;\n        while ('' !== css.charAt(i) &&\n            ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {\n            ++i;\n        }\n        i += 2;\n        if ('' === css.charAt(i - 1)) {\n            return error('End of comment missing');\n        }\n        var str = css.slice(2, i - 2);\n        column += 2;\n        updatePosition(str);\n        css = css.slice(i);\n        column += 2;\n        return pos({\n            type: 'comment',\n            comment: str\n        });\n    }\n    function selector() {\n        var m = match(/^([^{]+)/);\n        if (!m) {\n            return;\n        }\n        return trim(m[0])\n            .replace(/\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*\\/+/g, '')\n            .replace(/\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'/g, function (m) {\n            return m.replace(/,/g, '\\u200C');\n        })\n            .split(/\\s*(?![^(]*\\)),\\s*/)\n            .map(function (s) {\n            return s.replace(/\\u200C/g, ',');\n        });\n    }\n    function declaration() {\n        var pos = position();\n        var propMatch = match(/^(\\*?[-#\\/\\*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/);\n        if (!propMatch) {\n            return;\n        }\n        var prop = trim(propMatch[0]);\n        if (!match(/^:\\s*/)) {\n            return error(\"property missing ':'\");\n        }\n        var val = match(/^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^\\)]*?\\)|[^};])+)/);\n        var ret = pos({\n            type: 'declaration',\n            property: prop.replace(commentre, ''),\n            value: val ? trim(val[0]).replace(commentre, '') : ''\n        });\n        match(/^[;\\s]*/);\n        return ret;\n    }\n    function declarations() {\n        var decls = [];\n        if (!open()) {\n            return error(\"missing '{'\");\n        }\n        comments(decls);\n        var decl;\n        while ((decl = declaration())) {\n            if (decl !== false) {\n                decls.push(decl);\n                comments(decls);\n            }\n            decl = declaration();\n        }\n        if (!close()) {\n            return error(\"missing '}'\");\n        }\n        return decls;\n    }\n    function keyframe() {\n        var m;\n        var vals = [];\n        var pos = position();\n        while ((m = match(/^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/))) {\n            vals.push(m[1]);\n            match(/^,\\s*/);\n        }\n        if (!vals.length) {\n            return;\n        }\n        return pos({\n            type: 'keyframe',\n            values: vals,\n            declarations: declarations()\n        });\n    }\n    function atkeyframes() {\n        var pos = position();\n        var m = match(/^@([-\\w]+)?keyframes\\s*/);\n        if (!m) {\n            return;\n        }\n        var vendor = m[1];\n        m = match(/^([-\\w]+)\\s*/);\n        if (!m) {\n            return error('@keyframes missing name');\n        }\n        var name = m[1];\n        if (!open()) {\n            return error(\"@keyframes missing '{'\");\n        }\n        var frame;\n        var frames = comments();\n        while ((frame = keyframe())) {\n            frames.push(frame);\n            frames = frames.concat(comments());\n        }\n        if (!close()) {\n            return error(\"@keyframes missing '}'\");\n        }\n        return pos({\n            type: 'keyframes',\n            name: name,\n            vendor: vendor,\n            keyframes: frames\n        });\n    }\n    function atsupports() {\n        var pos = position();\n        var m = match(/^@supports *([^{]+)/);\n        if (!m) {\n            return;\n        }\n        var supports = trim(m[1]);\n        if (!open()) {\n            return error(\"@supports missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@supports missing '}'\");\n        }\n        return pos({\n            type: 'supports',\n            supports: supports,\n            rules: style\n        });\n    }\n    function athost() {\n        var pos = position();\n        var m = match(/^@host\\s*/);\n        if (!m) {\n            return;\n        }\n        if (!open()) {\n            return error(\"@host missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@host missing '}'\");\n        }\n        return pos({\n            type: 'host',\n            rules: style\n        });\n    }\n    function atmedia() {\n        var pos = position();\n        var m = match(/^@media *([^{]+)/);\n        if (!m) {\n            return;\n        }\n        var media = trim(m[1]);\n        if (!open()) {\n            return error(\"@media missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@media missing '}'\");\n        }\n        return pos({\n            type: 'media',\n            media: media,\n            rules: style\n        });\n    }\n    function atcustommedia() {\n        var pos = position();\n        var m = match(/^@custom-media\\s+(--[^\\s]+)\\s*([^{;]+);/);\n        if (!m) {\n            return;\n        }\n        return pos({\n            type: 'custom-media',\n            name: trim(m[1]),\n            media: trim(m[2])\n        });\n    }\n    function atpage() {\n        var pos = position();\n        var m = match(/^@page */);\n        if (!m) {\n            return;\n        }\n        var sel = selector() || [];\n        if (!open()) {\n            return error(\"@page missing '{'\");\n        }\n        var decls = comments();\n        var decl;\n        while ((decl = declaration())) {\n            decls.push(decl);\n            decls = decls.concat(comments());\n        }\n        if (!close()) {\n            return error(\"@page missing '}'\");\n        }\n        return pos({\n            type: 'page',\n            selectors: sel,\n            declarations: decls\n        });\n    }\n    function atdocument() {\n        var pos = position();\n        var m = match(/^@([-\\w]+)?document *([^{]+)/);\n        if (!m) {\n            return;\n        }\n        var vendor = trim(m[1]);\n        var doc = trim(m[2]);\n        if (!open()) {\n            return error(\"@document missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@document missing '}'\");\n        }\n        return pos({\n            type: 'document',\n            document: doc,\n            vendor: vendor,\n            rules: style\n        });\n    }\n    function atfontface() {\n        var pos = position();\n        var m = match(/^@font-face\\s*/);\n        if (!m) {\n            return;\n        }\n        if (!open()) {\n            return error(\"@font-face missing '{'\");\n        }\n        var decls = comments();\n        var decl;\n        while ((decl = declaration())) {\n            decls.push(decl);\n            decls = decls.concat(comments());\n        }\n        if (!close()) {\n            return error(\"@font-face missing '}'\");\n        }\n        return pos({\n            type: 'font-face',\n            declarations: decls\n        });\n    }\n    var atimport = _compileAtrule('import');\n    var atcharset = _compileAtrule('charset');\n    var atnamespace = _compileAtrule('namespace');\n    function _compileAtrule(name) {\n        var re = new RegExp('^@' + name + '\\\\s*([^;]+);');\n        return function () {\n            var pos = position();\n            var m = match(re);\n            if (!m) {\n                return;\n            }\n            var ret = { type: name };\n            ret[name] = m[1].trim();\n            return pos(ret);\n        };\n    }\n    function atrule() {\n        if (css[0] !== '@') {\n            return;\n        }\n        return (atkeyframes() ||\n            atmedia() ||\n            atcustommedia() ||\n            atsupports() ||\n            atimport() ||\n            atcharset() ||\n            atnamespace() ||\n            atdocument() ||\n            atpage() ||\n            athost() ||\n            atfontface());\n    }\n    function rule() {\n        var pos = position();\n        var sel = selector();\n        if (!sel) {\n            return error('selector missing');\n        }\n        comments();\n        return pos({\n            type: 'rule',\n            selectors: sel,\n            declarations: declarations()\n        });\n    }\n    return addParent(stylesheet());\n}\nfunction trim(str) {\n    return str ? str.replace(/^\\s+|\\s+$/g, '') : '';\n}\nfunction addParent(obj, parent) {\n    var isNode = obj && typeof obj.type === 'string';\n    var childParent = isNode ? obj : parent;\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\n        var k = _a[_i];\n        var value = obj[k];\n        if (Array.isArray(value)) {\n            value.forEach(function (v) {\n                addParent(v, childParent);\n            });\n        }\n        else if (value && typeof value === 'object') {\n            addParent(value, childParent);\n        }\n    }\n    if (isNode) {\n        Object.defineProperty(obj, 'parent', {\n            configurable: true,\n            writable: true,\n            enumerable: false,\n            value: parent || null\n        });\n    }\n    return obj;\n}\n\nvar tagMap = {\n    script: 'noscript',\n    altglyph: 'altGlyph',\n    altglyphdef: 'altGlyphDef',\n    altglyphitem: 'altGlyphItem',\n    animatecolor: 'animateColor',\n    animatemotion: 'animateMotion',\n    animatetransform: 'animateTransform',\n    clippath: 'clipPath',\n    feblend: 'feBlend',\n    fecolormatrix: 'feColorMatrix',\n    fecomponenttransfer: 'feComponentTransfer',\n    fecomposite: 'feComposite',\n    feconvolvematrix: 'feConvolveMatrix',\n    fediffuselighting: 'feDiffuseLighting',\n    fedisplacementmap: 'feDisplacementMap',\n    fedistantlight: 'feDistantLight',\n    fedropshadow: 'feDropShadow',\n    feflood: 'feFlood',\n    fefunca: 'feFuncA',\n    fefuncb: 'feFuncB',\n    fefuncg: 'feFuncG',\n    fefuncr: 'feFuncR',\n    fegaussianblur: 'feGaussianBlur',\n    feimage: 'feImage',\n    femerge: 'feMerge',\n    femergenode: 'feMergeNode',\n    femorphology: 'feMorphology',\n    feoffset: 'feOffset',\n    fepointlight: 'fePointLight',\n    fespecularlighting: 'feSpecularLighting',\n    fespotlight: 'feSpotLight',\n    fetile: 'feTile',\n    feturbulence: 'feTurbulence',\n    foreignobject: 'foreignObject',\n    glyphref: 'glyphRef',\n    lineargradient: 'linearGradient',\n    radialgradient: 'radialGradient'\n};\nfunction getTagName(n) {\n    var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;\n    if (tagName === 'link' && n.attributes._cssText) {\n        tagName = 'style';\n    }\n    return tagName;\n}\nfunction escapeRegExp(str) {\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\nvar HOVER_SELECTOR = /([^\\\\]):hover/;\nvar HOVER_SELECTOR_GLOBAL = new RegExp(HOVER_SELECTOR.source, 'g');\nfunction addHoverClass(cssText, cache) {\n    var cachedStyle = cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.get(cssText);\n    if (cachedStyle)\n        return cachedStyle;\n    var ast = parse(cssText, {\n        silent: true\n    });\n    if (!ast.stylesheet) {\n        return cssText;\n    }\n    var selectors = [];\n    ast.stylesheet.rules.forEach(function (rule) {\n        if ('selectors' in rule) {\n            (rule.selectors || []).forEach(function (selector) {\n                if (HOVER_SELECTOR.test(selector)) {\n                    selectors.push(selector);\n                }\n            });\n        }\n    });\n    if (selectors.length === 0) {\n        return cssText;\n    }\n    var selectorMatcher = new RegExp(selectors\n        .filter(function (selector, index) { return selectors.indexOf(selector) === index; })\n        .sort(function (a, b) { return b.length - a.length; })\n        .map(function (selector) {\n        return escapeRegExp(selector);\n    })\n        .join('|'), 'g');\n    var result = cssText.replace(selectorMatcher, function (selector) {\n        var newSelector = selector.replace(HOVER_SELECTOR_GLOBAL, '$1.\\\\:hover');\n        return selector + \", \" + newSelector;\n    });\n    cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.set(cssText, result);\n    return result;\n}\nfunction createCache() {\n    var stylesWithHoverClass = new Map();\n    return {\n        stylesWithHoverClass: stylesWithHoverClass\n    };\n}\nfunction buildNode(n, options) {\n    var doc = options.doc, hackCss = options.hackCss, cache = options.cache;\n    switch (n.type) {\n        case NodeType.Document:\n            return doc.implementation.createDocument(null, '', null);\n        case NodeType.DocumentType:\n            return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);\n        case NodeType.Element:\n            var tagName = getTagName(n);\n            var node_1;\n            if (n.isSVG) {\n                node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);\n            }\n            else {\n                node_1 = doc.createElement(tagName);\n            }\n            var _loop_1 = function (name_1) {\n                if (!n.attributes.hasOwnProperty(name_1)) {\n                    return \"continue\";\n                }\n                var value = n.attributes[name_1];\n                if (tagName === 'option' && name_1 === 'selected' && value === false) {\n                    return \"continue\";\n                }\n                value =\n                    typeof value === 'boolean' || typeof value === 'number' ? '' : value;\n                if (!name_1.startsWith('rr_')) {\n                    var isTextarea = tagName === 'textarea' && name_1 === 'value';\n                    var isRemoteOrDynamicCss = tagName === 'style' && name_1 === '_cssText';\n                    if (isRemoteOrDynamicCss && hackCss) {\n                        value = addHoverClass(value, cache);\n                    }\n                    if (isTextarea || isRemoteOrDynamicCss) {\n                        var child = doc.createTextNode(value);\n                        for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {\n                            var c = _a[_i];\n                            if (c.nodeType === node_1.TEXT_NODE) {\n                                node_1.removeChild(c);\n                            }\n                        }\n                        node_1.appendChild(child);\n                        return \"continue\";\n                    }\n                    try {\n                        if (n.isSVG && name_1 === 'xlink:href') {\n                            node_1.setAttributeNS('http://www.w3.org/1999/xlink', name_1, value);\n                        }\n                        else if (name_1 === 'onload' ||\n                            name_1 === 'onclick' ||\n                            name_1.substring(0, 7) === 'onmouse') {\n                            node_1.setAttribute('_' + name_1, value);\n                        }\n                        else if (tagName === 'meta' &&\n                            n.attributes['http-equiv'] === 'Content-Security-Policy' &&\n                            name_1 === 'content') {\n                            node_1.setAttribute('csp-content', value);\n                            return \"continue\";\n                        }\n                        else if (tagName === 'link' &&\n                            n.attributes.rel === 'preload' &&\n                            n.attributes.as === 'script') {\n                        }\n                        else if (tagName === 'link' &&\n                            n.attributes.rel === 'prefetch' &&\n                            typeof n.attributes.href === 'string' &&\n                            n.attributes.href.endsWith('.js')) {\n                        }\n                        else if (tagName === 'img' &&\n                            n.attributes.srcset &&\n                            n.attributes.rr_dataURL) {\n                            node_1.setAttribute('rrweb-original-srcset', n.attributes.srcset);\n                        }\n                        else {\n                            node_1.setAttribute(name_1, value);\n                        }\n                    }\n                    catch (error) {\n                    }\n                }\n                else {\n                    if (tagName === 'canvas' && name_1 === 'rr_dataURL') {\n                        var image_1 = document.createElement('img');\n                        image_1.src = value;\n                        image_1.onload = function () {\n                            var ctx = node_1.getContext('2d');\n                            if (ctx) {\n                                ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);\n                            }\n                        };\n                    }\n                    else if (tagName === 'img' && name_1 === 'rr_dataURL') {\n                        var image = node_1;\n                        if (!image.currentSrc.startsWith('data:')) {\n                            image.setAttribute('rrweb-original-src', n.attributes.src);\n                            image.src = value;\n                        }\n                    }\n                    if (name_1 === 'rr_width') {\n                        node_1.style.width = value;\n                    }\n                    else if (name_1 === 'rr_height') {\n                        node_1.style.height = value;\n                    }\n                    else if (name_1 === 'rr_mediaCurrentTime') {\n                        node_1.currentTime = n.attributes\n                            .rr_mediaCurrentTime;\n                    }\n                    else if (name_1 === 'rr_mediaState') {\n                        switch (value) {\n                            case 'played':\n                                node_1\n                                    .play()[\"catch\"](function (e) { return console.warn('media playback error', e); });\n                                break;\n                            case 'paused':\n                                node_1.pause();\n                                break;\n                        }\n                    }\n                }\n            };\n            for (var name_1 in n.attributes) {\n                _loop_1(name_1);\n            }\n            if (n.isShadowHost) {\n                if (!node_1.shadowRoot) {\n                    node_1.attachShadow({ mode: 'open' });\n                }\n                else {\n                    while (node_1.shadowRoot.firstChild) {\n                        node_1.shadowRoot.removeChild(node_1.shadowRoot.firstChild);\n                    }\n                }\n            }\n            return node_1;\n        case NodeType.Text:\n            return doc.createTextNode(n.isStyle && hackCss\n                ? addHoverClass(n.textContent, cache)\n                : n.textContent);\n        case NodeType.CDATA:\n            return doc.createCDATASection(n.textContent);\n        case NodeType.Comment:\n            return doc.createComment(n.textContent);\n        default:\n            return null;\n    }\n}\nfunction buildNodeWithSN(n, options) {\n    var doc = options.doc, mirror = options.mirror, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.hackCss, hackCss = _b === void 0 ? true : _b, afterAppend = options.afterAppend, cache = options.cache;\n    var node = buildNode(n, { doc: doc, hackCss: hackCss, cache: cache });\n    if (!node) {\n        return null;\n    }\n    if (n.rootId) {\n        console.assert(mirror.getNode(n.rootId) === doc, 'Target document should have the same root id.');\n    }\n    if (n.type === NodeType.Document) {\n        doc.close();\n        doc.open();\n        if (n.compatMode === 'BackCompat' &&\n            n.childNodes &&\n            n.childNodes[0].type !== NodeType.DocumentType) {\n            if (n.childNodes[0].type === NodeType.Element &&\n                'xmlns' in n.childNodes[0].attributes &&\n                n.childNodes[0].attributes.xmlns === 'http://www.w3.org/1999/xhtml') {\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">');\n            }\n            else {\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">');\n            }\n        }\n        node = doc;\n    }\n    mirror.add(node, n);\n    if ((n.type === NodeType.Document || n.type === NodeType.Element) &&\n        !skipChild) {\n        for (var _i = 0, _c = n.childNodes; _i < _c.length; _i++) {\n            var childN = _c[_i];\n            var childNode = buildNodeWithSN(childN, {\n                doc: doc,\n                mirror: mirror,\n                skipChild: false,\n                hackCss: hackCss,\n                afterAppend: afterAppend,\n                cache: cache\n            });\n            if (!childNode) {\n                console.warn('Failed to rebuild', childN);\n                continue;\n            }\n            if (childN.isShadow && isElement(node) && node.shadowRoot) {\n                node.shadowRoot.appendChild(childNode);\n            }\n            else {\n                node.appendChild(childNode);\n            }\n            if (afterAppend) {\n                afterAppend(childNode);\n            }\n        }\n    }\n    return node;\n}\nfunction visit(mirror, onVisit) {\n    function walk(node) {\n        onVisit(node);\n    }\n    for (var _i = 0, _a = mirror.getIds(); _i < _a.length; _i++) {\n        var id = _a[_i];\n        if (mirror.has(id)) {\n            walk(mirror.getNode(id));\n        }\n    }\n}\nfunction handleScroll(node, mirror) {\n    var n = mirror.getMeta(node);\n    if ((n === null || n === void 0 ? void 0 : n.type) !== NodeType.Element) {\n        return;\n    }\n    var el = node;\n    for (var name_2 in n.attributes) {\n        if (!(n.attributes.hasOwnProperty(name_2) && name_2.startsWith('rr_'))) {\n            continue;\n        }\n        var value = n.attributes[name_2];\n        if (name_2 === 'rr_scrollLeft') {\n            el.scrollLeft = value;\n        }\n        if (name_2 === 'rr_scrollTop') {\n            el.scrollTop = value;\n        }\n    }\n}\nfunction rebuild(n, options) {\n    var doc = options.doc, onVisit = options.onVisit, _a = options.hackCss, hackCss = _a === void 0 ? true : _a, afterAppend = options.afterAppend, cache = options.cache, _b = options.mirror, mirror = _b === void 0 ? new Mirror() : _b;\n    var node = buildNodeWithSN(n, {\n        doc: doc,\n        mirror: mirror,\n        skipChild: false,\n        hackCss: hackCss,\n        afterAppend: afterAppend,\n        cache: cache\n    });\n    visit(mirror, function (visitedNode) {\n        if (onVisit) {\n            onVisit(visitedNode);\n        }\n        handleScroll(visitedNode, mirror);\n    });\n    return node;\n}\n\nexport { IGNORED_NODE, Mirror, NodeType, addHoverClass, buildNodeWithSN, classMatchesRegex, cleanupSnapshot, createCache, createMirror, is2DCanvasBlank, isElement, isShadowRoot, maskInputValue, needMaskingText, rebuild, serializeNodeWithId, snapshot, transformAttribute, visitSnapshot };\n", "export function parseCSSText(cssText: string): Record<string, string> {\n  const res: Record<string, string> = {};\n  const listDelimiter = /;(?![^(]*\\))/g;\n  const propertyDelimiter = /:(.+)/;\n  const comment = /\\/\\*.*?\\*\\//g;\n  cssText\n    .replace(comment, '')\n    .split(listDelimiter)\n    .forEach(function (item) {\n      if (item) {\n        const tmp = item.split(propertyDelimiter);\n        tmp.length > 1 && (res[camelize(tmp[0].trim())] = tmp[1].trim());\n      }\n    });\n  return res;\n}\n\nexport function toCSSText(style: Record<string, string>): string {\n  const properties = [];\n  for (const name in style) {\n    const value = style[name];\n    if (typeof value !== 'string') continue;\n    const normalizedName = hyphenate(name);\n    properties.push(`${normalizedName}: ${value};`);\n  }\n  return properties.join(' ');\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nconst camelizeRE = /-([a-z])/g;\nconst CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9-]+$/;\nexport const camelize = (str: string): string => {\n  if (CUSTOM_PROPERTY_REGEX.test(str)) return str;\n  return str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''));\n};\n\n/**\n * Hyphenate a camelCase string.\n */\nconst hyphenateRE = /\\B([A-Z])/g;\nexport const hyphenate = (str: string): string => {\n  return str.replace(hyphenateRE, '-$1').toLowerCase();\n};\n", "import { NodeType as RRNodeType } from 'rrweb-snapshot';\nimport { parseCSSText, camelize, toCSSText } from './style';\nexport interface IRRNode {\n  parentElement: IRRNode | null;\n  parentNode: IRRNode | null;\n  childNodes: IRRNode[];\n  ownerDocument: IRRDocument;\n  readonly ELEMENT_NODE: number;\n  readonly TEXT_NODE: number;\n  // corresponding nodeType value of standard HTML Node\n  readonly nodeType: number;\n  readonly nodeName: string; // https://dom.spec.whatwg.org/#dom-node-nodename\n  readonly RRNodeType: RRNodeType;\n\n  firstChild: IRRNode | null;\n\n  lastChild: IRRNode | null;\n\n  nextSibling: IRRNode | null;\n\n  textContent: string | null;\n\n  contains(node: IRRNode): boolean;\n\n  appendChild(newChild: IRRNode): IRRNode;\n\n  insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;\n\n  removeChild(node: IRRNode): IRRNode;\n\n  toString(): string;\n}\nexport interface IRRDocument extends IRRNode {\n  documentElement: IRRElement | null;\n\n  body: IRRElement | null;\n\n  head: IRRElement | null;\n\n  implementation: IRRDocument;\n\n  firstElementChild: IRRElement | null;\n\n  readonly nodeName: '#document';\n\n  compatMode: 'BackCompat' | 'CSS1Compat';\n\n  createDocument(\n    _namespace: string | null,\n    _qualifiedName: string | null,\n    _doctype?: DocumentType | null,\n  ): IRRDocument;\n\n  createDocumentType(\n    qualifiedName: string,\n    publicId: string,\n    systemId: string,\n  ): IRRDocumentType;\n\n  createElement(tagName: string): IRRElement;\n\n  createElementNS(_namespaceURI: string, qualifiedName: string): IRRElement;\n\n  createTextNode(data: string): IRRText;\n\n  createComment(data: string): IRRComment;\n\n  createCDATASection(data: string): IRRCDATASection;\n\n  open(): void;\n\n  close(): void;\n\n  write(content: string): void;\n}\nexport interface IRRElement extends IRRNode {\n  tagName: string;\n  attributes: Record<string, string>;\n  shadowRoot: IRRElement | null;\n  scrollLeft?: number;\n  scrollTop?: number;\n  id: string;\n  className: string;\n  classList: ClassList;\n  style: CSSStyleDeclaration;\n\n  attachShadow(init: ShadowRootInit): IRRElement;\n\n  getAttribute(name: string): string | null;\n\n  setAttribute(name: string, attribute: string): void;\n\n  setAttributeNS(\n    namespace: string | null,\n    qualifiedName: string,\n    value: string,\n  ): void;\n\n  removeAttribute(name: string): void;\n\n  dispatchEvent(event: Event): boolean;\n}\nexport interface IRRDocumentType extends IRRNode {\n  readonly name: string;\n  readonly publicId: string;\n  readonly systemId: string;\n}\nexport interface IRRText extends IRRNode {\n  readonly nodeName: '#text';\n  data: string;\n}\nexport interface IRRComment extends IRRNode {\n  readonly nodeName: '#comment';\n  data: string;\n}\nexport interface IRRCDATASection extends IRRNode {\n  readonly nodeName: '#cdata-section';\n  data: string;\n}\n\ntype ConstrainedConstructor<T = Record<string, unknown>> = new (\n  ...args: any[]\n) => T;\n\n/**\n * This is designed as an abstract class so it should never be instantiated.\n */\nexport class BaseRRNode implements IRRNode {\n  public childNodes: IRRNode[] = [];\n  public parentElement: IRRNode | null = null;\n  public parentNode: IRRNode | null = null;\n  public textContent: string | null;\n  public ownerDocument: IRRDocument;\n  public readonly ELEMENT_NODE: number = NodeType.ELEMENT_NODE;\n  public readonly TEXT_NODE: number = NodeType.TEXT_NODE;\n  // corresponding nodeType value of standard HTML Node\n  public readonly nodeType: number;\n  public readonly nodeName: string;\n  public readonly RRNodeType: RRNodeType;\n\n  constructor(...args: any[]) {\n    //\n  }\n\n  public get firstChild(): IRRNode | null {\n    return this.childNodes[0] || null;\n  }\n\n  public get lastChild(): IRRNode | null {\n    return this.childNodes[this.childNodes.length - 1] || null;\n  }\n\n  public get nextSibling(): IRRNode | null {\n    const parentNode = this.parentNode;\n    if (!parentNode) return null;\n    const siblings = parentNode.childNodes;\n    const index = siblings.indexOf(this);\n    return siblings[index + 1] || null;\n  }\n\n  public contains(node: IRRNode) {\n    if (node === this) return true;\n    for (const child of this.childNodes) {\n      if (child.contains(node)) return true;\n    }\n    return false;\n  }\n\n  public appendChild(_newChild: IRRNode): IRRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  public insertBefore(_newChild: IRRNode, _refChild: IRRNode | null): IRRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  public removeChild(node: IRRNode): IRRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  public toString(): string {\n    return 'RRNode';\n  }\n}\n\nexport function BaseRRDocumentImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  return class BaseRRDocument extends RRNodeClass implements IRRDocument {\n    public readonly nodeType: number = NodeType.DOCUMENT_NODE;\n    public readonly nodeName: '#document' = '#document';\n    public readonly compatMode: 'BackCompat' | 'CSS1Compat' = 'CSS1Compat';\n    public readonly RRNodeType = RRNodeType.Document;\n    public textContent: string | null = null;\n\n    public get documentElement(): IRRElement | null {\n      return (\n        (this.childNodes.find(\n          (node) =>\n            node.RRNodeType === RRNodeType.Element &&\n            (node as IRRElement).tagName === 'HTML',\n        ) as IRRElement) || null\n      );\n    }\n\n    public get body(): IRRElement | null {\n      return (\n        (this.documentElement?.childNodes.find(\n          (node) =>\n            node.RRNodeType === RRNodeType.Element &&\n            (node as IRRElement).tagName === 'BODY',\n        ) as IRRElement) || null\n      );\n    }\n\n    public get head(): IRRElement | null {\n      return (\n        (this.documentElement?.childNodes.find(\n          (node) =>\n            node.RRNodeType === RRNodeType.Element &&\n            (node as IRRElement).tagName === 'HEAD',\n        ) as IRRElement) || null\n      );\n    }\n\n    public get implementation(): IRRDocument {\n      return this;\n    }\n\n    public get firstElementChild(): IRRElement | null {\n      return this.documentElement;\n    }\n\n    public appendChild(childNode: IRRNode): IRRNode {\n      const nodeType = childNode.RRNodeType;\n      if (\n        nodeType === RRNodeType.Element ||\n        nodeType === RRNodeType.DocumentType\n      ) {\n        if (this.childNodes.some((s) => s.RRNodeType === nodeType)) {\n          throw new Error(\n            `RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${\n              nodeType === RRNodeType.Element ? 'RRElement' : 'RRDoctype'\n            } on RRDocument allowed.`,\n          );\n        }\n      }\n      childNode.parentElement = null;\n      childNode.parentNode = this;\n      this.childNodes.push(childNode);\n      return childNode;\n    }\n\n    public insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode {\n      const nodeType = newChild.RRNodeType;\n      if (\n        nodeType === RRNodeType.Element ||\n        nodeType === RRNodeType.DocumentType\n      ) {\n        if (this.childNodes.some((s) => s.RRNodeType === nodeType)) {\n          throw new Error(\n            `RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${\n              nodeType === RRNodeType.Element ? 'RRElement' : 'RRDoctype'\n            } on RRDocument allowed.`,\n          );\n        }\n      }\n      if (refChild === null) return this.appendChild(newChild);\n      const childIndex = this.childNodes.indexOf(refChild);\n      if (childIndex == -1)\n        throw new Error(\n          \"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(childIndex, 0, newChild);\n      newChild.parentElement = null;\n      newChild.parentNode = this;\n      return newChild;\n    }\n\n    public removeChild(node: IRRNode) {\n      const indexOfChild = this.childNodes.indexOf(node);\n      if (indexOfChild === -1)\n        throw new Error(\n          \"Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(indexOfChild, 1);\n      node.parentElement = null;\n      node.parentNode = null;\n      return node;\n    }\n\n    public open() {\n      this.childNodes = [];\n    }\n\n    public close() {\n      //\n    }\n\n    /**\n     * Adhoc implementation for setting xhtml namespace in rebuilt.ts (rrweb-snapshot).\n     * There are two lines used this function:\n     * 1. doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">')\n     * 2. doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">')\n     */\n    public write(content: string) {\n      let publicId;\n      if (\n        content ===\n        '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">'\n      )\n        publicId = '-//W3C//DTD XHTML 1.0 Transitional//EN';\n      else if (\n        content ===\n        '<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">'\n      )\n        publicId = '-//W3C//DTD HTML 4.0 Transitional//EN';\n      if (publicId) {\n        const doctype = this.createDocumentType('html', publicId, '');\n        this.open();\n        this.appendChild(doctype);\n      }\n    }\n\n    createDocument(\n      _namespace: string | null,\n      _qualifiedName: string | null,\n      _doctype?: DocumentType | null,\n    ): IRRDocument {\n      return new BaseRRDocument();\n    }\n\n    createDocumentType(\n      qualifiedName: string,\n      publicId: string,\n      systemId: string,\n    ): IRRDocumentType {\n      const doctype = new (BaseRRDocumentTypeImpl(BaseRRNode))(\n        qualifiedName,\n        publicId,\n        systemId,\n      );\n      doctype.ownerDocument = this;\n      return doctype;\n    }\n\n    createElement(tagName: string): IRRElement {\n      const element = new (BaseRRElementImpl(BaseRRNode))(tagName);\n      element.ownerDocument = this;\n      return element;\n    }\n\n    createElementNS(_namespaceURI: string, qualifiedName: string): IRRElement {\n      return this.createElement(qualifiedName);\n    }\n\n    createTextNode(data: string): IRRText {\n      const text = new (BaseRRTextImpl(BaseRRNode))(data);\n      text.ownerDocument = this;\n      return text;\n    }\n\n    createComment(data: string): IRRComment {\n      const comment = new (BaseRRCommentImpl(BaseRRNode))(data);\n      comment.ownerDocument = this;\n      return comment;\n    }\n\n    createCDATASection(data: string): IRRCDATASection {\n      const CDATASection = new (BaseRRCDATASectionImpl(BaseRRNode))(data);\n      CDATASection.ownerDocument = this;\n      return CDATASection;\n    }\n\n    toString() {\n      return 'RRDocument';\n    }\n  };\n}\n\nexport function BaseRRDocumentTypeImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRDocumentType\n    extends RRNodeClass\n    implements IRRDocumentType {\n    public readonly nodeType: number = NodeType.DOCUMENT_TYPE_NODE;\n    public readonly RRNodeType = RRNodeType.DocumentType;\n    public readonly nodeName: string;\n    public readonly name: string;\n    public readonly publicId: string;\n    public readonly systemId: string;\n    public textContent: string | null = null;\n\n    constructor(qualifiedName: string, publicId: string, systemId: string) {\n      super();\n      this.name = qualifiedName;\n      this.publicId = publicId;\n      this.systemId = systemId;\n      this.nodeName = qualifiedName;\n    }\n\n    toString() {\n      return 'RRDocumentType';\n    }\n  };\n}\n\nexport function BaseRRElementImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRElement extends RRNodeClass implements IRRElement {\n    public readonly nodeType: number = NodeType.ELEMENT_NODE;\n    public readonly RRNodeType = RRNodeType.Element;\n    public readonly nodeName: string;\n    public tagName: string;\n    public attributes: Record<string, string> = {};\n    public shadowRoot: IRRElement | null = null;\n    public scrollLeft?: number;\n    public scrollTop?: number;\n\n    constructor(tagName: string) {\n      super();\n      this.tagName = tagName.toUpperCase();\n      this.nodeName = tagName.toUpperCase();\n    }\n\n    public get textContent(): string {\n      let result = '';\n      this.childNodes.forEach((node) => (result += node.textContent));\n      return result;\n    }\n\n    public set textContent(textContent: string) {\n      this.childNodes = [this.ownerDocument.createTextNode(textContent)];\n    }\n\n    public get classList(): ClassList {\n      return new ClassList(\n        this.attributes.class as string | undefined,\n        (newClassName) => {\n          this.attributes.class = newClassName;\n        },\n      );\n    }\n\n    public get id() {\n      return this.attributes.id || '';\n    }\n\n    public get className() {\n      return this.attributes.class || '';\n    }\n\n    public get style() {\n      const style = (this.attributes.style\n        ? parseCSSText(this.attributes.style)\n        : {}) as CSSStyleDeclaration;\n      const hyphenateRE = /\\B([A-Z])/g;\n      style.setProperty = (\n        name: string,\n        value: string | null,\n        priority?: string,\n      ) => {\n        if (hyphenateRE.test(name)) return;\n        const normalizedName = camelize(name);\n        if (!value) delete style[normalizedName];\n        else style[normalizedName] = value;\n        if (priority === 'important') style[normalizedName] += ' !important';\n        this.attributes.style = toCSSText(style);\n      };\n      style.removeProperty = (name: string) => {\n        if (hyphenateRE.test(name)) return '';\n        const normalizedName = camelize(name);\n        const value = style[normalizedName] || '';\n        delete style[normalizedName];\n        this.attributes.style = toCSSText(style);\n        return value;\n      };\n      return style;\n    }\n\n    public getAttribute(name: string) {\n      return this.attributes[name] || null;\n    }\n\n    public setAttribute(name: string, attribute: string) {\n      this.attributes[name] = attribute;\n    }\n\n    public setAttributeNS(\n      _namespace: string | null,\n      qualifiedName: string,\n      value: string,\n    ): void {\n      this.setAttribute(qualifiedName, value);\n    }\n\n    public removeAttribute(name: string) {\n      delete this.attributes[name];\n    }\n\n    public appendChild(newChild: IRRNode): IRRNode {\n      this.childNodes.push(newChild);\n      newChild.parentNode = this;\n      newChild.parentElement = this;\n      return newChild;\n    }\n\n    public insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode {\n      if (refChild === null) return this.appendChild(newChild);\n      const childIndex = this.childNodes.indexOf(refChild);\n      if (childIndex == -1)\n        throw new Error(\n          \"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(childIndex, 0, newChild);\n      newChild.parentElement = this;\n      newChild.parentNode = this;\n      return newChild;\n    }\n\n    public removeChild(node: IRRNode): IRRNode {\n      const indexOfChild = this.childNodes.indexOf(node);\n      if (indexOfChild === -1)\n        throw new Error(\n          \"Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.\",\n        );\n      this.childNodes.splice(indexOfChild, 1);\n      node.parentElement = null;\n      node.parentNode = null;\n      return node;\n    }\n\n    public attachShadow(_init: ShadowRootInit): IRRElement {\n      const shadowRoot = this.ownerDocument.createElement('SHADOWROOT');\n      this.shadowRoot = shadowRoot;\n      return shadowRoot;\n    }\n\n    public dispatchEvent(_event: Event) {\n      return true;\n    }\n\n    toString() {\n      let attributeString = '';\n      for (const attribute in this.attributes) {\n        attributeString += `${attribute}=\"${this.attributes[attribute]}\" `;\n      }\n      return `${this.tagName} ${attributeString}`;\n    }\n  };\n}\n\nexport function BaseRRMediaElementImpl<\n  RRElement extends ConstrainedConstructor<IRRElement>\n>(RRElementClass: RRElement) {\n  return class BaseRRMediaElement extends RRElementClass {\n    public currentTime?: number;\n    public volume?: number;\n    public paused?: boolean;\n    public muted?: boolean;\n    attachShadow(_init: ShadowRootInit): IRRElement {\n      throw new Error(\n        `RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow`,\n      );\n    }\n    public play() {\n      this.paused = false;\n    }\n    public pause() {\n      this.paused = true;\n    }\n  };\n}\n\nexport function BaseRRTextImpl<RRNode extends ConstrainedConstructor<IRRNode>>(\n  RRNodeClass: RRNode,\n) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRText extends RRNodeClass implements IRRText {\n    public readonly nodeType: number = NodeType.TEXT_NODE;\n    public readonly nodeName: '#text' = '#text';\n    public readonly RRNodeType = RRNodeType.Text;\n    public data: string;\n\n    constructor(data: string) {\n      super();\n      this.data = data;\n    }\n\n    public get textContent(): string {\n      return this.data;\n    }\n\n    public set textContent(textContent: string) {\n      this.data = textContent;\n    }\n\n    toString() {\n      return `RRText text=${JSON.stringify(this.data)}`;\n    }\n  };\n}\n\nexport function BaseRRCommentImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRComment extends RRNodeClass implements IRRComment {\n    public readonly nodeType: number = NodeType.COMMENT_NODE;\n    public readonly nodeName: '#comment' = '#comment';\n    public readonly RRNodeType = RRNodeType.Comment;\n    public data: string;\n\n    constructor(data: string) {\n      super();\n      this.data = data;\n    }\n\n    public get textContent(): string {\n      return this.data;\n    }\n\n    public set textContent(textContent: string) {\n      this.data = textContent;\n    }\n\n    toString() {\n      return `RRComment text=${JSON.stringify(this.data)}`;\n    }\n  };\n}\n\nexport function BaseRRCDATASectionImpl<\n  RRNode extends ConstrainedConstructor<IRRNode>\n>(RRNodeClass: RRNode) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return class BaseRRCDATASection\n    extends RRNodeClass\n    implements IRRCDATASection {\n    public readonly nodeName: '#cdata-section' = '#cdata-section';\n    public readonly nodeType: number = NodeType.CDATA_SECTION_NODE;\n    public readonly RRNodeType = RRNodeType.CDATA;\n    public data: string;\n\n    constructor(data: string) {\n      super();\n      this.data = data;\n    }\n\n    public get textContent(): string {\n      return this.data;\n    }\n\n    public set textContent(textContent: string) {\n      this.data = textContent;\n    }\n\n    toString() {\n      return `RRCDATASection data=${JSON.stringify(this.data)}`;\n    }\n  };\n}\n\nexport class ClassList {\n  private onChange: ((newClassText: string) => void) | undefined;\n  classes: string[] = [];\n\n  constructor(\n    classText?: string,\n    onChange?: ((newClassText: string) => void) | undefined,\n  ) {\n    if (classText) {\n      const classes = classText.trim().split(/\\s+/);\n      this.classes.push(...classes);\n    }\n    this.onChange = onChange;\n  }\n\n  add = (...classNames: string[]) => {\n    for (const item of classNames) {\n      const className = String(item);\n      if (this.classes.indexOf(className) >= 0) continue;\n      this.classes.push(className);\n    }\n    this.onChange && this.onChange(this.classes.join(' '));\n  };\n\n  remove = (...classNames: string[]) => {\n    this.classes = this.classes.filter(\n      (item) => classNames.indexOf(item) === -1,\n    );\n    this.onChange && this.onChange(this.classes.join(' '));\n  };\n}\n\nexport type CSSStyleDeclaration = Record<string, string> & {\n  setProperty: (\n    name: string,\n    value: string | null,\n    priority?: string | null,\n  ) => void;\n  removeProperty: (name: string) => string;\n};\n\n// Enumerate nodeType value of standard HTML Node.\nexport enum NodeType {\n  PLACEHOLDER, // This isn't a node type. Enum type value starts from zero but NodeType value starts from 1.\n  ELEMENT_NODE,\n  ATTRIBUTE_NODE,\n  TEXT_NODE,\n  CDATA_SECTION_NODE,\n  ENTITY_REFERENCE_NODE,\n  ENTITY_NODE,\n  PROCESSING_INSTRUCTION_NODE,\n  COMMENT_NODE,\n  DOCUMENT_NODE,\n  DOCUMENT_TYPE_NODE,\n  DOCUMENT_FRAGMENT_NODE,\n}\n", "import { NodeType as RRNodeType, Mirror as NodeMirror } from 'rrweb-snapshot';\nimport type {\n  canvasMutationData,\n  canvasEventWithTime,\n  inputData,\n  scrollData,\n} from 'rrweb/src/types';\nimport type {\n  IRRCDATASection,\n  IRRComment,\n  IRRDocument,\n  IRRDocumentType,\n  IRRElement,\n  IRRNode,\n  IRRText,\n} from './document';\nimport type {\n  RRCanvasElement,\n  RRElement,\n  RRIFrameElement,\n  RRMediaElement,\n  RRStyleElement,\n  RRDocument,\n  Mirror,\n} from '.';\n\nconst NAMESPACES: Record<string, string> = {\n  svg: 'http://www.w3.org/2000/svg',\n  'xlink:href': 'http://www.w3.org/1999/xlink',\n  xmlns: 'http://www.w3.org/2000/xmlns/',\n};\n\n// camel case svg element tag names\nconst SVGTagMap: Record<string, string> = {\n  altglyph: 'altGlyph',\n  altglyphdef: 'altGlyphDef',\n  altglyphitem: 'altGlyphItem',\n  animatecolor: 'animateColor',\n  animatemotion: 'animateMotion',\n  animatetransform: 'animateTransform',\n  clippath: 'clipPath',\n  feblend: 'feBlend',\n  fecolormatrix: 'feColorMatrix',\n  fecomponenttransfer: 'feComponentTransfer',\n  fecomposite: 'feComposite',\n  feconvolvematrix: 'feConvolveMatrix',\n  fediffuselighting: 'feDiffuseLighting',\n  fedisplacementmap: 'feDisplacementMap',\n  fedistantlight: 'feDistantLight',\n  fedropshadow: 'feDropShadow',\n  feflood: 'feFlood',\n  fefunca: 'feFuncA',\n  fefuncb: 'feFuncB',\n  fefuncg: 'feFuncG',\n  fefuncr: 'feFuncR',\n  fegaussianblur: 'feGaussianBlur',\n  feimage: 'feImage',\n  femerge: 'feMerge',\n  femergenode: 'feMergeNode',\n  femorphology: 'feMorphology',\n  feoffset: 'feOffset',\n  fepointlight: 'fePointLight',\n  fespecularlighting: 'feSpecularLighting',\n  fespotlight: 'feSpotLight',\n  fetile: 'feTile',\n  feturbulence: 'feTurbulence',\n  foreignobject: 'foreignObject',\n  glyphref: 'glyphRef',\n  lineargradient: 'linearGradient',\n  radialgradient: 'radialGradient',\n};\n\nexport type ReplayerHandler = {\n  mirror: NodeMirror;\n  applyCanvas: (\n    canvasEvent: canvasEventWithTime,\n    canvasMutationData: canvasMutationData,\n    target: HTMLCanvasElement,\n  ) => void;\n  applyInput: (data: inputData) => void;\n  applyScroll: (data: scrollData, isSync: boolean) => void;\n};\n\nexport function diff(\n  oldTree: Node,\n  newTree: IRRNode,\n  replayer: ReplayerHandler,\n  rrnodeMirror?: Mirror,\n) {\n  const oldChildren = oldTree.childNodes;\n  const newChildren = newTree.childNodes;\n  rrnodeMirror =\n    rrnodeMirror ||\n    (newTree as RRDocument).mirror ||\n    (newTree.ownerDocument as RRDocument).mirror;\n\n  if (oldChildren.length > 0 || newChildren.length > 0) {\n    diffChildren(\n      Array.from(oldChildren),\n      newChildren,\n      oldTree,\n      replayer,\n      rrnodeMirror,\n    );\n  }\n\n  let inputDataToApply = null,\n    scrollDataToApply = null;\n  switch (newTree.RRNodeType) {\n    case RRNodeType.Document: {\n      const newRRDocument = newTree as IRRDocument;\n      scrollDataToApply = (newRRDocument as RRDocument).scrollData;\n      break;\n    }\n    case RRNodeType.Element: {\n      const oldElement = oldTree as HTMLElement;\n      const newRRElement = newTree as IRRElement;\n      diffProps(oldElement, newRRElement, rrnodeMirror);\n      scrollDataToApply = (newRRElement as RRElement).scrollData;\n      inputDataToApply = (newRRElement as RRElement).inputData;\n      switch (newRRElement.tagName) {\n        case 'AUDIO':\n        case 'VIDEO': {\n          const oldMediaElement = oldTree as HTMLMediaElement;\n          const newMediaRRElement = newRRElement as RRMediaElement;\n          if (newMediaRRElement.paused !== undefined)\n            newMediaRRElement.paused\n              ? oldMediaElement.pause()\n              : oldMediaElement.play();\n          if (newMediaRRElement.muted !== undefined)\n            oldMediaElement.muted = newMediaRRElement.muted;\n          if (newMediaRRElement.volume !== undefined)\n            oldMediaElement.volume = newMediaRRElement.volume;\n          if (newMediaRRElement.currentTime !== undefined)\n            oldMediaElement.currentTime = newMediaRRElement.currentTime;\n          break;\n        }\n        case 'CANVAS':\n          (newTree as RRCanvasElement).canvasMutations.forEach(\n            (canvasMutation) =>\n              replayer.applyCanvas(\n                canvasMutation.event,\n                canvasMutation.mutation,\n                oldTree as HTMLCanvasElement,\n              ),\n          );\n          break;\n        case 'STYLE':\n          applyVirtualStyleRulesToNode(\n            oldElement as HTMLStyleElement,\n            (newTree as RRStyleElement).rules,\n          );\n          break;\n      }\n      if (newRRElement.shadowRoot) {\n        if (!oldElement.shadowRoot) oldElement.attachShadow({ mode: 'open' });\n        const oldChildren = oldElement.shadowRoot!.childNodes;\n        const newChildren = newRRElement.shadowRoot.childNodes;\n        if (oldChildren.length > 0 || newChildren.length > 0)\n          diffChildren(\n            Array.from(oldChildren),\n            newChildren,\n            oldElement.shadowRoot!,\n            replayer,\n            rrnodeMirror,\n          );\n      }\n      break;\n    }\n    case RRNodeType.Text:\n    case RRNodeType.Comment:\n    case RRNodeType.CDATA:\n      if (\n        oldTree.textContent !==\n        (newTree as IRRText | IRRComment | IRRCDATASection).data\n      )\n        oldTree.textContent = (newTree as\n          | IRRText\n          | IRRComment\n          | IRRCDATASection).data;\n      break;\n    default:\n  }\n\n  scrollDataToApply && replayer.applyScroll(scrollDataToApply, true);\n  /**\n   * Input data need to get applied after all children of this node are updated.\n   * Otherwise when we set a value for a select element whose options are empty, the value won't actually update.\n   */\n  inputDataToApply && replayer.applyInput(inputDataToApply);\n\n  // IFrame element doesn't have child nodes.\n  if (newTree.nodeName === 'IFRAME') {\n    const oldContentDocument = (oldTree as HTMLIFrameElement).contentDocument;\n    const newIFrameElement = newTree as RRIFrameElement;\n    // If the iframe is cross-origin, the contentDocument will be null.\n    if (oldContentDocument) {\n      const sn = rrnodeMirror.getMeta(newIFrameElement.contentDocument);\n      if (sn) {\n        replayer.mirror.add(oldContentDocument, { ...sn });\n      }\n      diff(\n        oldContentDocument,\n        newIFrameElement.contentDocument,\n        replayer,\n        rrnodeMirror,\n      );\n    }\n  }\n}\n\nfunction diffProps(\n  oldTree: HTMLElement,\n  newTree: IRRElement,\n  rrnodeMirror: Mirror,\n) {\n  const oldAttributes = oldTree.attributes;\n  const newAttributes = newTree.attributes;\n\n  for (const name in newAttributes) {\n    const newValue = newAttributes[name];\n    const sn = rrnodeMirror.getMeta(newTree);\n    if (sn && 'isSVG' in sn && sn.isSVG && NAMESPACES[name])\n      oldTree.setAttributeNS(NAMESPACES[name], name, newValue);\n    else if (newTree.tagName === 'CANVAS' && name === 'rr_dataURL') {\n      const image = document.createElement('img');\n      image.src = newValue;\n      image.onload = () => {\n        const ctx = (oldTree as HTMLCanvasElement).getContext('2d');\n        if (ctx) {\n          ctx.drawImage(image, 0, 0, image.width, image.height);\n        }\n      };\n    } else oldTree.setAttribute(name, newValue);\n  }\n\n  for (const { name } of Array.from(oldAttributes))\n    if (!(name in newAttributes)) oldTree.removeAttribute(name);\n\n  newTree.scrollLeft && (oldTree.scrollLeft = newTree.scrollLeft);\n  newTree.scrollTop && (oldTree.scrollTop = newTree.scrollTop);\n}\n\nfunction diffChildren(\n  oldChildren: (Node | undefined)[],\n  newChildren: IRRNode[],\n  parentNode: Node,\n  replayer: ReplayerHandler,\n  rrnodeMirror: Mirror,\n) {\n  let oldStartIndex = 0,\n    oldEndIndex = oldChildren.length - 1,\n    newStartIndex = 0,\n    newEndIndex = newChildren.length - 1;\n  let oldStartNode = oldChildren[oldStartIndex],\n    oldEndNode = oldChildren[oldEndIndex],\n    newStartNode = newChildren[newStartIndex],\n    newEndNode = newChildren[newEndIndex];\n  let oldIdToIndex: Record<number, number> | undefined = undefined,\n    indexInOld;\n  while (oldStartIndex <= oldEndIndex && newStartIndex <= newEndIndex) {\n    if (oldStartNode === undefined) {\n      oldStartNode = oldChildren[++oldStartIndex];\n    } else if (oldEndNode === undefined) {\n      oldEndNode = oldChildren[--oldEndIndex];\n    } else if (\n      replayer.mirror.getId(oldStartNode) === rrnodeMirror.getId(newStartNode)\n    ) {\n      diff(oldStartNode, newStartNode, replayer, rrnodeMirror);\n      oldStartNode = oldChildren[++oldStartIndex];\n      newStartNode = newChildren[++newStartIndex];\n    } else if (\n      replayer.mirror.getId(oldEndNode) === rrnodeMirror.getId(newEndNode)\n    ) {\n      diff(oldEndNode, newEndNode, replayer, rrnodeMirror);\n      oldEndNode = oldChildren[--oldEndIndex];\n      newEndNode = newChildren[--newEndIndex];\n    } else if (\n      replayer.mirror.getId(oldStartNode) === rrnodeMirror.getId(newEndNode)\n    ) {\n      parentNode.insertBefore(oldStartNode, oldEndNode.nextSibling);\n      diff(oldStartNode, newEndNode, replayer, rrnodeMirror);\n      oldStartNode = oldChildren[++oldStartIndex];\n      newEndNode = newChildren[--newEndIndex];\n    } else if (\n      replayer.mirror.getId(oldEndNode) === rrnodeMirror.getId(newStartNode)\n    ) {\n      parentNode.insertBefore(oldEndNode, oldStartNode);\n      diff(oldEndNode, newStartNode, replayer, rrnodeMirror);\n      oldEndNode = oldChildren[--oldEndIndex];\n      newStartNode = newChildren[++newStartIndex];\n    } else {\n      if (!oldIdToIndex) {\n        oldIdToIndex = {};\n        for (let i = oldStartIndex; i <= oldEndIndex; i++) {\n          const oldChild = oldChildren[i];\n          if (oldChild && replayer.mirror.hasNode(oldChild))\n            oldIdToIndex[replayer.mirror.getId(oldChild)] = i;\n        }\n      }\n      indexInOld = oldIdToIndex[rrnodeMirror.getId(newStartNode)];\n      if (indexInOld) {\n        const nodeToMove = oldChildren[indexInOld]!;\n        parentNode.insertBefore(nodeToMove, oldStartNode);\n        diff(nodeToMove, newStartNode, replayer, rrnodeMirror);\n        oldChildren[indexInOld] = undefined;\n      } else {\n        const newNode = createOrGetNode(\n          newStartNode,\n          replayer.mirror,\n          rrnodeMirror,\n        );\n\n        /**\n         * A mounted iframe element has an automatically created HTML element.\n         * We should delete it before insert a serialized one. Otherwise, an error 'Only one element on document allowed' will be thrown.\n         */\n        if (\n          replayer.mirror.getMeta(parentNode)?.type === RRNodeType.Document &&\n          replayer.mirror.getMeta(newNode)?.type === RRNodeType.Element &&\n          (parentNode as Document).documentElement\n        ) {\n          parentNode.removeChild((parentNode as Document).documentElement);\n          oldChildren[oldStartIndex] = undefined;\n          oldStartNode = undefined;\n        }\n        parentNode.insertBefore(newNode, oldStartNode || null);\n        diff(newNode, newStartNode, replayer, rrnodeMirror);\n      }\n      newStartNode = newChildren[++newStartIndex];\n    }\n  }\n  if (oldStartIndex > oldEndIndex) {\n    const referenceRRNode = newChildren[newEndIndex + 1];\n    let referenceNode = null;\n    if (referenceRRNode)\n      parentNode.childNodes.forEach((child) => {\n        if (\n          replayer.mirror.getId(child) === rrnodeMirror.getId(referenceRRNode)\n        )\n          referenceNode = child;\n      });\n    for (; newStartIndex <= newEndIndex; ++newStartIndex) {\n      const newNode = createOrGetNode(\n        newChildren[newStartIndex],\n        replayer.mirror,\n        rrnodeMirror,\n      );\n      parentNode.insertBefore(newNode, referenceNode);\n      diff(newNode, newChildren[newStartIndex], replayer, rrnodeMirror);\n    }\n  } else if (newStartIndex > newEndIndex) {\n    for (; oldStartIndex <= oldEndIndex; oldStartIndex++) {\n      const node = oldChildren[oldStartIndex];\n      if (node) {\n        parentNode.removeChild(node);\n        replayer.mirror.removeNodeFromMap(node);\n      }\n    }\n  }\n}\n\nexport function createOrGetNode(\n  rrNode: IRRNode,\n  domMirror: NodeMirror,\n  rrnodeMirror: Mirror,\n): Node {\n  let node = domMirror.getNode(rrnodeMirror.getId(rrNode));\n  const sn = rrnodeMirror.getMeta(rrNode);\n  if (node !== null) return node;\n  switch (rrNode.RRNodeType) {\n    case RRNodeType.Document:\n      node = new Document();\n      break;\n    case RRNodeType.DocumentType:\n      node = document.implementation.createDocumentType(\n        (rrNode as IRRDocumentType).name,\n        (rrNode as IRRDocumentType).publicId,\n        (rrNode as IRRDocumentType).systemId,\n      );\n      break;\n    case RRNodeType.Element: {\n      let tagName = (rrNode as IRRElement).tagName.toLowerCase();\n      tagName = SVGTagMap[tagName] || tagName;\n      if (sn && 'isSVG' in sn && sn?.isSVG) {\n        node = document.createElementNS(\n          NAMESPACES['svg'],\n          (rrNode as IRRElement).tagName.toLowerCase(),\n        );\n      } else node = document.createElement((rrNode as IRRElement).tagName);\n      break;\n    }\n    case RRNodeType.Text:\n      node = document.createTextNode((rrNode as IRRText).data);\n      break;\n    case RRNodeType.Comment:\n      node = document.createComment((rrNode as IRRComment).data);\n      break;\n    case RRNodeType.CDATA:\n      node = document.createCDATASection((rrNode as IRRCDATASection).data);\n      break;\n  }\n\n  if (sn) domMirror.add(node, { ...sn });\n  return node;\n}\n\nexport function getNestedRule(\n  rules: CSSRuleList,\n  position: number[],\n): CSSGroupingRule {\n  const rule = rules[position[0]] as CSSGroupingRule;\n  if (position.length === 1) {\n    return rule;\n  } else {\n    return getNestedRule(\n      (rule.cssRules[position[1]] as CSSGroupingRule).cssRules,\n      position.slice(2),\n    );\n  }\n}\n\nexport enum StyleRuleType {\n  Insert,\n  Remove,\n  Snapshot,\n  SetProperty,\n  RemoveProperty,\n}\ntype InsertRule = {\n  cssText: string;\n  type: StyleRuleType.Insert;\n  index?: number | number[];\n};\ntype RemoveRule = {\n  type: StyleRuleType.Remove;\n  index: number | number[];\n};\ntype SetPropertyRule = {\n  type: StyleRuleType.SetProperty;\n  index: number[];\n  property: string;\n  value: string | null;\n  priority: string | undefined;\n};\ntype RemovePropertyRule = {\n  type: StyleRuleType.RemoveProperty;\n  index: number[];\n  property: string;\n};\n\nexport type VirtualStyleRules = Array<\n  InsertRule | RemoveRule | SetPropertyRule | RemovePropertyRule\n>;\n\nexport function getPositionsAndIndex(nestedIndex: number[]) {\n  const positions = [...nestedIndex];\n  const index = positions.pop();\n  return { positions, index };\n}\n\nexport function applyVirtualStyleRulesToNode(\n  styleNode: HTMLStyleElement,\n  virtualStyleRules: VirtualStyleRules,\n) {\n  const sheet = styleNode.sheet!;\n\n  virtualStyleRules.forEach((rule) => {\n    if (rule.type === StyleRuleType.Insert) {\n      try {\n        if (Array.isArray(rule.index)) {\n          const { positions, index } = getPositionsAndIndex(rule.index);\n          const nestedRule = getNestedRule(sheet.cssRules, positions);\n          nestedRule.insertRule(rule.cssText, index);\n        } else {\n          sheet.insertRule(rule.cssText, rule.index);\n        }\n      } catch (e) {\n        /**\n         * sometimes we may capture rules with browser prefix\n         * insert rule with prefixs in other browsers may cause Error\n         */\n      }\n    } else if (rule.type === StyleRuleType.Remove) {\n      try {\n        if (Array.isArray(rule.index)) {\n          const { positions, index } = getPositionsAndIndex(rule.index);\n          const nestedRule = getNestedRule(sheet.cssRules, positions);\n          nestedRule.deleteRule(index || 0);\n        } else {\n          sheet.deleteRule(rule.index);\n        }\n      } catch (e) {\n        /**\n         * accessing styleSheet rules may cause SecurityError\n         * for specific access control settings\n         */\n      }\n    } else if (rule.type === StyleRuleType.SetProperty) {\n      const nativeRule = (getNestedRule(\n        sheet.cssRules,\n        rule.index,\n      ) as unknown) as CSSStyleRule;\n      nativeRule.style.setProperty(rule.property, rule.value, rule.priority);\n    } else if (rule.type === StyleRuleType.RemoveProperty) {\n      const nativeRule = (getNestedRule(\n        sheet.cssRules,\n        rule.index,\n      ) as unknown) as CSSStyleRule;\n      nativeRule.style.removeProperty(rule.property);\n    }\n  });\n}\n", "import {\n  NodeType as RRNodeType,\n  create<PERSON><PERSON><PERSON>r as createNode<PERSON>irror,\n} from 'rrweb-snapshot';\nimport type {\n  Mirror as NodeMirror,\n  IMirror,\n  serializedNodeWithId,\n} from 'rrweb-snapshot';\nimport type {\n  canvasMutationData,\n  canvasEventWithTime,\n  inputData,\n  scrollData,\n} from 'rrweb/src/types';\nimport type { VirtualStyleRules } from './diff';\nimport {\n  BaseRRNode as RRNode,\n  BaseRRCDATASectionImpl,\n  BaseRRCommentImpl,\n  BaseRRDocumentImpl,\n  BaseRRDocumentTypeImpl,\n  BaseRRElementImpl,\n  BaseRRMediaElementImpl,\n  BaseRRTextImpl,\n  IRRDocument,\n  IRRElement,\n  IRRNode,\n  NodeType,\n  IRRDocumentType,\n  IRRText,\n  IRRComment,\n} from './document';\n\nexport class RRDocument extends BaseRRDocumentImpl(RRNode) {\n  // In the rrweb replayer, there are some unserialized nodes like the element that stores the injected style rules.\n  // These unserialized nodes may interfere the execution of the diff algorithm.\n  // The id of serialized node is larger than 0. So this value ​​less than 0 is used as id for these unserialized nodes.\n  private _unserializedId = -1;\n\n  /**\n   * Every time the id is used, it will minus 1 automatically to avoid collisions.\n   */\n  public get unserializedId(): number {\n    return this._unserializedId--;\n  }\n\n  public mirror: Mirror = createMirror();\n\n  public scrollData: scrollData | null = null;\n\n  constructor(mirror?: Mirror) {\n    super();\n    if (mirror) {\n      this.mirror = mirror;\n    }\n  }\n\n  createDocument(\n    _namespace: string | null,\n    _qualifiedName: string | null,\n    _doctype?: DocumentType | null,\n  ) {\n    return new RRDocument();\n  }\n\n  createDocumentType(\n    qualifiedName: string,\n    publicId: string,\n    systemId: string,\n  ) {\n    const documentTypeNode = new RRDocumentType(\n      qualifiedName,\n      publicId,\n      systemId,\n    );\n    documentTypeNode.ownerDocument = this;\n    return documentTypeNode;\n  }\n\n  createElement<K extends keyof HTMLElementTagNameMap>(\n    tagName: K,\n  ): RRElementType<K>;\n  createElement(tagName: string): RRElement;\n  createElement(tagName: string) {\n    const upperTagName = tagName.toUpperCase();\n    let element;\n    switch (upperTagName) {\n      case 'AUDIO':\n      case 'VIDEO':\n        element = new RRMediaElement(upperTagName);\n        break;\n      case 'IFRAME':\n        element = new RRIFrameElement(upperTagName, this.mirror);\n        break;\n      case 'CANVAS':\n        element = new RRCanvasElement(upperTagName);\n        break;\n      case 'STYLE':\n        element = new RRStyleElement(upperTagName);\n        break;\n      default:\n        element = new RRElement(upperTagName);\n        break;\n    }\n    element.ownerDocument = this;\n    return element;\n  }\n\n  createComment(data: string) {\n    const commentNode = new RRComment(data);\n    commentNode.ownerDocument = this;\n    return commentNode;\n  }\n\n  createCDATASection(data: string) {\n    const sectionNode = new RRCDATASection(data);\n    sectionNode.ownerDocument = this;\n    return sectionNode;\n  }\n\n  createTextNode(data: string) {\n    const textNode = new RRText(data);\n    textNode.ownerDocument = this;\n    return textNode;\n  }\n\n  destroyTree() {\n    this.childNodes = [];\n    this.mirror.reset();\n  }\n\n  open() {\n    super.open();\n    this._unserializedId = -1;\n  }\n}\n\nexport const RRDocumentType = BaseRRDocumentTypeImpl(RRNode);\n\nexport class RRElement extends BaseRRElementImpl(RRNode) {\n  inputData: inputData | null = null;\n  scrollData: scrollData | null = null;\n}\n\nexport class RRMediaElement extends BaseRRMediaElementImpl(RRElement) {}\n\nexport class RRCanvasElement extends RRElement implements IRRElement {\n  public canvasMutations: {\n    event: canvasEventWithTime;\n    mutation: canvasMutationData;\n  }[] = [];\n  /**\n   * This is a dummy implementation to distinguish RRCanvasElement from real HTMLCanvasElement.\n   */\n  getContext(): RenderingContext | null {\n    return null;\n  }\n}\n\nexport class RRStyleElement extends RRElement {\n  public rules: VirtualStyleRules = [];\n}\n\nexport class RRIFrameElement extends RRElement {\n  contentDocument: RRDocument = new RRDocument();\n  constructor(upperTagName: string, mirror: Mirror) {\n    super(upperTagName);\n    this.contentDocument.mirror = mirror;\n  }\n}\n\nexport const RRText = BaseRRTextImpl(RRNode);\nexport type RRText = typeof RRText;\n\nexport const RRComment = BaseRRCommentImpl(RRNode);\nexport type RRComment = typeof RRComment;\n\nexport const RRCDATASection = BaseRRCDATASectionImpl(RRNode);\nexport type RRCDATASection = typeof RRCDATASection;\n\ninterface RRElementTagNameMap {\n  audio: RRMediaElement;\n  canvas: RRCanvasElement;\n  iframe: RRIFrameElement;\n  style: RRStyleElement;\n  video: RRMediaElement;\n}\n\ntype RRElementType<\n  K extends keyof HTMLElementTagNameMap\n> = K extends keyof RRElementTagNameMap ? RRElementTagNameMap[K] : RRElement;\n\nfunction getValidTagName(element: HTMLElement): string {\n  // https://github.com/rrweb-io/rrweb-snapshot/issues/56\n  if (element instanceof HTMLFormElement) {\n    return 'FORM';\n  }\n  return element.tagName.toUpperCase();\n}\n\n/**\n * Build a RRNode from a real Node.\n * @param node the real Node\n * @param rrdom the RRDocument\n * @param domMirror the NodeMirror that records the real document tree\n * @returns the built RRNode\n */\nexport function buildFromNode(\n  node: Node,\n  rrdom: IRRDocument,\n  domMirror: NodeMirror,\n  parentRRNode?: IRRNode | null,\n): IRRNode | null {\n  let rrNode: IRRNode;\n\n  switch (node.nodeType) {\n    case NodeType.DOCUMENT_NODE:\n      if (parentRRNode && parentRRNode.nodeName === 'IFRAME')\n        rrNode = (parentRRNode as RRIFrameElement).contentDocument;\n      else {\n        rrNode = rrdom;\n        (rrNode as IRRDocument).compatMode = (node as Document).compatMode as\n          | 'BackCompat'\n          | 'CSS1Compat';\n      }\n      break;\n    case NodeType.DOCUMENT_TYPE_NODE:\n      const documentType = node as DocumentType;\n      rrNode = rrdom.createDocumentType(\n        documentType.name,\n        documentType.publicId,\n        documentType.systemId,\n      );\n      break;\n    case NodeType.ELEMENT_NODE:\n      const elementNode = node as HTMLElement;\n      const tagName = getValidTagName(elementNode);\n      rrNode = rrdom.createElement(tagName);\n      const rrElement = rrNode as IRRElement;\n      for (const { name, value } of Array.from(elementNode.attributes)) {\n        rrElement.attributes[name] = value;\n      }\n      elementNode.scrollLeft && (rrElement.scrollLeft = elementNode.scrollLeft);\n      elementNode.scrollTop && (rrElement.scrollTop = elementNode.scrollTop);\n      /**\n       * We don't have to record special values of input elements at the beginning.\n       * Because if these values are changed later, the mutation will be applied through the batched input events on its RRElement after the diff algorithm is executed.\n       */\n      break;\n    case NodeType.TEXT_NODE:\n      rrNode = rrdom.createTextNode((node as Text).textContent || '');\n      break;\n    case NodeType.CDATA_SECTION_NODE:\n      rrNode = rrdom.createCDATASection((node as CDATASection).data);\n      break;\n    case NodeType.COMMENT_NODE:\n      rrNode = rrdom.createComment((node as Comment).textContent || '');\n      break;\n    // if node is a shadow root\n    case NodeType.DOCUMENT_FRAGMENT_NODE:\n      rrNode = (parentRRNode as IRRElement).attachShadow({ mode: 'open' });\n      break;\n    default:\n      return null;\n  }\n\n  let sn: serializedNodeWithId | null = domMirror.getMeta(node);\n\n  if (rrdom instanceof RRDocument) {\n    if (!sn) {\n      sn = getDefaultSN(rrNode, rrdom.unserializedId);\n      domMirror.add(node, sn);\n    }\n    rrdom.mirror.add(rrNode, { ...sn });\n  }\n\n  return rrNode;\n}\n\n/**\n * Build a RRDocument from a real document tree.\n * @param dom the real document tree\n * @param domMirror the NodeMirror that records the real document tree\n * @param rrdom the rrdom object to be constructed\n * @returns the build rrdom\n */\nexport function buildFromDom(\n  dom: Document,\n  domMirror: NodeMirror = createNodeMirror(),\n  rrdom: IRRDocument = new RRDocument(),\n) {\n  function walk(node: Node, parentRRNode: IRRNode | null) {\n    const rrNode = buildFromNode(node, rrdom, domMirror, parentRRNode);\n    if (rrNode === null) return;\n    if (\n      // if the parentRRNode isn't a RRIFrameElement\n      parentRRNode?.nodeName !== 'IFRAME' &&\n      // if node isn't a shadow root\n      node.nodeType !== NodeType.DOCUMENT_FRAGMENT_NODE\n    ) {\n      parentRRNode?.appendChild(rrNode);\n      rrNode.parentNode = parentRRNode;\n      rrNode.parentElement = parentRRNode as RRElement;\n    }\n\n    if (node.nodeName === 'IFRAME') {\n      walk((node as HTMLIFrameElement).contentDocument!, rrNode);\n    } else if (\n      node.nodeType === NodeType.DOCUMENT_NODE ||\n      node.nodeType === NodeType.ELEMENT_NODE ||\n      node.nodeType === NodeType.DOCUMENT_FRAGMENT_NODE\n    ) {\n      // if the node is a shadow dom\n      if (\n        node.nodeType === NodeType.ELEMENT_NODE &&\n        (node as HTMLElement).shadowRoot\n      )\n        walk((node as HTMLElement).shadowRoot!, rrNode);\n      node.childNodes.forEach((childNode) => walk(childNode, rrNode));\n    }\n  }\n  walk(dom, null);\n  return rrdom;\n}\n\nexport function createMirror(): Mirror {\n  return new Mirror();\n}\n\n// based on Mirror from rrweb-snapshots\nexport class Mirror implements IMirror<RRNode> {\n  private idNodeMap: Map<number, RRNode> = new Map();\n  private nodeMetaMap: WeakMap<RRNode, serializedNodeWithId> = new WeakMap();\n\n  getId(n: RRNode | undefined | null): number {\n    if (!n) return -1;\n\n    const id = this.getMeta(n)?.id;\n\n    // if n is not a serialized Node, use -1 as its id.\n    return id ?? -1;\n  }\n\n  getNode(id: number): RRNode | null {\n    return this.idNodeMap.get(id) || null;\n  }\n\n  getIds(): number[] {\n    return Array.from(this.idNodeMap.keys());\n  }\n\n  getMeta(n: RRNode): serializedNodeWithId | null {\n    return this.nodeMetaMap.get(n) || null;\n  }\n\n  // removes the node from idNodeMap\n  // doesn't remove the node from nodeMetaMap\n  removeNodeFromMap(n: RRNode) {\n    const id = this.getId(n);\n    this.idNodeMap.delete(id);\n\n    if (n.childNodes) {\n      n.childNodes.forEach((childNode) => this.removeNodeFromMap(childNode));\n    }\n  }\n  has(id: number): boolean {\n    return this.idNodeMap.has(id);\n  }\n\n  hasNode(node: RRNode): boolean {\n    return this.nodeMetaMap.has(node);\n  }\n\n  add(n: RRNode, meta: serializedNodeWithId) {\n    const id = meta.id;\n    this.idNodeMap.set(id, n);\n    this.nodeMetaMap.set(n, meta);\n  }\n\n  replace(id: number, n: RRNode) {\n    this.idNodeMap.set(id, n);\n  }\n\n  reset() {\n    this.idNodeMap = new Map();\n    this.nodeMetaMap = new WeakMap();\n  }\n}\n\n/**\n * Get a default serializedNodeWithId value for a RRNode.\n * @param id the serialized id to assign\n */\nexport function getDefaultSN(node: IRRNode, id: number): serializedNodeWithId {\n  switch (node.RRNodeType) {\n    case RRNodeType.Document:\n      return {\n        id,\n        type: node.RRNodeType,\n        childNodes: [],\n      };\n    case RRNodeType.DocumentType:\n      const doctype = node as IRRDocumentType;\n      return {\n        id,\n        type: node.RRNodeType,\n        name: doctype.name,\n        publicId: doctype.publicId,\n        systemId: doctype.systemId,\n      };\n    case RRNodeType.Element:\n      return {\n        id,\n        type: node.RRNodeType,\n        tagName: (node as IRRElement).tagName.toLowerCase(), // In rrweb data, all tagNames are lowercase.\n        attributes: {},\n        childNodes: [],\n      };\n    case RRNodeType.Text:\n      return {\n        id,\n        type: node.RRNodeType,\n        textContent: (node as IRRText).textContent || '',\n      };\n    case RRNodeType.Comment:\n      return {\n        id,\n        type: node.RRNodeType,\n        textContent: (node as IRRComment).textContent || '',\n      };\n    case RRNodeType.CDATA:\n      return {\n        id,\n        type: node.RRNodeType,\n        textContent: '',\n      };\n  }\n}\n\nexport {\n  diff,\n  createOrGetNode,\n  StyleRuleType,\n  ReplayerHandler,\n  VirtualStyleRules,\n} from './diff';\nexport * from './document';\n"], "names": ["NodeType", "Mirror", "this", "idNodeMap", "Map", "nodeMetaMap", "WeakMap", "prototype", "getId", "n", "_a", "id", "getMeta", "getNode", "get", "getIds", "Array", "from", "keys", "removeNodeFromMap", "_this", "childNodes", "for<PERSON>ach", "childNode", "has", "hasNode", "node", "add", "meta", "set", "replace", "reset", "toCSSText", "style", "properties", "name", "value", "normalizedName", "hyphenate", "push", "join", "camelizeRE", "CUSTOM_PROPERTY_REGEX", "camelize", "str", "test", "_", "c", "toUpperCase", "hyphenateRE", "toLowerCase", "BaseRRNode", "constructor", "args", "ELEMENT_NODE", "TEXT_NODE", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "nextS<PERSON>ling", "parentNode", "siblings", "index", "indexOf", "contains", "child", "append<PERSON><PERSON><PERSON>", "_new<PERSON><PERSON>d", "Error", "insertBefore", "_refChild", "<PERSON><PERSON><PERSON><PERSON>", "toString", "BaseRRDocumentImpl", "RRNodeClass", "BaseRRDocument", "DOCUMENT_NODE", "RRNodeType", "Document", "documentElement", "find", "Element", "tagName", "body", "head", "implementation", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "DocumentType", "some", "s", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "childIndex", "splice", "indexOfChild", "open", "close", "write", "content", "publicId", "doctype", "createDocumentType", "createDocument", "_namespace", "_qualifiedName", "_doctype", "qualifiedName", "systemId", "BaseRRDocumentTypeImpl", "ownerDocument", "createElement", "element", "BaseRRElementImpl", "createElementNS", "_namespaceURI", "createTextNode", "data", "text", "BaseRRTextImpl", "createComment", "comment", "BaseRRCommentImpl", "createCDATASection", "CDATASection", "BaseRRCDATASectionImpl", "super", "DOCUMENT_TYPE_NODE", "nodeName", "textContent", "result", "classList", "ClassList", "attributes", "class", "newClassName", "className", "cssText", "res", "propertyDelimiter", "split", "item", "tmp", "trim", "parseCSSText", "setProperty", "priority", "removeProperty", "getAttribute", "setAttribute", "attribute", "setAttributeNS", "removeAttribute", "attachShadow", "_init", "shadowRoot", "dispatchEvent", "_event", "attributeString", "BaseRRMediaElementImpl", "RRElementClass", "play", "paused", "pause", "Text", "JSON", "stringify", "COMMENT_NODE", "Comment", "CDATA_SECTION_NODE", "CDATA", "classText", "onChange", "classNames", "String", "classes", "filter", "NAMESPACES", "svg", "xmlns", "diff", "oldTree", "newTree", "replayer", "rrnodeMirror", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirror", "diff<PERSON><PERSON><PERSON><PERSON>", "inputDataToApply", "scrollDataToApply", "scrollData", "oldElement", "newRRElement", "oldAttributes", "newAttributes", "newValue", "sn", "isSVG", "image", "document", "src", "onload", "ctx", "getContext", "drawImage", "width", "height", "scrollLeft", "scrollTop", "diffProps", "inputData", "oldMediaElement", "newMediaRRElement", "undefined", "muted", "volume", "currentTime", "canvasMutations", "canvasMutation", "applyCanvas", "event", "mutation", "styleNode", "virtualStyleRules", "sheet", "rule", "type", "StyleRuleType", "Insert", "isArray", "positions", "getPositionsAndIndex", "getNestedRule", "cssRules", "insertRule", "e", "Remove", "deleteRule", "SetProperty", "property", "RemoveProperty", "applyVirtualStyleRulesToNode", "rules", "mode", "applyScroll", "applyInput", "oldContentDocument", "contentDocument", "newIFrameElement", "oldIdToIndex", "indexInOld", "oldStartIndex", "oldEndIndex", "newStartIndex", "newEndIndex", "oldStartNode", "oldEndNode", "newStartNode", "newEndNode", "i", "<PERSON><PERSON><PERSON><PERSON>", "nodeToMove", "newNode", "createOrGetNode", "referenceRRNode", "referenceNode", "rrNode", "dom<PERSON><PERSON><PERSON>r", "position", "slice", "nestedIndex", "pop", "RRDocument", "RRNode", "createMirror", "unserializedId", "_unserializedId", "documentTypeNode", "RRDocumentType", "upperTagName", "RRMediaElement", "RRIFrameElement", "RRCanvasElement", "RRStyleElement", "<PERSON><PERSON><PERSON>", "commentNode", "RRComment", "sectionNode", "RRCDATASection", "textNode", "RRText", "destroyTree", "buildFromNode", "rrdom", "parentRRNode", "compatMode", "documentType", "elementNode", "HTMLFormElement", "rr<PERSON><PERSON>", "DOCUMENT_FRAGMENT_NODE", "getDefaultSN", "buildFromDom", "dom", "createNodeMirror", "walk", "delete"], "mappings": "AAAA,IAAIA,GACJ,SAAWA,GACPA,EAASA,EAAmB,SAAI,GAAK,WACrCA,EAASA,EAAuB,aAAI,GAAK,eACzCA,EAASA,EAAkB,QAAI,GAAK,UACpCA,EAASA,EAAe,KAAI,GAAK,OACjCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAkB,QAAI,GAAK,UANxC,CAOGA,IAAaA,EAAW,KAU3B,IAAIC,EAAU,WACV,SAASA,IACLC,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,YAAc,IAAIC,QA8C3B,OA5CAL,EAAOM,UAAUC,MAAQ,SAAUC,GAC/B,IAAIC,EACJ,IAAKD,EACD,OAAQ,EACZ,IAAIE,EAAgC,QAA1BD,EAAKR,KAAKU,QAAQH,UAAuB,IAAPC,OAAgB,EAASA,EAAGC,GACxE,OAAOA,MAAAA,EAA+BA,GAAM,GAEhDV,EAAOM,UAAUM,QAAU,SAAUF,GACjC,OAAOT,KAAKC,UAAUW,IAAIH,IAAO,MAErCV,EAAOM,UAAUQ,OAAS,WACtB,OAAOC,MAAMC,KAAKf,KAAKC,UAAUe,SAErCjB,EAAOM,UAAUK,QAAU,SAAUH,GACjC,OAAOP,KAAKG,YAAYS,IAAIL,IAAM,MAEtCR,EAAOM,UAAUY,kBAAoB,SAAUV,GAC3C,IAAIW,EAAQlB,KACRS,EAAKT,KAAKM,MAAMC,GACpBP,KAAKC,UAAkB,OAAEQ,GACrBF,EAAEY,YACFZ,EAAEY,WAAWC,SAAQ,SAAUC,GAC3B,OAAOH,EAAMD,kBAAkBI,OAI3CtB,EAAOM,UAAUiB,IAAM,SAAUb,GAC7B,OAAOT,KAAKC,UAAUqB,IAAIb,IAE9BV,EAAOM,UAAUkB,QAAU,SAAUC,GACjC,OAAOxB,KAAKG,YAAYmB,IAAIE,IAEhCzB,EAAOM,UAAUoB,IAAM,SAAUlB,EAAGmB,GAChC,IAAIjB,EAAKiB,EAAKjB,GACdT,KAAKC,UAAU0B,IAAIlB,EAAIF,GACvBP,KAAKG,YAAYwB,IAAIpB,EAAGmB,IAE5B3B,EAAOM,UAAUuB,QAAU,SAAUnB,EAAIF,GACrCP,KAAKC,UAAU0B,IAAIlB,EAAIF,IAE3BR,EAAOM,UAAUwB,MAAQ,WACrB7B,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,YAAc,IAAIC,SAEpBL,cClDK+B,EAAUC,GACxB,MAAMC,EAAa,GACnB,IAAK,MAAMC,KAAQF,EAAO,CACxB,MAAMG,EAAQH,EAAME,GACpB,GAAqB,iBAAVC,EAAoB,SAC/B,MAAMC,EAAiBC,EAAUH,GACjCD,EAAWK,KAAK,GAAGF,MAAmBD,MAExC,OAAOF,EAAWM,KAAK,KAMzB,MAAMC,EAAa,YACbC,EAAwB,oBACjBC,EAAYC,GACnBF,EAAsBG,KAAKD,GAAaA,EACrCA,EAAId,QAAQW,GAAY,CAACK,EAAGC,IAAOA,EAAIA,EAAEC,cAAgB,KAM5DC,EAAc,aACPX,EAAaM,GACjBA,EAAId,QAAQmB,EAAa,OAAOC,oBCoF5BC,EAaXC,eAAeC,GAZRnD,gBAAwB,GACxBA,mBAAgC,KAChCA,gBAA6B,KAGpBA,kBAAuBF,EAASsD,aAChCpD,eAAoBF,EAASuD,UAU7CC,iBACE,OAAOtD,KAAKmB,WAAW,IAAM,KAG/BoC,gBACE,OAAOvD,KAAKmB,WAAWnB,KAAKmB,WAAWqC,OAAS,IAAM,KAGxDC,kBACE,MAAMC,EAAa1D,KAAK0D,WACxB,IAAKA,EAAY,OAAO,KACxB,MAAMC,EAAWD,EAAWvC,WACtByC,EAAQD,EAASE,QAAQ7D,MAC/B,OAAO2D,EAASC,EAAQ,IAAM,KAGzBE,SAAStC,GACd,GAAIA,IAASxB,KAAM,OAAO,EAC1B,IAAK,MAAM+D,KAAS/D,KAAKmB,WACvB,GAAI4C,EAAMD,SAAStC,GAAO,OAAO,EAEnC,OAAO,EAGFwC,YAAYC,GACjB,MAAM,IAAIC,MACR,+GAIGC,aAAaF,EAAoBG,GACtC,MAAM,IAAIF,MACR,gHAIGG,YAAY7C,GACjB,MAAM,IAAI0C,MACR,+GAIGI,WACL,MAAO,mBAIKC,EAEdC,GACA,OAAO,MAAMC,UAAuBD,EAA7BtB,kCACWlD,cAAmBF,EAAS4E,cAC5B1E,cAAwB,YACxBA,gBAA0C,aAC1CA,gBAAa2E,EAAWC,SACjC5E,iBAA6B,KAEpC6E,sBACE,OACG7E,KAAKmB,WAAW2D,MACdtD,GACCA,EAAKmD,aAAeA,EAAWI,SACE,SAAhCvD,EAAoBwD,WACL,KAIxBC,iBACE,iBACGjF,KAAK6E,sCAAiB1D,WAAW2D,MAC/BtD,GACCA,EAAKmD,aAAeA,EAAWI,SACE,SAAhCvD,EAAoBwD,YACL,KAIxBE,iBACE,iBACGlF,KAAK6E,sCAAiB1D,WAAW2D,MAC/BtD,GACCA,EAAKmD,aAAeA,EAAWI,SACE,SAAhCvD,EAAoBwD,YACL,KAIxBG,qBACE,OAAOnF,KAGToF,wBACE,OAAOpF,KAAK6E,gBAGPb,YAAY3C,GACjB,MAAMgE,EAAWhE,EAAUsD,WAC3B,IACEU,IAAaV,EAAWI,SACxBM,IAAaV,EAAWW,eAEpBtF,KAAKmB,WAAWoE,MAAMC,GAAMA,EAAEb,aAAeU,IAC/C,MAAM,IAAInB,MACR,yEACEmB,IAAaV,EAAWI,QAAU,YAAc,sCAQxD,OAHA1D,EAAUoE,cAAgB,KAC1BpE,EAAUqC,WAAa1D,KACvBA,KAAKmB,WAAWkB,KAAKhB,GACdA,EAGF8C,aAAauB,EAAmBC,GACrC,MAAMN,EAAWK,EAASf,WAC1B,IACEU,IAAaV,EAAWI,SACxBM,IAAaV,EAAWW,eAEpBtF,KAAKmB,WAAWoE,MAAMC,GAAMA,EAAEb,aAAeU,IAC/C,MAAM,IAAInB,MACR,0EACEmB,IAAaV,EAAWI,QAAU,YAAc,sCAKxD,GAAiB,OAAbY,EAAmB,OAAO3F,KAAKgE,YAAY0B,GAC/C,MAAME,EAAa5F,KAAKmB,WAAW0C,QAAQ8B,GAC3C,IAAmB,GAAfC,EACF,MAAM,IAAI1B,MACR,uIAKJ,OAHAlE,KAAKmB,WAAW0E,OAAOD,EAAY,EAAGF,GACtCA,EAASD,cAAgB,KACzBC,EAAShC,WAAa1D,KACf0F,EAGFrB,YAAY7C,GACjB,MAAMsE,EAAe9F,KAAKmB,WAAW0C,QAAQrC,GAC7C,IAAsB,IAAlBsE,EACF,MAAM,IAAI5B,MACR,4GAKJ,OAHAlE,KAAKmB,WAAW0E,OAAOC,EAAc,GACrCtE,EAAKiE,cAAgB,KACrBjE,EAAKkC,WAAa,KACXlC,EAGFuE,OACL/F,KAAKmB,WAAa,GAGb6E,SAUAC,MAAMC,GACX,IAAIC,EAWJ,GARE,uEADAD,EAGAC,EAAW,yCAGX,sEADAD,IAGAC,EAAW,yCACTA,EAAU,CACZ,MAAMC,EAAUpG,KAAKqG,mBAAmB,OAAQF,EAAU,IAC1DnG,KAAK+F,OACL/F,KAAKgE,YAAYoC,IAIrBE,eACEC,EACAC,EACAC,GAEA,OAAO,IAAIhC,EAGb4B,mBACEK,EACAP,EACAQ,GAEA,MAAMP,EAAU,IAAKQ,EAAuB3D,GAA5B,CACdyD,EACAP,EACAQ,GAGF,OADAP,EAAQS,cAAgB7G,KACjBoG,EAGTU,cAAc9B,GACZ,MAAM+B,EAAU,IAAKC,EAAkB/D,GAAvB,CAAoC+B,GAEpD,OADA+B,EAAQF,cAAgB7G,KACjB+G,EAGTE,gBAAgBC,EAAuBR,GACrC,OAAO1G,KAAK8G,cAAcJ,GAG5BS,eAAeC,GACb,MAAMC,EAAO,IAAKC,EAAerE,GAApB,CAAiCmE,GAE9C,OADAC,EAAKR,cAAgB7G,KACdqH,EAGTE,cAAcH,GACZ,MAAMI,EAAU,IAAKC,EAAkBxE,GAAvB,CAAoCmE,GAEpD,OADAI,EAAQX,cAAgB7G,KACjBwH,EAGTE,mBAAmBN,GACjB,MAAMO,EAAe,IAAKC,EAAuB3E,GAA5B,CAAyCmE,GAE9D,OADAO,EAAad,cAAgB7G,KACtB2H,EAGTrD,WACE,MAAO,wBAKGsC,EAEdpC,GAGA,OAAO,cACGA,EAURtB,YAAYwD,EAAuBP,EAAkBQ,GACnDkB,QATc7H,cAAmBF,EAASgI,mBAC5B9H,gBAAa2E,EAAWW,aAKjCtF,iBAA6B,KAIlCA,KAAKiC,KAAOyE,EACZ1G,KAAKmG,SAAWA,EAChBnG,KAAK2G,SAAWA,EAChB3G,KAAK+H,SAAWrB,EAGlBpC,WACE,MAAO,4BAKG0C,EAEdxC,GAGA,OAAO,cAA4BA,EAUjCtB,YAAY8B,GACV6C,QAVc7H,cAAmBF,EAASsD,aAC5BpD,gBAAa2E,EAAWI,QAGjC/E,gBAAqC,GACrCA,gBAAgC,KAMrCA,KAAKgF,QAAUA,EAAQlC,cACvB9C,KAAK+H,SAAW/C,EAAQlC,cAG1BkF,kBACE,IAAIC,EAAS,GAEb,OADAjI,KAAKmB,WAAWC,SAASI,GAAUyG,GAAUzG,EAAKwG,cAC3CC,EAGTD,gBAAuBA,GACrBhI,KAAKmB,WAAa,CAACnB,KAAK6G,cAAcM,eAAea,IAGvDE,gBACE,OAAO,IAAIC,EACTnI,KAAKoI,WAAWC,OACfC,IACCtI,KAAKoI,WAAWC,MAAQC,KAK9B7H,SACE,OAAOT,KAAKoI,WAAW3H,IAAM,GAG/B8H,gBACE,OAAOvI,KAAKoI,WAAWC,OAAS,GAGlCtG,YACE,MAAMA,EAAS/B,KAAKoI,WAAWrG,eDjdRyG,GAC3B,MAAMC,EAA8B,GAE9BC,EAAoB,QAW1B,OATAF,EACG5G,QAFa,eAEI,IACjB+G,MALmB,iBAMnBvH,SAAQ,SAAUwH,GACjB,GAAIA,EAAM,CACR,MAAMC,EAAMD,EAAKD,MAAMD,GACvBG,EAAIrF,OAAS,IAAMiF,EAAIhG,EAASoG,EAAI,GAAGC,SAAWD,EAAI,GAAGC,YAGxDL,ECocCM,CAAa/I,KAAKoI,WAAWrG,OAC7B,GACEgB,EAAc,aAqBpB,OApBAhB,EAAMiH,YAAc,CAClB/G,EACAC,EACA+G,KAEA,GAAIlG,EAAYJ,KAAKV,GAAO,OAC5B,MAAME,EAAiBM,EAASR,GAC3BC,EACAH,EAAMI,GAAkBD,SADVH,EAAMI,GAER,cAAb8G,IAA0BlH,EAAMI,IAAmB,eACvDnC,KAAKoI,WAAWrG,MAAQD,EAAUC,IAEpCA,EAAMmH,eAAkBjH,IACtB,GAAIc,EAAYJ,KAAKV,GAAO,MAAO,GACnC,MAAME,EAAiBM,EAASR,GAC1BC,EAAQH,EAAMI,IAAmB,GAGvC,cAFOJ,EAAMI,GACbnC,KAAKoI,WAAWrG,MAAQD,EAAUC,GAC3BG,GAEFH,EAGFoH,aAAalH,GAClB,OAAOjC,KAAKoI,WAAWnG,IAAS,KAG3BmH,aAAanH,EAAcoH,GAChCrJ,KAAKoI,WAAWnG,GAAQoH,EAGnBC,eACL/C,EACAG,EACAxE,GAEAlC,KAAKoJ,aAAa1C,EAAexE,GAG5BqH,gBAAgBtH,UACdjC,KAAKoI,WAAWnG,GAGlB+B,YAAY0B,GAIjB,OAHA1F,KAAKmB,WAAWkB,KAAKqD,GACrBA,EAAShC,WAAa1D,KACtB0F,EAASD,cAAgBzF,KAClB0F,EAGFvB,aAAauB,EAAmBC,GACrC,GAAiB,OAAbA,EAAmB,OAAO3F,KAAKgE,YAAY0B,GAC/C,MAAME,EAAa5F,KAAKmB,WAAW0C,QAAQ8B,GAC3C,IAAmB,GAAfC,EACF,MAAM,IAAI1B,MACR,uIAKJ,OAHAlE,KAAKmB,WAAW0E,OAAOD,EAAY,EAAGF,GACtCA,EAASD,cAAgBzF,KACzB0F,EAAShC,WAAa1D,KACf0F,EAGFrB,YAAY7C,GACjB,MAAMsE,EAAe9F,KAAKmB,WAAW0C,QAAQrC,GAC7C,IAAsB,IAAlBsE,EACF,MAAM,IAAI5B,MACR,2GAKJ,OAHAlE,KAAKmB,WAAW0E,OAAOC,EAAc,GACrCtE,EAAKiE,cAAgB,KACrBjE,EAAKkC,WAAa,KACXlC,EAGFgI,aAAaC,GAClB,MAAMC,EAAa1J,KAAK6G,cAAcC,cAAc,cAEpD,OADA9G,KAAK0J,WAAaA,EACXA,EAGFC,cAAcC,GACnB,OAAO,EAGTtF,WACE,IAAIuF,EAAkB,GACtB,IAAK,MAAMR,KAAarJ,KAAKoI,WAC3ByB,GAAmB,GAAGR,MAAcrJ,KAAKoI,WAAWiB,OAEtD,MAAO,GAAGrJ,KAAKgF,WAAW6E,eAKhBC,EAEdC,GACA,OAAO,cAAiCA,EAKtCP,aAAaC,GACX,MAAM,IAAIvF,MACR,iHAGG8F,OACLhK,KAAKiK,QAAS,EAETC,QACLlK,KAAKiK,QAAS,aAKJ3C,EACd9C,GAIA,OAAO,cAAyBA,EAM9BtB,YAAYkE,GACVS,QANc7H,cAAmBF,EAASuD,UAC5BrD,cAAoB,QACpBA,gBAAa2E,EAAWwF,KAKtCnK,KAAKoH,KAAOA,EAGdY,kBACE,OAAOhI,KAAKoH,KAGdY,gBAAuBA,GACrBhI,KAAKoH,KAAOY,EAGd1D,WACE,MAAO,eAAe8F,KAAKC,UAAUrK,KAAKoH,mBAKhCK,EAEdjD,GAGA,OAAO,cAA4BA,EAMjCtB,YAAYkE,GACVS,QANc7H,cAAmBF,EAASwK,aAC5BtK,cAAuB,WACvBA,gBAAa2E,EAAW4F,QAKtCvK,KAAKoH,KAAOA,EAGdY,kBACE,OAAOhI,KAAKoH,KAGdY,gBAAuBA,GACrBhI,KAAKoH,KAAOY,EAGd1D,WACE,MAAO,kBAAkB8F,KAAKC,UAAUrK,KAAKoH,mBAKnCQ,EAEdpD,GAGA,OAAO,cACGA,EAORtB,YAAYkE,GACVS,QANc7H,cAA6B,iBAC7BA,cAAmBF,EAAS0K,mBAC5BxK,gBAAa2E,EAAW8F,MAKtCzK,KAAKoH,KAAOA,EAGdY,kBACE,OAAOhI,KAAKoH,KAGdY,gBAAuBA,GACrBhI,KAAKoH,KAAOY,EAGd1D,WACE,MAAO,uBAAuB8F,KAAKC,UAAUrK,KAAKoH,gBAK3Ce,EAIXjF,YACEwH,EACAC,GAEA,GANF3K,aAAoB,GAapBA,SAAM,IAAI4K,KACR,IAAK,MAAMhC,KAAQgC,EAAY,CAC7B,MAAMrC,EAAYsC,OAAOjC,GACrB5I,KAAK8K,QAAQjH,QAAQ0E,IAAc,GACvCvI,KAAK8K,QAAQzI,KAAKkG,GAEpBvI,KAAK2K,UAAY3K,KAAK2K,SAAS3K,KAAK8K,QAAQxI,KAAK,OAGnDtC,YAAS,IAAI4K,KACX5K,KAAK8K,QAAU9K,KAAK8K,QAAQC,QACzBnC,IAAuC,IAA9BgC,EAAW/G,QAAQ+E,KAE/B5I,KAAK2K,UAAY3K,KAAK2K,SAAS3K,KAAK8K,QAAQxI,KAAK,OApB7CoI,EAAW,CACb,MAAMI,EAAUJ,EAAU5B,OAAOH,MAAM,OACvC3I,KAAK8K,QAAQzI,QAAQyI,GAEvB9K,KAAK2K,SAAWA,OA8BR7K,GAAZ,SAAYA,GACVA,iCACAA,mCACAA,uCACAA,6BACAA,+CACAA,qDACAA,iCACAA,iEACAA,mCACAA,qCACAA,gDACAA,wDAZF,CAAYA,IAAAA,OCtrBZ,MAAMkL,EAAqC,CACzCC,IAAK,6BACL,aAAc,+BACdC,MAAO,0CAsDOC,EACdC,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAcJ,EAAQjK,WACtBsK,EAAcJ,EAAQlK,WAC5BoK,EACEA,GACCF,EAAuBK,QACvBL,EAAQxE,cAA6B6E,QAEpCF,EAAYhI,OAAS,GAAKiI,EAAYjI,OAAS,IACjDmI,EACE7K,MAAMC,KAAKyK,GACXC,EACAL,EACAE,EACAC,GAIJ,IAAIK,EAAmB,KACrBC,EAAoB,KACtB,OAAQR,EAAQ1G,YACd,KAAKA,EAAWC,SAEdiH,EADsBR,EAC4BS,WAClD,MAEF,KAAKnH,EAAWI,QAAS,CACvB,MAAMgH,EAAaX,EACbY,EAAeX,EAIrB,OA2FN,SACED,EACAC,EACAE,GAEA,MAAMU,EAAgBb,EAAQhD,WACxB8D,EAAgBb,EAAQjD,WAE9B,IAAK,MAAMnG,KAAQiK,EAAe,CAChC,MAAMC,EAAWD,EAAcjK,GACzBmK,EAAKb,EAAa7K,QAAQ2K,GAChC,GAAIe,GAAM,UAAWA,GAAMA,EAAGC,OAASrB,EAAW/I,GAChDmJ,EAAQ9B,eAAe0B,EAAW/I,GAAOA,EAAMkK,QAC5C,GAAwB,WAApBd,EAAQrG,SAAiC,eAAT/C,EAAuB,CAC9D,MAAMqK,EAAQC,SAASzF,cAAc,OACrCwF,EAAME,IAAML,EACZG,EAAMG,OAAS,KACb,MAAMC,EAAOtB,EAA8BuB,WAAW,MAClDD,GACFA,EAAIE,UAAUN,EAAO,EAAG,EAAGA,EAAMO,MAAOP,EAAMQ,cAG7C1B,EAAQhC,aAAanH,EAAMkK,GAGpC,IAAK,MAAMlK,KAAEA,KAAUnB,MAAMC,KAAKkL,GAC1BhK,KAAQiK,GAAgBd,EAAQ7B,gBAAgBtH,GAExDoJ,EAAQ0B,aAAe3B,EAAQ2B,WAAa1B,EAAQ0B,YACpD1B,EAAQ2B,YAAc5B,EAAQ4B,UAAY3B,EAAQ2B,WA3H9CC,CAAUlB,EAAYC,EAAcT,GACpCM,EAAqBG,EAA2BF,WAChDF,EAAoBI,EAA2BkB,UACvClB,EAAahH,SACnB,IAAK,QACL,IAAK,QAAS,CACZ,MAAMmI,EAAkB/B,EAClBgC,EAAoBpB,OACOqB,IAA7BD,EAAkBnD,SACpBmD,EAAkBnD,OACdkD,EAAgBjD,QAChBiD,EAAgBnD,aACUqD,IAA5BD,EAAkBE,QACpBH,EAAgBG,MAAQF,EAAkBE,YACXD,IAA7BD,EAAkBG,SACpBJ,EAAgBI,OAASH,EAAkBG,aACPF,IAAlCD,EAAkBI,cACpBL,EAAgBK,YAAcJ,EAAkBI,aAClD,MAEF,IAAK,SACFnC,EAA4BoC,gBAAgBrM,SAC1CsM,GACCpC,EAASqC,YACPD,EAAeE,MACfF,EAAeG,SACfzC,KAGN,MACF,IAAK,kBA2TX0C,EACAC,GAEA,MAAMC,EAAQF,EAAUE,MAExBD,EAAkB3M,SAAS6M,IACzB,GAAIA,EAAKC,OAASC,EAAcC,OAC9B,IACE,GAAItN,MAAMuN,QAAQJ,EAAKrK,OAAQ,CAC7B,MAAM0K,UAAEA,EAAS1K,MAAEA,GAAU2K,EAAqBN,EAAKrK,OACpC4K,EAAcR,EAAMS,SAAUH,GACtCI,WAAWT,EAAKzF,QAAS5E,QAEpCoK,EAAMU,WAAWT,EAAKzF,QAASyF,EAAKrK,OAEtC,MAAO+K,SAMJ,GAAIV,EAAKC,OAASC,EAAcS,OACrC,IACE,GAAI9N,MAAMuN,QAAQJ,EAAKrK,OAAQ,CAC7B,MAAM0K,UAAEA,EAAS1K,MAAEA,GAAU2K,EAAqBN,EAAKrK,OACpC4K,EAAcR,EAAMS,SAAUH,GACtCO,WAAWjL,GAAS,QAE/BoK,EAAMa,WAAWZ,EAAKrK,OAExB,MAAO+K,SAMJ,GAAIV,EAAKC,OAASC,EAAcW,YAAa,CAC9BN,EAClBR,EAAMS,SACNR,EAAKrK,OAEI7B,MAAMiH,YAAYiF,EAAKc,SAAUd,EAAK/L,MAAO+L,EAAKhF,eACxD,GAAIgF,EAAKC,OAASC,EAAca,eAAgB,CACjCR,EAClBR,EAAMS,SACNR,EAAKrK,OAEI7B,MAAMmH,eAAe+E,EAAKc,cAzWjCE,CACElD,EACCV,EAA2B6D,OAIlC,GAAIlD,EAAatC,WAAY,CACtBqC,EAAWrC,YAAYqC,EAAWvC,aAAa,CAAE2F,KAAM,SAC5D,MAAM3D,EAAcO,EAAWrC,WAAYvI,WACrCsK,EAAcO,EAAatC,WAAWvI,YACxCqK,EAAYhI,OAAS,GAAKiI,EAAYjI,OAAS,IACjDmI,EACE7K,MAAMC,KAAKyK,GACXC,EACAM,EAAWrC,WACX4B,EACAC,GAGN,MAEF,KAAK5G,EAAWwF,KAChB,KAAKxF,EAAW4F,QAChB,KAAK5F,EAAW8F,MAEZW,EAAQpD,cACPqD,EAAmDjE,OAEpDgE,EAAQpD,YAAeqD,EAGFjE,MAa3B,GARAyE,GAAqBP,EAAS8D,YAAYvD,GAAmB,GAK7DD,GAAoBN,EAAS+D,WAAWzD,GAGf,WAArBP,EAAQtD,SAAuB,CACjC,MAAMuH,EAAsBlE,EAA8BmE,gBACpDC,EAAmBnE,EAEzB,GAAIiE,EAAoB,CACtB,MAAMlD,EAAKb,EAAa7K,QAAQ8O,EAAiBD,iBAC7CnD,GACFd,EAASI,OAAOjK,IAAI6N,mBAAyBlD,IAE/CjB,EACEmE,EACAE,EAAiBD,gBACjBjE,EACAC,KAsCR,SAASI,EACPH,EACAC,EACA/H,EACA4H,EACAC,WAEA,IAQIkE,EACFC,EATEC,EAAgB,EAClBC,EAAcpE,EAAYhI,OAAS,EACnCqM,EAAgB,EAChBC,EAAcrE,EAAYjI,OAAS,EACjCuM,EAAevE,EAAYmE,GAC7BK,EAAaxE,EAAYoE,GACzBK,EAAexE,EAAYoE,GAC3BK,EAAazE,EAAYqE,GAG3B,KAAOH,GAAiBC,GAAeC,GAAiBC,GACtD,QAAqBzC,IAAjB0C,EACFA,EAAevE,IAAcmE,QACxB,QAAmBtC,IAAf2C,EACTA,EAAaxE,IAAcoE,QACtB,GACLtE,EAASI,OAAOpL,MAAMyP,KAAkBxE,EAAajL,MAAM2P,GAE3D9E,EAAK4E,EAAcE,EAAc3E,EAAUC,GAC3CwE,EAAevE,IAAcmE,GAC7BM,EAAexE,IAAcoE,QACxB,GACLvE,EAASI,OAAOpL,MAAM0P,KAAgBzE,EAAajL,MAAM4P,GAEzD/E,EAAK6E,EAAYE,EAAY5E,EAAUC,GACvCyE,EAAaxE,IAAcoE,GAC3BM,EAAazE,IAAcqE,QACtB,GACLxE,EAASI,OAAOpL,MAAMyP,KAAkBxE,EAAajL,MAAM4P,GAE3DxM,EAAWS,aAAa4L,EAAcC,EAAWvM,aACjD0H,EAAK4E,EAAcG,EAAY5E,EAAUC,GACzCwE,EAAevE,IAAcmE,GAC7BO,EAAazE,IAAcqE,QACtB,GACLxE,EAASI,OAAOpL,MAAM0P,KAAgBzE,EAAajL,MAAM2P,GAEzDvM,EAAWS,aAAa6L,EAAYD,GACpC5E,EAAK6E,EAAYC,EAAc3E,EAAUC,GACzCyE,EAAaxE,IAAcoE,GAC3BK,EAAexE,IAAcoE,OACxB,CACL,IAAKJ,EAAc,CACjBA,EAAe,GACf,IAAK,IAAIU,EAAIR,EAAeQ,GAAKP,EAAaO,IAAK,CACjD,MAAMC,EAAW5E,EAAY2E,GACzBC,GAAY9E,EAASI,OAAOnK,QAAQ6O,KACtCX,EAAanE,EAASI,OAAOpL,MAAM8P,IAAaD,IAItD,GADAT,EAAaD,EAAalE,EAAajL,MAAM2P,IACzCP,EAAY,CACd,MAAMW,EAAa7E,EAAYkE,GAC/BhM,EAAWS,aAAakM,EAAYN,GACpC5E,EAAKkF,EAAYJ,EAAc3E,EAAUC,GACzCC,EAAYkE,QAAcrC,MACrB,CACL,MAAMiD,EAAUC,EACdN,EACA3E,EAASI,OACTH,cAQAD,EAASI,OAAOhL,QAAQgD,yBAAawK,QAASvJ,EAAWC,qBACzD0G,EAASI,OAAOhL,QAAQ4P,yBAAUpC,QAASvJ,EAAWI,SACrDrB,EAAwBmB,kBAEzBnB,EAAWW,YAAaX,EAAwBmB,iBAChD2G,EAAYmE,QAAiBtC,EAC7B0C,OAAe1C,GAEjB3J,EAAWS,aAAamM,EAASP,GAAgB,MACjD5E,EAAKmF,EAASL,EAAc3E,EAAUC,GAExC0E,EAAexE,IAAcoE,GAGjC,GAAIF,EAAgBC,EAAa,CAC/B,MAAMY,EAAkB/E,EAAYqE,EAAc,GAClD,IAAIW,EAAgB,KAQpB,IAPID,GACF9M,EAAWvC,WAAWC,SAAS2C,IAE3BuH,EAASI,OAAOpL,MAAMyD,KAAWwH,EAAajL,MAAMkQ,KAEpDC,EAAgB1M,MAEf8L,GAAiBC,IAAeD,EAAe,CACpD,MAAMS,EAAUC,EACd9E,EAAYoE,GACZvE,EAASI,OACTH,GAEF7H,EAAWS,aAAamM,EAASG,GACjCtF,EAAKmF,EAAS7E,EAAYoE,GAAgBvE,EAAUC,SAEjD,GAAIsE,EAAgBC,EACzB,KAAOH,GAAiBC,EAAaD,IAAiB,CACpD,MAAMnO,EAAOgK,EAAYmE,GACrBnO,IACFkC,EAAWW,YAAY7C,GACvB8J,EAASI,OAAOzK,kBAAkBO,cAM1B+O,EACdG,EACAC,EACApF,GAEA,IAAI/J,EAAOmP,EAAUhQ,QAAQ4K,EAAajL,MAAMoQ,IAChD,MAAMtE,EAAKb,EAAa7K,QAAQgQ,GAChC,GAAa,OAATlP,EAAe,OAAOA,EAC1B,OAAQkP,EAAO/L,YACb,KAAKA,EAAWC,SACdpD,EAAO,IAAIoD,SACX,MACF,KAAKD,EAAWW,aACd9D,EAAO+K,SAASpH,eAAekB,mBAC5BqK,EAA2BzO,KAC3ByO,EAA2BvK,SAC3BuK,EAA2B/J,UAE9B,MACF,KAAKhC,EAAWI,QACC2L,EAAsB1L,QAAQhC,cAG3CxB,EADE4K,GAAM,UAAWA,IAAMA,MAAAA,SAAAA,EAAIC,OACtBE,SAAStF,gBACd+D,EAAgB,IACf0F,EAAsB1L,QAAQhC,eAErBuJ,SAASzF,cAAe4J,EAAsB1L,SAC5D,MAEF,KAAKL,EAAWwF,KACd3I,EAAO+K,SAASpF,eAAgBuJ,EAAmBtJ,MACnD,MACF,KAAKzC,EAAW4F,QACd/I,EAAO+K,SAAShF,cAAemJ,EAAsBtJ,MACrD,MACF,KAAKzC,EAAW8F,MACdjJ,EAAO+K,SAAS7E,mBAAoBgJ,EAA2BtJ,MAKnE,OADIgF,GAAIuE,EAAUlP,IAAID,mBAAW4K,IAC1B5K,WAGOgN,EACdU,EACA0B,GAEA,MAAM3C,EAAOiB,EAAM0B,EAAS,IAC5B,OAAwB,IAApBA,EAASpN,OACJyK,EAEAO,EACJP,EAAKQ,SAASmC,EAAS,IAAwBnC,SAChDmC,EAASC,MAAM,QAKT1C,WAiCII,EAAqBuC,GACnC,MAAMxC,EAAY,IAAIwC,GAChBlN,EAAQ0K,EAAUyC,MACxB,MAAO,CAAEzC,UAAAA,EAAW1K,MAAAA,IApCtB,SAAYuK,GACVA,uBACAA,uBACAA,2BACAA,iCACAA,uCALF,CAAYA,IAAAA,aCpYC6C,UAAmBzM,EAAmB0M,IAiBjD/N,YAAYwI,GACV7D,QAdM7H,sBAAmB,EASpBA,YAAiBkR,IAEjBlR,gBAAgC,KAIjC0L,IACF1L,KAAK0L,OAASA,GAXlByF,qBACE,OAAOnR,KAAKoR,kBAcd9K,eACEC,EACAC,EACAC,GAEA,OAAO,IAAIuK,EAGb3K,mBACEK,EACAP,EACAQ,GAEA,MAAM0K,EAAmB,IAAIC,EAC3B5K,EACAP,EACAQ,GAGF,OADA0K,EAAiBxK,cAAgB7G,KAC1BqR,EAOTvK,cAAc9B,GACZ,MAAMuM,EAAevM,EAAQlC,cAC7B,IAAIiE,EACJ,OAAQwK,GACN,IAAK,QACL,IAAK,QACHxK,EAAU,IAAIyK,EAAeD,GAC7B,MACF,IAAK,SACHxK,EAAU,IAAI0K,EAAgBF,EAAcvR,KAAK0L,QACjD,MACF,IAAK,SACH3E,EAAU,IAAI2K,EAAgBH,GAC9B,MACF,IAAK,QACHxK,EAAU,IAAI4K,EAAeJ,GAC7B,MACF,QACExK,EAAU,IAAI6K,EAAUL,GAI5B,OADAxK,EAAQF,cAAgB7G,KACjB+G,EAGTQ,cAAcH,GACZ,MAAMyK,EAAc,IAAIC,EAAU1K,GAElC,OADAyK,EAAYhL,cAAgB7G,KACrB6R,EAGTnK,mBAAmBN,GACjB,MAAM2K,EAAc,IAAIC,EAAe5K,GAEvC,OADA2K,EAAYlL,cAAgB7G,KACrB+R,EAGT5K,eAAeC,GACb,MAAM6K,EAAW,IAAIC,EAAO9K,GAE5B,OADA6K,EAASpL,cAAgB7G,KAClBiS,EAGTE,cACEnS,KAAKmB,WAAa,GAClBnB,KAAK0L,OAAO7J,QAGdkE,OACE8B,MAAM9B,OACN/F,KAAKoR,iBAAmB,SAIfE,EAAiB1K,EAAuBqK,SAExCW,UAAkB5K,EAAkBiK,IAAjD/N,kCACElD,eAA8B,KAC9BA,gBAAgC,YAGrBwR,UAAuB1H,EAAuB8H,WAE9CF,UAAwBE,EAArC1O,kCACSlD,qBAGD,GAIN2M,aACE,OAAO,YAIEgF,UAAuBC,EAApC1O,kCACSlD,WAA2B,UAGvByR,UAAwBG,EAEnC1O,YAAYqO,EAAsB7F,GAChC7D,MAAM0J,GAFRvR,qBAA8B,IAAIgR,EAGhChR,KAAKuP,gBAAgB7D,OAASA,SAIrBwG,EAAS5K,EAAe2J,GAGxBa,EAAYrK,EAAkBwJ,GAG9Be,EAAiBpK,EAAuBqJ,YA8BrCmB,EACd5Q,EACA6Q,EACA1B,EACA2B,GAEA,IAAI5B,EAEJ,OAAQlP,EAAK6D,UACX,KAAKvF,EAAS4E,cACR4N,GAA0C,WAA1BA,EAAavK,SAC/B2I,EAAU4B,EAAiC/C,iBAE3CmB,EAAS2B,EACR3B,EAAuB6B,WAAc/Q,EAAkB+Q,YAI1D,MACF,KAAKzS,EAASgI,mBACZ,MAAM0K,EAAehR,EACrBkP,EAAS2B,EAAMhM,mBACbmM,EAAavQ,KACbuQ,EAAarM,SACbqM,EAAa7L,UAEf,MACF,KAAK7G,EAASsD,aACZ,MAAMqP,EAAcjR,EACdwD,GA5Ca+B,EA4Ca0L,aA1CbC,gBACd,OAEF3L,EAAQ/B,QAAQlC,cAwCnB4N,EAAS2B,EAAMvL,cAAc9B,GAC7B,MAAM2N,EAAYjC,EAClB,IAAK,MAAMzO,KAAEA,EAAIC,MAAEA,KAAWpB,MAAMC,KAAK0R,EAAYrK,YACnDuK,EAAUvK,WAAWnG,GAAQC,EAE/BuQ,EAAY1F,aAAe4F,EAAU5F,WAAa0F,EAAY1F,YAC9D0F,EAAYzF,YAAc2F,EAAU3F,UAAYyF,EAAYzF,WAK5D,MACF,KAAKlN,EAASuD,UACZqN,EAAS2B,EAAMlL,eAAgB3F,EAAcwG,aAAe,IAC5D,MACF,KAAKlI,EAAS0K,mBACZkG,EAAS2B,EAAM3K,mBAAoBlG,EAAsB4F,MACzD,MACF,KAAKtH,EAASwK,aACZoG,EAAS2B,EAAM9K,cAAe/F,EAAiBwG,aAAe,IAC9D,MAEF,KAAKlI,EAAS8S,uBACZlC,EAAU4B,EAA4B9I,aAAa,CAAE2F,KAAM,SAC3D,MACF,QACE,OAAO,KAvEb,IAAyBpI,EA0EvB,IAAIqF,EAAkCuE,EAAUjQ,QAAQc,GAUxD,OARI6Q,aAAiBrB,IACd5E,IACHA,EAAKyG,EAAanC,EAAQ2B,EAAMlB,gBAChCR,EAAUlP,IAAID,EAAM4K,IAEtBiG,EAAM3G,OAAOjK,IAAIiP,mBAAatE,KAGzBsE,WAUOoC,EACdC,EACApC,EJ5NF,WACI,OAAO,IAAI5Q,EI2NWiT,GACxBX,EAAqB,IAAIrB,GAiCzB,OA/BA,SAASiC,EAAKzR,EAAY8Q,GACxB,MAAM5B,EAAS0B,EAAc5Q,EAAM6Q,EAAO1B,EAAW2B,GACtC,OAAX5B,IAGyB,YAA3B4B,MAAAA,SAAAA,EAAcvK,WAEdvG,EAAK6D,WAAavF,EAAS8S,yBAE3BN,MAAAA,GAAAA,EAActO,YAAY0M,GAC1BA,EAAOhN,WAAa4O,EACpB5B,EAAOjL,cAAgB6M,GAGH,WAAlB9Q,EAAKuG,SACPkL,EAAMzR,EAA2B+N,gBAAkBmB,GAEnDlP,EAAK6D,WAAavF,EAAS4E,eAC3BlD,EAAK6D,WAAavF,EAASsD,cAC3B5B,EAAK6D,WAAavF,EAAS8S,yBAIzBpR,EAAK6D,WAAavF,EAASsD,cAC1B5B,EAAqBkI,YAEtBuJ,EAAMzR,EAAqBkI,WAAagH,GAC1ClP,EAAKL,WAAWC,SAASC,GAAc4R,EAAK5R,EAAWqP,OAG3DuC,CAAKF,EAAK,MACHV,WAGOnB,IACd,OAAO,IAAInR,QAIAA,EAAbmD,cACUlD,eAAiC,IAAIE,IACrCF,iBAAqD,IAAII,QAEjEE,MAAMC,SACJ,IAAKA,EAAG,OAAQ,EAEhB,MAAME,YAAKT,KAAKU,QAAQH,yBAAIE,GAG5B,OAAOA,MAAAA,EAAAA,GAAO,EAGhBE,QAAQF,GACN,OAAOT,KAAKC,UAAUW,IAAIH,IAAO,KAGnCI,SACE,OAAOC,MAAMC,KAAKf,KAAKC,UAAUe,QAGnCN,QAAQH,GACN,OAAOP,KAAKG,YAAYS,IAAIL,IAAM,KAKpCU,kBAAkBV,GAChB,MAAME,EAAKT,KAAKM,MAAMC,GACtBP,KAAKC,UAAUiT,OAAOzS,GAElBF,EAAEY,YACJZ,EAAEY,WAAWC,SAASC,GAAcrB,KAAKiB,kBAAkBI,KAG/DC,IAAIb,GACF,OAAOT,KAAKC,UAAUqB,IAAIb,GAG5Bc,QAAQC,GACN,OAAOxB,KAAKG,YAAYmB,IAAIE,GAG9BC,IAAIlB,EAAWmB,GACb,MAAMjB,EAAKiB,EAAKjB,GAChBT,KAAKC,UAAU0B,IAAIlB,EAAIF,GACvBP,KAAKG,YAAYwB,IAAIpB,EAAGmB,GAG1BE,QAAQnB,EAAYF,GAClBP,KAAKC,UAAU0B,IAAIlB,EAAIF,GAGzBsB,QACE7B,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,YAAc,IAAIC,kBAQXyS,EAAarR,EAAef,GAC1C,OAAQe,EAAKmD,YACX,KAAKA,EAAWC,SACd,MAAO,CACLnE,GAAAA,EACAyN,KAAM1M,EAAKmD,WACXxD,WAAY,IAEhB,KAAKwD,EAAWW,aACd,MAAMc,EAAU5E,EAChB,MAAO,CACLf,GAAAA,EACAyN,KAAM1M,EAAKmD,WACX1C,KAAMmE,EAAQnE,KACdkE,SAAUC,EAAQD,SAClBQ,SAAUP,EAAQO,UAEtB,KAAKhC,EAAWI,QACd,MAAO,CACLtE,GAAAA,EACAyN,KAAM1M,EAAKmD,WACXK,QAAUxD,EAAoBwD,QAAQhC,cACtCoF,WAAY,GACZjH,WAAY,IAEhB,KAAKwD,EAAWwF,KAMhB,KAAKxF,EAAW4F,QACd,MAAO,CACL9J,GAAAA,EACAyN,KAAM1M,EAAKmD,WACXqD,YAAcxG,EAAoBwG,aAAe,IAErD,KAAKrD,EAAW8F,MACd,MAAO,CACLhK,GAAAA,EACAyN,KAAM1M,EAAKmD,WACXqD,YAAa"}