{"version": 3, "file": "rrweb-record.min.js", "sources": ["../../../rrweb-snapshot/es/rrweb-snapshot.js", "../../src/utils.ts", "../../../types/dist/types.js", "../../src/record/mutation.ts", "../../src/record/observer.ts", "../../src/record/cross-origin-iframe-mirror.ts", "../../src/record/iframe-manager.ts", "../../src/record/shadow-dom-manager.ts", "../../../../node_modules/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../../src/record/observers/canvas/serialize-args.ts", "../../src/record/observers/canvas/2d.ts", "../../src/record/observers/canvas/canvas.ts", "../../src/record/observers/canvas/webgl.ts", "../../src/record/observers/canvas/canvas-manager.ts", "../../src/record/stylesheet-manager.ts", "../../src/record/index.ts"], "sourcesContent": ["var NodeType;\r\n(function (NodeType) {\r\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\r\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\r\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\r\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\r\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\r\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\r\n})(NodeType || (NodeType = {}));\n\nfunction isElement(n) {\r\n    return n.nodeType === n.ELEMENT_NODE;\r\n}\r\nfunction isShadowRoot(n) {\r\n    var host = n === null || n === void 0 ? void 0 : n.host;\r\n    return Boolean((host === null || host === void 0 ? void 0 : host.shadowRoot) === n);\r\n}\r\nfunction isNativeShadowDom(shadowRoot) {\r\n    return Object.prototype.toString.call(shadowRoot) === '[object ShadowRoot]';\r\n}\r\nfunction fixBrowserCompatibilityIssuesInCSS(cssText) {\r\n    if (cssText.includes(' background-clip: text;') &&\r\n        !cssText.includes(' -webkit-background-clip: text;')) {\r\n        cssText = cssText.replace(' background-clip: text;', ' -webkit-background-clip: text; background-clip: text;');\r\n    }\r\n    return cssText;\r\n}\r\nfunction getCssRulesString(s) {\r\n    try {\r\n        var rules = s.rules || s.cssRules;\r\n        return rules\r\n            ? fixBrowserCompatibilityIssuesInCSS(Array.from(rules).map(getCssRuleString).join(''))\r\n            : null;\r\n    }\r\n    catch (error) {\r\n        return null;\r\n    }\r\n}\r\nfunction getCssRuleString(rule) {\r\n    var cssStringified = rule.cssText;\r\n    if (isCSSImportRule(rule)) {\r\n        try {\r\n            cssStringified = getCssRulesString(rule.styleSheet) || cssStringified;\r\n        }\r\n        catch (_a) {\r\n        }\r\n    }\r\n    return cssStringified;\r\n}\r\nfunction isCSSImportRule(rule) {\r\n    return 'styleSheet' in rule;\r\n}\r\nvar Mirror = (function () {\r\n    function Mirror() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n    Mirror.prototype.getId = function (n) {\r\n        var _a;\r\n        if (!n)\r\n            return -1;\r\n        var id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\r\n        return id !== null && id !== void 0 ? id : -1;\r\n    };\r\n    Mirror.prototype.getNode = function (id) {\r\n        return this.idNodeMap.get(id) || null;\r\n    };\r\n    Mirror.prototype.getIds = function () {\r\n        return Array.from(this.idNodeMap.keys());\r\n    };\r\n    Mirror.prototype.getMeta = function (n) {\r\n        return this.nodeMetaMap.get(n) || null;\r\n    };\r\n    Mirror.prototype.removeNodeFromMap = function (n) {\r\n        var _this = this;\r\n        var id = this.getId(n);\r\n        this.idNodeMap[\"delete\"](id);\r\n        if (n.childNodes) {\r\n            n.childNodes.forEach(function (childNode) {\r\n                return _this.removeNodeFromMap(childNode);\r\n            });\r\n        }\r\n    };\r\n    Mirror.prototype.has = function (id) {\r\n        return this.idNodeMap.has(id);\r\n    };\r\n    Mirror.prototype.hasNode = function (node) {\r\n        return this.nodeMetaMap.has(node);\r\n    };\r\n    Mirror.prototype.add = function (n, meta) {\r\n        var id = meta.id;\r\n        this.idNodeMap.set(id, n);\r\n        this.nodeMetaMap.set(n, meta);\r\n    };\r\n    Mirror.prototype.replace = function (id, n) {\r\n        var oldNode = this.getNode(id);\r\n        if (oldNode) {\r\n            var meta = this.nodeMetaMap.get(oldNode);\r\n            if (meta)\r\n                this.nodeMetaMap.set(n, meta);\r\n        }\r\n        this.idNodeMap.set(id, n);\r\n    };\r\n    Mirror.prototype.reset = function () {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    };\r\n    return Mirror;\r\n}());\r\nfunction createMirror() {\r\n    return new Mirror();\r\n}\r\nfunction maskInputValue(_a) {\r\n    var maskInputOptions = _a.maskInputOptions, tagName = _a.tagName, type = _a.type, value = _a.value, maskInputFn = _a.maskInputFn;\r\n    var text = value || '';\r\n    if (maskInputOptions[tagName.toLowerCase()] ||\r\n        maskInputOptions[type]) {\r\n        if (maskInputFn) {\r\n            text = maskInputFn(text);\r\n        }\r\n        else {\r\n            text = '*'.repeat(text.length);\r\n        }\r\n    }\r\n    return text;\r\n}\r\nvar ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\r\nfunction is2DCanvasBlank(canvas) {\r\n    var ctx = canvas.getContext('2d');\r\n    if (!ctx)\r\n        return true;\r\n    var chunkSize = 50;\r\n    for (var x = 0; x < canvas.width; x += chunkSize) {\r\n        for (var y = 0; y < canvas.height; y += chunkSize) {\r\n            var getImageData = ctx.getImageData;\r\n            var originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData\r\n                ? getImageData[ORIGINAL_ATTRIBUTE_NAME]\r\n                : getImageData;\r\n            var pixelBuffer = new Uint32Array(originalGetImageData.call(ctx, x, y, Math.min(chunkSize, canvas.width - x), Math.min(chunkSize, canvas.height - y)).data.buffer);\r\n            if (pixelBuffer.some(function (pixel) { return pixel !== 0; }))\r\n                return false;\r\n        }\r\n    }\r\n    return true;\r\n}\n\nvar _id = 1;\r\nvar tagNameRegex = new RegExp('[^a-z0-9-_:]');\r\nvar IGNORED_NODE = -2;\r\nfunction genId() {\r\n    return _id++;\r\n}\r\nfunction getValidTagName(element) {\r\n    if (element instanceof HTMLFormElement) {\r\n        return 'form';\r\n    }\r\n    var processedTagName = element.tagName.toLowerCase().trim();\r\n    if (tagNameRegex.test(processedTagName)) {\r\n        return 'div';\r\n    }\r\n    return processedTagName;\r\n}\r\nfunction stringifyStyleSheet(sheet) {\r\n    return sheet.cssRules\r\n        ? Array.from(sheet.cssRules)\r\n            .map(function (rule) { return rule.cssText || ''; })\r\n            .join('')\r\n        : '';\r\n}\r\nfunction extractOrigin(url) {\r\n    var origin = '';\r\n    if (url.indexOf('//') > -1) {\r\n        origin = url.split('/').slice(0, 3).join('/');\r\n    }\r\n    else {\r\n        origin = url.split('/')[0];\r\n    }\r\n    origin = origin.split('?')[0];\r\n    return origin;\r\n}\r\nvar canvasService;\r\nvar canvasCtx;\r\nvar URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\r\nvar RELATIVE_PATH = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\\\|\\/\\/|#).*/;\r\nvar DATA_URI = /^(data:)([^,]*),(.*)/i;\r\nfunction absoluteToStylesheet(cssText, href) {\r\n    return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {\r\n        var filePath = path1 || path2 || path3;\r\n        var maybeQuote = quote1 || quote2 || '';\r\n        if (!filePath) {\r\n            return origin;\r\n        }\r\n        if (!RELATIVE_PATH.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (DATA_URI.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (filePath[0] === '/') {\r\n            return \"url(\".concat(maybeQuote).concat(extractOrigin(href) + filePath).concat(maybeQuote, \")\");\r\n        }\r\n        var stack = href.split('/');\r\n        var parts = filePath.split('/');\r\n        stack.pop();\r\n        for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\r\n            var part = parts_1[_i];\r\n            if (part === '.') {\r\n                continue;\r\n            }\r\n            else if (part === '..') {\r\n                stack.pop();\r\n            }\r\n            else {\r\n                stack.push(part);\r\n            }\r\n        }\r\n        return \"url(\".concat(maybeQuote).concat(stack.join('/')).concat(maybeQuote, \")\");\r\n    });\r\n}\r\nvar SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\r\nvar SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\r\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\r\n    if (attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var pos = 0;\r\n    function collectCharacters(regEx) {\r\n        var chars;\r\n        var match = regEx.exec(attributeValue.substring(pos));\r\n        if (match) {\r\n            chars = match[0];\r\n            pos += chars.length;\r\n            return chars;\r\n        }\r\n        return '';\r\n    }\r\n    var output = [];\r\n    while (true) {\r\n        collectCharacters(SRCSET_COMMAS_OR_SPACES);\r\n        if (pos >= attributeValue.length) {\r\n            break;\r\n        }\r\n        var url = collectCharacters(SRCSET_NOT_SPACES);\r\n        if (url.slice(-1) === ',') {\r\n            url = absoluteToDoc(doc, url.substring(0, url.length - 1));\r\n            output.push(url);\r\n        }\r\n        else {\r\n            var descriptorsStr = '';\r\n            url = absoluteToDoc(doc, url);\r\n            var inParens = false;\r\n            while (true) {\r\n                var c = attributeValue.charAt(pos);\r\n                if (c === '') {\r\n                    output.push((url + descriptorsStr).trim());\r\n                    break;\r\n                }\r\n                else if (!inParens) {\r\n                    if (c === ',') {\r\n                        pos += 1;\r\n                        output.push((url + descriptorsStr).trim());\r\n                        break;\r\n                    }\r\n                    else if (c === '(') {\r\n                        inParens = true;\r\n                    }\r\n                }\r\n                else {\r\n                    if (c === ')') {\r\n                        inParens = false;\r\n                    }\r\n                }\r\n                descriptorsStr += c;\r\n                pos += 1;\r\n            }\r\n        }\r\n    }\r\n    return output.join(', ');\r\n}\r\nfunction absoluteToDoc(doc, attributeValue) {\r\n    if (!attributeValue || attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var a = doc.createElement('a');\r\n    a.href = attributeValue;\r\n    return a.href;\r\n}\r\nfunction isSVGElement(el) {\r\n    return Boolean(el.tagName === 'svg' || el.ownerSVGElement);\r\n}\r\nfunction getHref() {\r\n    var a = document.createElement('a');\r\n    a.href = '';\r\n    return a.href;\r\n}\r\nfunction transformAttribute(doc, tagName, name, value) {\r\n    if (name === 'src' ||\r\n        (name === 'href' && value && !(tagName === 'use' && value[0] === '#'))) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'xlink:href' && value && value[0] !== '#') {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'background' &&\r\n        value &&\r\n        (tagName === 'table' || tagName === 'td' || tagName === 'th')) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'srcset' && value) {\r\n        return getAbsoluteSrcsetString(doc, value);\r\n    }\r\n    else if (name === 'style' && value) {\r\n        return absoluteToStylesheet(value, getHref());\r\n    }\r\n    else if (tagName === 'object' && name === 'data' && value) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else {\r\n        return value;\r\n    }\r\n}\r\nfunction _isBlockedElement(element, blockClass, blockSelector) {\r\n    if (typeof blockClass === 'string') {\r\n        if (element.classList.contains(blockClass)) {\r\n            return true;\r\n        }\r\n    }\r\n    else {\r\n        for (var eIndex = element.classList.length; eIndex--;) {\r\n            var className = element.classList[eIndex];\r\n            if (blockClass.test(className)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    if (blockSelector) {\r\n        return element.matches(blockSelector);\r\n    }\r\n    return false;\r\n}\r\nfunction classMatchesRegex(node, regex, checkAncestors) {\r\n    if (!node)\r\n        return false;\r\n    if (node.nodeType !== node.ELEMENT_NODE) {\r\n        if (!checkAncestors)\r\n            return false;\r\n        return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n    }\r\n    for (var eIndex = node.classList.length; eIndex--;) {\r\n        var className = node.classList[eIndex];\r\n        if (regex.test(className)) {\r\n            return true;\r\n        }\r\n    }\r\n    if (!checkAncestors)\r\n        return false;\r\n    return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n}\r\nfunction needMaskingText(node, maskTextClass, maskTextSelector) {\r\n    var el = node.nodeType === node.ELEMENT_NODE\r\n        ? node\r\n        : node.parentElement;\r\n    if (el === null)\r\n        return false;\r\n    if (typeof maskTextClass === 'string') {\r\n        if (el.classList.contains(maskTextClass))\r\n            return true;\r\n        if (el.closest(\".\".concat(maskTextClass)))\r\n            return true;\r\n    }\r\n    else {\r\n        if (classMatchesRegex(el, maskTextClass, true))\r\n            return true;\r\n    }\r\n    if (maskTextSelector) {\r\n        if (el.matches(maskTextSelector))\r\n            return true;\r\n        if (el.closest(maskTextSelector))\r\n            return true;\r\n    }\r\n    return false;\r\n}\r\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\r\n    var win = iframeEl.contentWindow;\r\n    if (!win) {\r\n        return;\r\n    }\r\n    var fired = false;\r\n    var readyState;\r\n    try {\r\n        readyState = win.document.readyState;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (readyState !== 'complete') {\r\n        var timer_1 = setTimeout(function () {\r\n            if (!fired) {\r\n                listener();\r\n                fired = true;\r\n            }\r\n        }, iframeLoadTimeout);\r\n        iframeEl.addEventListener('load', function () {\r\n            clearTimeout(timer_1);\r\n            fired = true;\r\n            listener();\r\n        });\r\n        return;\r\n    }\r\n    var blankUrl = 'about:blank';\r\n    if (win.location.href !== blankUrl ||\r\n        iframeEl.src === blankUrl ||\r\n        iframeEl.src === '') {\r\n        setTimeout(listener, 0);\r\n        return iframeEl.addEventListener('load', listener);\r\n    }\r\n    iframeEl.addEventListener('load', listener);\r\n}\r\nfunction onceStylesheetLoaded(link, listener, styleSheetLoadTimeout) {\r\n    var fired = false;\r\n    var styleSheetLoaded;\r\n    try {\r\n        styleSheetLoaded = link.sheet;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (styleSheetLoaded)\r\n        return;\r\n    var timer = setTimeout(function () {\r\n        if (!fired) {\r\n            listener();\r\n            fired = true;\r\n        }\r\n    }, styleSheetLoadTimeout);\r\n    link.addEventListener('load', function () {\r\n        clearTimeout(timer);\r\n        fired = true;\r\n        listener();\r\n    });\r\n}\r\nfunction serializeNode(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c;\r\n    var rootId = getRootId(doc, mirror);\r\n    switch (n.nodeType) {\r\n        case n.DOCUMENT_NODE:\r\n            if (n.compatMode !== 'CSS1Compat') {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: [],\r\n                    compatMode: n.compatMode\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: []\r\n                };\r\n            }\r\n        case n.DOCUMENT_TYPE_NODE:\r\n            return {\r\n                type: NodeType.DocumentType,\r\n                name: n.name,\r\n                publicId: n.publicId,\r\n                systemId: n.systemId,\r\n                rootId: rootId\r\n            };\r\n        case n.ELEMENT_NODE:\r\n            return serializeElementNode(n, {\r\n                doc: doc,\r\n                blockClass: blockClass,\r\n                blockSelector: blockSelector,\r\n                inlineStylesheet: inlineStylesheet,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn,\r\n                dataURLOptions: dataURLOptions,\r\n                inlineImages: inlineImages,\r\n                recordCanvas: recordCanvas,\r\n                keepIframeSrcFn: keepIframeSrcFn,\r\n                newlyAddedElement: newlyAddedElement,\r\n                rootId: rootId\r\n            });\r\n        case n.TEXT_NODE:\r\n            return serializeTextNode(n, {\r\n                maskTextClass: maskTextClass,\r\n                maskTextSelector: maskTextSelector,\r\n                maskTextFn: maskTextFn,\r\n                rootId: rootId\r\n            });\r\n        case n.CDATA_SECTION_NODE:\r\n            return {\r\n                type: NodeType.CDATA,\r\n                textContent: '',\r\n                rootId: rootId\r\n            };\r\n        case n.COMMENT_NODE:\r\n            return {\r\n                type: NodeType.Comment,\r\n                textContent: n.textContent || '',\r\n                rootId: rootId\r\n            };\r\n        default:\r\n            return false;\r\n    }\r\n}\r\nfunction getRootId(doc, mirror) {\r\n    if (!mirror.hasNode(doc))\r\n        return undefined;\r\n    var docId = mirror.getId(doc);\r\n    return docId === 1 ? undefined : docId;\r\n}\r\nfunction serializeTextNode(n, options) {\r\n    var _a;\r\n    var maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, maskTextFn = options.maskTextFn, rootId = options.rootId;\r\n    var parentTagName = n.parentNode && n.parentNode.tagName;\r\n    var textContent = n.textContent;\r\n    var isStyle = parentTagName === 'STYLE' ? true : undefined;\r\n    var isScript = parentTagName === 'SCRIPT' ? true : undefined;\r\n    if (isStyle && textContent) {\r\n        try {\r\n            if (n.nextSibling || n.previousSibling) {\r\n            }\r\n            else if ((_a = n.parentNode.sheet) === null || _a === void 0 ? void 0 : _a.cssRules) {\r\n                textContent = stringifyStyleSheet(n.parentNode.sheet);\r\n            }\r\n        }\r\n        catch (err) {\r\n            console.warn(\"Cannot get CSS styles from text's parentNode. Error: \".concat(err), n);\r\n        }\r\n        textContent = absoluteToStylesheet(textContent, getHref());\r\n    }\r\n    if (isScript) {\r\n        textContent = 'SCRIPT_PLACEHOLDER';\r\n    }\r\n    if (!isStyle &&\r\n        !isScript &&\r\n        textContent &&\r\n        needMaskingText(n, maskTextClass, maskTextSelector)) {\r\n        textContent = maskTextFn\r\n            ? maskTextFn(textContent)\r\n            : textContent.replace(/[\\S]/g, '*');\r\n    }\r\n    return {\r\n        type: NodeType.Text,\r\n        textContent: textContent || '',\r\n        isStyle: isStyle,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction serializeElementNode(n, options) {\r\n    var doc = options.doc, blockClass = options.blockClass, blockSelector = options.blockSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c, rootId = options.rootId;\r\n    var needBlock = _isBlockedElement(n, blockClass, blockSelector);\r\n    var tagName = getValidTagName(n);\r\n    var attributes = {};\r\n    var len = n.attributes.length;\r\n    for (var i = 0; i < len; i++) {\r\n        var attr = n.attributes[i];\r\n        attributes[attr.name] = transformAttribute(doc, tagName, attr.name, attr.value);\r\n    }\r\n    if (tagName === 'link' && inlineStylesheet) {\r\n        var stylesheet = Array.from(doc.styleSheets).find(function (s) {\r\n            return s.href === n.href;\r\n        });\r\n        var cssText = null;\r\n        if (stylesheet) {\r\n            cssText = getCssRulesString(stylesheet);\r\n        }\r\n        if (cssText) {\r\n            delete attributes.rel;\r\n            delete attributes.href;\r\n            attributes._cssText = absoluteToStylesheet(cssText, stylesheet.href);\r\n        }\r\n    }\r\n    if (tagName === 'style' &&\r\n        n.sheet &&\r\n        !(n.innerText || n.textContent || '').trim().length) {\r\n        var cssText = getCssRulesString(n.sheet);\r\n        if (cssText) {\r\n            attributes._cssText = absoluteToStylesheet(cssText, getHref());\r\n        }\r\n    }\r\n    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {\r\n        var value = n.value;\r\n        var checked = n.checked;\r\n        if (attributes.type !== 'radio' &&\r\n            attributes.type !== 'checkbox' &&\r\n            attributes.type !== 'submit' &&\r\n            attributes.type !== 'button' &&\r\n            value) {\r\n            attributes.value = maskInputValue({\r\n                type: attributes.type,\r\n                tagName: tagName,\r\n                value: value,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn\r\n            });\r\n        }\r\n        else if (checked) {\r\n            attributes.checked = checked;\r\n        }\r\n    }\r\n    if (tagName === 'option') {\r\n        if (n.selected && !maskInputOptions['select']) {\r\n            attributes.selected = true;\r\n        }\r\n        else {\r\n            delete attributes.selected;\r\n        }\r\n    }\r\n    if (tagName === 'canvas' && recordCanvas) {\r\n        if (n.__context === '2d') {\r\n            if (!is2DCanvasBlank(n)) {\r\n                attributes.rr_dataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n        }\r\n        else if (!('__context' in n)) {\r\n            var canvasDataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            var blankCanvas = document.createElement('canvas');\r\n            blankCanvas.width = n.width;\r\n            blankCanvas.height = n.height;\r\n            var blankCanvasDataURL = blankCanvas.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            if (canvasDataURL !== blankCanvasDataURL) {\r\n                attributes.rr_dataURL = canvasDataURL;\r\n            }\r\n        }\r\n    }\r\n    if (tagName === 'img' && inlineImages) {\r\n        if (!canvasService) {\r\n            canvasService = doc.createElement('canvas');\r\n            canvasCtx = canvasService.getContext('2d');\r\n        }\r\n        var image_1 = n;\r\n        var oldValue_1 = image_1.crossOrigin;\r\n        image_1.crossOrigin = 'anonymous';\r\n        var recordInlineImage = function () {\r\n            try {\r\n                canvasService.width = image_1.naturalWidth;\r\n                canvasService.height = image_1.naturalHeight;\r\n                canvasCtx.drawImage(image_1, 0, 0);\r\n                attributes.rr_dataURL = canvasService.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n            catch (err) {\r\n                console.warn(\"Cannot inline img src=\".concat(image_1.currentSrc, \"! Error: \").concat(err));\r\n            }\r\n            oldValue_1\r\n                ? (attributes.crossOrigin = oldValue_1)\r\n                : image_1.removeAttribute('crossorigin');\r\n        };\r\n        if (image_1.complete && image_1.naturalWidth !== 0)\r\n            recordInlineImage();\r\n        else\r\n            image_1.onload = recordInlineImage;\r\n    }\r\n    if (tagName === 'audio' || tagName === 'video') {\r\n        attributes.rr_mediaState = n.paused\r\n            ? 'paused'\r\n            : 'played';\r\n        attributes.rr_mediaCurrentTime = n.currentTime;\r\n    }\r\n    if (!newlyAddedElement) {\r\n        if (n.scrollLeft) {\r\n            attributes.rr_scrollLeft = n.scrollLeft;\r\n        }\r\n        if (n.scrollTop) {\r\n            attributes.rr_scrollTop = n.scrollTop;\r\n        }\r\n    }\r\n    if (needBlock) {\r\n        var _d = n.getBoundingClientRect(), width = _d.width, height = _d.height;\r\n        attributes = {\r\n            \"class\": attributes[\"class\"],\r\n            rr_width: \"\".concat(width, \"px\"),\r\n            rr_height: \"\".concat(height, \"px\")\r\n        };\r\n    }\r\n    if (tagName === 'iframe' && !keepIframeSrcFn(attributes.src)) {\r\n        if (!n.contentDocument) {\r\n            attributes.rr_src = attributes.src;\r\n        }\r\n        delete attributes.src;\r\n    }\r\n    return {\r\n        type: NodeType.Element,\r\n        tagName: tagName,\r\n        attributes: attributes,\r\n        childNodes: [],\r\n        isSVG: isSVGElement(n) || undefined,\r\n        needBlock: needBlock,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction lowerIfExists(maybeAttr) {\r\n    if (maybeAttr === undefined) {\r\n        return '';\r\n    }\r\n    else {\r\n        return maybeAttr.toLowerCase();\r\n    }\r\n}\r\nfunction slimDOMExcluded(sn, slimDOMOptions) {\r\n    if (slimDOMOptions.comment && sn.type === NodeType.Comment) {\r\n        return true;\r\n    }\r\n    else if (sn.type === NodeType.Element) {\r\n        if (slimDOMOptions.script &&\r\n            (sn.tagName === 'script' ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'preload' &&\r\n                    sn.attributes.as === 'script') ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'prefetch' &&\r\n                    typeof sn.attributes.href === 'string' &&\r\n                    sn.attributes.href.endsWith('.js')))) {\r\n            return true;\r\n        }\r\n        else if (slimDOMOptions.headFavicon &&\r\n            ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon') ||\r\n                (sn.tagName === 'meta' &&\r\n                    (lowerIfExists(sn.attributes.name).match(/^msapplication-tile(image|color)$/) ||\r\n                        lowerIfExists(sn.attributes.name) === 'application-name' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'apple-touch-icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'shortcut icon')))) {\r\n            return true;\r\n        }\r\n        else if (sn.tagName === 'meta') {\r\n            if (slimDOMOptions.headMetaDescKeywords &&\r\n                lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaSocial &&\r\n                (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) ||\r\n                    lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) ||\r\n                    lowerIfExists(sn.attributes.name) === 'pinterest')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaRobots &&\r\n                (lowerIfExists(sn.attributes.name) === 'robots' ||\r\n                    lowerIfExists(sn.attributes.name) === 'googlebot' ||\r\n                    lowerIfExists(sn.attributes.name) === 'bingbot')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaHttpEquiv &&\r\n                sn.attributes['http-equiv'] !== undefined) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaAuthorship &&\r\n                (lowerIfExists(sn.attributes.name) === 'author' ||\r\n                    lowerIfExists(sn.attributes.name) === 'generator' ||\r\n                    lowerIfExists(sn.attributes.name) === 'framework' ||\r\n                    lowerIfExists(sn.attributes.name) === 'publisher' ||\r\n                    lowerIfExists(sn.attributes.name) === 'progid' ||\r\n                    lowerIfExists(sn.attributes.property).match(/^article:/) ||\r\n                    lowerIfExists(sn.attributes.property).match(/^product:/))) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaVerification &&\r\n                (lowerIfExists(sn.attributes.name) === 'google-site-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'yandex-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'csrf-token' ||\r\n                    lowerIfExists(sn.attributes.name) === 'p:domain_verify' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verify-v1' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'shopify-checkout-api-token')) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction serializeNodeWithId(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.inlineStylesheet, inlineStylesheet = _b === void 0 ? true : _b, _c = options.maskInputOptions, maskInputOptions = _c === void 0 ? {} : _c, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, slimDOMOptions = options.slimDOMOptions, _d = options.dataURLOptions, dataURLOptions = _d === void 0 ? {} : _d, _e = options.inlineImages, inlineImages = _e === void 0 ? false : _e, _f = options.recordCanvas, recordCanvas = _f === void 0 ? false : _f, onSerialize = options.onSerialize, onIframeLoad = options.onIframeLoad, _g = options.iframeLoadTimeout, iframeLoadTimeout = _g === void 0 ? 5000 : _g, onStylesheetLoad = options.onStylesheetLoad, _h = options.stylesheetLoadTimeout, stylesheetLoadTimeout = _h === void 0 ? 5000 : _h, _j = options.keepIframeSrcFn, keepIframeSrcFn = _j === void 0 ? function () { return false; } : _j, _k = options.newlyAddedElement, newlyAddedElement = _k === void 0 ? false : _k;\r\n    var _l = options.preserveWhiteSpace, preserveWhiteSpace = _l === void 0 ? true : _l;\r\n    var _serializedNode = serializeNode(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: newlyAddedElement\r\n    });\r\n    if (!_serializedNode) {\r\n        console.warn(n, 'not serialized');\r\n        return null;\r\n    }\r\n    var id;\r\n    if (mirror.hasNode(n)) {\r\n        id = mirror.getId(n);\r\n    }\r\n    else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||\r\n        (!preserveWhiteSpace &&\r\n            _serializedNode.type === NodeType.Text &&\r\n            !_serializedNode.isStyle &&\r\n            !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, '').length)) {\r\n        id = IGNORED_NODE;\r\n    }\r\n    else {\r\n        id = genId();\r\n    }\r\n    var serializedNode = Object.assign(_serializedNode, { id: id });\r\n    mirror.add(n, serializedNode);\r\n    if (id === IGNORED_NODE) {\r\n        return null;\r\n    }\r\n    if (onSerialize) {\r\n        onSerialize(n);\r\n    }\r\n    var recordChild = !skipChild;\r\n    if (serializedNode.type === NodeType.Element) {\r\n        recordChild = recordChild && !serializedNode.needBlock;\r\n        delete serializedNode.needBlock;\r\n        var shadowRoot = n.shadowRoot;\r\n        if (shadowRoot && isNativeShadowDom(shadowRoot))\r\n            serializedNode.isShadowHost = true;\r\n    }\r\n    if ((serializedNode.type === NodeType.Document ||\r\n        serializedNode.type === NodeType.Element) &&\r\n        recordChild) {\r\n        if (slimDOMOptions.headWhitespace &&\r\n            serializedNode.type === NodeType.Element &&\r\n            serializedNode.tagName === 'head') {\r\n            preserveWhiteSpace = false;\r\n        }\r\n        var bypassOptions = {\r\n            doc: doc,\r\n            mirror: mirror,\r\n            blockClass: blockClass,\r\n            blockSelector: blockSelector,\r\n            maskTextClass: maskTextClass,\r\n            maskTextSelector: maskTextSelector,\r\n            skipChild: skipChild,\r\n            inlineStylesheet: inlineStylesheet,\r\n            maskInputOptions: maskInputOptions,\r\n            maskTextFn: maskTextFn,\r\n            maskInputFn: maskInputFn,\r\n            slimDOMOptions: slimDOMOptions,\r\n            dataURLOptions: dataURLOptions,\r\n            inlineImages: inlineImages,\r\n            recordCanvas: recordCanvas,\r\n            preserveWhiteSpace: preserveWhiteSpace,\r\n            onSerialize: onSerialize,\r\n            onIframeLoad: onIframeLoad,\r\n            iframeLoadTimeout: iframeLoadTimeout,\r\n            onStylesheetLoad: onStylesheetLoad,\r\n            stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n            keepIframeSrcFn: keepIframeSrcFn\r\n        };\r\n        for (var _i = 0, _m = Array.from(n.childNodes); _i < _m.length; _i++) {\r\n            var childN = _m[_i];\r\n            var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n            if (serializedChildNode) {\r\n                serializedNode.childNodes.push(serializedChildNode);\r\n            }\r\n        }\r\n        if (isElement(n) && n.shadowRoot) {\r\n            for (var _o = 0, _p = Array.from(n.shadowRoot.childNodes); _o < _p.length; _o++) {\r\n                var childN = _p[_o];\r\n                var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n                if (serializedChildNode) {\r\n                    isNativeShadowDom(n.shadowRoot) &&\r\n                        (serializedChildNode.isShadow = true);\r\n                    serializedNode.childNodes.push(serializedChildNode);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (n.parentNode &&\r\n        isShadowRoot(n.parentNode) &&\r\n        isNativeShadowDom(n.parentNode)) {\r\n        serializedNode.isShadow = true;\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'iframe') {\r\n        onceIframeLoaded(n, function () {\r\n            var iframeDoc = n.contentDocument;\r\n            if (iframeDoc && onIframeLoad) {\r\n                var serializedIframeNode = serializeNodeWithId(iframeDoc, {\r\n                    doc: iframeDoc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedIframeNode) {\r\n                    onIframeLoad(n, serializedIframeNode);\r\n                }\r\n            }\r\n        }, iframeLoadTimeout);\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'link' &&\r\n        serializedNode.attributes.rel === 'stylesheet') {\r\n        onceStylesheetLoaded(n, function () {\r\n            if (onStylesheetLoad) {\r\n                var serializedLinkNode = serializeNodeWithId(n, {\r\n                    doc: doc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedLinkNode) {\r\n                    onStylesheetLoad(n, serializedLinkNode);\r\n                }\r\n            }\r\n        }, stylesheetLoadTimeout);\r\n    }\r\n    return serializedNode;\r\n}\r\nfunction snapshot(n, options) {\r\n    var _a = options || {}, _b = _a.mirror, mirror = _b === void 0 ? new Mirror() : _b, _c = _a.blockClass, blockClass = _c === void 0 ? 'rr-block' : _c, _d = _a.blockSelector, blockSelector = _d === void 0 ? null : _d, _e = _a.maskTextClass, maskTextClass = _e === void 0 ? 'rr-mask' : _e, _f = _a.maskTextSelector, maskTextSelector = _f === void 0 ? null : _f, _g = _a.inlineStylesheet, inlineStylesheet = _g === void 0 ? true : _g, _h = _a.inlineImages, inlineImages = _h === void 0 ? false : _h, _j = _a.recordCanvas, recordCanvas = _j === void 0 ? false : _j, _k = _a.maskAllInputs, maskAllInputs = _k === void 0 ? false : _k, maskTextFn = _a.maskTextFn, maskInputFn = _a.maskInputFn, _l = _a.slimDOM, slimDOM = _l === void 0 ? false : _l, dataURLOptions = _a.dataURLOptions, preserveWhiteSpace = _a.preserveWhiteSpace, onSerialize = _a.onSerialize, onIframeLoad = _a.onIframeLoad, iframeLoadTimeout = _a.iframeLoadTimeout, onStylesheetLoad = _a.onStylesheetLoad, stylesheetLoadTimeout = _a.stylesheetLoadTimeout, _m = _a.keepIframeSrcFn, keepIframeSrcFn = _m === void 0 ? function () { return false; } : _m;\r\n    var maskInputOptions = maskAllInputs === true\r\n        ? {\r\n            color: true,\r\n            date: true,\r\n            'datetime-local': true,\r\n            email: true,\r\n            month: true,\r\n            number: true,\r\n            range: true,\r\n            search: true,\r\n            tel: true,\r\n            text: true,\r\n            time: true,\r\n            url: true,\r\n            week: true,\r\n            textarea: true,\r\n            select: true,\r\n            password: true\r\n        }\r\n        : maskAllInputs === false\r\n            ? {\r\n                password: true\r\n            }\r\n            : maskAllInputs;\r\n    var slimDOMOptions = slimDOM === true || slimDOM === 'all'\r\n        ?\r\n            {\r\n                script: true,\r\n                comment: true,\r\n                headFavicon: true,\r\n                headWhitespace: true,\r\n                headMetaDescKeywords: slimDOM === 'all',\r\n                headMetaSocial: true,\r\n                headMetaRobots: true,\r\n                headMetaHttpEquiv: true,\r\n                headMetaAuthorship: true,\r\n                headMetaVerification: true\r\n            }\r\n        : slimDOM === false\r\n            ? {}\r\n            : slimDOM;\r\n    return serializeNodeWithId(n, {\r\n        doc: n,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        skipChild: false,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        slimDOMOptions: slimDOMOptions,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        preserveWhiteSpace: preserveWhiteSpace,\r\n        onSerialize: onSerialize,\r\n        onIframeLoad: onIframeLoad,\r\n        iframeLoadTimeout: iframeLoadTimeout,\r\n        onStylesheetLoad: onStylesheetLoad,\r\n        stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: false\r\n    });\r\n}\r\nfunction visitSnapshot(node, onVisit) {\r\n    function walk(current) {\r\n        onVisit(current);\r\n        if (current.type === NodeType.Document ||\r\n            current.type === NodeType.Element) {\r\n            current.childNodes.forEach(walk);\r\n        }\r\n    }\r\n    walk(node);\r\n}\r\nfunction cleanupSnapshot() {\r\n    _id = 1;\r\n}\n\nvar commentre = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\r\nfunction parse(css, options) {\r\n    if (options === void 0) { options = {}; }\r\n    var lineno = 1;\r\n    var column = 1;\r\n    function updatePosition(str) {\r\n        var lines = str.match(/\\n/g);\r\n        if (lines) {\r\n            lineno += lines.length;\r\n        }\r\n        var i = str.lastIndexOf('\\n');\r\n        column = i === -1 ? column + str.length : str.length - i;\r\n    }\r\n    function position() {\r\n        var start = { line: lineno, column: column };\r\n        return function (node) {\r\n            node.position = new Position(start);\r\n            whitespace();\r\n            return node;\r\n        };\r\n    }\r\n    var Position = (function () {\r\n        function Position(start) {\r\n            this.start = start;\r\n            this.end = { line: lineno, column: column };\r\n            this.source = options.source;\r\n        }\r\n        return Position;\r\n    }());\r\n    Position.prototype.content = css;\r\n    var errorsList = [];\r\n    function error(msg) {\r\n        var err = new Error(\"\".concat(options.source || '', \":\").concat(lineno, \":\").concat(column, \": \").concat(msg));\r\n        err.reason = msg;\r\n        err.filename = options.source;\r\n        err.line = lineno;\r\n        err.column = column;\r\n        err.source = css;\r\n        if (options.silent) {\r\n            errorsList.push(err);\r\n        }\r\n        else {\r\n            throw err;\r\n        }\r\n    }\r\n    function stylesheet() {\r\n        var rulesList = rules();\r\n        return {\r\n            type: 'stylesheet',\r\n            stylesheet: {\r\n                source: options.source,\r\n                rules: rulesList,\r\n                parsingErrors: errorsList\r\n            }\r\n        };\r\n    }\r\n    function open() {\r\n        return match(/^{\\s*/);\r\n    }\r\n    function close() {\r\n        return match(/^}/);\r\n    }\r\n    function rules() {\r\n        var node;\r\n        var rules = [];\r\n        whitespace();\r\n        comments(rules);\r\n        while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\r\n            if (node !== false) {\r\n                rules.push(node);\r\n                comments(rules);\r\n            }\r\n        }\r\n        return rules;\r\n    }\r\n    function match(re) {\r\n        var m = re.exec(css);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var str = m[0];\r\n        updatePosition(str);\r\n        css = css.slice(str.length);\r\n        return m;\r\n    }\r\n    function whitespace() {\r\n        match(/^\\s*/);\r\n    }\r\n    function comments(rules) {\r\n        if (rules === void 0) { rules = []; }\r\n        var c;\r\n        while ((c = comment())) {\r\n            if (c !== false) {\r\n                rules.push(c);\r\n            }\r\n            c = comment();\r\n        }\r\n        return rules;\r\n    }\r\n    function comment() {\r\n        var pos = position();\r\n        if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\r\n            return;\r\n        }\r\n        var i = 2;\r\n        while ('' !== css.charAt(i) &&\r\n            ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {\r\n            ++i;\r\n        }\r\n        i += 2;\r\n        if ('' === css.charAt(i - 1)) {\r\n            return error('End of comment missing');\r\n        }\r\n        var str = css.slice(2, i - 2);\r\n        column += 2;\r\n        updatePosition(str);\r\n        css = css.slice(i);\r\n        column += 2;\r\n        return pos({\r\n            type: 'comment',\r\n            comment: str\r\n        });\r\n    }\r\n    function selector() {\r\n        var m = match(/^([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return trim(m[0])\r\n            .replace(/\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*\\/+/g, '')\r\n            .replace(/\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'/g, function (m) {\r\n            return m.replace(/,/g, '\\u200C');\r\n        })\r\n            .split(/\\s*(?![^(]*\\)),\\s*/)\r\n            .map(function (s) {\r\n            return s.replace(/\\u200C/g, ',');\r\n        });\r\n    }\r\n    function declaration() {\r\n        var pos = position();\r\n        var propMatch = match(/^(\\*?[-#\\/\\*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/);\r\n        if (!propMatch) {\r\n            return;\r\n        }\r\n        var prop = trim(propMatch[0]);\r\n        if (!match(/^:\\s*/)) {\r\n            return error(\"property missing ':'\");\r\n        }\r\n        var val = match(/^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^\\)]*?\\)|[^};])+)/);\r\n        var ret = pos({\r\n            type: 'declaration',\r\n            property: prop.replace(commentre, ''),\r\n            value: val ? trim(val[0]).replace(commentre, '') : ''\r\n        });\r\n        match(/^[;\\s]*/);\r\n        return ret;\r\n    }\r\n    function declarations() {\r\n        var decls = [];\r\n        if (!open()) {\r\n            return error(\"missing '{'\");\r\n        }\r\n        comments(decls);\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            if (decl !== false) {\r\n                decls.push(decl);\r\n                comments(decls);\r\n            }\r\n            decl = declaration();\r\n        }\r\n        if (!close()) {\r\n            return error(\"missing '}'\");\r\n        }\r\n        return decls;\r\n    }\r\n    function keyframe() {\r\n        var m;\r\n        var vals = [];\r\n        var pos = position();\r\n        while ((m = match(/^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/))) {\r\n            vals.push(m[1]);\r\n            match(/^,\\s*/);\r\n        }\r\n        if (!vals.length) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'keyframe',\r\n            values: vals,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    function atkeyframes() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?keyframes\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = m[1];\r\n        m = match(/^([-\\w]+)\\s*/);\r\n        if (!m) {\r\n            return error('@keyframes missing name');\r\n        }\r\n        var name = m[1];\r\n        if (!open()) {\r\n            return error(\"@keyframes missing '{'\");\r\n        }\r\n        var frame;\r\n        var frames = comments();\r\n        while ((frame = keyframe())) {\r\n            frames.push(frame);\r\n            frames = frames.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@keyframes missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'keyframes',\r\n            name: name,\r\n            vendor: vendor,\r\n            keyframes: frames\r\n        });\r\n    }\r\n    function atsupports() {\r\n        var pos = position();\r\n        var m = match(/^@supports *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var supports = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@supports missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@supports missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'supports',\r\n            supports: supports,\r\n            rules: style\r\n        });\r\n    }\r\n    function athost() {\r\n        var pos = position();\r\n        var m = match(/^@host\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@host missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@host missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'host',\r\n            rules: style\r\n        });\r\n    }\r\n    function atmedia() {\r\n        var pos = position();\r\n        var m = match(/^@media *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var media = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@media missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@media missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'media',\r\n            media: media,\r\n            rules: style\r\n        });\r\n    }\r\n    function atcustommedia() {\r\n        var pos = position();\r\n        var m = match(/^@custom-media\\s+(--[^\\s]+)\\s*([^{;]+);/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'custom-media',\r\n            name: trim(m[1]),\r\n            media: trim(m[2])\r\n        });\r\n    }\r\n    function atpage() {\r\n        var pos = position();\r\n        var m = match(/^@page */);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var sel = selector() || [];\r\n        if (!open()) {\r\n            return error(\"@page missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@page missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'page',\r\n            selectors: sel,\r\n            declarations: decls\r\n        });\r\n    }\r\n    function atdocument() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?document *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = trim(m[1]);\r\n        var doc = trim(m[2]);\r\n        if (!open()) {\r\n            return error(\"@document missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@document missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'document',\r\n            document: doc,\r\n            vendor: vendor,\r\n            rules: style\r\n        });\r\n    }\r\n    function atfontface() {\r\n        var pos = position();\r\n        var m = match(/^@font-face\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@font-face missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@font-face missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'font-face',\r\n            declarations: decls\r\n        });\r\n    }\r\n    var atimport = _compileAtrule('import');\r\n    var atcharset = _compileAtrule('charset');\r\n    var atnamespace = _compileAtrule('namespace');\r\n    function _compileAtrule(name) {\r\n        var re = new RegExp('^@' + name + '\\\\s*([^;]+);');\r\n        return function () {\r\n            var pos = position();\r\n            var m = match(re);\r\n            if (!m) {\r\n                return;\r\n            }\r\n            var ret = { type: name };\r\n            ret[name] = m[1].trim();\r\n            return pos(ret);\r\n        };\r\n    }\r\n    function atrule() {\r\n        if (css[0] !== '@') {\r\n            return;\r\n        }\r\n        return (atkeyframes() ||\r\n            atmedia() ||\r\n            atcustommedia() ||\r\n            atsupports() ||\r\n            atimport() ||\r\n            atcharset() ||\r\n            atnamespace() ||\r\n            atdocument() ||\r\n            atpage() ||\r\n            athost() ||\r\n            atfontface());\r\n    }\r\n    function rule() {\r\n        var pos = position();\r\n        var sel = selector();\r\n        if (!sel) {\r\n            return error('selector missing');\r\n        }\r\n        comments();\r\n        return pos({\r\n            type: 'rule',\r\n            selectors: sel,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    return addParent(stylesheet());\r\n}\r\nfunction trim(str) {\r\n    return str ? str.replace(/^\\s+|\\s+$/g, '') : '';\r\n}\r\nfunction addParent(obj, parent) {\r\n    var isNode = obj && typeof obj.type === 'string';\r\n    var childParent = isNode ? obj : parent;\r\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\r\n        var k = _a[_i];\r\n        var value = obj[k];\r\n        if (Array.isArray(value)) {\r\n            value.forEach(function (v) {\r\n                addParent(v, childParent);\r\n            });\r\n        }\r\n        else if (value && typeof value === 'object') {\r\n            addParent(value, childParent);\r\n        }\r\n    }\r\n    if (isNode) {\r\n        Object.defineProperty(obj, 'parent', {\r\n            configurable: true,\r\n            writable: true,\r\n            enumerable: false,\r\n            value: parent || null\r\n        });\r\n    }\r\n    return obj;\r\n}\n\nvar tagMap = {\r\n    script: 'noscript',\r\n    altglyph: 'altGlyph',\r\n    altglyphdef: 'altGlyphDef',\r\n    altglyphitem: 'altGlyphItem',\r\n    animatecolor: 'animateColor',\r\n    animatemotion: 'animateMotion',\r\n    animatetransform: 'animateTransform',\r\n    clippath: 'clipPath',\r\n    feblend: 'feBlend',\r\n    fecolormatrix: 'feColorMatrix',\r\n    fecomponenttransfer: 'feComponentTransfer',\r\n    fecomposite: 'feComposite',\r\n    feconvolvematrix: 'feConvolveMatrix',\r\n    fediffuselighting: 'feDiffuseLighting',\r\n    fedisplacementmap: 'feDisplacementMap',\r\n    fedistantlight: 'feDistantLight',\r\n    fedropshadow: 'feDropShadow',\r\n    feflood: 'feFlood',\r\n    fefunca: 'feFuncA',\r\n    fefuncb: 'feFuncB',\r\n    fefuncg: 'feFuncG',\r\n    fefuncr: 'feFuncR',\r\n    fegaussianblur: 'feGaussianBlur',\r\n    feimage: 'feImage',\r\n    femerge: 'feMerge',\r\n    femergenode: 'feMergeNode',\r\n    femorphology: 'feMorphology',\r\n    feoffset: 'feOffset',\r\n    fepointlight: 'fePointLight',\r\n    fespecularlighting: 'feSpecularLighting',\r\n    fespotlight: 'feSpotLight',\r\n    fetile: 'feTile',\r\n    feturbulence: 'feTurbulence',\r\n    foreignobject: 'foreignObject',\r\n    glyphref: 'glyphRef',\r\n    lineargradient: 'linearGradient',\r\n    radialgradient: 'radialGradient'\r\n};\r\nfunction getTagName(n) {\r\n    var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;\r\n    if (tagName === 'link' && n.attributes._cssText) {\r\n        tagName = 'style';\r\n    }\r\n    return tagName;\r\n}\r\nfunction escapeRegExp(str) {\r\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n}\r\nvar HOVER_SELECTOR = /([^\\\\]):hover/;\r\nvar HOVER_SELECTOR_GLOBAL = new RegExp(HOVER_SELECTOR.source, 'g');\r\nfunction addHoverClass(cssText, cache) {\r\n    var cachedStyle = cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.get(cssText);\r\n    if (cachedStyle)\r\n        return cachedStyle;\r\n    var ast = parse(cssText, {\r\n        silent: true\r\n    });\r\n    if (!ast.stylesheet) {\r\n        return cssText;\r\n    }\r\n    var selectors = [];\r\n    ast.stylesheet.rules.forEach(function (rule) {\r\n        if ('selectors' in rule) {\r\n            (rule.selectors || []).forEach(function (selector) {\r\n                if (HOVER_SELECTOR.test(selector)) {\r\n                    selectors.push(selector);\r\n                }\r\n            });\r\n        }\r\n    });\r\n    if (selectors.length === 0) {\r\n        return cssText;\r\n    }\r\n    var selectorMatcher = new RegExp(selectors\r\n        .filter(function (selector, index) { return selectors.indexOf(selector) === index; })\r\n        .sort(function (a, b) { return b.length - a.length; })\r\n        .map(function (selector) {\r\n        return escapeRegExp(selector);\r\n    })\r\n        .join('|'), 'g');\r\n    var result = cssText.replace(selectorMatcher, function (selector) {\r\n        var newSelector = selector.replace(HOVER_SELECTOR_GLOBAL, '$1.\\\\:hover');\r\n        return \"\".concat(selector, \", \").concat(newSelector);\r\n    });\r\n    cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.set(cssText, result);\r\n    return result;\r\n}\r\nfunction createCache() {\r\n    var stylesWithHoverClass = new Map();\r\n    return {\r\n        stylesWithHoverClass: stylesWithHoverClass\r\n    };\r\n}\r\nfunction buildNode(n, options) {\r\n    var doc = options.doc, hackCss = options.hackCss, cache = options.cache;\r\n    switch (n.type) {\r\n        case NodeType.Document:\r\n            return doc.implementation.createDocument(null, '', null);\r\n        case NodeType.DocumentType:\r\n            return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);\r\n        case NodeType.Element: {\r\n            var tagName = getTagName(n);\r\n            var node_1;\r\n            if (n.isSVG) {\r\n                node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);\r\n            }\r\n            else {\r\n                node_1 = doc.createElement(tagName);\r\n            }\r\n            var specialAttributes = {};\r\n            for (var name_1 in n.attributes) {\r\n                if (!Object.prototype.hasOwnProperty.call(n.attributes, name_1)) {\r\n                    continue;\r\n                }\r\n                var value = n.attributes[name_1];\r\n                if (tagName === 'option' &&\r\n                    name_1 === 'selected' &&\r\n                    value === false) {\r\n                    continue;\r\n                }\r\n                if (value === true)\r\n                    value = '';\r\n                if (name_1.startsWith('rr_')) {\r\n                    specialAttributes[name_1] = value;\r\n                    continue;\r\n                }\r\n                var isTextarea = tagName === 'textarea' && name_1 === 'value';\r\n                var isRemoteOrDynamicCss = tagName === 'style' && name_1 === '_cssText';\r\n                if (isRemoteOrDynamicCss && hackCss && typeof value === 'string') {\r\n                    value = addHoverClass(value, cache);\r\n                }\r\n                if ((isTextarea || isRemoteOrDynamicCss) && typeof value === 'string') {\r\n                    var child = doc.createTextNode(value);\r\n                    for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {\r\n                        var c = _a[_i];\r\n                        if (c.nodeType === node_1.TEXT_NODE) {\r\n                            node_1.removeChild(c);\r\n                        }\r\n                    }\r\n                    node_1.appendChild(child);\r\n                    continue;\r\n                }\r\n                try {\r\n                    if (n.isSVG && name_1 === 'xlink:href') {\r\n                        node_1.setAttributeNS('http://www.w3.org/1999/xlink', name_1, value.toString());\r\n                    }\r\n                    else if (name_1 === 'onload' ||\r\n                        name_1 === 'onclick' ||\r\n                        name_1.substring(0, 7) === 'onmouse') {\r\n                        node_1.setAttribute('_' + name_1, value.toString());\r\n                    }\r\n                    else if (tagName === 'meta' &&\r\n                        n.attributes['http-equiv'] === 'Content-Security-Policy' &&\r\n                        name_1 === 'content') {\r\n                        node_1.setAttribute('csp-content', value.toString());\r\n                        continue;\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'preload' &&\r\n                        n.attributes.as === 'script') {\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'prefetch' &&\r\n                        typeof n.attributes.href === 'string' &&\r\n                        n.attributes.href.endsWith('.js')) {\r\n                    }\r\n                    else if (tagName === 'img' &&\r\n                        n.attributes.srcset &&\r\n                        n.attributes.rr_dataURL) {\r\n                        node_1.setAttribute('rrweb-original-srcset', n.attributes.srcset);\r\n                    }\r\n                    else {\r\n                        node_1.setAttribute(name_1, value.toString());\r\n                    }\r\n                }\r\n                catch (error) {\r\n                }\r\n            }\r\n            var _loop_1 = function (name_2) {\r\n                var value = specialAttributes[name_2];\r\n                if (tagName === 'canvas' && name_2 === 'rr_dataURL') {\r\n                    var image_1 = document.createElement('img');\r\n                    image_1.onload = function () {\r\n                        var ctx = node_1.getContext('2d');\r\n                        if (ctx) {\r\n                            ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);\r\n                        }\r\n                    };\r\n                    image_1.src = value.toString();\r\n                    if (node_1.RRNodeType)\r\n                        node_1.rr_dataURL = value.toString();\r\n                }\r\n                else if (tagName === 'img' && name_2 === 'rr_dataURL') {\r\n                    var image = node_1;\r\n                    if (!image.currentSrc.startsWith('data:')) {\r\n                        image.setAttribute('rrweb-original-src', n.attributes.src);\r\n                        image.src = value.toString();\r\n                    }\r\n                }\r\n                if (name_2 === 'rr_width') {\r\n                    node_1.style.width = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_height') {\r\n                    node_1.style.height = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_mediaCurrentTime' &&\r\n                    typeof value === 'number') {\r\n                    node_1.currentTime = value;\r\n                }\r\n                else if (name_2 === 'rr_mediaState') {\r\n                    switch (value) {\r\n                        case 'played':\r\n                            node_1\r\n                                .play()[\"catch\"](function (e) { return console.warn('media playback error', e); });\r\n                            break;\r\n                        case 'paused':\r\n                            node_1.pause();\r\n                            break;\r\n                    }\r\n                }\r\n            };\r\n            for (var name_2 in specialAttributes) {\r\n                _loop_1(name_2);\r\n            }\r\n            if (n.isShadowHost) {\r\n                if (!node_1.shadowRoot) {\r\n                    node_1.attachShadow({ mode: 'open' });\r\n                }\r\n                else {\r\n                    while (node_1.shadowRoot.firstChild) {\r\n                        node_1.shadowRoot.removeChild(node_1.shadowRoot.firstChild);\r\n                    }\r\n                }\r\n            }\r\n            return node_1;\r\n        }\r\n        case NodeType.Text:\r\n            return doc.createTextNode(n.isStyle && hackCss\r\n                ? addHoverClass(n.textContent, cache)\r\n                : n.textContent);\r\n        case NodeType.CDATA:\r\n            return doc.createCDATASection(n.textContent);\r\n        case NodeType.Comment:\r\n            return doc.createComment(n.textContent);\r\n        default:\r\n            return null;\r\n    }\r\n}\r\nfunction buildNodeWithSN(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.hackCss, hackCss = _b === void 0 ? true : _b, afterAppend = options.afterAppend, cache = options.cache;\r\n    var node = buildNode(n, { doc: doc, hackCss: hackCss, cache: cache });\r\n    if (!node) {\r\n        return null;\r\n    }\r\n    if (n.rootId && mirror.getNode(n.rootId) !== doc) {\r\n        mirror.replace(n.rootId, doc);\r\n    }\r\n    if (n.type === NodeType.Document) {\r\n        doc.close();\r\n        doc.open();\r\n        if (n.compatMode === 'BackCompat' &&\r\n            n.childNodes &&\r\n            n.childNodes[0].type !== NodeType.DocumentType) {\r\n            if (n.childNodes[0].type === NodeType.Element &&\r\n                'xmlns' in n.childNodes[0].attributes &&\r\n                n.childNodes[0].attributes.xmlns === 'http://www.w3.org/1999/xhtml') {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">');\r\n            }\r\n            else {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">');\r\n            }\r\n        }\r\n        node = doc;\r\n    }\r\n    mirror.add(node, n);\r\n    if ((n.type === NodeType.Document || n.type === NodeType.Element) &&\r\n        !skipChild) {\r\n        for (var _i = 0, _c = n.childNodes; _i < _c.length; _i++) {\r\n            var childN = _c[_i];\r\n            var childNode = buildNodeWithSN(childN, {\r\n                doc: doc,\r\n                mirror: mirror,\r\n                skipChild: false,\r\n                hackCss: hackCss,\r\n                afterAppend: afterAppend,\r\n                cache: cache\r\n            });\r\n            if (!childNode) {\r\n                console.warn('Failed to rebuild', childN);\r\n                continue;\r\n            }\r\n            if (childN.isShadow && isElement(node) && node.shadowRoot) {\r\n                node.shadowRoot.appendChild(childNode);\r\n            }\r\n            else {\r\n                node.appendChild(childNode);\r\n            }\r\n            if (afterAppend) {\r\n                afterAppend(childNode, childN.id);\r\n            }\r\n        }\r\n    }\r\n    return node;\r\n}\r\nfunction visit(mirror, onVisit) {\r\n    function walk(node) {\r\n        onVisit(node);\r\n    }\r\n    for (var _i = 0, _a = mirror.getIds(); _i < _a.length; _i++) {\r\n        var id = _a[_i];\r\n        if (mirror.has(id)) {\r\n            walk(mirror.getNode(id));\r\n        }\r\n    }\r\n}\r\nfunction handleScroll(node, mirror) {\r\n    var n = mirror.getMeta(node);\r\n    if ((n === null || n === void 0 ? void 0 : n.type) !== NodeType.Element) {\r\n        return;\r\n    }\r\n    var el = node;\r\n    for (var name_3 in n.attributes) {\r\n        if (!(Object.prototype.hasOwnProperty.call(n.attributes, name_3) &&\r\n            name_3.startsWith('rr_'))) {\r\n            continue;\r\n        }\r\n        var value = n.attributes[name_3];\r\n        if (name_3 === 'rr_scrollLeft') {\r\n            el.scrollLeft = value;\r\n        }\r\n        if (name_3 === 'rr_scrollTop') {\r\n            el.scrollTop = value;\r\n        }\r\n    }\r\n}\r\nfunction rebuild(n, options) {\r\n    var doc = options.doc, onVisit = options.onVisit, _a = options.hackCss, hackCss = _a === void 0 ? true : _a, afterAppend = options.afterAppend, cache = options.cache, _b = options.mirror, mirror = _b === void 0 ? new Mirror() : _b;\r\n    var node = buildNodeWithSN(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        skipChild: false,\r\n        hackCss: hackCss,\r\n        afterAppend: afterAppend,\r\n        cache: cache\r\n    });\r\n    visit(mirror, function (visitedNode) {\r\n        if (onVisit) {\r\n            onVisit(visitedNode);\r\n        }\r\n        handleScroll(visitedNode, mirror);\r\n    });\r\n    return node;\r\n}\n\nexport { IGNORED_NODE, Mirror, NodeType, addHoverClass, buildNodeWithSN, classMatchesRegex, cleanupSnapshot, createCache, createMirror, genId, getCssRuleString, getCssRulesString, is2DCanvasBlank, isCSSImportRule, isElement, isNativeShadowDom, isShadowRoot, maskInputValue, needMaskingText, rebuild, serializeNodeWithId, snapshot, transformAttribute, visitSnapshot };\n", "import type {\n  throttleOptions,\n  listenerHandler,\n  hookR<PERSON>tter,\n  blockClass,\n  addedNodeMutation,\n  DocumentDimension,\n  IWindow,\n  DeprecatedMirror,\n  textMutation,\n} from '@rrweb/types';\nimport type { I<PERSON>ir<PERSON>r, Mirror } from 'rrweb-snapshot';\nimport { isShadowRoot, IGNORED_NODE, classMatchesRegex } from 'rrweb-snapshot';\nimport type { RRNode, RRIFrameElement } from 'rrdom';\n\nexport function on(\n  type: string,\n  fn: EventListenerOrEventListenerObject,\n  target: Document | IWindow = document,\n): listenerHandler {\n  const options = { capture: true, passive: true };\n  target.addEventListener(type, fn, options);\n  return () => target.removeEventListener(type, fn, options);\n}\n\n// https://github.com/rrweb-io/rrweb/pull/407\nconst DEPARTED_MIRROR_ACCESS_WARNING =\n  'Please stop import mirror directly. Instead of that,' +\n  '\\r\\n' +\n  'now you can use replayer.getMirror() to access the mirror instance of a replayer,' +\n  '\\r\\n' +\n  'or you can use record.mirror to access the mirror instance during recording.';\nexport let _mirror: DeprecatedMirror = {\n  map: {},\n  getId() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return -1;\n  },\n  getNode() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return null;\n  },\n  removeNodeFromMap() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n  has() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return false;\n  },\n  reset() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n};\nif (typeof window !== 'undefined' && window.Proxy && window.Reflect) {\n  _mirror = new Proxy(_mirror, {\n    get(target, prop, receiver) {\n      if (prop === 'map') {\n        console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n      }\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      return Reflect.get(target, prop, receiver);\n    },\n  });\n}\n\n// copy from underscore and modified\nexport function throttle<T>(\n  func: (arg: T) => void,\n  wait: number,\n  options: throttleOptions = {},\n) {\n  let timeout: ReturnType<typeof setTimeout> | null = null;\n  let previous = 0;\n  return function (...args: T[]) {\n    const now = Date.now();\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n    const remaining = wait - (now - previous);\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-this-alias\n    const context = this;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(() => {\n        previous = options.leading === false ? 0 : Date.now();\n        timeout = null;\n        func.apply(context, args);\n      }, remaining);\n    }\n  };\n}\n\nexport function hookSetter<T>(\n  target: T,\n  key: string | number | symbol,\n  d: PropertyDescriptor,\n  isRevoked?: boolean,\n  win = window,\n): hookResetter {\n  const original = win.Object.getOwnPropertyDescriptor(target, key);\n  win.Object.defineProperty(\n    target,\n    key,\n    isRevoked\n      ? d\n      : {\n          set(value) {\n            // put hooked setter into event loop to avoid of set latency\n            setTimeout(() => {\n              d.set!.call(this, value);\n            }, 0);\n            if (original && original.set) {\n              original.set.call(this, value);\n            }\n          },\n        },\n  );\n  return () => hookSetter(target, key, original || {}, true);\n}\n\n// copy from https://github.com/getsentry/sentry-javascript/blob/b2109071975af8bf0316d3b5b38f519bdaf5dc15/packages/utils/src/object.ts\nexport function patch(\n  source: { [key: string]: any },\n  name: string,\n  replacement: (...args: unknown[]) => unknown,\n): () => void {\n  try {\n    if (!(name in source)) {\n      return () => {\n        //\n      };\n    }\n\n    const original = source[name] as () => unknown;\n    const wrapped = replacement(original);\n\n    // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n    // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n    if (typeof wrapped === 'function') {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __rrweb_original__: {\n          enumerable: false,\n          value: original,\n        },\n      });\n    }\n\n    source[name] = wrapped;\n\n    return () => {\n      source[name] = original;\n    };\n  } catch {\n    return () => {\n      //\n    };\n    // This can throw if multiple fill happens on a global object like XMLHttpRequest\n    // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n  }\n}\n\nexport function getWindowHeight(): number {\n  return (\n    window.innerHeight ||\n    (document.documentElement && document.documentElement.clientHeight) ||\n    (document.body && document.body.clientHeight)\n  );\n}\n\nexport function getWindowWidth(): number {\n  return (\n    window.innerWidth ||\n    (document.documentElement && document.documentElement.clientWidth) ||\n    (document.body && document.body.clientWidth)\n  );\n}\n\n/**\n * Checks if the given element set to be blocked by rrweb\n * @param node - node to check\n * @param blockClass - class name to check\n * @param blockSelector - css selectors to check\n * @param checkAncestors - whether to search through parent nodes for the block class\n * @returns true/false if the node was blocked or not\n */\nexport function isBlocked(\n  node: Node | null,\n  blockClass: blockClass,\n  blockSelector: string | null,\n  checkAncestors: boolean,\n): boolean {\n  if (!node) {\n    return false;\n  }\n  const el: HTMLElement | null =\n    node.nodeType === node.ELEMENT_NODE\n      ? (node as HTMLElement)\n      : node.parentElement;\n  if (!el) return false;\n\n  if (typeof blockClass === 'string') {\n    if (el.classList.contains(blockClass)) return true;\n    if (checkAncestors && el.closest('.' + blockClass) !== null) return true;\n  } else {\n    if (classMatchesRegex(el, blockClass, checkAncestors)) return true;\n  }\n  if (blockSelector) {\n    if ((node as HTMLElement).matches(blockSelector)) return true;\n    if (checkAncestors && el.closest(blockSelector) !== null) return true;\n  }\n  return false;\n}\n\nexport function isSerialized(n: Node, mirror: Mirror): boolean {\n  return mirror.getId(n) !== -1;\n}\n\nexport function isIgnored(n: Node, mirror: Mirror): boolean {\n  // The main part of the slimDOM check happens in\n  // rrweb-snapshot::serializeNodeWithId\n  return mirror.getId(n) === IGNORED_NODE;\n}\n\nexport function isAncestorRemoved(target: Node, mirror: Mirror): boolean {\n  if (isShadowRoot(target)) {\n    return false;\n  }\n  const id = mirror.getId(target);\n  if (!mirror.has(id)) {\n    return true;\n  }\n  if (\n    target.parentNode &&\n    target.parentNode.nodeType === target.DOCUMENT_NODE\n  ) {\n    return false;\n  }\n  // if the root is not document, it means the node is not in the DOM tree anymore\n  if (!target.parentNode) {\n    return true;\n  }\n  return isAncestorRemoved(target.parentNode, mirror);\n}\n\nexport function isTouchEvent(\n  event: MouseEvent | TouchEvent,\n): event is TouchEvent {\n  return Boolean((event as TouchEvent).changedTouches);\n}\n\nexport function polyfill(win = window) {\n  if ('NodeList' in win && !win.NodeList.prototype.forEach) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    win.NodeList.prototype.forEach = (Array.prototype\n      .forEach as unknown) as NodeList['forEach'];\n  }\n\n  if ('DOMTokenList' in win && !win.DOMTokenList.prototype.forEach) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    win.DOMTokenList.prototype.forEach = (Array.prototype\n      .forEach as unknown) as DOMTokenList['forEach'];\n  }\n\n  // https://github.com/Financial-Times/polyfill-service/pull/183\n  if (!Node.prototype.contains) {\n    Node.prototype.contains = (...args: unknown[]) => {\n      let node = args[0] as Node | null;\n      if (!(0 in args)) {\n        throw new TypeError('1 argument is required');\n      }\n\n      do {\n        if (this === node) {\n          return true;\n        }\n      } while ((node = node && node.parentNode));\n\n      return false;\n    };\n  }\n}\n\ntype ResolveTree = {\n  value: addedNodeMutation;\n  children: ResolveTree[];\n  parent: ResolveTree | null;\n};\n\nexport function queueToResolveTrees(queue: addedNodeMutation[]): ResolveTree[] {\n  const queueNodeMap: Record<number, ResolveTree> = {};\n  const putIntoMap = (\n    m: addedNodeMutation,\n    parent: ResolveTree | null,\n  ): ResolveTree => {\n    const nodeInTree: ResolveTree = {\n      value: m,\n      parent,\n      children: [],\n    };\n    queueNodeMap[m.node.id] = nodeInTree;\n    return nodeInTree;\n  };\n\n  const queueNodeTrees: ResolveTree[] = [];\n  for (const mutation of queue) {\n    const { nextId, parentId } = mutation;\n    if (nextId && nextId in queueNodeMap) {\n      const nextInTree = queueNodeMap[nextId];\n      if (nextInTree.parent) {\n        const idx = nextInTree.parent.children.indexOf(nextInTree);\n        nextInTree.parent.children.splice(\n          idx,\n          0,\n          putIntoMap(mutation, nextInTree.parent),\n        );\n      } else {\n        const idx = queueNodeTrees.indexOf(nextInTree);\n        queueNodeTrees.splice(idx, 0, putIntoMap(mutation, null));\n      }\n      continue;\n    }\n    if (parentId in queueNodeMap) {\n      const parentInTree = queueNodeMap[parentId];\n      parentInTree.children.push(putIntoMap(mutation, parentInTree));\n      continue;\n    }\n    queueNodeTrees.push(putIntoMap(mutation, null));\n  }\n\n  return queueNodeTrees;\n}\n\nexport function iterateResolveTree(\n  tree: ResolveTree,\n  cb: (mutation: addedNodeMutation) => unknown,\n) {\n  cb(tree.value);\n  /**\n   * The resolve tree was designed to reflect the DOM layout,\n   * but we need append next sibling first, so we do a reverse\n   * loop here.\n   */\n  for (let i = tree.children.length - 1; i >= 0; i--) {\n    iterateResolveTree(tree.children[i], cb);\n  }\n}\n\nexport type AppendedIframe = {\n  mutationInQueue: addedNodeMutation;\n  builtNode: HTMLIFrameElement | RRIFrameElement;\n};\n\nexport function isSerializedIframe<TNode extends Node | RRNode>(\n  n: TNode,\n  mirror: IMirror<TNode>,\n): boolean {\n  return Boolean(n.nodeName === 'IFRAME' && mirror.getMeta(n));\n}\n\nexport function isSerializedStylesheet<TNode extends Node | RRNode>(\n  n: TNode,\n  mirror: IMirror<TNode>,\n): boolean {\n  return Boolean(\n    n.nodeName === 'LINK' &&\n      n.nodeType === n.ELEMENT_NODE &&\n      (n as HTMLElement).getAttribute &&\n      (n as HTMLElement).getAttribute('rel') === 'stylesheet' &&\n      mirror.getMeta(n),\n  );\n}\n\nexport function getBaseDimension(\n  node: Node,\n  rootIframe: Node,\n): DocumentDimension {\n  const frameElement = node.ownerDocument?.defaultView?.frameElement;\n  if (!frameElement || frameElement === rootIframe) {\n    return {\n      x: 0,\n      y: 0,\n      relativeScale: 1,\n      absoluteScale: 1,\n    };\n  }\n\n  const frameDimension = frameElement.getBoundingClientRect();\n  const frameBaseDimension = getBaseDimension(frameElement, rootIframe);\n  // the iframe element may have a scale transform\n  const relativeScale = frameDimension.height / frameElement.clientHeight;\n  return {\n    x:\n      frameDimension.x * frameBaseDimension.relativeScale +\n      frameBaseDimension.x,\n    y:\n      frameDimension.y * frameBaseDimension.relativeScale +\n      frameBaseDimension.y,\n    relativeScale,\n    absoluteScale: frameBaseDimension.absoluteScale * relativeScale,\n  };\n}\n\nexport function hasShadowRoot<T extends Node | RRNode>(\n  n: T,\n): n is T & { shadowRoot: ShadowRoot } {\n  return Boolean(((n as unknown) as Element)?.shadowRoot);\n}\n\nexport function getNestedRule(\n  rules: CSSRuleList,\n  position: number[],\n): CSSGroupingRule {\n  const rule = rules[position[0]] as CSSGroupingRule;\n  if (position.length === 1) {\n    return rule;\n  } else {\n    return getNestedRule(\n      (rule.cssRules[position[1]] as CSSGroupingRule).cssRules,\n      position.slice(2),\n    );\n  }\n}\n\nexport function getPositionsAndIndex(nestedIndex: number[]) {\n  const positions = [...nestedIndex];\n  const index = positions.pop();\n  return { positions, index };\n}\n\n/**\n * Returns the latest mutation in the queue for each node.\n * @param mutations - mutations The text mutations to filter.\n * @returns The filtered text mutations.\n */\nexport function uniqueTextMutations(mutations: textMutation[]): textMutation[] {\n  const idSet = new Set<number>();\n  const uniqueMutations: textMutation[] = [];\n\n  for (let i = mutations.length; i--; ) {\n    const mutation = mutations[i];\n    if (!idSet.has(mutation.id)) {\n      uniqueMutations.push(mutation);\n      idSet.add(mutation.id);\n    }\n  }\n\n  return uniqueMutations;\n}\n\nexport class StyleSheetMirror {\n  private id = 1;\n  private styleIDMap = new WeakMap<CSSStyleSheet, number>();\n  private idStyleMap = new Map<number, CSSStyleSheet>();\n\n  getId(stylesheet: CSSStyleSheet): number {\n    return this.styleIDMap.get(stylesheet) ?? -1;\n  }\n\n  has(stylesheet: CSSStyleSheet): boolean {\n    return this.styleIDMap.has(stylesheet);\n  }\n\n  /**\n   * @returns If the stylesheet is in the mirror, returns the id of the stylesheet. If not, return the new assigned id.\n   */\n  add(stylesheet: CSSStyleSheet, id?: number): number {\n    if (this.has(stylesheet)) return this.getId(stylesheet);\n    let newId: number;\n    if (id === undefined) {\n      newId = this.id++;\n    } else newId = id;\n    this.styleIDMap.set(stylesheet, newId);\n    this.idStyleMap.set(newId, stylesheet);\n    return newId;\n  }\n\n  getStyle(id: number): CSSStyleSheet | null {\n    return this.idStyleMap.get(id) || null;\n  }\n\n  reset(): void {\n    this.styleIDMap = new WeakMap();\n    this.idStyleMap = new Map();\n    this.id = 1;\n  }\n\n  generateId(): number {\n    return this.id++;\n  }\n}\n", "var EventType = /* @__PURE__ */ ((EventType2) => {\n  EventType2[EventType2[\"DomContentLoaded\"] = 0] = \"DomContentLoaded\";\n  EventType2[EventType2[\"Load\"] = 1] = \"Load\";\n  EventType2[EventType2[\"FullSnapshot\"] = 2] = \"FullSnapshot\";\n  EventType2[EventType2[\"IncrementalSnapshot\"] = 3] = \"IncrementalSnapshot\";\n  EventType2[EventType2[\"Meta\"] = 4] = \"Meta\";\n  EventType2[EventType2[\"Custom\"] = 5] = \"Custom\";\n  EventType2[EventType2[\"Plugin\"] = 6] = \"Plugin\";\n  return EventType2;\n})(EventType || {});\nvar IncrementalSource = /* @__PURE__ */ ((IncrementalSource2) => {\n  IncrementalSource2[IncrementalSource2[\"Mutation\"] = 0] = \"Mutation\";\n  IncrementalSource2[IncrementalSource2[\"MouseMove\"] = 1] = \"MouseMove\";\n  IncrementalSource2[IncrementalSource2[\"MouseInteraction\"] = 2] = \"MouseInteraction\";\n  IncrementalSource2[IncrementalSource2[\"Scroll\"] = 3] = \"Scroll\";\n  IncrementalSource2[IncrementalSource2[\"ViewportResize\"] = 4] = \"ViewportResize\";\n  IncrementalSource2[IncrementalSource2[\"Input\"] = 5] = \"Input\";\n  IncrementalSource2[IncrementalSource2[\"TouchMove\"] = 6] = \"TouchMove\";\n  IncrementalSource2[IncrementalSource2[\"MediaInteraction\"] = 7] = \"MediaInteraction\";\n  IncrementalSource2[IncrementalSource2[\"StyleSheetRule\"] = 8] = \"StyleSheetRule\";\n  IncrementalSource2[IncrementalSource2[\"CanvasMutation\"] = 9] = \"CanvasMutation\";\n  IncrementalSource2[IncrementalSource2[\"Font\"] = 10] = \"Font\";\n  IncrementalSource2[IncrementalSource2[\"Log\"] = 11] = \"Log\";\n  IncrementalSource2[IncrementalSource2[\"Drag\"] = 12] = \"Drag\";\n  IncrementalSource2[IncrementalSource2[\"StyleDeclaration\"] = 13] = \"StyleDeclaration\";\n  IncrementalSource2[IncrementalSource2[\"Selection\"] = 14] = \"Selection\";\n  IncrementalSource2[IncrementalSource2[\"AdoptedStyleSheet\"] = 15] = \"AdoptedStyleSheet\";\n  return IncrementalSource2;\n})(IncrementalSource || {});\nvar MouseInteractions = /* @__PURE__ */ ((MouseInteractions2) => {\n  MouseInteractions2[MouseInteractions2[\"MouseUp\"] = 0] = \"MouseUp\";\n  MouseInteractions2[MouseInteractions2[\"MouseDown\"] = 1] = \"MouseDown\";\n  MouseInteractions2[MouseInteractions2[\"Click\"] = 2] = \"Click\";\n  MouseInteractions2[MouseInteractions2[\"ContextMenu\"] = 3] = \"ContextMenu\";\n  MouseInteractions2[MouseInteractions2[\"DblClick\"] = 4] = \"DblClick\";\n  MouseInteractions2[MouseInteractions2[\"Focus\"] = 5] = \"Focus\";\n  MouseInteractions2[MouseInteractions2[\"Blur\"] = 6] = \"Blur\";\n  MouseInteractions2[MouseInteractions2[\"TouchStart\"] = 7] = \"TouchStart\";\n  MouseInteractions2[MouseInteractions2[\"TouchMove_Departed\"] = 8] = \"TouchMove_Departed\";\n  MouseInteractions2[MouseInteractions2[\"TouchEnd\"] = 9] = \"TouchEnd\";\n  MouseInteractions2[MouseInteractions2[\"TouchCancel\"] = 10] = \"TouchCancel\";\n  return MouseInteractions2;\n})(MouseInteractions || {});\nvar CanvasContext = /* @__PURE__ */ ((CanvasContext2) => {\n  CanvasContext2[CanvasContext2[\"2D\"] = 0] = \"2D\";\n  CanvasContext2[CanvasContext2[\"WebGL\"] = 1] = \"WebGL\";\n  CanvasContext2[CanvasContext2[\"WebGL2\"] = 2] = \"WebGL2\";\n  return CanvasContext2;\n})(CanvasContext || {});\nvar MediaInteractions = /* @__PURE__ */ ((MediaInteractions2) => {\n  MediaInteractions2[MediaInteractions2[\"Play\"] = 0] = \"Play\";\n  MediaInteractions2[MediaInteractions2[\"Pause\"] = 1] = \"Pause\";\n  MediaInteractions2[MediaInteractions2[\"Seeked\"] = 2] = \"Seeked\";\n  MediaInteractions2[MediaInteractions2[\"VolumeChange\"] = 3] = \"VolumeChange\";\n  MediaInteractions2[MediaInteractions2[\"RateChange\"] = 4] = \"RateChange\";\n  return MediaInteractions2;\n})(MediaInteractions || {});\nvar ReplayerEvents = /* @__PURE__ */ ((ReplayerEvents2) => {\n  ReplayerEvents2[\"Start\"] = \"start\";\n  ReplayerEvents2[\"Pause\"] = \"pause\";\n  ReplayerEvents2[\"Resume\"] = \"resume\";\n  ReplayerEvents2[\"Resize\"] = \"resize\";\n  ReplayerEvents2[\"Finish\"] = \"finish\";\n  ReplayerEvents2[\"FullsnapshotRebuilded\"] = \"fullsnapshot-rebuilded\";\n  ReplayerEvents2[\"LoadStylesheetStart\"] = \"load-stylesheet-start\";\n  ReplayerEvents2[\"LoadStylesheetEnd\"] = \"load-stylesheet-end\";\n  ReplayerEvents2[\"SkipStart\"] = \"skip-start\";\n  ReplayerEvents2[\"SkipEnd\"] = \"skip-end\";\n  ReplayerEvents2[\"MouseInteraction\"] = \"mouse-interaction\";\n  ReplayerEvents2[\"EventCast\"] = \"event-cast\";\n  ReplayerEvents2[\"CustomEvent\"] = \"custom-event\";\n  ReplayerEvents2[\"Flush\"] = \"flush\";\n  ReplayerEvents2[\"StateChange\"] = \"state-change\";\n  ReplayerEvents2[\"PlayBack\"] = \"play-back\";\n  ReplayerEvents2[\"Destroy\"] = \"destroy\";\n  return ReplayerEvents2;\n})(ReplayerEvents || {});\nexport {\n  CanvasContext,\n  EventType,\n  IncrementalSource,\n  MediaInteractions,\n  MouseInteractions,\n  ReplayerEvents\n};\n//# sourceMappingURL=types.js.map\n", "import {\n  serializeNodeWithId,\n  transformA<PERSON>ribute,\n  IGNORED_NODE,\n  isShadowRoot,\n  needMaskingText,\n  maskInputValue,\n  Mirror,\n  isNativeShadowDom,\n} from 'rrweb-snapshot';\nimport type { observerParam, MutationBufferParam } from '../types';\nimport type {\n  mutationR<PERSON>ord,\n  textCursor,\n  attributeCursor,\n  removedNodeMutation,\n  addedNodeMutation,\n  styleAttributeValue,\n  Optional,\n} from '@rrweb/types';\nimport {\n  isBlocked,\n  isAncestorRemoved,\n  isIgnored,\n  isSerialized,\n  hasShadowRoot,\n  isSerializedIframe,\n  isSerializedStylesheet,\n} from '../utils';\n\ntype DoubleLinkedListNode = {\n  previous: DoubleLinkedListNode | null;\n  next: DoubleLinkedListNode | null;\n  value: NodeInLinkedList;\n};\ntype NodeInLinkedList = Node & {\n  __ln: DoubleLinkedListNode;\n};\n\nfunction isNodeInLinkedList(n: Node | NodeInLinkedList): n is NodeInLinkedList {\n  return '__ln' in n;\n}\n\nclass DoubleLinkedList {\n  public length = 0;\n  public head: DoubleLinkedListNode | null = null;\n\n  public get(position: number) {\n    if (position >= this.length) {\n      throw new Error('Position outside of list range');\n    }\n\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next || null;\n    }\n    return current;\n  }\n\n  public addNode(n: Node) {\n    const node: DoubleLinkedListNode = {\n      value: n as NodeInLinkedList,\n      previous: null,\n      next: null,\n    };\n    (n as NodeInLinkedList).__ln = node;\n    if (n.previousSibling && isNodeInLinkedList(n.previousSibling)) {\n      const current = n.previousSibling.__ln.next;\n      node.next = current;\n      node.previous = n.previousSibling.__ln;\n      n.previousSibling.__ln.next = node;\n      if (current) {\n        current.previous = node;\n      }\n    } else if (\n      n.nextSibling &&\n      isNodeInLinkedList(n.nextSibling) &&\n      n.nextSibling.__ln.previous\n    ) {\n      const current = n.nextSibling.__ln.previous;\n      node.previous = current;\n      node.next = n.nextSibling.__ln;\n      n.nextSibling.__ln.previous = node;\n      if (current) {\n        current.next = node;\n      }\n    } else {\n      if (this.head) {\n        this.head.previous = node;\n      }\n      node.next = this.head;\n      this.head = node;\n    }\n    this.length++;\n  }\n\n  public removeNode(n: NodeInLinkedList) {\n    const current = n.__ln;\n    if (!this.head) {\n      return;\n    }\n\n    if (!current.previous) {\n      this.head = current.next;\n      if (this.head) {\n        this.head.previous = null;\n      }\n    } else {\n      current.previous.next = current.next;\n      if (current.next) {\n        current.next.previous = current.previous;\n      }\n    }\n    if (n.__ln) {\n      delete (n as Optional<NodeInLinkedList, '__ln'>).__ln;\n    }\n    this.length--;\n  }\n}\n\nconst moveKey = (id: number, parentId: number) => `${id}@${parentId}`;\n\n/**\n * controls behaviour of a MutationObserver\n */\nexport default class MutationBuffer {\n  private frozen = false;\n  private locked = false;\n\n  private texts: textCursor[] = [];\n  private attributes: attributeCursor[] = [];\n  private removes: removedNodeMutation[] = [];\n  private mapRemoves: Node[] = [];\n\n  private movedMap: Record<string, true> = {};\n\n  /**\n   * the browser MutationObserver emits multiple mutations after\n   * a delay for performance reasons, making tracing added nodes hard\n   * in our `processMutations` callback function.\n   * For example, if we append an element el_1 into body, and then append\n   * another element el_2 into el_1, these two mutations may be passed to the\n   * callback function together when the two operations were done.\n   * Generally we need to trace child nodes of newly added nodes, but in this\n   * case if we count el_2 as el_1's child node in the first mutation record,\n   * then we will count el_2 again in the second mutation record which was\n   * duplicated.\n   * To avoid of duplicate counting added nodes, we use a Set to store\n   * added nodes and its child nodes during iterate mutation records. Then\n   * collect added nodes from the Set which have no duplicate copy. But\n   * this also causes newly added nodes will not be serialized with id ASAP,\n   * which means all the id related calculation should be lazy too.\n   */\n  private addedSet = new Set<Node>();\n  private movedSet = new Set<Node>();\n  private droppedSet = new Set<Node>();\n\n  private mutationCb: observerParam['mutationCb'];\n  private blockClass: observerParam['blockClass'];\n  private blockSelector: observerParam['blockSelector'];\n  private maskTextClass: observerParam['maskTextClass'];\n  private maskTextSelector: observerParam['maskTextSelector'];\n  private inlineStylesheet: observerParam['inlineStylesheet'];\n  private maskInputOptions: observerParam['maskInputOptions'];\n  private maskTextFn: observerParam['maskTextFn'];\n  private maskInputFn: observerParam['maskInputFn'];\n  private keepIframeSrcFn: observerParam['keepIframeSrcFn'];\n  private recordCanvas: observerParam['recordCanvas'];\n  private inlineImages: observerParam['inlineImages'];\n  private slimDOMOptions: observerParam['slimDOMOptions'];\n  private dataURLOptions: observerParam['dataURLOptions'];\n  private doc: observerParam['doc'];\n  private mirror: observerParam['mirror'];\n  private iframeManager: observerParam['iframeManager'];\n  private stylesheetManager: observerParam['stylesheetManager'];\n  private shadowDomManager: observerParam['shadowDomManager'];\n  private canvasManager: observerParam['canvasManager'];\n\n  public init(options: MutationBufferParam) {\n    ([\n      'mutationCb',\n      'blockClass',\n      'blockSelector',\n      'maskTextClass',\n      'maskTextSelector',\n      'inlineStylesheet',\n      'maskInputOptions',\n      'maskTextFn',\n      'maskInputFn',\n      'keepIframeSrcFn',\n      'recordCanvas',\n      'inlineImages',\n      'slimDOMOptions',\n      'dataURLOptions',\n      'doc',\n      'mirror',\n      'iframeManager',\n      'stylesheetManager',\n      'shadowDomManager',\n      'canvasManager',\n    ] as const).forEach((key) => {\n      // just a type trick, the runtime result is correct\n      this[key] = options[key] as never;\n    });\n  }\n\n  public freeze() {\n    this.frozen = true;\n    this.canvasManager.freeze();\n  }\n\n  public unfreeze() {\n    this.frozen = false;\n    this.canvasManager.unfreeze();\n    this.emit();\n  }\n\n  public isFrozen() {\n    return this.frozen;\n  }\n\n  public lock() {\n    this.locked = true;\n    this.canvasManager.lock();\n  }\n\n  public unlock() {\n    this.locked = false;\n    this.canvasManager.unlock();\n    this.emit();\n  }\n\n  public reset() {\n    this.shadowDomManager.reset();\n    this.canvasManager.reset();\n  }\n\n  public processMutations = (mutations: mutationRecord[]) => {\n    mutations.forEach(this.processMutation); // adds mutations to the buffer\n    this.emit(); // clears buffer if not locked/frozen\n  };\n\n  public emit = () => {\n    if (this.frozen || this.locked) {\n      return;\n    }\n\n    // delay any modification of the mirror until this function\n    // so that the mirror for takeFullSnapshot doesn't get mutated while it's event is being processed\n\n    const adds: addedNodeMutation[] = [];\n\n    /**\n     * Sometimes child node may be pushed before its newly added\n     * parent, so we init a queue to store these nodes.\n     */\n    const addList = new DoubleLinkedList();\n    const getNextId = (n: Node): number | null => {\n      let ns: Node | null = n;\n      let nextId: number | null = IGNORED_NODE; // slimDOM: ignored\n      while (nextId === IGNORED_NODE) {\n        ns = ns && ns.nextSibling;\n        nextId = ns && this.mirror.getId(ns);\n      }\n      return nextId;\n    };\n    const pushAdd = (n: Node) => {\n      let shadowHost: Element | null = null;\n      if (\n        n.getRootNode?.()?.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n        (n.getRootNode() as ShadowRoot).host\n      )\n        shadowHost = (n.getRootNode() as ShadowRoot).host;\n      // If n is in a nested shadow dom.\n      let rootShadowHost = shadowHost;\n      while (\n        rootShadowHost?.getRootNode?.()?.nodeType ===\n          Node.DOCUMENT_FRAGMENT_NODE &&\n        (rootShadowHost.getRootNode() as ShadowRoot).host\n      )\n        rootShadowHost = (rootShadowHost.getRootNode() as ShadowRoot).host;\n      // ensure contains is passed a Node, or it will throw an error\n      const notInDoc =\n        !this.doc.contains(n) &&\n        (!rootShadowHost || !this.doc.contains(rootShadowHost));\n      if (!n.parentNode || notInDoc) {\n        return;\n      }\n      const parentId = isShadowRoot(n.parentNode)\n        ? this.mirror.getId(shadowHost)\n        : this.mirror.getId(n.parentNode);\n      const nextId = getNextId(n);\n      if (parentId === -1 || nextId === -1) {\n        return addList.addNode(n);\n      }\n      const sn = serializeNodeWithId(n, {\n        doc: this.doc,\n        mirror: this.mirror,\n        blockClass: this.blockClass,\n        blockSelector: this.blockSelector,\n        maskTextClass: this.maskTextClass,\n        maskTextSelector: this.maskTextSelector,\n        skipChild: true,\n        newlyAddedElement: true,\n        inlineStylesheet: this.inlineStylesheet,\n        maskInputOptions: this.maskInputOptions,\n        maskTextFn: this.maskTextFn,\n        maskInputFn: this.maskInputFn,\n        slimDOMOptions: this.slimDOMOptions,\n        dataURLOptions: this.dataURLOptions,\n        recordCanvas: this.recordCanvas,\n        inlineImages: this.inlineImages,\n        onSerialize: (currentN) => {\n          if (isSerializedIframe(currentN, this.mirror)) {\n            this.iframeManager.addIframe(currentN as HTMLIFrameElement);\n          }\n          if (isSerializedStylesheet(currentN, this.mirror)) {\n            this.stylesheetManager.trackLinkElement(\n              currentN as HTMLLinkElement,\n            );\n          }\n          if (hasShadowRoot(n)) {\n            this.shadowDomManager.addShadowRoot(n.shadowRoot, this.doc);\n          }\n        },\n        onIframeLoad: (iframe, childSn) => {\n          this.iframeManager.attachIframe(iframe, childSn);\n          this.shadowDomManager.observeAttachShadow(iframe);\n        },\n        onStylesheetLoad: (link, childSn) => {\n          this.stylesheetManager.attachLinkElement(link, childSn);\n        },\n      });\n      if (sn) {\n        adds.push({\n          parentId,\n          nextId,\n          node: sn,\n        });\n      }\n    };\n\n    while (this.mapRemoves.length) {\n      this.mirror.removeNodeFromMap(this.mapRemoves.shift()!);\n    }\n\n    for (const n of Array.from(this.movedSet.values())) {\n      if (\n        isParentRemoved(this.removes, n, this.mirror) &&\n        !this.movedSet.has(n.parentNode!)\n      ) {\n        continue;\n      }\n      pushAdd(n);\n    }\n\n    for (const n of Array.from(this.addedSet.values())) {\n      if (\n        !isAncestorInSet(this.droppedSet, n) &&\n        !isParentRemoved(this.removes, n, this.mirror)\n      ) {\n        pushAdd(n);\n      } else if (isAncestorInSet(this.movedSet, n)) {\n        pushAdd(n);\n      } else {\n        this.droppedSet.add(n);\n      }\n    }\n\n    let candidate: DoubleLinkedListNode | null = null;\n    while (addList.length) {\n      let node: DoubleLinkedListNode | null = null;\n      if (candidate) {\n        const parentId = this.mirror.getId(candidate.value.parentNode);\n        const nextId = getNextId(candidate.value);\n        if (parentId !== -1 && nextId !== -1) {\n          node = candidate;\n        }\n      }\n      if (!node) {\n        for (let index = addList.length - 1; index >= 0; index--) {\n          const _node = addList.get(index);\n          // ensure _node is defined before attempting to find value\n          if (_node) {\n            const parentId = this.mirror.getId(_node.value.parentNode);\n            const nextId = getNextId(_node.value);\n\n            if (nextId === -1) continue;\n            // nextId !== -1 && parentId !== -1\n            else if (parentId !== -1) {\n              node = _node;\n              break;\n            }\n            // nextId !== -1 && parentId === -1 This branch can happen if the node is the child of shadow root\n            else {\n              const unhandledNode = _node.value;\n              // If the node is the direct child of a shadow root, we treat the shadow host as its parent node.\n              if (\n                unhandledNode.parentNode &&\n                unhandledNode.parentNode.nodeType ===\n                  Node.DOCUMENT_FRAGMENT_NODE\n              ) {\n                const shadowHost = (unhandledNode.parentNode as ShadowRoot)\n                  .host;\n                const parentId = this.mirror.getId(shadowHost);\n                if (parentId !== -1) {\n                  node = _node;\n                  break;\n                }\n              }\n            }\n          }\n        }\n      }\n      if (!node) {\n        /**\n         * If all nodes in queue could not find a serialized parent,\n         * it may be a bug or corner case. We need to escape the\n         * dead while loop at once.\n         */\n        while (addList.head) {\n          addList.removeNode(addList.head.value);\n        }\n        break;\n      }\n      candidate = node.previous;\n      addList.removeNode(node.value);\n      pushAdd(node.value);\n    }\n\n    const payload = {\n      texts: this.texts\n        .map((text) => ({\n          id: this.mirror.getId(text.node),\n          value: text.value,\n        }))\n        // text mutation's id was not in the mirror map means the target node has been removed\n        .filter((text) => this.mirror.has(text.id)),\n      attributes: this.attributes\n        .map((attribute) => ({\n          id: this.mirror.getId(attribute.node),\n          attributes: attribute.attributes,\n        }))\n        // attribute mutation's id was not in the mirror map means the target node has been removed\n        .filter((attribute) => this.mirror.has(attribute.id)),\n      removes: this.removes,\n      adds,\n    };\n    // payload may be empty if the mutations happened in some blocked elements\n    if (\n      !payload.texts.length &&\n      !payload.attributes.length &&\n      !payload.removes.length &&\n      !payload.adds.length\n    ) {\n      return;\n    }\n\n    // reset\n    this.texts = [];\n    this.attributes = [];\n    this.removes = [];\n    this.addedSet = new Set<Node>();\n    this.movedSet = new Set<Node>();\n    this.droppedSet = new Set<Node>();\n    this.movedMap = {};\n\n    this.mutationCb(payload);\n  };\n\n  private processMutation = (m: mutationRecord) => {\n    if (isIgnored(m.target, this.mirror)) {\n      return;\n    }\n    switch (m.type) {\n      case 'characterData': {\n        const value = m.target.textContent;\n        if (\n          !isBlocked(m.target, this.blockClass, this.blockSelector, false) &&\n          value !== m.oldValue\n        ) {\n          this.texts.push({\n            value:\n              needMaskingText(\n                m.target,\n                this.maskTextClass,\n                this.maskTextSelector,\n              ) && value\n                ? this.maskTextFn\n                  ? this.maskTextFn(value)\n                  : value.replace(/[\\S]/g, '*')\n                : value,\n            node: m.target,\n          });\n        }\n        break;\n      }\n      case 'attributes': {\n        const target = m.target as HTMLElement;\n        let value = (m.target as HTMLElement).getAttribute(m.attributeName!);\n        if (m.attributeName === 'value') {\n          value = maskInputValue({\n            maskInputOptions: this.maskInputOptions,\n            tagName: (m.target as HTMLElement).tagName,\n            type: (m.target as HTMLElement).getAttribute('type'),\n            value,\n            maskInputFn: this.maskInputFn,\n          });\n        }\n        if (\n          isBlocked(m.target, this.blockClass, this.blockSelector, false) ||\n          value === m.oldValue\n        ) {\n          return;\n        }\n\n        let item: attributeCursor | undefined = this.attributes.find(\n          (a) => a.node === m.target,\n        );\n        if (\n          target.tagName === 'IFRAME' &&\n          m.attributeName === 'src' &&\n          !this.keepIframeSrcFn(value as string)\n        ) {\n          if (!(target as HTMLIFrameElement).contentDocument) {\n            // we can't record it directly as we can't see into it\n            // preserve the src attribute so a decision can be taken at replay time\n            m.attributeName = 'rr_src';\n          } else {\n            return;\n          }\n        }\n        if (!item) {\n          item = {\n            node: m.target,\n            attributes: {},\n          };\n          this.attributes.push(item);\n        }\n        if (m.attributeName === 'style') {\n          const old = this.doc.createElement('span');\n          if (m.oldValue) {\n            old.setAttribute('style', m.oldValue);\n          }\n          if (\n            item.attributes.style === undefined ||\n            item.attributes.style === null\n          ) {\n            item.attributes.style = {};\n          }\n          const styleObj = item.attributes.style as styleAttributeValue;\n          for (const pname of Array.from(target.style)) {\n            const newValue = target.style.getPropertyValue(pname);\n            const newPriority = target.style.getPropertyPriority(pname);\n            if (\n              newValue !== old.style.getPropertyValue(pname) ||\n              newPriority !== old.style.getPropertyPriority(pname)\n            ) {\n              if (newPriority === '') {\n                styleObj[pname] = newValue;\n              } else {\n                styleObj[pname] = [newValue, newPriority];\n              }\n            }\n          }\n          for (const pname of Array.from(old.style)) {\n            if (target.style.getPropertyValue(pname) === '') {\n              // \"if not set, returns the empty string\"\n              styleObj[pname] = false; // delete\n            }\n          }\n        } else {\n          // overwrite attribute if the mutations was triggered in same time\n          item.attributes[m.attributeName!] = transformAttribute(\n            this.doc,\n            target.tagName,\n            m.attributeName!,\n            value!,\n          );\n        }\n        break;\n      }\n      case 'childList': {\n        /**\n         * Parent is blocked, ignore all child mutations\n         */\n        if (isBlocked(m.target, this.blockClass, this.blockSelector, true))\n          return;\n\n        m.addedNodes.forEach((n) => this.genAdds(n, m.target));\n        m.removedNodes.forEach((n) => {\n          const nodeId = this.mirror.getId(n);\n          const parentId = isShadowRoot(m.target)\n            ? this.mirror.getId(m.target.host)\n            : this.mirror.getId(m.target);\n          if (\n            isBlocked(m.target, this.blockClass, this.blockSelector, false) ||\n            isIgnored(n, this.mirror) ||\n            !isSerialized(n, this.mirror)\n          ) {\n            return;\n          }\n          // removed node has not been serialized yet, just remove it from the Set\n          if (this.addedSet.has(n)) {\n            deepDelete(this.addedSet, n);\n            this.droppedSet.add(n);\n          } else if (this.addedSet.has(m.target) && nodeId === -1) {\n            /**\n             * If target was newly added and removed child node was\n             * not serialized, it means the child node has been removed\n             * before callback fired, so we can ignore it because\n             * newly added node will be serialized without child nodes.\n             * TODO: verify this\n             */\n          } else if (isAncestorRemoved(m.target, this.mirror)) {\n            /**\n             * If parent id was not in the mirror map any more, it\n             * means the parent node has already been removed. So\n             * the node is also removed which we do not need to track\n             * and replay.\n             */\n          } else if (\n            this.movedSet.has(n) &&\n            this.movedMap[moveKey(nodeId, parentId)]\n          ) {\n            deepDelete(this.movedSet, n);\n          } else {\n            this.removes.push({\n              parentId,\n              id: nodeId,\n              isShadow:\n                isShadowRoot(m.target) && isNativeShadowDom(m.target)\n                  ? true\n                  : undefined,\n            });\n          }\n          this.mapRemoves.push(n);\n        });\n        break;\n      }\n      default:\n        break;\n    }\n  };\n\n  /**\n   * Make sure you check if `n`'s parent is blocked before calling this function\n   * */\n  private genAdds = (n: Node, target?: Node) => {\n    if (this.mirror.hasNode(n)) {\n      if (isIgnored(n, this.mirror)) {\n        return;\n      }\n      this.movedSet.add(n);\n      let targetId: number | null = null;\n      if (target && this.mirror.hasNode(target)) {\n        targetId = this.mirror.getId(target);\n      }\n      if (targetId && targetId !== -1) {\n        this.movedMap[moveKey(this.mirror.getId(n), targetId)] = true;\n      }\n    } else {\n      this.addedSet.add(n);\n      this.droppedSet.delete(n);\n    }\n\n    // if this node is blocked `serializeNode` will turn it into a placeholder element\n    // but we have to remove it's children otherwise they will be added as placeholders too\n    if (!isBlocked(n, this.blockClass, this.blockSelector, false))\n      n.childNodes.forEach((childN) => this.genAdds(childN));\n  };\n}\n\n/**\n * Some utils to handle the mutation observer DOM records.\n * It should be more clear to extend the native data structure\n * like Set and Map, but currently Typescript does not support\n * that.\n */\nfunction deepDelete(addsSet: Set<Node>, n: Node) {\n  addsSet.delete(n);\n  n.childNodes.forEach((childN) => deepDelete(addsSet, childN));\n}\n\nfunction isParentRemoved(\n  removes: removedNodeMutation[],\n  n: Node,\n  mirror: Mirror,\n): boolean {\n  if (removes.length === 0) return false;\n  return _isParentRemoved(removes, n, mirror);\n}\n\nfunction _isParentRemoved(\n  removes: removedNodeMutation[],\n  n: Node,\n  mirror: Mirror,\n): boolean {\n  const { parentNode } = n;\n  if (!parentNode) {\n    return false;\n  }\n  const parentId = mirror.getId(parentNode);\n  if (removes.some((r) => r.id === parentId)) {\n    return true;\n  }\n  return _isParentRemoved(removes, parentNode, mirror);\n}\n\nfunction isAncestorInSet(set: Set<Node>, n: Node): boolean {\n  if (set.size === 0) return false;\n  return _isAncestorInSet(set, n);\n}\n\nfunction _isAncestorInSet(set: Set<Node>, n: Node): boolean {\n  const { parentNode } = n;\n  if (!parentNode) {\n    return false;\n  }\n  if (set.has(parentNode)) {\n    return true;\n  }\n  return _isAncestorInSet(set, parentNode);\n}\n", "import { MaskInputOptions, maskInputValue, Mirror } from 'rrweb-snapshot';\nimport type { FontFaceSet } from 'css-font-loading-module';\nimport {\n  throttle,\n  on,\n  hookSetter,\n  getWindowHeight,\n  getWindowWidth,\n  isBlocked,\n  isTouchEvent,\n  patch,\n  StyleSheetMirror,\n} from '../utils';\nimport type { observerParam, MutationBufferParam } from '../types';\nimport {\n  mutationCallBack,\n  mousemoveCallBack,\n  mousePosition,\n  mouseInteractionCallBack,\n  MouseInteractions,\n  listenerHandler,\n  scrollCallback,\n  styleSheetRuleCallback,\n  viewportResizeCallback,\n  inputValue,\n  inputCallback,\n  hookResetter,\n  IncrementalSource,\n  hooksParam,\n  Arguments,\n  mediaInteractionCallback,\n  MediaInteractions,\n  canvasMutationCallback,\n  fontCallback,\n  fontParam,\n  styleDeclarationCallback,\n  IWindow,\n  SelectionRange,\n  selectionCallback,\n} from '@rrweb/types';\nimport MutationBuffer from './mutation';\n\ntype WindowWithStoredMutationObserver = IWindow & {\n  __rrMutationObserver?: MutationObserver;\n};\ntype WindowWithAngularZone = IWindow & {\n  Zone?: {\n    __symbol__?: (key: string) => string;\n  };\n};\n\nexport const mutationBuffers: MutationBuffer[] = [];\n\nconst isCSSGroupingRuleSupported = typeof CSSGroupingRule !== 'undefined';\nconst isCSSMediaRuleSupported = typeof CSSMediaRule !== 'undefined';\nconst isCSSSupportsRuleSupported = typeof CSSSupportsRule !== 'undefined';\nconst isCSSConditionRuleSupported = typeof CSSConditionRule !== 'undefined';\n\n// Event.path is non-standard and used in some older browsers\ntype NonStandardEvent = Omit<Event, 'composedPath'> & {\n  path: EventTarget[];\n};\n\nfunction getEventTarget(event: Event | NonStandardEvent): EventTarget | null {\n  try {\n    if ('composedPath' in event) {\n      const path = event.composedPath();\n      if (path.length) {\n        return path[0];\n      }\n    } else if ('path' in event && event.path.length) {\n      return event.path[0];\n    }\n    return event.target;\n  } catch {\n    return event.target;\n  }\n}\n\nexport function initMutationObserver(\n  options: MutationBufferParam,\n  rootEl: Node,\n): MutationObserver {\n  const mutationBuffer = new MutationBuffer();\n  mutationBuffers.push(mutationBuffer);\n  // see mutation.ts for details\n  mutationBuffer.init(options);\n  let mutationObserverCtor =\n    window.MutationObserver ||\n    /**\n     * Some websites may disable MutationObserver by removing it from the window object.\n     * If someone is using rrweb to build a browser extention or things like it, they\n     * could not change the website's code but can have an opportunity to inject some\n     * code before the website executing its JS logic.\n     * Then they can do this to store the native MutationObserver:\n     * window.__rrMutationObserver = MutationObserver\n     */\n    (window as WindowWithStoredMutationObserver).__rrMutationObserver;\n  const angularZoneSymbol = (window as WindowWithAngularZone)?.Zone?.__symbol__?.(\n    'MutationObserver',\n  );\n  if (\n    angularZoneSymbol &&\n    ((window as unknown) as Record<string, typeof MutationObserver>)[\n      angularZoneSymbol\n    ]\n  ) {\n    mutationObserverCtor = ((window as unknown) as Record<\n      string,\n      typeof MutationObserver\n    >)[angularZoneSymbol];\n  }\n  const observer = new (mutationObserverCtor as new (\n    callback: MutationCallback,\n  ) => MutationObserver)(mutationBuffer.processMutations.bind(mutationBuffer));\n  observer.observe(rootEl, {\n    attributes: true,\n    attributeOldValue: true,\n    characterData: true,\n    characterDataOldValue: true,\n    childList: true,\n    subtree: true,\n  });\n  return observer;\n}\n\nfunction initMoveObserver({\n  mousemoveCb,\n  sampling,\n  doc,\n  mirror,\n}: observerParam): listenerHandler {\n  if (sampling.mousemove === false) {\n    return () => {\n      //\n    };\n  }\n\n  const threshold =\n    typeof sampling.mousemove === 'number' ? sampling.mousemove : 50;\n  const callbackThreshold =\n    typeof sampling.mousemoveCallback === 'number'\n      ? sampling.mousemoveCallback\n      : 500;\n\n  let positions: mousePosition[] = [];\n  let timeBaseline: number | null;\n  const wrappedCb = throttle(\n    (\n      source:\n        | IncrementalSource.MouseMove\n        | IncrementalSource.TouchMove\n        | IncrementalSource.Drag,\n    ) => {\n      const totalOffset = Date.now() - timeBaseline!;\n      mousemoveCb(\n        positions.map((p) => {\n          p.timeOffset -= totalOffset;\n          return p;\n        }),\n        source,\n      );\n      positions = [];\n      timeBaseline = null;\n    },\n    callbackThreshold,\n  );\n  const updatePosition = throttle<MouseEvent | TouchEvent | DragEvent>(\n    (evt) => {\n      const target = getEventTarget(evt);\n      const { clientX, clientY } = isTouchEvent(evt)\n        ? evt.changedTouches[0]\n        : evt;\n      if (!timeBaseline) {\n        timeBaseline = Date.now();\n      }\n      positions.push({\n        x: clientX,\n        y: clientY,\n        id: mirror.getId(target as Node),\n        timeOffset: Date.now() - timeBaseline,\n      });\n      // it is possible DragEvent is undefined even on devices\n      // that support event 'drag'\n      wrappedCb(\n        typeof DragEvent !== 'undefined' && evt instanceof DragEvent\n          ? IncrementalSource.Drag\n          : evt instanceof MouseEvent\n          ? IncrementalSource.MouseMove\n          : IncrementalSource.TouchMove,\n      );\n    },\n    threshold,\n    {\n      trailing: false,\n    },\n  );\n  const handlers = [\n    on('mousemove', updatePosition, doc),\n    on('touchmove', updatePosition, doc),\n    on('drag', updatePosition, doc),\n  ];\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n\nfunction initMouseInteractionObserver({\n  mouseInteractionCb,\n  doc,\n  mirror,\n  blockClass,\n  blockSelector,\n  sampling,\n}: observerParam): listenerHandler {\n  if (sampling.mouseInteraction === false) {\n    return () => {\n      //\n    };\n  }\n  const disableMap: Record<string, boolean | undefined> =\n    sampling.mouseInteraction === true ||\n    sampling.mouseInteraction === undefined\n      ? {}\n      : sampling.mouseInteraction;\n\n  const handlers: listenerHandler[] = [];\n  const getHandler = (eventKey: keyof typeof MouseInteractions) => {\n    return (event: MouseEvent | TouchEvent) => {\n      const target = getEventTarget(event) as Node;\n      if (isBlocked(target, blockClass, blockSelector, true)) {\n        return;\n      }\n      const e = isTouchEvent(event) ? event.changedTouches[0] : event;\n      if (!e) {\n        return;\n      }\n      const id = mirror.getId(target);\n      const { clientX, clientY } = e;\n      mouseInteractionCb({\n        type: MouseInteractions[eventKey],\n        id,\n        x: clientX,\n        y: clientY,\n      });\n    };\n  };\n  Object.keys(MouseInteractions)\n    .filter(\n      (key) =>\n        Number.isNaN(Number(key)) &&\n        !key.endsWith('_Departed') &&\n        disableMap[key] !== false,\n    )\n    .forEach((eventKey: keyof typeof MouseInteractions) => {\n      const eventName = eventKey.toLowerCase();\n      const handler = getHandler(eventKey);\n      handlers.push(on(eventName, handler, doc));\n    });\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n\nexport function initScrollObserver({\n  scrollCb,\n  doc,\n  mirror,\n  blockClass,\n  blockSelector,\n  sampling,\n}: Pick<\n  observerParam,\n  'scrollCb' | 'doc' | 'mirror' | 'blockClass' | 'blockSelector' | 'sampling'\n>): listenerHandler {\n  const updatePosition = throttle<UIEvent>((evt) => {\n    const target = getEventTarget(evt);\n    if (!target || isBlocked(target as Node, blockClass, blockSelector, true)) {\n      return;\n    }\n    const id = mirror.getId(target as Node);\n    if (target === doc) {\n      const scrollEl = (doc.scrollingElement || doc.documentElement)!;\n      scrollCb({\n        id,\n        x: scrollEl.scrollLeft,\n        y: scrollEl.scrollTop,\n      });\n    } else {\n      scrollCb({\n        id,\n        x: (target as HTMLElement).scrollLeft,\n        y: (target as HTMLElement).scrollTop,\n      });\n    }\n  }, sampling.scroll || 100);\n  return on('scroll', updatePosition, doc);\n}\n\nfunction initViewportResizeObserver({\n  viewportResizeCb,\n}: observerParam): listenerHandler {\n  let lastH = -1;\n  let lastW = -1;\n  const updateDimension = throttle(() => {\n    const height = getWindowHeight();\n    const width = getWindowWidth();\n    if (lastH !== height || lastW !== width) {\n      viewportResizeCb({\n        width: Number(width),\n        height: Number(height),\n      });\n      lastH = height;\n      lastW = width;\n    }\n  }, 200);\n  return on('resize', updateDimension, window);\n}\n\nfunction wrapEventWithUserTriggeredFlag(\n  v: inputValue,\n  enable: boolean,\n): inputValue {\n  const value = { ...v };\n  if (!enable) delete value.userTriggered;\n  return value;\n}\n\nexport const INPUT_TAGS = ['INPUT', 'TEXTAREA', 'SELECT'];\nconst lastInputValueMap: WeakMap<EventTarget, inputValue> = new WeakMap();\nfunction initInputObserver({\n  inputCb,\n  doc,\n  mirror,\n  blockClass,\n  blockSelector,\n  ignoreClass,\n  maskInputOptions,\n  maskInputFn,\n  sampling,\n  userTriggeredOnInput,\n}: observerParam): listenerHandler {\n  function eventHandler(event: Event) {\n    let target = getEventTarget(event);\n    const userTriggered = event.isTrusted;\n    /**\n     * If a site changes the value 'selected' of an option element, the value of its parent element, usually a select element, will be changed as well.\n     * We can treat this change as a value change of the select element the current target belongs to.\n     */\n    if (target && (target as Element).tagName === 'OPTION')\n      target = (target as Element).parentElement;\n    if (\n      !target ||\n      !(target as Element).tagName ||\n      INPUT_TAGS.indexOf((target as Element).tagName) < 0 ||\n      isBlocked(target as Node, blockClass, blockSelector, true)\n    ) {\n      return;\n    }\n    const type: string | undefined = (target as HTMLInputElement).type;\n    if ((target as HTMLElement).classList.contains(ignoreClass)) {\n      return;\n    }\n    let text = (target as HTMLInputElement).value;\n    let isChecked = false;\n    if (type === 'radio' || type === 'checkbox') {\n      isChecked = (target as HTMLInputElement).checked;\n    } else if (\n      maskInputOptions[\n        (target as Element).tagName.toLowerCase() as keyof MaskInputOptions\n      ] ||\n      maskInputOptions[type as keyof MaskInputOptions]\n    ) {\n      text = maskInputValue({\n        maskInputOptions,\n        tagName: (target as HTMLElement).tagName,\n        type,\n        value: text,\n        maskInputFn,\n      });\n    }\n    cbWithDedup(\n      target,\n      wrapEventWithUserTriggeredFlag(\n        { text, isChecked, userTriggered },\n        userTriggeredOnInput,\n      ),\n    );\n    // if a radio was checked\n    // the other radios with the same name attribute will be unchecked.\n    const name: string | undefined = (target as HTMLInputElement).name;\n    if (type === 'radio' && name && isChecked) {\n      doc\n        .querySelectorAll(`input[type=\"radio\"][name=\"${name}\"]`)\n        .forEach((el) => {\n          if (el !== target) {\n            cbWithDedup(\n              el,\n              wrapEventWithUserTriggeredFlag(\n                {\n                  text: (el as HTMLInputElement).value,\n                  isChecked: !isChecked,\n                  userTriggered: false,\n                },\n                userTriggeredOnInput,\n              ),\n            );\n          }\n        });\n    }\n  }\n  function cbWithDedup(target: EventTarget, v: inputValue) {\n    const lastInputValue = lastInputValueMap.get(target);\n    if (\n      !lastInputValue ||\n      lastInputValue.text !== v.text ||\n      lastInputValue.isChecked !== v.isChecked\n    ) {\n      lastInputValueMap.set(target, v);\n      const id = mirror.getId(target as Node);\n      inputCb({\n        ...v,\n        id,\n      });\n    }\n  }\n  const events = sampling.input === 'last' ? ['change'] : ['input', 'change'];\n  const handlers: Array<\n    listenerHandler | hookResetter\n  > = events.map((eventName) => on(eventName, eventHandler, doc));\n  const currentWindow = doc.defaultView;\n  if (!currentWindow) {\n    return () => {\n      handlers.forEach((h) => h());\n    };\n  }\n  const propertyDescriptor = currentWindow.Object.getOwnPropertyDescriptor(\n    currentWindow.HTMLInputElement.prototype,\n    'value',\n  );\n  const hookProperties: Array<[HTMLElement, string]> = [\n    [currentWindow.HTMLInputElement.prototype, 'value'],\n    [currentWindow.HTMLInputElement.prototype, 'checked'],\n    [currentWindow.HTMLSelectElement.prototype, 'value'],\n    [currentWindow.HTMLTextAreaElement.prototype, 'value'],\n    // Some UI library use selectedIndex to set select value\n    [currentWindow.HTMLSelectElement.prototype, 'selectedIndex'],\n    [currentWindow.HTMLOptionElement.prototype, 'selected'],\n  ];\n  if (propertyDescriptor && propertyDescriptor.set) {\n    handlers.push(\n      ...hookProperties.map((p) =>\n        hookSetter<HTMLElement>(\n          p[0],\n          p[1],\n          {\n            set() {\n              // mock to a normal event\n              eventHandler({ target: this as EventTarget } as Event);\n            },\n          },\n          false,\n          currentWindow,\n        ),\n      ),\n    );\n  }\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n\ntype GroupingCSSRule =\n  | CSSGroupingRule\n  | CSSMediaRule\n  | CSSSupportsRule\n  | CSSConditionRule;\ntype GroupingCSSRuleTypes =\n  | typeof CSSGroupingRule\n  | typeof CSSMediaRule\n  | typeof CSSSupportsRule\n  | typeof CSSConditionRule;\n\nfunction getNestedCSSRulePositions(rule: CSSRule): number[] {\n  const positions: number[] = [];\n  function recurse(childRule: CSSRule, pos: number[]) {\n    if (\n      (isCSSGroupingRuleSupported &&\n        childRule.parentRule instanceof CSSGroupingRule) ||\n      (isCSSMediaRuleSupported &&\n        childRule.parentRule instanceof CSSMediaRule) ||\n      (isCSSSupportsRuleSupported &&\n        childRule.parentRule instanceof CSSSupportsRule) ||\n      (isCSSConditionRuleSupported &&\n        childRule.parentRule instanceof CSSConditionRule)\n    ) {\n      const rules = Array.from(\n        (childRule.parentRule as GroupingCSSRule).cssRules,\n      );\n      const index = rules.indexOf(childRule);\n      pos.unshift(index);\n    } else if (childRule.parentStyleSheet) {\n      const rules = Array.from(childRule.parentStyleSheet.cssRules);\n      const index = rules.indexOf(childRule);\n      pos.unshift(index);\n    }\n    return pos;\n  }\n  return recurse(rule, positions);\n}\n\n/**\n * For StyleSheets in Element, this function retrieves id of its host element.\n * For adopted StyleSheets, this function retrieves its styleId from a styleMirror.\n */\nfunction getIdAndStyleId(\n  sheet: CSSStyleSheet | undefined | null,\n  mirror: Mirror,\n  styleMirror: StyleSheetMirror,\n): {\n  styleId?: number;\n  id?: number;\n} {\n  let id, styleId;\n  if (!sheet) return {};\n  if (sheet.ownerNode) id = mirror.getId(sheet.ownerNode as Node);\n  else styleId = styleMirror.getId(sheet);\n  return {\n    styleId,\n    id,\n  };\n}\n\nfunction initStyleSheetObserver(\n  { styleSheetRuleCb, mirror, stylesheetManager }: observerParam,\n  { win }: { win: IWindow },\n): listenerHandler {\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const insertRule = win.CSSStyleSheet.prototype.insertRule;\n  win.CSSStyleSheet.prototype.insertRule = function (\n    this: CSSStyleSheet,\n    rule: string,\n    index?: number,\n  ) {\n    const { id, styleId } = getIdAndStyleId(\n      this,\n      mirror,\n      stylesheetManager.styleMirror,\n    );\n\n    if ((id && id !== -1) || (styleId && styleId !== -1)) {\n      styleSheetRuleCb({\n        id,\n        styleId,\n        adds: [{ rule, index }],\n      });\n    }\n    return insertRule.apply(this, [rule, index]);\n  };\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const deleteRule = win.CSSStyleSheet.prototype.deleteRule;\n  win.CSSStyleSheet.prototype.deleteRule = function (\n    this: CSSStyleSheet,\n    index: number,\n  ) {\n    const { id, styleId } = getIdAndStyleId(\n      this,\n      mirror,\n      stylesheetManager.styleMirror,\n    );\n\n    if ((id && id !== -1) || (styleId && styleId !== -1)) {\n      styleSheetRuleCb({\n        id,\n        styleId,\n        removes: [{ index }],\n      });\n    }\n    return deleteRule.apply(this, [index]);\n  };\n\n  let replace: (text: string) => Promise<CSSStyleSheet>;\n  if (win.CSSStyleSheet.prototype.replace) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    replace = win.CSSStyleSheet.prototype.replace;\n    win.CSSStyleSheet.prototype.replace = function (\n      this: CSSStyleSheet,\n      text: string,\n    ) {\n      const { id, styleId } = getIdAndStyleId(\n        this,\n        mirror,\n        stylesheetManager.styleMirror,\n      );\n\n      if ((id && id !== -1) || (styleId && styleId !== -1)) {\n        styleSheetRuleCb({\n          id,\n          styleId,\n          replace: text,\n        });\n      }\n      return replace.apply(this, [text]);\n    };\n  }\n\n  let replaceSync: (text: string) => void;\n  if (win.CSSStyleSheet.prototype.replaceSync) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    replaceSync = win.CSSStyleSheet.prototype.replaceSync;\n    win.CSSStyleSheet.prototype.replaceSync = function (\n      this: CSSStyleSheet,\n      text: string,\n    ) {\n      const { id, styleId } = getIdAndStyleId(\n        this,\n        mirror,\n        stylesheetManager.styleMirror,\n      );\n\n      if ((id && id !== -1) || (styleId && styleId !== -1)) {\n        styleSheetRuleCb({\n          id,\n          styleId,\n          replaceSync: text,\n        });\n      }\n      return replaceSync.apply(this, [text]);\n    };\n  }\n\n  const supportedNestedCSSRuleTypes: {\n    [key: string]: GroupingCSSRuleTypes;\n  } = {};\n  if (isCSSGroupingRuleSupported) {\n    supportedNestedCSSRuleTypes.CSSGroupingRule = win.CSSGroupingRule;\n  } else {\n    // Some browsers (Safari) don't support CSSGroupingRule\n    // https://caniuse.com/?search=cssgroupingrule\n    // fall back to monkey patching classes that would have inherited from CSSGroupingRule\n\n    if (isCSSMediaRuleSupported) {\n      supportedNestedCSSRuleTypes.CSSMediaRule = win.CSSMediaRule;\n    }\n    if (isCSSConditionRuleSupported) {\n      supportedNestedCSSRuleTypes.CSSConditionRule = win.CSSConditionRule;\n    }\n    if (isCSSSupportsRuleSupported) {\n      supportedNestedCSSRuleTypes.CSSSupportsRule = win.CSSSupportsRule;\n    }\n  }\n\n  const unmodifiedFunctions: {\n    [key: string]: {\n      insertRule: (rule: string, index?: number) => number;\n      deleteRule: (index: number) => void;\n    };\n  } = {};\n\n  Object.entries(supportedNestedCSSRuleTypes).forEach(([typeKey, type]) => {\n    unmodifiedFunctions[typeKey] = {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      insertRule: type.prototype.insertRule,\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      deleteRule: type.prototype.deleteRule,\n    };\n\n    type.prototype.insertRule = function (\n      this: CSSGroupingRule,\n      rule: string,\n      index?: number,\n    ) {\n      const { id, styleId } = getIdAndStyleId(\n        this.parentStyleSheet,\n        mirror,\n        stylesheetManager.styleMirror,\n      );\n\n      if ((id && id !== -1) || (styleId && styleId !== -1)) {\n        styleSheetRuleCb({\n          id,\n          styleId,\n          adds: [\n            {\n              rule,\n              index: [\n                ...getNestedCSSRulePositions(this as CSSRule),\n                index || 0, // defaults to 0\n              ],\n            },\n          ],\n        });\n      }\n      return unmodifiedFunctions[typeKey].insertRule.apply(this, [rule, index]);\n    };\n\n    type.prototype.deleteRule = function (\n      this: CSSGroupingRule,\n      index: number,\n    ) {\n      const { id, styleId } = getIdAndStyleId(\n        this.parentStyleSheet,\n        mirror,\n        stylesheetManager.styleMirror,\n      );\n\n      if ((id && id !== -1) || (styleId && styleId !== -1)) {\n        styleSheetRuleCb({\n          id,\n          styleId,\n          removes: [\n            { index: [...getNestedCSSRulePositions(this as CSSRule), index] },\n          ],\n        });\n      }\n      return unmodifiedFunctions[typeKey].deleteRule.apply(this, [index]);\n    };\n  });\n\n  return () => {\n    win.CSSStyleSheet.prototype.insertRule = insertRule;\n    win.CSSStyleSheet.prototype.deleteRule = deleteRule;\n    replace && (win.CSSStyleSheet.prototype.replace = replace);\n    replaceSync && (win.CSSStyleSheet.prototype.replaceSync = replaceSync);\n    Object.entries(supportedNestedCSSRuleTypes).forEach(([typeKey, type]) => {\n      type.prototype.insertRule = unmodifiedFunctions[typeKey].insertRule;\n      type.prototype.deleteRule = unmodifiedFunctions[typeKey].deleteRule;\n    });\n  };\n}\n\nexport function initAdoptedStyleSheetObserver(\n  {\n    mirror,\n    stylesheetManager,\n  }: Pick<observerParam, 'mirror' | 'stylesheetManager'>,\n  host: Document | ShadowRoot,\n): listenerHandler {\n  let hostId: number | null = null;\n  // host of adoptedStyleSheets is outermost document or IFrame's document\n  if (host.nodeName === '#document') hostId = mirror.getId(host);\n  // The host is a ShadowRoot.\n  else hostId = mirror.getId((host as ShadowRoot).host);\n\n  const patchTarget =\n    host.nodeName === '#document'\n      ? (host as Document).defaultView?.Document\n      : host.ownerDocument?.defaultView?.ShadowRoot;\n  const originalPropertyDescriptor = Object.getOwnPropertyDescriptor(\n    patchTarget?.prototype,\n    'adoptedStyleSheets',\n  );\n  if (\n    hostId === null ||\n    hostId === -1 ||\n    !patchTarget ||\n    !originalPropertyDescriptor\n  )\n    return () => {\n      //\n    };\n\n  // Patch adoptedStyleSheets by overriding the original one.\n  Object.defineProperty(host, 'adoptedStyleSheets', {\n    configurable: originalPropertyDescriptor.configurable,\n    enumerable: originalPropertyDescriptor.enumerable,\n    get(): CSSStyleSheet[] {\n      return originalPropertyDescriptor.get?.call(this) as CSSStyleSheet[];\n    },\n    set(sheets: CSSStyleSheet[]) {\n      const result = originalPropertyDescriptor.set?.call(this, sheets);\n      if (hostId !== null && hostId !== -1) {\n        try {\n          stylesheetManager.adoptStyleSheets(sheets, hostId);\n        } catch (e) {\n          // for safety\n        }\n      }\n      return result;\n    },\n  });\n\n  return () => {\n    Object.defineProperty(host, 'adoptedStyleSheets', {\n      configurable: originalPropertyDescriptor.configurable,\n      enumerable: originalPropertyDescriptor.enumerable,\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      get: originalPropertyDescriptor.get,\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      set: originalPropertyDescriptor.set,\n    });\n  };\n}\n\nfunction initStyleDeclarationObserver(\n  {\n    styleDeclarationCb,\n    mirror,\n    ignoreCSSAttributes,\n    stylesheetManager,\n  }: observerParam,\n  { win }: { win: IWindow },\n): listenerHandler {\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const setProperty = win.CSSStyleDeclaration.prototype.setProperty;\n  win.CSSStyleDeclaration.prototype.setProperty = function (\n    this: CSSStyleDeclaration,\n    property: string,\n    value: string,\n    priority: string,\n  ) {\n    // ignore this mutation if we do not care about this css attribute\n    if (ignoreCSSAttributes.has(property)) {\n      return setProperty.apply(this, [property, value, priority]);\n    }\n    const { id, styleId } = getIdAndStyleId(\n      this.parentRule?.parentStyleSheet,\n      mirror,\n      stylesheetManager.styleMirror,\n    );\n    if ((id && id !== -1) || (styleId && styleId !== -1)) {\n      styleDeclarationCb({\n        id,\n        styleId,\n        set: {\n          property,\n          value,\n          priority,\n        },\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        index: getNestedCSSRulePositions(this.parentRule!),\n      });\n    }\n    return setProperty.apply(this, [property, value, priority]);\n  };\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const removeProperty = win.CSSStyleDeclaration.prototype.removeProperty;\n  win.CSSStyleDeclaration.prototype.removeProperty = function (\n    this: CSSStyleDeclaration,\n    property: string,\n  ) {\n    // ignore this mutation if we do not care about this css attribute\n    if (ignoreCSSAttributes.has(property)) {\n      return removeProperty.apply(this, [property]);\n    }\n    const { id, styleId } = getIdAndStyleId(\n      this.parentRule?.parentStyleSheet,\n      mirror,\n      stylesheetManager.styleMirror,\n    );\n    if ((id && id !== -1) || (styleId && styleId !== -1)) {\n      styleDeclarationCb({\n        id,\n        styleId,\n        remove: {\n          property,\n        },\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        index: getNestedCSSRulePositions(this.parentRule!),\n      });\n    }\n    return removeProperty.apply(this, [property]);\n  };\n\n  return () => {\n    win.CSSStyleDeclaration.prototype.setProperty = setProperty;\n    win.CSSStyleDeclaration.prototype.removeProperty = removeProperty;\n  };\n}\n\nfunction initMediaInteractionObserver({\n  mediaInteractionCb,\n  blockClass,\n  blockSelector,\n  mirror,\n  sampling,\n}: observerParam): listenerHandler {\n  const handler = (type: MediaInteractions) =>\n    throttle((event: Event) => {\n      const target = getEventTarget(event);\n      if (\n        !target ||\n        isBlocked(target as Node, blockClass, blockSelector, true)\n      ) {\n        return;\n      }\n      const {\n        currentTime,\n        volume,\n        muted,\n        playbackRate,\n      } = target as HTMLMediaElement;\n      mediaInteractionCb({\n        type,\n        id: mirror.getId(target as Node),\n        currentTime,\n        volume,\n        muted,\n        playbackRate,\n      });\n    }, sampling.media || 500);\n  const handlers = [\n    on('play', handler(MediaInteractions.Play)),\n    on('pause', handler(MediaInteractions.Pause)),\n    on('seeked', handler(MediaInteractions.Seeked)),\n    on('volumechange', handler(MediaInteractions.VolumeChange)),\n    on('ratechange', handler(MediaInteractions.RateChange)),\n  ];\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n\nfunction initFontObserver({ fontCb, doc }: observerParam): listenerHandler {\n  const win = doc.defaultView as IWindow;\n  if (!win) {\n    return () => {\n      //\n    };\n  }\n\n  const handlers: listenerHandler[] = [];\n\n  const fontMap = new WeakMap<FontFace, fontParam>();\n\n  const originalFontFace = win.FontFace;\n  win.FontFace = (function FontFace(\n    family: string,\n    source: string | ArrayBufferLike,\n    descriptors?: FontFaceDescriptors,\n  ) {\n    const fontFace = new originalFontFace(family, source, descriptors);\n    fontMap.set(fontFace, {\n      family,\n      buffer: typeof source !== 'string',\n      descriptors,\n      fontSource:\n        typeof source === 'string'\n          ? source\n          : JSON.stringify(Array.from(new Uint8Array(source))),\n    });\n    return fontFace;\n  } as unknown) as typeof FontFace;\n\n  const restoreHandler = patch(\n    doc.fonts,\n    'add',\n    function (original: (font: FontFace) => void) {\n      return function (this: FontFaceSet, fontFace: FontFace) {\n        setTimeout(() => {\n          const p = fontMap.get(fontFace);\n          if (p) {\n            fontCb(p);\n            fontMap.delete(fontFace);\n          }\n        }, 0);\n        return original.apply(this, [fontFace]);\n      };\n    },\n  );\n\n  handlers.push(() => {\n    win.FontFace = originalFontFace;\n  });\n  handlers.push(restoreHandler);\n\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n\nfunction initSelectionObserver(param: observerParam): listenerHandler {\n  const { doc, mirror, blockClass, blockSelector, selectionCb } = param;\n  let collapsed = true;\n\n  const updateSelection = () => {\n    const selection = doc.getSelection();\n\n    if (!selection || (collapsed && selection?.isCollapsed)) return;\n\n    collapsed = selection.isCollapsed || false;\n\n    const ranges: SelectionRange[] = [];\n    const count = selection.rangeCount || 0;\n\n    for (let i = 0; i < count; i++) {\n      const range = selection.getRangeAt(i);\n\n      const { startContainer, startOffset, endContainer, endOffset } = range;\n\n      const blocked =\n        isBlocked(startContainer, blockClass, blockSelector, true) ||\n        isBlocked(endContainer, blockClass, blockSelector, true);\n\n      if (blocked) continue;\n\n      ranges.push({\n        start: mirror.getId(startContainer),\n        startOffset,\n        end: mirror.getId(endContainer),\n        endOffset,\n      });\n    }\n\n    selectionCb({ ranges });\n  };\n\n  updateSelection();\n\n  return on('selectionchange', updateSelection);\n}\n\nfunction mergeHooks(o: observerParam, hooks: hooksParam) {\n  const {\n    mutationCb,\n    mousemoveCb,\n    mouseInteractionCb,\n    scrollCb,\n    viewportResizeCb,\n    inputCb,\n    mediaInteractionCb,\n    styleSheetRuleCb,\n    styleDeclarationCb,\n    canvasMutationCb,\n    fontCb,\n    selectionCb,\n  } = o;\n  o.mutationCb = (...p: Arguments<mutationCallBack>) => {\n    if (hooks.mutation) {\n      hooks.mutation(...p);\n    }\n    mutationCb(...p);\n  };\n  o.mousemoveCb = (...p: Arguments<mousemoveCallBack>) => {\n    if (hooks.mousemove) {\n      hooks.mousemove(...p);\n    }\n    mousemoveCb(...p);\n  };\n  o.mouseInteractionCb = (...p: Arguments<mouseInteractionCallBack>) => {\n    if (hooks.mouseInteraction) {\n      hooks.mouseInteraction(...p);\n    }\n    mouseInteractionCb(...p);\n  };\n  o.scrollCb = (...p: Arguments<scrollCallback>) => {\n    if (hooks.scroll) {\n      hooks.scroll(...p);\n    }\n    scrollCb(...p);\n  };\n  o.viewportResizeCb = (...p: Arguments<viewportResizeCallback>) => {\n    if (hooks.viewportResize) {\n      hooks.viewportResize(...p);\n    }\n    viewportResizeCb(...p);\n  };\n  o.inputCb = (...p: Arguments<inputCallback>) => {\n    if (hooks.input) {\n      hooks.input(...p);\n    }\n    inputCb(...p);\n  };\n  o.mediaInteractionCb = (...p: Arguments<mediaInteractionCallback>) => {\n    if (hooks.mediaInteaction) {\n      hooks.mediaInteaction(...p);\n    }\n    mediaInteractionCb(...p);\n  };\n  o.styleSheetRuleCb = (...p: Arguments<styleSheetRuleCallback>) => {\n    if (hooks.styleSheetRule) {\n      hooks.styleSheetRule(...p);\n    }\n    styleSheetRuleCb(...p);\n  };\n  o.styleDeclarationCb = (...p: Arguments<styleDeclarationCallback>) => {\n    if (hooks.styleDeclaration) {\n      hooks.styleDeclaration(...p);\n    }\n    styleDeclarationCb(...p);\n  };\n  o.canvasMutationCb = (...p: Arguments<canvasMutationCallback>) => {\n    if (hooks.canvasMutation) {\n      hooks.canvasMutation(...p);\n    }\n    canvasMutationCb(...p);\n  };\n  o.fontCb = (...p: Arguments<fontCallback>) => {\n    if (hooks.font) {\n      hooks.font(...p);\n    }\n    fontCb(...p);\n  };\n  o.selectionCb = (...p: Arguments<selectionCallback>) => {\n    if (hooks.selection) {\n      hooks.selection(...p);\n    }\n    selectionCb(...p);\n  };\n}\n\nexport function initObservers(\n  o: observerParam,\n  hooks: hooksParam = {},\n): listenerHandler {\n  const currentWindow = o.doc.defaultView; // basically document.window\n  if (!currentWindow) {\n    return () => {\n      //\n    };\n  }\n\n  mergeHooks(o, hooks);\n  const mutationObserver = initMutationObserver(o, o.doc);\n  const mousemoveHandler = initMoveObserver(o);\n  const mouseInteractionHandler = initMouseInteractionObserver(o);\n  const scrollHandler = initScrollObserver(o);\n  const viewportResizeHandler = initViewportResizeObserver(o);\n  const inputHandler = initInputObserver(o);\n  const mediaInteractionHandler = initMediaInteractionObserver(o);\n\n  const styleSheetObserver = initStyleSheetObserver(o, { win: currentWindow });\n  const adoptedStyleSheetObserver = initAdoptedStyleSheetObserver(o, o.doc);\n  const styleDeclarationObserver = initStyleDeclarationObserver(o, {\n    win: currentWindow,\n  });\n  const fontObserver = o.collectFonts\n    ? initFontObserver(o)\n    : () => {\n        //\n      };\n  const selectionObserver = initSelectionObserver(o);\n\n  // plugins\n  const pluginHandlers: listenerHandler[] = [];\n  for (const plugin of o.plugins) {\n    pluginHandlers.push(\n      plugin.observer(plugin.callback, currentWindow, plugin.options),\n    );\n  }\n\n  return () => {\n    mutationBuffers.forEach((b) => b.reset());\n    mutationObserver.disconnect();\n    mousemoveHandler();\n    mouseInteractionHandler();\n    scrollHandler();\n    viewportResizeHandler();\n    inputHandler();\n    mediaInteractionHandler();\n    styleSheetObserver();\n    adoptedStyleSheetObserver();\n    styleDeclarationObserver();\n    fontObserver();\n    selectionObserver();\n    pluginHandlers.forEach((h) => h());\n  };\n}\n", "import type { ICrossOriginIframeMirror } from '@rrweb/types';\nexport default class CrossOriginIframeMirror\n  implements ICrossOriginIframeMirror {\n  private iframeIdToRemoteIdMap: WeakMap<\n    HTMLIFrameElement,\n    Map<number, number>\n  > = new WeakMap();\n  private iframeRemoteIdToIdMap: WeakMap<\n    HTMLIFrameElement,\n    Map<number, number>\n  > = new WeakMap();\n\n  constructor(private generateIdFn: () => number) {}\n\n  getId(\n    iframe: HTMLIFrameElement,\n    remoteId: number,\n    idToRemoteMap?: Map<number, number>,\n    remoteToIdMap?: Map<number, number>,\n  ): number {\n    const idToRemoteIdMap = idToRemoteMap || this.getIdToRemoteIdMap(iframe);\n    const remoteIdToIdMap = remoteToIdMap || this.getRemoteIdToIdMap(iframe);\n\n    let id = idToRemoteIdMap.get(remoteId);\n    if (!id) {\n      id = this.generateIdFn();\n      idToRemoteIdMap.set(remoteId, id);\n      remoteIdToIdMap.set(id, remoteId);\n    }\n    return id;\n  }\n\n  getIds(iframe: HTMLIFrameElement, remoteId: number[]): number[] {\n    const idToRemoteIdMap = this.getIdToRemoteIdMap(iframe);\n    const remoteIdToIdMap = this.getRemoteIdToIdMap(iframe);\n    return remoteId.map((id) =>\n      this.getId(iframe, id, idToRemoteIdMap, remoteIdToIdMap),\n    );\n  }\n\n  getRemoteId(\n    iframe: HTMLIFrameElement,\n    id: number,\n    map?: Map<number, number>,\n  ): number {\n    const remoteIdToIdMap = map || this.getRemoteIdToIdMap(iframe);\n\n    if (typeof id !== 'number') return id;\n\n    const remoteId = remoteIdToIdMap.get(id);\n    if (!remoteId) return -1;\n    return remoteId;\n  }\n\n  getRemoteIds(iframe: HTMLIFrameElement, ids: number[]): number[] {\n    const remoteIdToIdMap = this.getRemoteIdToIdMap(iframe);\n\n    return ids.map((id) => this.getRemoteId(iframe, id, remoteIdToIdMap));\n  }\n\n  reset(iframe?: HTMLIFrameElement) {\n    if (!iframe) {\n      this.iframeIdToRemoteIdMap = new WeakMap();\n      this.iframeRemoteIdToIdMap = new WeakMap();\n      return;\n    }\n    this.iframeIdToRemoteIdMap.delete(iframe);\n    this.iframeRemoteIdToIdMap.delete(iframe);\n  }\n\n  private getIdToRemoteIdMap(iframe: HTMLIFrameElement) {\n    let idToRemoteIdMap = this.iframeIdToRemoteIdMap.get(iframe);\n    if (!idToRemoteIdMap) {\n      idToRemoteIdMap = new Map();\n      this.iframeIdToRemoteIdMap.set(iframe, idToRemoteIdMap);\n    }\n    return idToRemoteIdMap;\n  }\n\n  private getRemoteIdToIdMap(iframe: HTMLIFrameElement) {\n    let remoteIdToIdMap = this.iframeRemoteIdToIdMap.get(iframe);\n    if (!remoteIdToIdMap) {\n      remoteIdToIdMap = new Map();\n      this.iframeRemoteIdToIdMap.set(iframe, remoteIdToIdMap);\n    }\n    return remoteIdToIdMap;\n  }\n}\n", "import type { Mirror, serializedNodeWithId } from 'rrweb-snapshot';\nimport { genId } from 'rrweb-snapshot';\nimport type { CrossOriginIframeMessageEvent } from '../types';\nimport CrossOriginIframeMirror from './cross-origin-iframe-mirror';\nimport { EventType, IncrementalSource } from '@rrweb/types';\nimport type { eventWithTime, mutationCallBack } from '@rrweb/types';\nimport type { StylesheetManager } from './stylesheet-manager';\n\nexport class IframeManager {\n  private iframes: WeakMap<HTMLIFrameElement, true> = new WeakMap();\n  private crossOriginIframeMap: WeakMap<\n    MessageEventSource,\n    HTMLIFrameElement\n  > = new WeakMap();\n  public crossOriginIframeMirror = new CrossOriginIframeMirror(genId);\n  public crossOriginIframeStyleMirror: CrossOriginIframeMirror;\n  private mirror: Mirror;\n  private mutationCb: mutationCallBack;\n  private wrappedEmit: (e: eventWithTime, isCheckout?: boolean) => void;\n  private loadListener?: (iframeEl: HTMLIFrameElement) => unknown;\n  private stylesheetManager: StylesheetManager;\n  private recordCrossOriginIframes: boolean;\n\n  constructor(options: {\n    mirror: Mirror;\n    mutationCb: mutationCallBack;\n    stylesheetManager: StylesheetManager;\n    recordCrossOriginIframes: boolean;\n    wrappedEmit: (e: eventWithTime, isCheckout?: boolean) => void;\n  }) {\n    this.mutationCb = options.mutationCb;\n    this.wrappedEmit = options.wrappedEmit;\n    this.stylesheetManager = options.stylesheetManager;\n    this.recordCrossOriginIframes = options.recordCrossOriginIframes;\n    this.crossOriginIframeStyleMirror = new CrossOriginIframeMirror(\n      this.stylesheetManager.styleMirror.generateId.bind(\n        this.stylesheetManager.styleMirror,\n      ),\n    );\n    this.mirror = options.mirror;\n    if (this.recordCrossOriginIframes) {\n      window.addEventListener('message', this.handleMessage.bind(this));\n    }\n  }\n\n  public addIframe(iframeEl: HTMLIFrameElement) {\n    this.iframes.set(iframeEl, true);\n    if (iframeEl.contentWindow)\n      this.crossOriginIframeMap.set(iframeEl.contentWindow, iframeEl);\n  }\n\n  public addLoadListener(cb: (iframeEl: HTMLIFrameElement) => unknown) {\n    this.loadListener = cb;\n  }\n\n  public attachIframe(\n    iframeEl: HTMLIFrameElement,\n    childSn: serializedNodeWithId,\n  ) {\n    this.mutationCb({\n      adds: [\n        {\n          parentId: this.mirror.getId(iframeEl),\n          nextId: null,\n          node: childSn,\n        },\n      ],\n      removes: [],\n      texts: [],\n      attributes: [],\n      isAttachIframe: true,\n    });\n    this.loadListener?.(iframeEl);\n\n    if (\n      iframeEl.contentDocument &&\n      iframeEl.contentDocument.adoptedStyleSheets &&\n      iframeEl.contentDocument.adoptedStyleSheets.length > 0\n    )\n      this.stylesheetManager.adoptStyleSheets(\n        iframeEl.contentDocument.adoptedStyleSheets,\n        this.mirror.getId(iframeEl.contentDocument),\n      );\n  }\n  private handleMessage(message: MessageEvent | CrossOriginIframeMessageEvent) {\n    if ((message as CrossOriginIframeMessageEvent).data.type === 'rrweb') {\n      const iframeSourceWindow = message.source;\n      if (!iframeSourceWindow) return;\n\n      const iframeEl = this.crossOriginIframeMap.get(message.source);\n      if (!iframeEl) return;\n\n      const transformedEvent = this.transformCrossOriginEvent(\n        iframeEl,\n        (message as CrossOriginIframeMessageEvent).data.event,\n      );\n\n      if (transformedEvent)\n        this.wrappedEmit(\n          transformedEvent,\n          (message as CrossOriginIframeMessageEvent).data.isCheckout,\n        );\n    }\n  }\n\n  private transformCrossOriginEvent(\n    iframeEl: HTMLIFrameElement,\n    e: eventWithTime,\n  ): eventWithTime | false {\n    switch (e.type) {\n      case EventType.FullSnapshot: {\n        this.crossOriginIframeMirror.reset(iframeEl);\n        this.crossOriginIframeStyleMirror.reset(iframeEl);\n        /**\n         * Replaces the original id of the iframe with a new set of unique ids\n         */\n        this.replaceIdOnNode(e.data.node, iframeEl);\n        return {\n          timestamp: e.timestamp,\n          type: EventType.IncrementalSnapshot,\n          data: {\n            source: IncrementalSource.Mutation,\n            adds: [\n              {\n                parentId: this.mirror.getId(iframeEl),\n                nextId: null,\n                node: e.data.node,\n              },\n            ],\n            removes: [],\n            texts: [],\n            attributes: [],\n            isAttachIframe: true,\n          },\n        };\n      }\n      case EventType.Meta:\n      case EventType.Load:\n      case EventType.DomContentLoaded: {\n        return false;\n      }\n      case EventType.Plugin: {\n        return e;\n      }\n      case EventType.Custom: {\n        this.replaceIds(\n          e.data.payload as {\n            id?: unknown;\n            parentId?: unknown;\n            previousId?: unknown;\n            nextId?: unknown;\n          },\n          iframeEl,\n          ['id', 'parentId', 'previousId', 'nextId'],\n        );\n        return e;\n      }\n      case EventType.IncrementalSnapshot: {\n        switch (e.data.source) {\n          case IncrementalSource.Mutation: {\n            e.data.adds.forEach((n) => {\n              this.replaceIds(n, iframeEl, [\n                'parentId',\n                'nextId',\n                'previousId',\n              ]);\n              this.replaceIdOnNode(n.node, iframeEl);\n            });\n            e.data.removes.forEach((n) => {\n              this.replaceIds(n, iframeEl, ['parentId', 'id']);\n            });\n            e.data.attributes.forEach((n) => {\n              this.replaceIds(n, iframeEl, ['id']);\n            });\n            e.data.texts.forEach((n) => {\n              this.replaceIds(n, iframeEl, ['id']);\n            });\n            return e;\n          }\n          case IncrementalSource.Drag:\n          case IncrementalSource.TouchMove:\n          case IncrementalSource.MouseMove: {\n            e.data.positions.forEach((p) => {\n              this.replaceIds(p, iframeEl, ['id']);\n            });\n            return e;\n          }\n          case IncrementalSource.ViewportResize: {\n            // can safely ignore these events\n            return false;\n          }\n          case IncrementalSource.MediaInteraction:\n          case IncrementalSource.MouseInteraction:\n          case IncrementalSource.Scroll:\n          case IncrementalSource.CanvasMutation:\n          case IncrementalSource.Input: {\n            this.replaceIds(e.data, iframeEl, ['id']);\n            return e;\n          }\n          case IncrementalSource.StyleSheetRule:\n          case IncrementalSource.StyleDeclaration: {\n            this.replaceIds(e.data, iframeEl, ['id']);\n            this.replaceStyleIds(e.data, iframeEl, ['styleId']);\n            return e;\n          }\n          case IncrementalSource.Font: {\n            // fine as-is no modification needed\n            return e;\n          }\n          case IncrementalSource.Selection: {\n            e.data.ranges.forEach((range) => {\n              this.replaceIds(range, iframeEl, ['start', 'end']);\n            });\n            return e;\n          }\n          case IncrementalSource.AdoptedStyleSheet: {\n            this.replaceIds(e.data, iframeEl, ['id']);\n            this.replaceStyleIds(e.data, iframeEl, ['styleIds']);\n            e.data.styles?.forEach((style) => {\n              this.replaceStyleIds(style, iframeEl, ['styleId']);\n            });\n            return e;\n          }\n        }\n      }\n    }\n  }\n\n  private replace<T extends Record<string, unknown>>(\n    iframeMirror: CrossOriginIframeMirror,\n    obj: T,\n    iframeEl: HTMLIFrameElement,\n    keys: Array<keyof T>,\n  ): T {\n    for (const key of keys) {\n      if (!Array.isArray(obj[key]) && typeof obj[key] !== 'number') continue;\n      if (Array.isArray(obj[key])) {\n        obj[key] = iframeMirror.getIds(\n          iframeEl,\n          obj[key] as number[],\n        ) as T[keyof T];\n      } else {\n        (obj[key] as number) = iframeMirror.getId(iframeEl, obj[key] as number);\n      }\n    }\n\n    return obj;\n  }\n\n  private replaceIds<T extends Record<string, unknown>>(\n    obj: T,\n    iframeEl: HTMLIFrameElement,\n    keys: Array<keyof T>,\n  ): T {\n    return this.replace(this.crossOriginIframeMirror, obj, iframeEl, keys);\n  }\n\n  private replaceStyleIds<T extends Record<string, unknown>>(\n    obj: T,\n    iframeEl: HTMLIFrameElement,\n    keys: Array<keyof T>,\n  ): T {\n    return this.replace(this.crossOriginIframeStyleMirror, obj, iframeEl, keys);\n  }\n\n  private replaceIdOnNode(\n    node: serializedNodeWithId,\n    iframeEl: HTMLIFrameElement,\n  ) {\n    this.replaceIds(node, iframeEl, ['id']);\n    if ('childNodes' in node) {\n      node.childNodes.forEach((child) => {\n        this.replaceIdOnNode(child, iframeEl);\n      });\n    }\n  }\n}\n", "import type { Mu<PERSON>BufferParam } from '../types';\nimport type {\n  mutationCallBack,\n  scrollCallback,\n  SamplingStrategy,\n} from '@rrweb/types';\nimport {\n  initMutationObserver,\n  initScrollObserver,\n  initAdoptedStyleSheetObserver,\n} from './observer';\nimport { patch } from '../utils';\nimport type { Mirror } from 'rrweb-snapshot';\nimport { isNativeShadowDom } from 'rrweb-snapshot';\n\ntype BypassOptions = Omit<\n  MutationBufferParam,\n  'doc' | 'mutationCb' | 'mirror' | 'shadowDomManager'\n> & {\n  sampling: SamplingStrategy;\n};\n\nexport class ShadowDomManager {\n  private shadowDoms = new WeakSet<ShadowRoot>();\n  private mutationCb: mutationCallBack;\n  private scrollCb: scrollCallback;\n  private bypassOptions: BypassOptions;\n  private mirror: Mirror;\n  private restorePatches: (() => void)[] = [];\n\n  constructor(options: {\n    mutationCb: mutationCallBack;\n    scrollCb: scrollCallback;\n    bypassOptions: BypassOptions;\n    mirror: Mirror;\n  }) {\n    this.mutationCb = options.mutationCb;\n    this.scrollCb = options.scrollCb;\n    this.bypassOptions = options.bypassOptions;\n    this.mirror = options.mirror;\n\n    // Patch 'attachShadow' to observe newly added shadow doms.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const manager = this;\n    this.restorePatches.push(\n      patch(\n        Element.prototype,\n        'attachShadow',\n        function (original: (init: ShadowRootInit) => ShadowRoot) {\n          return function (this: HTMLElement, option: ShadowRootInit) {\n            const shadowRoot = original.call(this, option);\n            if (this.shadowRoot)\n              manager.addShadowRoot(this.shadowRoot, this.ownerDocument);\n            return shadowRoot;\n          };\n        },\n      ),\n    );\n  }\n\n  public addShadowRoot(shadowRoot: ShadowRoot, doc: Document) {\n    if (!isNativeShadowDom(shadowRoot)) return;\n    if (this.shadowDoms.has(shadowRoot)) return;\n    this.shadowDoms.add(shadowRoot);\n    initMutationObserver(\n      {\n        ...this.bypassOptions,\n        doc,\n        mutationCb: this.mutationCb,\n        mirror: this.mirror,\n        shadowDomManager: this,\n      },\n      shadowRoot,\n    );\n    initScrollObserver({\n      ...this.bypassOptions,\n      scrollCb: this.scrollCb,\n      // https://gist.github.com/praveenpuglia/0832da687ed5a5d7a0907046c9ef1813\n      // scroll is not allowed to pass the boundary, so we need to listen the shadow document\n      doc: (shadowRoot as unknown) as Document,\n      mirror: this.mirror,\n    });\n    // Defer this to avoid adoptedStyleSheet events being created before the full snapshot is created or attachShadow action is recorded.\n    setTimeout(() => {\n      if (\n        shadowRoot.adoptedStyleSheets &&\n        shadowRoot.adoptedStyleSheets.length > 0\n      )\n        this.bypassOptions.stylesheetManager.adoptStyleSheets(\n          shadowRoot.adoptedStyleSheets,\n          this.mirror.getId(shadowRoot.host),\n        );\n      initAdoptedStyleSheetObserver(\n        {\n          mirror: this.mirror,\n          stylesheetManager: this.bypassOptions.stylesheetManager,\n        },\n        shadowRoot,\n      );\n    }, 0);\n  }\n\n  /**\n   * Monkey patch 'attachShadow' of an IFrameElement to observe newly added shadow doms.\n   */\n  public observeAttachShadow(iframeElement: HTMLIFrameElement) {\n    if (iframeElement.contentWindow) {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const manager = this;\n      this.restorePatches.push(\n        patch(\n          (iframeElement.contentWindow as Window & {\n            HTMLElement: { prototype: HTMLElement };\n          }).HTMLElement.prototype,\n          'attachShadow',\n          function (original: (init: ShadowRootInit) => ShadowRoot) {\n            return function (this: HTMLElement, option: ShadowRootInit) {\n              const shadowRoot = original.call(this, option);\n              if (this.shadowRoot)\n                manager.addShadowRoot(\n                  this.shadowRoot,\n                  iframeElement.contentDocument as Document,\n                );\n              return shadowRoot;\n            };\n          },\n        ),\n      );\n    }\n  }\n\n  public reset() {\n    this.restorePatches.forEach((restorePatch) => restorePatch());\n    this.shadowDoms = new WeakSet();\n  }\n}\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2021 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { encode } from 'base64-arraybuffer';\nimport type { IWindow, CanvasArg } from '@rrweb/types';\n\n// TODO: unify with `replay/webgl.ts`\ntype CanvasVarMap = Map<string, unknown[]>;\nconst canvasVarMap: Map<RenderingContext, CanvasVarMap> = new Map();\nexport function variableListFor(ctx: RenderingContext, ctor: string) {\n  let contextMap = canvasVarMap.get(ctx);\n  if (!contextMap) {\n    contextMap = new Map();\n    canvasVarMap.set(ctx, contextMap);\n  }\n  if (!contextMap.has(ctor)) {\n    contextMap.set(ctor, []);\n  }\n  return contextMap.get(ctor) as unknown[];\n}\n\nexport const saveWebGLVar = (\n  value: unknown,\n  win: IWindow,\n  ctx: RenderingContext,\n): number | void => {\n  if (\n    !value ||\n    !(isInstanceOfWebGLObject(value, win) || typeof value === 'object')\n  )\n    return;\n\n  const name = value.constructor.name;\n  const list = variableListFor(ctx, name);\n  let index = list.indexOf(value);\n\n  if (index === -1) {\n    index = list.length;\n    list.push(value);\n  }\n  return index;\n};\n\n// from webgl-recorder: https://github.com/evanw/webgl-recorder/blob/bef0e65596e981ee382126587e2dcbe0fc7748e2/webgl-recorder.js#L50-L77\nexport function serializeArg(\n  value: unknown,\n  win: IWindow,\n  ctx: RenderingContext,\n): CanvasArg {\n  if (value instanceof Array) {\n    return value.map((arg) => serializeArg(arg, win, ctx));\n  } else if (value === null) {\n    return value;\n  } else if (\n    value instanceof Float32Array ||\n    value instanceof Float64Array ||\n    value instanceof Int32Array ||\n    value instanceof Uint32Array ||\n    value instanceof Uint8Array ||\n    value instanceof Uint16Array ||\n    value instanceof Int16Array ||\n    value instanceof Int8Array ||\n    value instanceof Uint8ClampedArray\n  ) {\n    const name = value.constructor.name;\n    return {\n      rr_type: name,\n      args: [Object.values(value)],\n    };\n  } else if (\n    // SharedArrayBuffer disabled on most browsers due to spectre.\n    // More info: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/SharedArrayBuffer/SharedArrayBuffer\n    // value instanceof SharedArrayBuffer ||\n    value instanceof ArrayBuffer\n  ) {\n    const name = value.constructor.name as 'ArrayBuffer';\n    const base64 = encode(value);\n\n    return {\n      rr_type: name,\n      base64,\n    };\n  } else if (value instanceof DataView) {\n    const name = value.constructor.name;\n    return {\n      rr_type: name,\n      args: [\n        serializeArg(value.buffer, win, ctx),\n        value.byteOffset,\n        value.byteLength,\n      ],\n    };\n  } else if (value instanceof HTMLImageElement) {\n    const name = value.constructor.name;\n    const { src } = value;\n    return {\n      rr_type: name,\n      src,\n    };\n  } else if (value instanceof HTMLCanvasElement) {\n    const name = 'HTMLImageElement';\n    // TODO: move `toDataURL` to web worker if possible\n    const src = value.toDataURL(); // heavy on large canvas\n    return {\n      rr_type: name,\n      src,\n    };\n  } else if (value instanceof ImageData) {\n    const name = value.constructor.name;\n    return {\n      rr_type: name,\n      args: [serializeArg(value.data, win, ctx), value.width, value.height],\n    };\n    // } else if (value instanceof Blob) {\n    //   const name = value.constructor.name;\n    //   return {\n    //     rr_type: name,\n    //     data: [serializeArg(await value.arrayBuffer(), win, ctx)],\n    //     type: value.type,\n    //   };\n  } else if (isInstanceOfWebGLObject(value, win) || typeof value === 'object') {\n    const name = value.constructor.name;\n    const index = saveWebGLVar(value, win, ctx) as number;\n\n    return {\n      rr_type: name,\n      index: index,\n    };\n  }\n\n  return value as CanvasArg;\n}\n\nexport const serializeArgs = (\n  args: Array<unknown>,\n  win: IWindow,\n  ctx: RenderingContext,\n) => {\n  return [...args].map((arg) => serializeArg(arg, win, ctx));\n};\n\nexport const isInstanceOfWebGLObject = (\n  value: unknown,\n  win: IWindow,\n): value is\n  | WebGLActiveInfo\n  | WebGLBuffer\n  | WebGLFramebuffer\n  | WebGLProgram\n  | WebGLRenderbuffer\n  | WebGLShader\n  | WebGLShaderPrecisionFormat\n  | WebGLTexture\n  | WebGLUniformLocation\n  | WebGLVertexArrayObject => {\n  const webGLConstructorNames: string[] = [\n    'WebGLActiveInfo',\n    'WebGLBuffer',\n    'WebGLFramebuffer',\n    'WebGLProgram',\n    'WebGLRenderbuffer',\n    'WebGLShader',\n    'WebGLShaderPrecisionFormat',\n    'WebGLTexture',\n    'WebGLUniformLocation',\n    'WebGLVertexArrayObject',\n    // In old Chrome versions, value won't be an instanceof WebGLVertexArrayObject.\n    'WebGLVertexArrayObjectOES',\n  ];\n  const supportedWebGLConstructorNames = webGLConstructorNames.filter(\n    (name: string) => typeof win[name as keyof Window] === 'function',\n  );\n  return Boolean(\n    supportedWebGLConstructorNames.find(\n      (name: string) => value instanceof win[name as keyof Window],\n    ),\n  );\n};\n", "import type { Mirror } from 'rrweb-snapshot';\nimport {\n  blockClass,\n  CanvasContext,\n  canvasManagerMutationCallback,\n  IWindow,\n  listenerHandler,\n} from '@rrweb/types';\nimport { hookSetter, isBlocked, patch } from '../../../utils';\nimport { serializeArgs } from './serialize-args';\n\nexport default function initCanvas2DMutationObserver(\n  cb: canvasManagerMutationCallback,\n  win: IWindow,\n  blockClass: blockClass,\n  blockSelector: string | null,\n): listenerHandler {\n  const handlers: listenerHandler[] = [];\n  const props2D = Object.getOwnPropertyNames(\n    win.CanvasRenderingContext2D.prototype,\n  );\n  for (const prop of props2D) {\n    try {\n      if (\n        typeof win.CanvasRenderingContext2D.prototype[\n          prop as keyof CanvasRenderingContext2D\n        ] !== 'function'\n      ) {\n        continue;\n      }\n      const restoreHandler = patch(\n        win.CanvasRenderingContext2D.prototype,\n        prop,\n        function (\n          original: (\n            this: CanvasRenderingContext2D,\n            ...args: unknown[]\n          ) => void,\n        ) {\n          return function (\n            this: CanvasRenderingContext2D,\n            ...args: Array<unknown>\n          ) {\n            if (!isBlocked(this.canvas, blockClass, blockSelector, true)) {\n              // Using setTimeout as toDataURL can be heavy\n              // and we'd rather not block the main thread\n              setTimeout(() => {\n                const recordArgs = serializeArgs([...args], win, this);\n                cb(this.canvas, {\n                  type: CanvasContext['2D'],\n                  property: prop,\n                  args: recordArgs,\n                });\n              }, 0);\n            }\n            return original.apply(this, args);\n          };\n        },\n      );\n      handlers.push(restoreHandler);\n    } catch {\n      const hookHandler = hookSetter<CanvasRenderingContext2D>(\n        win.CanvasRenderingContext2D.prototype,\n        prop,\n        {\n          set(v) {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access\n            cb(this.canvas, {\n              type: CanvasContext['2D'],\n              property: prop,\n              args: [v],\n              setter: true,\n            });\n          },\n        },\n      );\n      handlers.push(hookHandler);\n    }\n  }\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n", "import type { ICanvas } from 'rrweb-snapshot';\nimport type { blockClass, IWindow, listenerHandler } from '@rrweb/types';\nimport { isBlocked, patch } from '../../../utils';\n\nexport default function initCanvasContextObserver(\n  win: IWindow,\n  blockClass: blockClass,\n  blockSelector: string | null,\n): listenerHandler {\n  const handlers: listenerHandler[] = [];\n  try {\n    const restoreHandler = patch(\n      win.HTMLCanvasElement.prototype,\n      'getContext',\n      function (\n        original: (\n          this: ICanvas,\n          contextType: string,\n          ...args: Array<unknown>\n        ) => void,\n      ) {\n        return function (\n          this: ICanvas,\n          contextType: string,\n          ...args: Array<unknown>\n        ) {\n          if (!isBlocked(this, blockClass, blockSelector, true)) {\n            if (!('__context' in this)) this.__context = contextType;\n          }\n          return original.apply(this, [contextType, ...args]);\n        };\n      },\n    );\n    handlers.push(restoreHandler);\n  } catch {\n    console.error('failed to patch HTMLCanvasElement.prototype.getContext');\n  }\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n", "import type { Mirror } from 'rrweb-snapshot';\nimport {\n  blockClass,\n  CanvasContext,\n  canvasManagerMutationCallback,\n  canvasMutationWithType,\n  IWindow,\n  listenerHandler,\n} from '@rrweb/types';\nimport { hookSetter, isBlocked, patch } from '../../../utils';\nimport { saveWebGLVar, serializeArgs } from './serialize-args';\n\nfunction patchGLPrototype(\n  prototype: WebGLRenderingContext | WebGL2RenderingContext,\n  type: CanvasContext,\n  cb: canvasManagerMutationCallback,\n  blockClass: blockClass,\n  blockSelector: string | null,\n  mirror: Mirror,\n  win: IWindow,\n): listenerHandler[] {\n  const handlers: listenerHandler[] = [];\n\n  const props = Object.getOwnPropertyNames(prototype);\n\n  for (const prop of props) {\n    if (\n      //prop.startsWith('get') ||  // e.g. getProgramParameter, but too risky\n      [\n        'isContextLost',\n        'canvas',\n        'drawingBufferWidth',\n        'drawingBufferHeight',\n      ].includes(prop)\n    ) {\n      // skip read only propery/functions\n      continue;\n    }\n    try {\n      if (typeof prototype[prop as keyof typeof prototype] !== 'function') {\n        continue;\n      }\n      const restoreHandler = patch(\n        prototype,\n        prop,\n        function (\n          original: (this: typeof prototype, ...args: Array<unknown>) => void,\n        ) {\n          return function (this: typeof prototype, ...args: Array<unknown>) {\n            const result = original.apply(this, args);\n            saveWebGLVar(result, win, this);\n            if (!isBlocked(this.canvas, blockClass, blockSelector, true)) {\n              const recordArgs = serializeArgs([...args], win, this);\n              const mutation: canvasMutationWithType = {\n                type,\n                property: prop,\n                args: recordArgs,\n              };\n              // TODO: this could potentially also be an OffscreenCanvas as well as HTMLCanvasElement\n              cb(this.canvas, mutation);\n            }\n\n            return result;\n          };\n        },\n      );\n      handlers.push(restoreHandler);\n    } catch {\n      const hookHandler = hookSetter<typeof prototype>(prototype, prop, {\n        set(v) {\n          // TODO: this could potentially also be an OffscreenCanvas as well as HTMLCanvasElement\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          cb(this.canvas as HTMLCanvasElement, {\n            type,\n            property: prop,\n            args: [v],\n            setter: true,\n          });\n        },\n      });\n      handlers.push(hookHandler);\n    }\n  }\n\n  return handlers;\n}\n\nexport default function initCanvasWebGLMutationObserver(\n  cb: canvasManagerMutationCallback,\n  win: IWindow,\n  blockClass: blockClass,\n  blockSelector: string | null,\n  mirror: Mirror,\n): listenerHandler {\n  const handlers: listenerHandler[] = [];\n\n  handlers.push(\n    ...patchGLPrototype(\n      win.WebGLRenderingContext.prototype,\n      CanvasContext.WebGL,\n      cb,\n      blockClass,\n      blockSelector,\n      mirror,\n      win,\n    ),\n  );\n\n  if (typeof win.WebGL2RenderingContext !== 'undefined') {\n    handlers.push(\n      ...patchGLPrototype(\n        win.WebGL2RenderingContext.prototype,\n        CanvasContext.WebGL2,\n        cb,\n        blockClass,\n        blockSelector,\n        mirror,\n        win,\n      ),\n    );\n  }\n\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\n", "import type { ICanvas, Mirror, DataURLOptions } from 'rrweb-snapshot';\nimport type {\n  blockClass,\n  canvasManagerMutationCallback,\n  canvasMutationCallback,\n  canvasMutationCommand,\n  canvasMutationWithType,\n  IWindow,\n  listenerHandler,\n  CanvasArg,\n} from '@rrweb/types';\nimport { isBlocked } from '../../../utils';\nimport { CanvasContext } from '@rrweb/types';\nimport initCanvas2DMutationObserver from './2d';\nimport initCanvasContextObserver from './canvas';\nimport initCanvasWebGLMutationObserver from './webgl';\nimport ImageBitmapDataURLWorker from 'web-worker:../../workers/image-bitmap-data-url-worker.ts';\nimport type { ImageBitmapDataURLRequestWorker } from '../../workers/image-bitmap-data-url-worker';\n\nexport type RafStamps = { latestId: number; invokeId: number | null };\n\ntype pendingCanvasMutationsMap = Map<\n  HTMLCanvasElement,\n  canvasMutationWithType[]\n>;\n\nexport class CanvasManager {\n  private pendingCanvasMutations: pendingCanvasMutationsMap = new Map();\n  private rafStamps: RafStamps = { latestId: 0, invokeId: null };\n  private mirror: Mirror;\n\n  private mutationCb: canvasMutationCallback;\n  private resetObservers?: listenerHandler;\n  private frozen = false;\n  private locked = false;\n\n  public reset() {\n    this.pendingCanvasMutations.clear();\n    this.resetObservers && this.resetObservers();\n  }\n\n  public freeze() {\n    this.frozen = true;\n  }\n\n  public unfreeze() {\n    this.frozen = false;\n  }\n\n  public lock() {\n    this.locked = true;\n  }\n\n  public unlock() {\n    this.locked = false;\n  }\n\n  constructor(options: {\n    recordCanvas: boolean;\n    mutationCb: canvasMutationCallback;\n    win: IWindow;\n    blockClass: blockClass;\n    blockSelector: string | null;\n    mirror: Mirror;\n    sampling?: 'all' | number;\n    dataURLOptions: DataURLOptions;\n  }) {\n    const {\n      sampling = 'all',\n      win,\n      blockClass,\n      blockSelector,\n      recordCanvas,\n      dataURLOptions,\n    } = options;\n    this.mutationCb = options.mutationCb;\n    this.mirror = options.mirror;\n\n    if (recordCanvas && sampling === 'all')\n      this.initCanvasMutationObserver(win, blockClass, blockSelector);\n    if (recordCanvas && typeof sampling === 'number')\n      this.initCanvasFPSObserver(sampling, win, blockClass, blockSelector, {\n        dataURLOptions,\n      });\n  }\n\n  private processMutation: canvasManagerMutationCallback = (\n    target,\n    mutation,\n  ) => {\n    const newFrame =\n      this.rafStamps.invokeId &&\n      this.rafStamps.latestId !== this.rafStamps.invokeId;\n    if (newFrame || !this.rafStamps.invokeId)\n      this.rafStamps.invokeId = this.rafStamps.latestId;\n\n    if (!this.pendingCanvasMutations.has(target)) {\n      this.pendingCanvasMutations.set(target, []);\n    }\n\n    this.pendingCanvasMutations.get(target)!.push(mutation);\n  };\n\n  private initCanvasFPSObserver(\n    fps: number,\n    win: IWindow,\n    blockClass: blockClass,\n    blockSelector: string | null,\n    options: {\n      dataURLOptions: DataURLOptions;\n    },\n  ) {\n    const canvasContextReset = initCanvasContextObserver(\n      win,\n      blockClass,\n      blockSelector,\n    );\n    const snapshotInProgressMap: Map<number, boolean> = new Map();\n    const worker = new ImageBitmapDataURLWorker() as ImageBitmapDataURLRequestWorker;\n    worker.onmessage = (e) => {\n      const { id } = e.data;\n      snapshotInProgressMap.set(id, false);\n\n      if (!('base64' in e.data)) return;\n\n      const { base64, type, width, height } = e.data;\n      this.mutationCb({\n        id,\n        type: CanvasContext['2D'],\n        commands: [\n          {\n            property: 'clearRect', // wipe canvas\n            args: [0, 0, width, height],\n          },\n          {\n            property: 'drawImage', // draws (semi-transparent) image\n            args: [\n              {\n                rr_type: 'ImageBitmap',\n                args: [\n                  {\n                    rr_type: 'Blob',\n                    data: [{ rr_type: 'ArrayBuffer', base64 }],\n                    type,\n                  },\n                ],\n              } as CanvasArg,\n              0,\n              0,\n            ],\n          },\n        ],\n      });\n    };\n\n    const timeBetweenSnapshots = 1000 / fps;\n    let lastSnapshotTime = 0;\n    let rafId: number;\n\n    const getCanvas = (): HTMLCanvasElement[] => {\n      const matchedCanvas: HTMLCanvasElement[] = [];\n      win.document.querySelectorAll('canvas').forEach((canvas) => {\n        if (!isBlocked(canvas, blockClass, blockSelector, true)) {\n          matchedCanvas.push(canvas);\n        }\n      });\n      return matchedCanvas;\n    };\n\n    const takeCanvasSnapshots = (timestamp: DOMHighResTimeStamp) => {\n      if (\n        lastSnapshotTime &&\n        timestamp - lastSnapshotTime < timeBetweenSnapshots\n      ) {\n        rafId = requestAnimationFrame(takeCanvasSnapshots);\n        return;\n      }\n      lastSnapshotTime = timestamp;\n\n      getCanvas()\n        // eslint-disable-next-line @typescript-eslint/no-misused-promises\n        .forEach(async (canvas: HTMLCanvasElement) => {\n          const id = this.mirror.getId(canvas);\n          if (snapshotInProgressMap.get(id)) return;\n          snapshotInProgressMap.set(id, true);\n          if (['webgl', 'webgl2'].includes((canvas as ICanvas).__context)) {\n            // if the canvas hasn't been modified recently,\n            // its contents won't be in memory and `createImageBitmap`\n            // will return a transparent imageBitmap\n\n            const context = canvas.getContext((canvas as ICanvas).__context) as\n              | WebGLRenderingContext\n              | WebGL2RenderingContext\n              | null;\n            if (\n              context?.getContextAttributes()?.preserveDrawingBuffer === false\n            ) {\n              // Hack to load canvas back into memory so `createImageBitmap` can grab it's contents.\n              // Context: https://twitter.com/Juice10/status/1499775271758704643\n              // This hack might change the background color of the canvas in the unlikely event that\n              // the canvas background was changed but clear was not called directly afterwards.\n              context?.clear(context.COLOR_BUFFER_BIT);\n            }\n          }\n          const bitmap = await createImageBitmap(canvas);\n          worker.postMessage(\n            {\n              id,\n              bitmap,\n              width: canvas.width,\n              height: canvas.height,\n              dataURLOptions: options.dataURLOptions,\n            },\n            [bitmap],\n          );\n        });\n      rafId = requestAnimationFrame(takeCanvasSnapshots);\n    };\n\n    rafId = requestAnimationFrame(takeCanvasSnapshots);\n\n    this.resetObservers = () => {\n      canvasContextReset();\n      cancelAnimationFrame(rafId);\n    };\n  }\n\n  private initCanvasMutationObserver(\n    win: IWindow,\n    blockClass: blockClass,\n    blockSelector: string | null,\n  ): void {\n    this.startRAFTimestamping();\n    this.startPendingCanvasMutationFlusher();\n\n    const canvasContextReset = initCanvasContextObserver(\n      win,\n      blockClass,\n      blockSelector,\n    );\n    const canvas2DReset = initCanvas2DMutationObserver(\n      this.processMutation.bind(this),\n      win,\n      blockClass,\n      blockSelector,\n    );\n\n    const canvasWebGL1and2Reset = initCanvasWebGLMutationObserver(\n      this.processMutation.bind(this),\n      win,\n      blockClass,\n      blockSelector,\n      this.mirror,\n    );\n\n    this.resetObservers = () => {\n      canvasContextReset();\n      canvas2DReset();\n      canvasWebGL1and2Reset();\n    };\n  }\n\n  private startPendingCanvasMutationFlusher() {\n    requestAnimationFrame(() => this.flushPendingCanvasMutations());\n  }\n\n  private startRAFTimestamping() {\n    const setLatestRAFTimestamp = (timestamp: DOMHighResTimeStamp) => {\n      this.rafStamps.latestId = timestamp;\n      requestAnimationFrame(setLatestRAFTimestamp);\n    };\n    requestAnimationFrame(setLatestRAFTimestamp);\n  }\n\n  flushPendingCanvasMutations() {\n    this.pendingCanvasMutations.forEach(\n      (values: canvasMutationCommand[], canvas: HTMLCanvasElement) => {\n        const id = this.mirror.getId(canvas);\n        this.flushPendingCanvasMutationFor(canvas, id);\n      },\n    );\n    requestAnimationFrame(() => this.flushPendingCanvasMutations());\n  }\n\n  flushPendingCanvasMutationFor(canvas: HTMLCanvasElement, id: number) {\n    if (this.frozen || this.locked) {\n      return;\n    }\n\n    const valuesWithType = this.pendingCanvasMutations.get(canvas);\n    if (!valuesWithType || id === -1) return;\n\n    const values = valuesWithType.map((value) => {\n      const { type, ...rest } = value;\n      return rest;\n    });\n    const { type } = valuesWithType[0];\n\n    this.mutationCb({ id, type, commands: values });\n\n    this.pendingCanvasMutations.delete(canvas);\n  }\n}\n", "import type { elementNode, serializedNodeWithId } from 'rrweb-snapshot';\nimport { getCssRuleString } from 'rrweb-snapshot';\nimport type {\n  adoptedStyleSheetCallback,\n  adoptedStyleSheetParam,\n  attributeMutation,\n  mutationCallBack,\n} from '@rrweb/types';\nimport { StyleSheetMirror } from '../utils';\n\nexport class StylesheetManager {\n  private trackedLinkElements: WeakSet<HTMLLinkElement> = new WeakSet();\n  private mutationCb: mutationCallBack;\n  private adoptedStyleSheetCb: adoptedStyleSheetCallback;\n  public styleMirror = new StyleSheetMirror();\n\n  constructor(options: {\n    mutationCb: mutationCallBack;\n    adoptedStyleSheetCb: adoptedStyleSheetCallback;\n  }) {\n    this.mutationCb = options.mutationCb;\n    this.adoptedStyleSheetCb = options.adoptedStyleSheetCb;\n  }\n\n  public attachLinkElement(\n    linkEl: HTMLLinkElement,\n    childSn: serializedNodeWithId,\n  ) {\n    if ('_cssText' in (childSn as elementNode).attributes)\n      this.mutationCb({\n        adds: [],\n        removes: [],\n        texts: [],\n        attributes: [\n          {\n            id: childSn.id,\n            attributes: (childSn as elementNode)\n              .attributes as attributeMutation['attributes'],\n          },\n        ],\n      });\n\n    this.trackLinkElement(linkEl);\n  }\n\n  public trackLinkElement(linkEl: HTMLLinkElement) {\n    if (this.trackedLinkElements.has(linkEl)) return;\n\n    this.trackedLinkElements.add(linkEl);\n    this.trackStylesheetInLinkElement(linkEl);\n  }\n\n  public adoptStyleSheets(sheets: CSSStyleSheet[], hostId: number) {\n    if (sheets.length === 0) return;\n    const adoptedStyleSheetData: adoptedStyleSheetParam = {\n      id: hostId,\n      styleIds: [] as number[],\n    };\n    const styles: NonNullable<adoptedStyleSheetParam['styles']> = [];\n    for (const sheet of sheets) {\n      let styleId;\n      if (!this.styleMirror.has(sheet)) {\n        styleId = this.styleMirror.add(sheet);\n        const rules = Array.from(sheet.rules || CSSRule);\n        styles.push({\n          styleId,\n          rules: rules.map((r, index) => {\n            return {\n              rule: getCssRuleString(r),\n              index,\n            };\n          }),\n        });\n      } else styleId = this.styleMirror.getId(sheet);\n      adoptedStyleSheetData.styleIds.push(styleId);\n    }\n    if (styles.length > 0) adoptedStyleSheetData.styles = styles;\n    this.adoptedStyleSheetCb(adoptedStyleSheetData);\n  }\n\n  public reset() {\n    this.styleMirror.reset();\n    this.trackedLinkElements = new WeakSet();\n  }\n\n  // TODO: take snapshot on stylesheet reload by applying event listener\n  private trackStylesheetInLinkElement(linkEl: HTMLLinkElement) {\n    // linkEl.addEventListener('load', () => {\n    //   // re-loaded, maybe take another snapshot?\n    // });\n  }\n}\n", "import {\n  snapshot,\n  MaskInputOptions,\n  SlimDOMOptions,\n  createMirror,\n} from 'rrweb-snapshot';\nimport { initObservers, mutationBuffers } from './observer';\nimport {\n  on,\n  getWindowWidth,\n  getWindowHeight,\n  polyfill,\n  hasShadowRoot,\n  isSerializedIframe,\n  isSerializedStylesheet,\n} from '../utils';\nimport type { recordOptions } from '../types';\nimport {\n  EventType,\n  event,\n  eventWithTime,\n  IncrementalSource,\n  listenerHandler,\n  mutationCallbackParam,\n  scrollCallback,\n  canvasMutationParam,\n  adoptedStyleSheetParam,\n} from '@rrweb/types';\nimport type { CrossOriginIframeMessageEventContent } from '../types';\nimport { IframeManager } from './iframe-manager';\nimport { ShadowDomManager } from './shadow-dom-manager';\nimport { CanvasManager } from './observers/canvas/canvas-manager';\nimport { StylesheetManager } from './stylesheet-manager';\n\nfunction wrapEvent(e: event): eventWithTime {\n  return {\n    ...e,\n    timestamp: Date.now(),\n  };\n}\n\nlet wrappedEmit!: (e: eventWithTime, isCheckout?: boolean) => void;\n\nlet takeFullSnapshot!: (isCheckout?: boolean) => void;\nlet canvasManager!: CanvasManager;\nlet recording = false;\n\nconst mirror = createMirror();\nfunction record<T = eventWithTime>(\n  options: recordOptions<T> = {},\n): listenerHandler | undefined {\n  const {\n    emit,\n    checkoutEveryNms,\n    checkoutEveryNth,\n    blockClass = 'rr-block',\n    blockSelector = null,\n    ignoreClass = 'rr-ignore',\n    maskTextClass = 'rr-mask',\n    maskTextSelector = null,\n    inlineStylesheet = true,\n    maskAllInputs,\n    maskInputOptions: _maskInputOptions,\n    slimDOMOptions: _slimDOMOptions,\n    maskInputFn,\n    maskTextFn,\n    hooks,\n    packFn,\n    sampling = {},\n    dataURLOptions = {},\n    mousemoveWait,\n    recordCanvas = false,\n    recordCrossOriginIframes = false,\n    userTriggeredOnInput = false,\n    collectFonts = false,\n    inlineImages = false,\n    plugins,\n    keepIframeSrcFn = () => false,\n    ignoreCSSAttributes = new Set([]),\n  } = options;\n\n  const inEmittingFrame = recordCrossOriginIframes\n    ? window.parent === window\n    : true;\n\n  let passEmitsToParent = false;\n  if (!inEmittingFrame) {\n    try {\n      window.parent.document; // throws if parent is cross-origin\n      passEmitsToParent = false; // if parent is same origin we collect iframe events from the parent\n    } catch (e) {\n      passEmitsToParent = true;\n    }\n  }\n\n  // runtime checks for user options\n  if (inEmittingFrame && !emit) {\n    throw new Error('emit function is required');\n  }\n  // move departed options to new options\n  if (mousemoveWait !== undefined && sampling.mousemove === undefined) {\n    sampling.mousemove = mousemoveWait;\n  }\n\n  // reset mirror in case `record` this was called earlier\n  mirror.reset();\n\n  const maskInputOptions: MaskInputOptions =\n    maskAllInputs === true\n      ? {\n          color: true,\n          date: true,\n          'datetime-local': true,\n          email: true,\n          month: true,\n          number: true,\n          range: true,\n          search: true,\n          tel: true,\n          text: true,\n          time: true,\n          url: true,\n          week: true,\n          textarea: true,\n          select: true,\n          password: true,\n        }\n      : _maskInputOptions !== undefined\n      ? _maskInputOptions\n      : { password: true };\n\n  const slimDOMOptions: SlimDOMOptions =\n    _slimDOMOptions === true || _slimDOMOptions === 'all'\n      ? {\n          script: true,\n          comment: true,\n          headFavicon: true,\n          headWhitespace: true,\n          headMetaSocial: true,\n          headMetaRobots: true,\n          headMetaHttpEquiv: true,\n          headMetaVerification: true,\n          // the following are off for slimDOMOptions === true,\n          // as they destroy some (hidden) info:\n          headMetaAuthorship: _slimDOMOptions === 'all',\n          headMetaDescKeywords: _slimDOMOptions === 'all',\n        }\n      : _slimDOMOptions\n      ? _slimDOMOptions\n      : {};\n\n  polyfill();\n\n  let lastFullSnapshotEvent: eventWithTime;\n  let incrementalSnapshotCount = 0;\n\n  const eventProcessor = (e: eventWithTime): T => {\n    for (const plugin of plugins || []) {\n      if (plugin.eventProcessor) {\n        e = plugin.eventProcessor(e);\n      }\n    }\n    if (packFn) {\n      e = (packFn(e) as unknown) as eventWithTime;\n    }\n    return (e as unknown) as T;\n  };\n  wrappedEmit = (e: eventWithTime, isCheckout?: boolean) => {\n    if (\n      mutationBuffers[0]?.isFrozen() &&\n      e.type !== EventType.FullSnapshot &&\n      !(\n        e.type === EventType.IncrementalSnapshot &&\n        e.data.source === IncrementalSource.Mutation\n      )\n    ) {\n      // we've got a user initiated event so first we need to apply\n      // all DOM changes that have been buffering during paused state\n      mutationBuffers.forEach((buf) => buf.unfreeze());\n    }\n\n    if (inEmittingFrame) {\n      emit?.(eventProcessor(e), isCheckout);\n    } else if (passEmitsToParent) {\n      const message: CrossOriginIframeMessageEventContent<T> = {\n        type: 'rrweb',\n        event: eventProcessor(e),\n        isCheckout,\n      };\n      window.parent.postMessage(message, '*');\n    }\n\n    if (e.type === EventType.FullSnapshot) {\n      lastFullSnapshotEvent = e;\n      incrementalSnapshotCount = 0;\n    } else if (e.type === EventType.IncrementalSnapshot) {\n      // attach iframe should be considered as full snapshot\n      if (\n        e.data.source === IncrementalSource.Mutation &&\n        e.data.isAttachIframe\n      ) {\n        return;\n      }\n\n      incrementalSnapshotCount++;\n      const exceedCount =\n        checkoutEveryNth && incrementalSnapshotCount >= checkoutEveryNth;\n      const exceedTime =\n        checkoutEveryNms &&\n        e.timestamp - lastFullSnapshotEvent.timestamp > checkoutEveryNms;\n      if (exceedCount || exceedTime) {\n        takeFullSnapshot(true);\n      }\n    }\n  };\n\n  const wrappedMutationEmit = (m: mutationCallbackParam) => {\n    wrappedEmit(\n      wrapEvent({\n        type: EventType.IncrementalSnapshot,\n        data: {\n          source: IncrementalSource.Mutation,\n          ...m,\n        },\n      }),\n    );\n  };\n  const wrappedScrollEmit: scrollCallback = (p) =>\n    wrappedEmit(\n      wrapEvent({\n        type: EventType.IncrementalSnapshot,\n        data: {\n          source: IncrementalSource.Scroll,\n          ...p,\n        },\n      }),\n    );\n  const wrappedCanvasMutationEmit = (p: canvasMutationParam) =>\n    wrappedEmit(\n      wrapEvent({\n        type: EventType.IncrementalSnapshot,\n        data: {\n          source: IncrementalSource.CanvasMutation,\n          ...p,\n        },\n      }),\n    );\n\n  const wrappedAdoptedStyleSheetEmit = (a: adoptedStyleSheetParam) =>\n    wrappedEmit(\n      wrapEvent({\n        type: EventType.IncrementalSnapshot,\n        data: {\n          source: IncrementalSource.AdoptedStyleSheet,\n          ...a,\n        },\n      }),\n    );\n\n  const stylesheetManager = new StylesheetManager({\n    mutationCb: wrappedMutationEmit,\n    adoptedStyleSheetCb: wrappedAdoptedStyleSheetEmit,\n  });\n\n  const iframeManager = new IframeManager({\n    mirror,\n    mutationCb: wrappedMutationEmit,\n    stylesheetManager: stylesheetManager,\n    recordCrossOriginIframes,\n    wrappedEmit,\n  });\n\n  /**\n   * Exposes mirror to the plugins\n   */\n  for (const plugin of plugins || []) {\n    if (plugin.getMirror)\n      plugin.getMirror({\n        nodeMirror: mirror,\n        crossOriginIframeMirror: iframeManager.crossOriginIframeMirror,\n        crossOriginIframeStyleMirror:\n          iframeManager.crossOriginIframeStyleMirror,\n      });\n  }\n\n  canvasManager = new CanvasManager({\n    recordCanvas,\n    mutationCb: wrappedCanvasMutationEmit,\n    win: window,\n    blockClass,\n    blockSelector,\n    mirror,\n    sampling: sampling.canvas,\n    dataURLOptions,\n  });\n\n  const shadowDomManager = new ShadowDomManager({\n    mutationCb: wrappedMutationEmit,\n    scrollCb: wrappedScrollEmit,\n    bypassOptions: {\n      blockClass,\n      blockSelector,\n      maskTextClass,\n      maskTextSelector,\n      inlineStylesheet,\n      maskInputOptions,\n      dataURLOptions,\n      maskTextFn,\n      maskInputFn,\n      recordCanvas,\n      inlineImages,\n      sampling,\n      slimDOMOptions,\n      iframeManager,\n      stylesheetManager,\n      canvasManager,\n      keepIframeSrcFn,\n    },\n    mirror,\n  });\n\n  takeFullSnapshot = (isCheckout = false) => {\n    wrappedEmit(\n      wrapEvent({\n        type: EventType.Meta,\n        data: {\n          href: window.location.href,\n          width: getWindowWidth(),\n          height: getWindowHeight(),\n        },\n      }),\n      isCheckout,\n    );\n\n    // When we take a full snapshot, old tracked StyleSheets need to be removed.\n    stylesheetManager.reset();\n\n    mutationBuffers.forEach((buf) => buf.lock()); // don't allow any mirror modifications during snapshotting\n    const node = snapshot(document, {\n      mirror,\n      blockClass,\n      blockSelector,\n      maskTextClass,\n      maskTextSelector,\n      inlineStylesheet,\n      maskAllInputs: maskInputOptions,\n      maskTextFn,\n      slimDOM: slimDOMOptions,\n      dataURLOptions,\n      recordCanvas,\n      inlineImages,\n      onSerialize: (n) => {\n        if (isSerializedIframe(n, mirror)) {\n          iframeManager.addIframe(n as HTMLIFrameElement);\n        }\n        if (isSerializedStylesheet(n, mirror)) {\n          stylesheetManager.trackLinkElement(n as HTMLLinkElement);\n        }\n        if (hasShadowRoot(n)) {\n          shadowDomManager.addShadowRoot(n.shadowRoot, document);\n        }\n      },\n      onIframeLoad: (iframe, childSn) => {\n        iframeManager.attachIframe(iframe, childSn);\n        shadowDomManager.observeAttachShadow(iframe);\n      },\n      onStylesheetLoad: (linkEl, childSn) => {\n        stylesheetManager.attachLinkElement(linkEl, childSn);\n      },\n      keepIframeSrcFn,\n    });\n\n    if (!node) {\n      return console.warn('Failed to snapshot the document');\n    }\n\n    wrappedEmit(\n      wrapEvent({\n        type: EventType.FullSnapshot,\n        data: {\n          node,\n          initialOffset: {\n            left:\n              window.pageXOffset !== undefined\n                ? window.pageXOffset\n                : document?.documentElement.scrollLeft ||\n                  document?.body?.parentElement?.scrollLeft ||\n                  document?.body?.scrollLeft ||\n                  0,\n            top:\n              window.pageYOffset !== undefined\n                ? window.pageYOffset\n                : document?.documentElement.scrollTop ||\n                  document?.body?.parentElement?.scrollTop ||\n                  document?.body?.scrollTop ||\n                  0,\n          },\n        },\n      }),\n    );\n    mutationBuffers.forEach((buf) => buf.unlock()); // generate & emit any mutations that happened during snapshotting, as can now apply against the newly built mirror\n\n    // Some old browsers don't support adoptedStyleSheets.\n    if (document.adoptedStyleSheets && document.adoptedStyleSheets.length > 0)\n      stylesheetManager.adoptStyleSheets(\n        document.adoptedStyleSheets,\n        mirror.getId(document),\n      );\n  };\n\n  try {\n    const handlers: listenerHandler[] = [];\n    handlers.push(\n      on('DOMContentLoaded', () => {\n        wrappedEmit(\n          wrapEvent({\n            type: EventType.DomContentLoaded,\n            data: {},\n          }),\n        );\n      }),\n    );\n\n    const observe = (doc: Document) => {\n      return initObservers(\n        {\n          mutationCb: wrappedMutationEmit,\n          mousemoveCb: (positions, source) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source,\n                  positions,\n                },\n              }),\n            ),\n          mouseInteractionCb: (d) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.MouseInteraction,\n                  ...d,\n                },\n              }),\n            ),\n          scrollCb: wrappedScrollEmit,\n          viewportResizeCb: (d) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.ViewportResize,\n                  ...d,\n                },\n              }),\n            ),\n          inputCb: (v) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.Input,\n                  ...v,\n                },\n              }),\n            ),\n          mediaInteractionCb: (p) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.MediaInteraction,\n                  ...p,\n                },\n              }),\n            ),\n          styleSheetRuleCb: (r) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.StyleSheetRule,\n                  ...r,\n                },\n              }),\n            ),\n          styleDeclarationCb: (r) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.StyleDeclaration,\n                  ...r,\n                },\n              }),\n            ),\n          canvasMutationCb: wrappedCanvasMutationEmit,\n          fontCb: (p) =>\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.Font,\n                  ...p,\n                },\n              }),\n            ),\n          selectionCb: (p) => {\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.IncrementalSnapshot,\n                data: {\n                  source: IncrementalSource.Selection,\n                  ...p,\n                },\n              }),\n            );\n          },\n          blockClass,\n          ignoreClass,\n          maskTextClass,\n          maskTextSelector,\n          maskInputOptions,\n          inlineStylesheet,\n          sampling,\n          recordCanvas,\n          inlineImages,\n          userTriggeredOnInput,\n          collectFonts,\n          doc,\n          maskInputFn,\n          maskTextFn,\n          keepIframeSrcFn,\n          blockSelector,\n          slimDOMOptions,\n          dataURLOptions,\n          mirror,\n          iframeManager,\n          stylesheetManager,\n          shadowDomManager,\n          canvasManager,\n          ignoreCSSAttributes,\n          plugins:\n            plugins\n              ?.filter((p) => p.observer)\n              ?.map((p) => ({\n                observer: p.observer!,\n                options: p.options,\n                callback: (payload: object) =>\n                  wrappedEmit(\n                    wrapEvent({\n                      type: EventType.Plugin,\n                      data: {\n                        plugin: p.name,\n                        payload,\n                      },\n                    }),\n                  ),\n              })) || [],\n        },\n        hooks,\n      );\n    };\n\n    iframeManager.addLoadListener((iframeEl) => {\n      handlers.push(observe(iframeEl.contentDocument!));\n    });\n\n    const init = () => {\n      takeFullSnapshot();\n      handlers.push(observe(document));\n      recording = true;\n    };\n    if (\n      document.readyState === 'interactive' ||\n      document.readyState === 'complete'\n    ) {\n      init();\n    } else {\n      handlers.push(\n        on(\n          'load',\n          () => {\n            wrappedEmit(\n              wrapEvent({\n                type: EventType.Load,\n                data: {},\n              }),\n            );\n            init();\n          },\n          window,\n        ),\n      );\n    }\n    return () => {\n      handlers.forEach((h) => h());\n      recording = false;\n    };\n  } catch (error) {\n    // TODO: handle internal error\n    console.warn(error);\n  }\n}\n\nrecord.addCustomEvent = <T>(tag: string, payload: T) => {\n  if (!recording) {\n    throw new Error('please add custom event after start recording');\n  }\n  wrappedEmit(\n    wrapEvent({\n      type: EventType.Custom,\n      data: {\n        tag,\n        payload,\n      },\n    }),\n  );\n};\n\nrecord.freezePage = () => {\n  mutationBuffers.forEach((buf) => buf.freeze());\n};\n\nrecord.takeFullSnapshot = (isCheckout?: boolean) => {\n  if (!recording) {\n    throw new Error('please take full snapshot after start recording');\n  }\n  takeFullSnapshot(isCheckout);\n};\n\nrecord.mirror = mirror;\n\nexport default record;\n"], "names": ["k", "ae", "X", "pe", "he", "ye", "ie", "Se", "Z", "V", "ne", "C", "Y", "a", "p", "c", "d", "D", "g", "L", "H", "B", "x", "U", "z", "_", "N", "m", "O", "P", "A", "F", "E", "T", "te", "I", "W", "R", "oe", "Q", "K", "J", "$", "h", "ee", "o", "n", "s", "b", "f", "S", "y", "w", "i", "v", "u", "l", "M", "ge", "be", "De", "Ee", "Fe", "Te", "ve", "Ie", "we", "ke", "Oe", "Ce", "re", "Me"], "mappings": ";;;IAAA,IAAIA,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAS,CAAC,EAAEA,GAAC,GAAGA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAASC,IAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,wDAAwD,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAACC,IAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,SAASA,IAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAGC,IAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,SAASA,IAAE,CAAC,CAAC,CAAC,CAAC,OAAM,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,SAASC,IAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,CAACC,IAAE,CAAC,CAAC,CAAC,CAAC,SAASC,IAAE,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,eAAe,CAAC,OAAM,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,4CAA4C,CAAC,EAAE,CAAC,qDAAqD,CAAC,EAAE,CAAC,uBAAuB,CAAC,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,OAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,OAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,SAASA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,SAASC,IAAE,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAACD,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,CAACD,GAAC,CAAC,CAAC,CAACE,IAAE,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAACD,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,OAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAU,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,EAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,YAAY,CAAC,CAAC,IAAI,CAACT,GAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAACA,GAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,OAAM,CAAC,IAAI,CAACA,GAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,OAAM,CAAC,IAAI,CAACA,GAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,OAAM,CAAC,IAAI,CAACA,GAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,OAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAACQ,GAAC,CAAC,CAAC,CAACE,IAAE,EAAE,EAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAACV,GAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAC,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAACQ,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAACA,GAAC,CAAC,CAAC,CAACE,IAAE,EAAE,CAAC,EAAC,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC,CAAC,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,IAAI,CAACL,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,EAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,OAAO,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAACL,GAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAASW,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,GAAGX,GAAC,CAAC,OAAO,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,SAAS,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,UAAU,EAAE,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,eAAe,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,GAAGW,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,mCAAmC,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,kBAAkB,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,kBAAkB,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,GAAGA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,GAAGA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,kBAAkB,GAAGA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,GAAGA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,0BAA0B,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,qBAAqB,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,YAAY,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,iBAAiB,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,cAAc,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,4BAA4B,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,UAAU,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGZ,GAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAACM,IAAE,CAAC,CAAC,CAACC,IAAE,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,IAAE,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAGN,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAEE,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAGF,GAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,GAAGX,IAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACW,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGV,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGF,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAACY,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGZ,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAACY,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,UAAU,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAOA,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;ICAp9e,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAMC,GAAC,CAAC,CAAC;AACrO;AACA,4EAA4E,CAAC,CAAQ,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAACA,GAAC,CAAC,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,KAAK,CAACA,GAAC,EAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,KAAK,CAACA,GAAC,EAAC,CAAC,CAAC,CAAC,OAAO,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,CAACA,GAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAQ,SAAS,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAAC,CAAQ,SAAS,eAAe,EAAE,CAAC,OAAO,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAQ,SAAS,cAAc,EAAE,CAAC,OAAO,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAQ,SAAS,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAM,CAAC,CAAC,CAAC,KAAK,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,OAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAQ,SAAS,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAQ,SAAS,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGC,IAAC,CAAQ,SAAS,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAQ,SAAS,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,OAAM,CAAC,CAAC,CAAC,EAAC,CAA8jB,SAAS,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAA0Y,SAAS,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAkX,MAAM,gBAAgB,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC,CAAC,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC;;ICF/rJ,IAAIA,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAAClB,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAACmB,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;ICAt6C,SAASA,GAAC,CAAC,CAAC,CAAC,CAAC,OAAM,MAAM,GAAG,CAAC,CAAC,MAAMC,GAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAED,GAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,KAAK,GAAG,CAAC,CAAC,WAAW,EAAEA,GAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAgB,MAAME,GAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,GAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAID,GAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,IAAC,CAAC,KAAK,CAAC,GAAGA,IAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC,CAACJ,EAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACP,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,EAAE,CAACF,kBAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAACc,sBAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAACC,aAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAACC,GAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAACA,GAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,GAAG,CAACC,SAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAACC,SAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAACV,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,GAAG,OAAO,GAAG,CAAC,CAACW,EAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAACD,SAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC,aAAa,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAACE,EAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,CAAC,GAAGF,SAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAACT,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAACS,SAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAED,SAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAACI,YAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEC,iBAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAACb,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAEc,GAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,KAAK,CAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,SAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAACC,SAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,eAAe,CAAC,eAAe,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,GAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,GAAE,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,GAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,SAASF,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAACQ,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;ICAh9P,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAIF,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAACZ,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAoU,MAAM,eAAe,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,eAAe,EAAE,WAAW,CAACM,GAAC,CAAC,OAAO,gBAAgB,EAAE,WAAW,CAAC,SAASG,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAQ,SAAS,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIM,GAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASxB,IAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAACyB,QAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,QAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAACP,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACQ,YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,SAAS,EAAE,WAAW,EAAE,CAAC,YAAY,SAAS,CAACnB,GAAC,CAAC,IAAI,CAAC,CAAC,YAAY,UAAU,CAACA,GAAC,CAAC,SAAS,CAACA,GAAC,CAAC,SAAS,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACN,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAACiB,GAAC,CAAC,CAAC,CAAC,CAAC,GAAGS,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAACD,YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,MAAM,CAAC,IAAI,CAACA,GAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACX,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAQ,SAAS,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACwB,QAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAACP,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAES,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO1B,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS2B,IAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACH,QAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAACI,eAAC,EAAE,CAAC,CAAC,CAACC,cAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO7B,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAQ,MAAM,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,SAASV,IAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC2B,GAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAES,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAACI,EAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAACtB,GAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC,EAAER,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE+B,UAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,UAAU,YAAY,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC,UAAU,YAAY,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,UAAU,YAAY,eAAe,EAAEjB,GAAC,EAAE,CAAC,CAAC,UAAU,YAAY,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,KAAK,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASkB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAClB,GAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACkB,GAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAU,CAAC,EAAC,CAAC,CAAQ,SAAS,6BAA6B,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,cAAc,CAAC,EAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAER,QAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAACP,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAES,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC1B,EAAC,CAAC,MAAM,CAAC,CAAC,CAACqB,GAAC,CAAC,IAAI,CAAC,CAAC,CAACrB,EAAC,CAAC,OAAO,CAAC,CAAC,CAACqB,GAAC,CAAC,KAAK,CAAC,CAAC,CAACrB,EAAC,CAAC,QAAQ,CAAC,CAAC,CAACqB,GAAC,CAAC,MAAM,CAAC,CAAC,CAACrB,EAAC,CAAC,cAAc,CAAC,CAAC,CAACqB,GAAC,CAAC,YAAY,CAAC,CAAC,CAACrB,EAAC,CAAC,YAAY,CAAC,CAAC,CAACqB,GAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAM,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACY,KAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAACP,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC1B,EAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAASJ,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,EAAC,CAAQ,SAAS,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAACA,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACG,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC4B,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACrC,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC;;ICA7/X,MAAMe,GAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,QAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;ICAp3B,MAAM,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI6B,GAAC,CAAC7B,IAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI6B,GAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAKC,GAAC,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAACA,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,CAACC,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKD,GAAC,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,gBAAgB,CAAC,OAAM,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAKA,GAAC,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,KAAKC,GAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,cAAc,CAAC,OAAM,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,gBAAgB,CAAC,KAAKA,GAAC,CAAC,gBAAgB,CAAC,KAAKA,GAAC,CAAC,MAAM,CAAC,KAAKA,GAAC,CAAC,cAAc,CAAC,KAAKA,GAAC,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,cAAc,CAAC,KAAKA,GAAC,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,GAAC,CAAC,iBAAiB,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC;;ICA1+G,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAChC,GAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI4B,GAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAACK,GAAC,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAIrB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGgB,GAAC,CAAC,IAAI,IAAI,CAAC,IAAIA,GAAC,CAAC,CAAC,CAAC,CAACK,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGZ,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAyM,MAAM,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAACD,KAAC,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACmC,GAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAACC,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAACC,6BAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAACtC,KAAC,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,QAAO,CAAC;;ICA92D;IACA;IACA;IACA;IACA;IACA,IAAI,KAAK,GAAG,kEAAkE,CAAC;IAC/E;IACA,IAAI,MAAM,GAAG,OAAO,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IAC1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACvC,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,MAAM,GAAG,UAAU,WAAW,EAAE;IACpC,IAAI,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC;IAChF,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;IACjC,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvC,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1E,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC3C,KAAK;IACL,IAAI,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACvB,QAAQ,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IAC9D,KAAK;IACL,SAAS,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IAC5B,QAAQ,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/D,KAAK;IACL,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;;IC1B2C,MAAM,CAAC,CAAC,IAAI,GAAG,CAAQ,SAAS,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAQ,MAAM,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,YAAY,YAAY,EAAE,CAAC,YAAY,YAAY,EAAE,CAAC,YAAY,UAAU,EAAE,CAAC,YAAY,WAAW,EAAE,CAAC,YAAY,UAAU,EAAE,CAAC,YAAY,WAAW,EAAE,CAAC,YAAY,UAAU,EAAE,CAAC,YAAY,SAAS,EAAE,CAAC,YAAY,iBAAiB,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAACuC,MAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,YAAY,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,GAAG,CAAC,YAAY,SAAS,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAQ,MAAM,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,aAAa,CAAC,kBAAkB,CAAC,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,4BAA4B,CAAC,cAAc,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,2BAA2B,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;ICA3qD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,MAAM,CAAC,CAACC,KAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,OAAOC,SAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAACN,aAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAACI,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAACrC,UAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAACqC,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,OAAM,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC;;ICA/rB,SAAStC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAACD,KAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO0C,SAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,wDAAwD,EAAC,CAAC,OAAM,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC;;ICAzN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,MAAM,CAAC,CAACR,KAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGS,YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAACrB,SAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACjB,aAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAACmC,UAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAgB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,SAAS,CAACtC,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,SAAS,CAACA,GAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAn8B,IAAIgC,GAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,EAAEA,GAAC,CAAC,IAAI,IAAI,CAAC,IAAIA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAA2O,MAAM,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,GAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACX,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAIP,aAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAACqB,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAACC,SAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,MAAM,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,EAAC,EAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,iCAAiC,EAAE,CAAC,MAAM,CAAC,CAACf,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACN,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACJ,GAAC,CAACuB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAACvB,GAAC,GAAE,EAAC,CAAC,iCAAiC,EAAE,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAC,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,2BAA2B,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAM,MAAC,CAAC,CAAC,CAAC,CAAY,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC;;ICAhjH,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,IAAIA,gBAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,oBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACX,IAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,QAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAAE;;ICAtgC,IAAI,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAA2kB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC0C,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAACC,QAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACxB,eAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAI,GAAGtB,GAAC,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAGc,GAAC,CAAC,QAAQ,CAAC,EAAEQ,eAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAGtB,GAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,GAAGc,GAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,iBAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,aAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,uBAAuB,CAAC,4BAA4B,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,aAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIC,gBAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAClD,GAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAACmD,cAAE,EAAE,CAAC,MAAM,CAACC,eAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC9B,eAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC+B,EAAE,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAACC,kBAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAACC,sBAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAACC,aAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACxD,GAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACsB,eAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAACmC,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACzD,GAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO0D,aAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC1D,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACA,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAACc,GAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACd,GAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,UAAU,GAAG,aAAa,EAAE,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAACyD,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACzD,GAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACA,GAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAACsB,eAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;;;;;;;;"}