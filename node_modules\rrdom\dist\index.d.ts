import { NodeType as RRNodeType } from 'rrweb-snapshot';
import type { Mirror as Node<PERSON><PERSON><PERSON><PERSON>, IMirror, serializedNodeWithId } from 'rrweb-snapshot';
import type { canvasMutationData, canvasEventWithTime, inputData, scrollData, styleSheetRuleData, styleDeclarationData } from '@rrweb/types';
import { BaseRRNode as RRNode, IRRDocument, IRRElement, IRRNode, IRRDocumentType, IRRText, IRRComment } from './document';
declare const RRDocument_base: {
    new (...args: any[]): {
        readonly nodeType: number;
        readonly nodeName: "#document";
        readonly compatMode: "BackCompat" | "CSS1Compat";
        readonly RRNodeType: RRNodeType.Document;
        textContent: string | null;
        readonly documentElement: IRRElement | null;
        readonly body: IRRElement | null;
        readonly head: IRRElement | null;
        readonly implementation: IRRDocument;
        readonly firstElementChild: IRRElement | null;
        appendChild(childNode: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
        open(): void;
        close(): void;
        write(content: string): void;
        createDocument(_namespace: string | null, _qualifiedName: string | null, _doctype?: DocumentType | null | undefined): IRRDocument;
        createDocumentType(qualifiedName: string, publicId: string, systemId: string): IRRDocumentType;
        createElement(tagName: string): IRRElement;
        createElementNS(_namespaceURI: string, qualifiedName: string): IRRElement;
        createTextNode(data: string): IRRText;
        createComment(data: string): IRRComment;
        createCDATASection(data: string): import("./document").IRRCDATASection;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
    };
} & typeof RRNode;
export declare class RRDocument extends RRDocument_base {
    private UNSERIALIZED_STARTING_ID;
    private _unserializedId;
    get unserializedId(): number;
    mirror: Mirror;
    scrollData: scrollData | null;
    constructor(mirror?: Mirror);
    createDocument(_namespace: string | null, _qualifiedName: string | null, _doctype?: DocumentType | null): RRDocument;
    createDocumentType(qualifiedName: string, publicId: string, systemId: string): {
        readonly nodeType: number;
        readonly RRNodeType: RRNodeType.DocumentType;
        readonly nodeName: string;
        readonly name: string;
        readonly publicId: string;
        readonly systemId: string;
        textContent: string | null;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    } & RRNode;
    createElement<K extends keyof HTMLElementTagNameMap>(tagName: K): RRElementType<K>;
    createElement(tagName: string): RRElement;
    createComment(data: string): {
        readonly nodeType: number;
        readonly nodeName: "#comment";
        readonly RRNodeType: RRNodeType.Comment;
        data: string;
        textContent: string;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    } & RRNode;
    createCDATASection(data: string): {
        readonly nodeName: "#cdata-section";
        readonly nodeType: number;
        readonly RRNodeType: RRNodeType.CDATA;
        data: string;
        textContent: string;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    } & RRNode;
    createTextNode(data: string): {
        readonly nodeType: number;
        readonly nodeName: "#text";
        readonly RRNodeType: RRNodeType.Text;
        data: string;
        textContent: string;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    } & RRNode;
    destroyTree(): void;
    open(): void;
}
export declare const RRDocumentType: {
    new (qualifiedName: string, publicId: string, systemId: string): {
        readonly nodeType: number;
        readonly RRNodeType: RRNodeType.DocumentType;
        readonly nodeName: string;
        readonly name: string;
        readonly publicId: string;
        readonly systemId: string;
        textContent: string | null;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    };
} & typeof RRNode;
declare const RRElement_base: {
    new (tagName: string): {
        readonly nodeType: number;
        readonly RRNodeType: RRNodeType.Element;
        readonly nodeName: string;
        tagName: string;
        attributes: Record<string, string>;
        shadowRoot: IRRElement | null;
        scrollLeft?: number | undefined;
        scrollTop?: number | undefined;
        textContent: string;
        readonly classList: import("./document").ClassList;
        readonly id: string;
        readonly className: string;
        readonly style: import("./document").CSSStyleDeclaration;
        getAttribute(name: string): string | null;
        setAttribute(name: string, attribute: string): void;
        setAttributeNS(_namespace: string | null, qualifiedName: string, value: string): void;
        removeAttribute(name: string): void;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
        attachShadow(_init: ShadowRootInit): IRRElement;
        dispatchEvent(_event: Event): boolean;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
    };
} & typeof RRNode;
export declare class RRElement extends RRElement_base {
    inputData: inputData | null;
    scrollData: scrollData | null;
}
declare const RRMediaElement_base: {
    new (...args: any[]): {
        currentTime?: number | undefined;
        volume?: number | undefined;
        paused?: boolean | undefined;
        muted?: boolean | undefined;
        playbackRate?: number | undefined;
        attachShadow(_init: ShadowRootInit): IRRElement;
        play(): void;
        pause(): void;
        tagName: string;
        attributes: Record<string, string>;
        shadowRoot: IRRElement | null;
        scrollLeft?: number | undefined;
        scrollTop?: number | undefined;
        id: string;
        className: string;
        classList: import("./document").ClassList;
        style: import("./document").CSSStyleDeclaration;
        getAttribute(name: string): string | null;
        setAttribute(name: string, attribute: string): void;
        setAttributeNS(namespace: string | null, qualifiedName: string, value: string): void;
        removeAttribute(name: string): void;
        dispatchEvent(event: Event): boolean;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        readonly nodeType: number;
        readonly nodeName: string;
        readonly RRNodeType: RRNodeType;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        textContent: string | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
        toString(): string;
    };
} & typeof RRElement;
export declare class RRMediaElement extends RRMediaElement_base {
}
export declare class RRCanvasElement extends RRElement implements IRRElement {
    rr_dataURL: string | null;
    canvasMutations: {
        event: canvasEventWithTime;
        mutation: canvasMutationData;
    }[];
    getContext(): RenderingContext | null;
}
export declare class RRStyleElement extends RRElement {
    rules: (styleSheetRuleData | styleDeclarationData)[];
}
export declare class RRIFrameElement extends RRElement {
    contentDocument: RRDocument;
    constructor(upperTagName: string, mirror: Mirror);
}
export declare const RRText: {
    new (data: string): {
        readonly nodeType: number;
        readonly nodeName: "#text";
        readonly RRNodeType: RRNodeType.Text;
        data: string;
        textContent: string;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    };
} & typeof RRNode;
export declare type RRText = typeof RRText;
export declare const RRComment: {
    new (data: string): {
        readonly nodeType: number;
        readonly nodeName: "#comment";
        readonly RRNodeType: RRNodeType.Comment;
        data: string;
        textContent: string;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    };
} & typeof RRNode;
export declare type RRComment = typeof RRComment;
export declare const RRCDATASection: {
    new (data: string): {
        readonly nodeName: "#cdata-section";
        readonly nodeType: number;
        readonly RRNodeType: RRNodeType.CDATA;
        data: string;
        textContent: string;
        toString(): string;
        parentElement: IRRNode | null;
        parentNode: IRRNode | null;
        childNodes: IRRNode[];
        ownerDocument: IRRDocument;
        readonly ELEMENT_NODE: number;
        readonly TEXT_NODE: number;
        firstChild: IRRNode | null;
        lastChild: IRRNode | null;
        nextSibling: IRRNode | null;
        contains(node: IRRNode): boolean;
        appendChild(newChild: IRRNode): IRRNode;
        insertBefore(newChild: IRRNode, refChild: IRRNode | null): IRRNode;
        removeChild(node: IRRNode): IRRNode;
    };
} & typeof RRNode;
export declare type RRCDATASection = typeof RRCDATASection;
interface RRElementTagNameMap {
    audio: RRMediaElement;
    canvas: RRCanvasElement;
    iframe: RRIFrameElement;
    style: RRStyleElement;
    video: RRMediaElement;
}
declare type RRElementType<K extends keyof HTMLElementTagNameMap> = K extends keyof RRElementTagNameMap ? RRElementTagNameMap[K] : RRElement;
export declare function buildFromNode(node: Node, rrdom: IRRDocument, domMirror: NodeMirror, parentRRNode?: IRRNode | null): IRRNode | null;
export declare function buildFromDom(dom: Document, domMirror?: NodeMirror, rrdom?: IRRDocument): IRRDocument;
export declare function createMirror(): Mirror;
export declare class Mirror implements IMirror<RRNode> {
    private idNodeMap;
    private nodeMetaMap;
    getId(n: RRNode | undefined | null): number;
    getNode(id: number): RRNode | null;
    getIds(): number[];
    getMeta(n: RRNode): serializedNodeWithId | null;
    removeNodeFromMap(n: RRNode): void;
    has(id: number): boolean;
    hasNode(node: RRNode): boolean;
    add(n: RRNode, meta: serializedNodeWithId): void;
    replace(id: number, n: RRNode): void;
    reset(): void;
}
export declare function getDefaultSN(node: IRRNode, id: number): serializedNodeWithId;
export declare function printRRDom(rootNode: IRRNode, mirror: IMirror<IRRNode>): string;
export { RRNode };
export { diff, createOrGetNode, ReplayerHandler } from './diff';
export * from './document';
