{"version": 3, "file": "document-nodejs.min.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../../rrweb-snapshot/es/rrweb-snapshot.js", "../src/style.ts", "../src/document-nodejs.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "var NodeType;\n(function (NodeType) {\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\n})(NodeType || (NodeType = {}));\n\nfunction isElement(n) {\n    return n.nodeType === n.ELEMENT_NODE;\n}\nfunction isShadowRoot(n) {\n    var _a;\n    var host = (_a = n) === null || _a === void 0 ? void 0 : _a.host;\n    return Boolean(host && host.shadowRoot && host.shadowRoot === n);\n}\nfunction maskInputValue(_a) {\n    var maskInputOptions = _a.maskInputOptions, tagName = _a.tagName, type = _a.type, value = _a.value, maskInputFn = _a.maskInputFn;\n    var text = value || '';\n    if (maskInputOptions[tagName.toLowerCase()] ||\n        maskInputOptions[type]) {\n        if (maskInputFn) {\n            text = maskInputFn(text);\n        }\n        else {\n            text = '*'.repeat(text.length);\n        }\n    }\n    return text;\n}\nvar ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\nfunction is2DCanvasBlank(canvas) {\n    var ctx = canvas.getContext('2d');\n    if (!ctx)\n        return true;\n    var chunkSize = 50;\n    for (var x = 0; x < canvas.width; x += chunkSize) {\n        for (var y = 0; y < canvas.height; y += chunkSize) {\n            var getImageData = ctx.getImageData;\n            var originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData\n                ? getImageData[ORIGINAL_ATTRIBUTE_NAME]\n                : getImageData;\n            var pixelBuffer = new Uint32Array(originalGetImageData.call(ctx, x, y, Math.min(chunkSize, canvas.width - x), Math.min(chunkSize, canvas.height - y)).data.buffer);\n            if (pixelBuffer.some(function (pixel) { return pixel !== 0; }))\n                return false;\n        }\n    }\n    return true;\n}\n\nvar _id = 1;\nvar tagNameRegex = new RegExp('[^a-z0-9-_:]');\nvar IGNORED_NODE = -2;\nfunction genId() {\n    return _id++;\n}\nfunction getValidTagName(element) {\n    if (element instanceof HTMLFormElement) {\n        return 'form';\n    }\n    var processedTagName = element.tagName.toLowerCase().trim();\n    if (tagNameRegex.test(processedTagName)) {\n        return 'div';\n    }\n    return processedTagName;\n}\nfunction getCssRulesString(s) {\n    try {\n        var rules = s.rules || s.cssRules;\n        return rules ? Array.from(rules).map(getCssRuleString).join('') : null;\n    }\n    catch (error) {\n        return null;\n    }\n}\nfunction getCssRuleString(rule) {\n    var cssStringified = rule.cssText;\n    if (isCSSImportRule(rule)) {\n        try {\n            cssStringified = getCssRulesString(rule.styleSheet) || cssStringified;\n        }\n        catch (_a) {\n        }\n    }\n    return cssStringified;\n}\nfunction isCSSImportRule(rule) {\n    return 'styleSheet' in rule;\n}\nfunction stringifyStyleSheet(sheet) {\n    return sheet.cssRules\n        ? Array.from(sheet.cssRules)\n            .map(function (rule) { return rule.cssText || ''; })\n            .join('')\n        : '';\n}\nfunction extractOrigin(url) {\n    var origin = '';\n    if (url.indexOf('//') > -1) {\n        origin = url.split('/').slice(0, 3).join('/');\n    }\n    else {\n        origin = url.split('/')[0];\n    }\n    origin = origin.split('?')[0];\n    return origin;\n}\nvar canvasService;\nvar canvasCtx;\nvar URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\nvar RELATIVE_PATH = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\\\|\\/\\/|#).*/;\nvar DATA_URI = /^(data:)([^,]*),(.*)/i;\nfunction absoluteToStylesheet(cssText, href) {\n    return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {\n        var filePath = path1 || path2 || path3;\n        var maybeQuote = quote1 || quote2 || '';\n        if (!filePath) {\n            return origin;\n        }\n        if (!RELATIVE_PATH.test(filePath)) {\n            return \"url(\" + maybeQuote + filePath + maybeQuote + \")\";\n        }\n        if (DATA_URI.test(filePath)) {\n            return \"url(\" + maybeQuote + filePath + maybeQuote + \")\";\n        }\n        if (filePath[0] === '/') {\n            return \"url(\" + maybeQuote + (extractOrigin(href) + filePath) + maybeQuote + \")\";\n        }\n        var stack = href.split('/');\n        var parts = filePath.split('/');\n        stack.pop();\n        for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\n            var part = parts_1[_i];\n            if (part === '.') {\n                continue;\n            }\n            else if (part === '..') {\n                stack.pop();\n            }\n            else {\n                stack.push(part);\n            }\n        }\n        return \"url(\" + maybeQuote + stack.join('/') + maybeQuote + \")\";\n    });\n}\nvar SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\nvar SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\n    if (attributeValue.trim() === '') {\n        return attributeValue;\n    }\n    var pos = 0;\n    function collectCharacters(regEx) {\n        var chars;\n        var match = regEx.exec(attributeValue.substring(pos));\n        if (match) {\n            chars = match[0];\n            pos += chars.length;\n            return chars;\n        }\n        return '';\n    }\n    var output = [];\n    while (true) {\n        collectCharacters(SRCSET_COMMAS_OR_SPACES);\n        if (pos >= attributeValue.length) {\n            break;\n        }\n        var url = collectCharacters(SRCSET_NOT_SPACES);\n        if (url.slice(-1) === ',') {\n            url = absoluteToDoc(doc, url.substring(0, url.length - 1));\n            output.push(url);\n        }\n        else {\n            var descriptorsStr = '';\n            url = absoluteToDoc(doc, url);\n            var inParens = false;\n            while (true) {\n                var c = attributeValue.charAt(pos);\n                if (c === '') {\n                    output.push((url + descriptorsStr).trim());\n                    break;\n                }\n                else if (!inParens) {\n                    if (c === ',') {\n                        pos += 1;\n                        output.push((url + descriptorsStr).trim());\n                        break;\n                    }\n                    else if (c === '(') {\n                        inParens = true;\n                    }\n                }\n                else {\n                    if (c === ')') {\n                        inParens = false;\n                    }\n                }\n                descriptorsStr += c;\n                pos += 1;\n            }\n        }\n    }\n    return output.join(', ');\n}\nfunction absoluteToDoc(doc, attributeValue) {\n    if (!attributeValue || attributeValue.trim() === '') {\n        return attributeValue;\n    }\n    var a = doc.createElement('a');\n    a.href = attributeValue;\n    return a.href;\n}\nfunction isSVGElement(el) {\n    return Boolean(el.tagName === 'svg' || el.ownerSVGElement);\n}\nfunction getHref() {\n    var a = document.createElement('a');\n    a.href = '';\n    return a.href;\n}\nfunction transformAttribute(doc, tagName, name, value) {\n    if (name === 'src' || (name === 'href' && value)) {\n        return absoluteToDoc(doc, value);\n    }\n    else if (name === 'xlink:href' && value && value[0] !== '#') {\n        return absoluteToDoc(doc, value);\n    }\n    else if (name === 'background' &&\n        value &&\n        (tagName === 'table' || tagName === 'td' || tagName === 'th')) {\n        return absoluteToDoc(doc, value);\n    }\n    else if (name === 'srcset' && value) {\n        return getAbsoluteSrcsetString(doc, value);\n    }\n    else if (name === 'style' && value) {\n        return absoluteToStylesheet(value, getHref());\n    }\n    else if (tagName === 'object' && name === 'data' && value) {\n        return absoluteToDoc(doc, value);\n    }\n    else {\n        return value;\n    }\n}\nfunction _isBlockedElement(element, blockClass, blockSelector) {\n    if (typeof blockClass === 'string') {\n        if (element.classList.contains(blockClass)) {\n            return true;\n        }\n    }\n    else {\n        for (var eIndex = 0; eIndex < element.classList.length; eIndex++) {\n            var className = element.classList[eIndex];\n            if (blockClass.test(className)) {\n                return true;\n            }\n        }\n    }\n    if (blockSelector) {\n        return element.matches(blockSelector);\n    }\n    return false;\n}\nfunction needMaskingText(node, maskTextClass, maskTextSelector) {\n    if (!node) {\n        return false;\n    }\n    if (node.nodeType === node.ELEMENT_NODE) {\n        if (typeof maskTextClass === 'string') {\n            if (node.classList.contains(maskTextClass)) {\n                return true;\n            }\n        }\n        else {\n            for (var eIndex = 0; eIndex < node.classList.length; eIndex++) {\n                var className = node.classList[eIndex];\n                if (maskTextClass.test(className)) {\n                    return true;\n                }\n            }\n        }\n        if (maskTextSelector) {\n            if (node.matches(maskTextSelector)) {\n                return true;\n            }\n        }\n        return needMaskingText(node.parentNode, maskTextClass, maskTextSelector);\n    }\n    if (node.nodeType === node.TEXT_NODE) {\n        return needMaskingText(node.parentNode, maskTextClass, maskTextSelector);\n    }\n    return needMaskingText(node.parentNode, maskTextClass, maskTextSelector);\n}\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\n    var win = iframeEl.contentWindow;\n    if (!win) {\n        return;\n    }\n    var fired = false;\n    var readyState;\n    try {\n        readyState = win.document.readyState;\n    }\n    catch (error) {\n        return;\n    }\n    if (readyState !== 'complete') {\n        var timer_1 = setTimeout(function () {\n            if (!fired) {\n                listener();\n                fired = true;\n            }\n        }, iframeLoadTimeout);\n        iframeEl.addEventListener('load', function () {\n            clearTimeout(timer_1);\n            fired = true;\n            listener();\n        });\n        return;\n    }\n    var blankUrl = 'about:blank';\n    if (win.location.href !== blankUrl ||\n        iframeEl.src === blankUrl ||\n        iframeEl.src === '') {\n        setTimeout(listener, 0);\n        return;\n    }\n    iframeEl.addEventListener('load', listener);\n}\nfunction serializeNode(n, options) {\n    var _a;\n    var doc = options.doc, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, inlineStylesheet = options.inlineStylesheet, _b = options.maskInputOptions, maskInputOptions = _b === void 0 ? {} : _b, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, _c = options.dataURLOptions, dataURLOptions = _c === void 0 ? {} : _c, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn;\n    var rootId;\n    if (doc.__sn) {\n        var docId = doc.__sn.id;\n        rootId = docId === 1 ? undefined : docId;\n    }\n    switch (n.nodeType) {\n        case n.DOCUMENT_NODE:\n            if (n.compatMode !== 'CSS1Compat') {\n                return {\n                    type: NodeType.Document,\n                    childNodes: [],\n                    compatMode: n.compatMode,\n                    rootId: rootId\n                };\n            }\n            else {\n                return {\n                    type: NodeType.Document,\n                    childNodes: [],\n                    rootId: rootId\n                };\n            }\n        case n.DOCUMENT_TYPE_NODE:\n            return {\n                type: NodeType.DocumentType,\n                name: n.name,\n                publicId: n.publicId,\n                systemId: n.systemId,\n                rootId: rootId\n            };\n        case n.ELEMENT_NODE:\n            var needBlock = _isBlockedElement(n, blockClass, blockSelector);\n            var tagName = getValidTagName(n);\n            var attributes_1 = {};\n            for (var _i = 0, _d = Array.from(n.attributes); _i < _d.length; _i++) {\n                var _e = _d[_i], name_1 = _e.name, value = _e.value;\n                attributes_1[name_1] = transformAttribute(doc, tagName, name_1, value);\n            }\n            if (tagName === 'link' && inlineStylesheet) {\n                var stylesheet = Array.from(doc.styleSheets).find(function (s) {\n                    return s.href === n.href;\n                });\n                var cssText = null;\n                if (stylesheet) {\n                    cssText = getCssRulesString(stylesheet);\n                }\n                if (cssText) {\n                    delete attributes_1.rel;\n                    delete attributes_1.href;\n                    attributes_1._cssText = absoluteToStylesheet(cssText, stylesheet.href);\n                }\n            }\n            if (tagName === 'style' &&\n                n.sheet &&\n                !(n.innerText ||\n                    n.textContent ||\n                    '').trim().length) {\n                var cssText = getCssRulesString(n.sheet);\n                if (cssText) {\n                    attributes_1._cssText = absoluteToStylesheet(cssText, getHref());\n                }\n            }\n            if (tagName === 'input' ||\n                tagName === 'textarea' ||\n                tagName === 'select') {\n                var value = n.value;\n                if (attributes_1.type !== 'radio' &&\n                    attributes_1.type !== 'checkbox' &&\n                    attributes_1.type !== 'submit' &&\n                    attributes_1.type !== 'button' &&\n                    value) {\n                    attributes_1.value = maskInputValue({\n                        type: attributes_1.type,\n                        tagName: tagName,\n                        value: value,\n                        maskInputOptions: maskInputOptions,\n                        maskInputFn: maskInputFn\n                    });\n                }\n                else if (n.checked) {\n                    attributes_1.checked = n.checked;\n                }\n            }\n            if (tagName === 'option') {\n                if (n.selected && !maskInputOptions['select']) {\n                    attributes_1.selected = true;\n                }\n                else {\n                    delete attributes_1.selected;\n                }\n            }\n            if (tagName === 'canvas' && recordCanvas) {\n                if (n.__context === '2d') {\n                    if (!is2DCanvasBlank(n)) {\n                        attributes_1.rr_dataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n                    }\n                }\n                else if (!('__context' in n)) {\n                    var canvasDataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n                    var blankCanvas = document.createElement('canvas');\n                    blankCanvas.width = n.width;\n                    blankCanvas.height = n.height;\n                    var blankCanvasDataURL = blankCanvas.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n                    if (canvasDataURL !== blankCanvasDataURL) {\n                        attributes_1.rr_dataURL = canvasDataURL;\n                    }\n                }\n            }\n            if (tagName === 'img' && inlineImages) {\n                if (!canvasService) {\n                    canvasService = doc.createElement('canvas');\n                    canvasCtx = canvasService.getContext('2d');\n                }\n                var image_1 = n;\n                var oldValue_1 = image_1.crossOrigin;\n                image_1.crossOrigin = 'anonymous';\n                var recordInlineImage = function () {\n                    try {\n                        canvasService.width = image_1.naturalWidth;\n                        canvasService.height = image_1.naturalHeight;\n                        canvasCtx.drawImage(image_1, 0, 0);\n                        attributes_1.rr_dataURL = canvasService.toDataURL(dataURLOptions.type, dataURLOptions.quality);\n                    }\n                    catch (err) {\n                        console.warn(\"Cannot inline img src=\" + image_1.currentSrc + \"! Error: \" + err);\n                    }\n                    oldValue_1\n                        ? (attributes_1.crossOrigin = oldValue_1)\n                        : delete attributes_1.crossOrigin;\n                };\n                if (image_1.complete && image_1.naturalWidth !== 0)\n                    recordInlineImage();\n                else\n                    image_1.onload = recordInlineImage;\n            }\n            if (tagName === 'audio' || tagName === 'video') {\n                attributes_1.rr_mediaState = n.paused\n                    ? 'paused'\n                    : 'played';\n                attributes_1.rr_mediaCurrentTime = n.currentTime;\n            }\n            if (n.scrollLeft) {\n                attributes_1.rr_scrollLeft = n.scrollLeft;\n            }\n            if (n.scrollTop) {\n                attributes_1.rr_scrollTop = n.scrollTop;\n            }\n            if (needBlock) {\n                var _f = n.getBoundingClientRect(), width = _f.width, height = _f.height;\n                attributes_1 = {\n                    \"class\": attributes_1[\"class\"],\n                    rr_width: width + \"px\",\n                    rr_height: height + \"px\"\n                };\n            }\n            if (tagName === 'iframe' && !keepIframeSrcFn(attributes_1.src)) {\n                if (!n.contentDocument) {\n                    attributes_1.rr_src = attributes_1.src;\n                }\n                delete attributes_1.src;\n            }\n            return {\n                type: NodeType.Element,\n                tagName: tagName,\n                attributes: attributes_1,\n                childNodes: [],\n                isSVG: isSVGElement(n) || undefined,\n                needBlock: needBlock,\n                rootId: rootId\n            };\n        case n.TEXT_NODE:\n            var parentTagName = n.parentNode && n.parentNode.tagName;\n            var textContent = n.textContent;\n            var isStyle = parentTagName === 'STYLE' ? true : undefined;\n            var isScript = parentTagName === 'SCRIPT' ? true : undefined;\n            if (isStyle && textContent) {\n                try {\n                    if (n.nextSibling || n.previousSibling) {\n                    }\n                    else if ((_a = n.parentNode.sheet) === null || _a === void 0 ? void 0 : _a.cssRules) {\n                        textContent = stringifyStyleSheet(n.parentNode.sheet);\n                    }\n                }\n                catch (err) {\n                    console.warn(\"Cannot get CSS styles from text's parentNode. Error: \" + err, n);\n                }\n                textContent = absoluteToStylesheet(textContent, getHref());\n            }\n            if (isScript) {\n                textContent = 'SCRIPT_PLACEHOLDER';\n            }\n            if (!isStyle &&\n                !isScript &&\n                needMaskingText(n, maskTextClass, maskTextSelector) &&\n                textContent) {\n                textContent = maskTextFn\n                    ? maskTextFn(textContent)\n                    : textContent.replace(/[\\S]/g, '*');\n            }\n            return {\n                type: NodeType.Text,\n                textContent: textContent || '',\n                isStyle: isStyle,\n                rootId: rootId\n            };\n        case n.CDATA_SECTION_NODE:\n            return {\n                type: NodeType.CDATA,\n                textContent: '',\n                rootId: rootId\n            };\n        case n.COMMENT_NODE:\n            return {\n                type: NodeType.Comment,\n                textContent: n.textContent || '',\n                rootId: rootId\n            };\n        default:\n            return false;\n    }\n}\nfunction lowerIfExists(maybeAttr) {\n    if (maybeAttr === undefined) {\n        return '';\n    }\n    else {\n        return maybeAttr.toLowerCase();\n    }\n}\nfunction slimDOMExcluded(sn, slimDOMOptions) {\n    if (slimDOMOptions.comment && sn.type === NodeType.Comment) {\n        return true;\n    }\n    else if (sn.type === NodeType.Element) {\n        if (slimDOMOptions.script &&\n            (sn.tagName === 'script' ||\n                (sn.tagName === 'link' &&\n                    sn.attributes.rel === 'preload' &&\n                    sn.attributes.as === 'script') ||\n                (sn.tagName === 'link' &&\n                    sn.attributes.rel === 'prefetch' &&\n                    typeof sn.attributes.href === 'string' &&\n                    sn.attributes.href.endsWith('.js')))) {\n            return true;\n        }\n        else if (slimDOMOptions.headFavicon &&\n            ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon') ||\n                (sn.tagName === 'meta' &&\n                    (lowerIfExists(sn.attributes.name).match(/^msapplication-tile(image|color)$/) ||\n                        lowerIfExists(sn.attributes.name) === 'application-name' ||\n                        lowerIfExists(sn.attributes.rel) === 'icon' ||\n                        lowerIfExists(sn.attributes.rel) === 'apple-touch-icon' ||\n                        lowerIfExists(sn.attributes.rel) === 'shortcut icon')))) {\n            return true;\n        }\n        else if (sn.tagName === 'meta') {\n            if (slimDOMOptions.headMetaDescKeywords &&\n                lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaSocial &&\n                (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) ||\n                    lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) ||\n                    lowerIfExists(sn.attributes.name) === 'pinterest')) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaRobots &&\n                (lowerIfExists(sn.attributes.name) === 'robots' ||\n                    lowerIfExists(sn.attributes.name) === 'googlebot' ||\n                    lowerIfExists(sn.attributes.name) === 'bingbot')) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaHttpEquiv &&\n                sn.attributes['http-equiv'] !== undefined) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaAuthorship &&\n                (lowerIfExists(sn.attributes.name) === 'author' ||\n                    lowerIfExists(sn.attributes.name) === 'generator' ||\n                    lowerIfExists(sn.attributes.name) === 'framework' ||\n                    lowerIfExists(sn.attributes.name) === 'publisher' ||\n                    lowerIfExists(sn.attributes.name) === 'progid' ||\n                    lowerIfExists(sn.attributes.property).match(/^article:/) ||\n                    lowerIfExists(sn.attributes.property).match(/^product:/))) {\n                return true;\n            }\n            else if (slimDOMOptions.headMetaVerification &&\n                (lowerIfExists(sn.attributes.name) === 'google-site-verification' ||\n                    lowerIfExists(sn.attributes.name) === 'yandex-verification' ||\n                    lowerIfExists(sn.attributes.name) === 'csrf-token' ||\n                    lowerIfExists(sn.attributes.name) === 'p:domain_verify' ||\n                    lowerIfExists(sn.attributes.name) === 'verify-v1' ||\n                    lowerIfExists(sn.attributes.name) === 'verification' ||\n                    lowerIfExists(sn.attributes.name) === 'shopify-checkout-api-token')) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nfunction serializeNodeWithId(n, options) {\n    var doc = options.doc, map = options.map, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.inlineStylesheet, inlineStylesheet = _b === void 0 ? true : _b, _c = options.maskInputOptions, maskInputOptions = _c === void 0 ? {} : _c, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, slimDOMOptions = options.slimDOMOptions, _d = options.dataURLOptions, dataURLOptions = _d === void 0 ? {} : _d, _e = options.inlineImages, inlineImages = _e === void 0 ? false : _e, _f = options.recordCanvas, recordCanvas = _f === void 0 ? false : _f, onSerialize = options.onSerialize, onIframeLoad = options.onIframeLoad, _g = options.iframeLoadTimeout, iframeLoadTimeout = _g === void 0 ? 5000 : _g, _h = options.keepIframeSrcFn, keepIframeSrcFn = _h === void 0 ? function () { return false; } : _h;\n    var _j = options.preserveWhiteSpace, preserveWhiteSpace = _j === void 0 ? true : _j;\n    var _serializedNode = serializeNode(n, {\n        doc: doc,\n        blockClass: blockClass,\n        blockSelector: blockSelector,\n        maskTextClass: maskTextClass,\n        maskTextSelector: maskTextSelector,\n        inlineStylesheet: inlineStylesheet,\n        maskInputOptions: maskInputOptions,\n        maskTextFn: maskTextFn,\n        maskInputFn: maskInputFn,\n        dataURLOptions: dataURLOptions,\n        inlineImages: inlineImages,\n        recordCanvas: recordCanvas,\n        keepIframeSrcFn: keepIframeSrcFn\n    });\n    if (!_serializedNode) {\n        console.warn(n, 'not serialized');\n        return null;\n    }\n    var id;\n    if ('__sn' in n) {\n        id = n.__sn.id;\n    }\n    else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||\n        (!preserveWhiteSpace &&\n            _serializedNode.type === NodeType.Text &&\n            !_serializedNode.isStyle &&\n            !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, '').length)) {\n        id = IGNORED_NODE;\n    }\n    else {\n        id = genId();\n    }\n    var serializedNode = Object.assign(_serializedNode, { id: id });\n    n.__sn = serializedNode;\n    if (id === IGNORED_NODE) {\n        return null;\n    }\n    map[id] = n;\n    if (onSerialize) {\n        onSerialize(n);\n    }\n    var recordChild = !skipChild;\n    if (serializedNode.type === NodeType.Element) {\n        recordChild = recordChild && !serializedNode.needBlock;\n        delete serializedNode.needBlock;\n        if (n.shadowRoot)\n            serializedNode.isShadowHost = true;\n    }\n    if ((serializedNode.type === NodeType.Document ||\n        serializedNode.type === NodeType.Element) &&\n        recordChild) {\n        if (slimDOMOptions.headWhitespace &&\n            _serializedNode.type === NodeType.Element &&\n            _serializedNode.tagName === 'head') {\n            preserveWhiteSpace = false;\n        }\n        var bypassOptions = {\n            doc: doc,\n            map: map,\n            blockClass: blockClass,\n            blockSelector: blockSelector,\n            maskTextClass: maskTextClass,\n            maskTextSelector: maskTextSelector,\n            skipChild: skipChild,\n            inlineStylesheet: inlineStylesheet,\n            maskInputOptions: maskInputOptions,\n            maskTextFn: maskTextFn,\n            maskInputFn: maskInputFn,\n            slimDOMOptions: slimDOMOptions,\n            dataURLOptions: dataURLOptions,\n            inlineImages: inlineImages,\n            recordCanvas: recordCanvas,\n            preserveWhiteSpace: preserveWhiteSpace,\n            onSerialize: onSerialize,\n            onIframeLoad: onIframeLoad,\n            iframeLoadTimeout: iframeLoadTimeout,\n            keepIframeSrcFn: keepIframeSrcFn\n        };\n        for (var _i = 0, _k = Array.from(n.childNodes); _i < _k.length; _i++) {\n            var childN = _k[_i];\n            var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\n            if (serializedChildNode) {\n                serializedNode.childNodes.push(serializedChildNode);\n            }\n        }\n        if (isElement(n) && n.shadowRoot) {\n            for (var _l = 0, _m = Array.from(n.shadowRoot.childNodes); _l < _m.length; _l++) {\n                var childN = _m[_l];\n                var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\n                if (serializedChildNode) {\n                    serializedChildNode.isShadow = true;\n                    serializedNode.childNodes.push(serializedChildNode);\n                }\n            }\n        }\n    }\n    if (n.parentNode && isShadowRoot(n.parentNode)) {\n        serializedNode.isShadow = true;\n    }\n    if (serializedNode.type === NodeType.Element &&\n        serializedNode.tagName === 'iframe') {\n        onceIframeLoaded(n, function () {\n            var iframeDoc = n.contentDocument;\n            if (iframeDoc && onIframeLoad) {\n                var serializedIframeNode = serializeNodeWithId(iframeDoc, {\n                    doc: iframeDoc,\n                    map: map,\n                    blockClass: blockClass,\n                    blockSelector: blockSelector,\n                    maskTextClass: maskTextClass,\n                    maskTextSelector: maskTextSelector,\n                    skipChild: false,\n                    inlineStylesheet: inlineStylesheet,\n                    maskInputOptions: maskInputOptions,\n                    maskTextFn: maskTextFn,\n                    maskInputFn: maskInputFn,\n                    slimDOMOptions: slimDOMOptions,\n                    dataURLOptions: dataURLOptions,\n                    inlineImages: inlineImages,\n                    recordCanvas: recordCanvas,\n                    preserveWhiteSpace: preserveWhiteSpace,\n                    onSerialize: onSerialize,\n                    onIframeLoad: onIframeLoad,\n                    iframeLoadTimeout: iframeLoadTimeout,\n                    keepIframeSrcFn: keepIframeSrcFn\n                });\n                if (serializedIframeNode) {\n                    onIframeLoad(n, serializedIframeNode);\n                }\n            }\n        }, iframeLoadTimeout);\n    }\n    return serializedNode;\n}\nfunction snapshot(n, options) {\n    var _a = options || {}, _b = _a.blockClass, blockClass = _b === void 0 ? 'rr-block' : _b, _c = _a.blockSelector, blockSelector = _c === void 0 ? null : _c, _d = _a.maskTextClass, maskTextClass = _d === void 0 ? 'rr-mask' : _d, _e = _a.maskTextSelector, maskTextSelector = _e === void 0 ? null : _e, _f = _a.inlineStylesheet, inlineStylesheet = _f === void 0 ? true : _f, _g = _a.inlineImages, inlineImages = _g === void 0 ? false : _g, _h = _a.recordCanvas, recordCanvas = _h === void 0 ? false : _h, _j = _a.maskAllInputs, maskAllInputs = _j === void 0 ? false : _j, maskTextFn = _a.maskTextFn, maskInputFn = _a.maskInputFn, _k = _a.slimDOM, slimDOM = _k === void 0 ? false : _k, dataURLOptions = _a.dataURLOptions, preserveWhiteSpace = _a.preserveWhiteSpace, onSerialize = _a.onSerialize, onIframeLoad = _a.onIframeLoad, iframeLoadTimeout = _a.iframeLoadTimeout, _l = _a.keepIframeSrcFn, keepIframeSrcFn = _l === void 0 ? function () { return false; } : _l;\n    var idNodeMap = {};\n    var maskInputOptions = maskAllInputs === true\n        ? {\n            color: true,\n            date: true,\n            'datetime-local': true,\n            email: true,\n            month: true,\n            number: true,\n            range: true,\n            search: true,\n            tel: true,\n            text: true,\n            time: true,\n            url: true,\n            week: true,\n            textarea: true,\n            select: true,\n            password: true\n        }\n        : maskAllInputs === false\n            ? {\n                password: true\n            }\n            : maskAllInputs;\n    var slimDOMOptions = slimDOM === true || slimDOM === 'all'\n        ?\n            {\n                script: true,\n                comment: true,\n                headFavicon: true,\n                headWhitespace: true,\n                headMetaDescKeywords: slimDOM === 'all',\n                headMetaSocial: true,\n                headMetaRobots: true,\n                headMetaHttpEquiv: true,\n                headMetaAuthorship: true,\n                headMetaVerification: true\n            }\n        : slimDOM === false\n            ? {}\n            : slimDOM;\n    return [\n        serializeNodeWithId(n, {\n            doc: n,\n            map: idNodeMap,\n            blockClass: blockClass,\n            blockSelector: blockSelector,\n            maskTextClass: maskTextClass,\n            maskTextSelector: maskTextSelector,\n            skipChild: false,\n            inlineStylesheet: inlineStylesheet,\n            maskInputOptions: maskInputOptions,\n            maskTextFn: maskTextFn,\n            maskInputFn: maskInputFn,\n            slimDOMOptions: slimDOMOptions,\n            dataURLOptions: dataURLOptions,\n            inlineImages: inlineImages,\n            recordCanvas: recordCanvas,\n            preserveWhiteSpace: preserveWhiteSpace,\n            onSerialize: onSerialize,\n            onIframeLoad: onIframeLoad,\n            iframeLoadTimeout: iframeLoadTimeout,\n            keepIframeSrcFn: keepIframeSrcFn\n        }),\n        idNodeMap,\n    ];\n}\nfunction visitSnapshot(node, onVisit) {\n    function walk(current) {\n        onVisit(current);\n        if (current.type === NodeType.Document ||\n            current.type === NodeType.Element) {\n            current.childNodes.forEach(walk);\n        }\n    }\n    walk(node);\n}\nfunction cleanupSnapshot() {\n    _id = 1;\n}\n\nvar commentre = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\nfunction parse(css, options) {\n    if (options === void 0) { options = {}; }\n    var lineno = 1;\n    var column = 1;\n    function updatePosition(str) {\n        var lines = str.match(/\\n/g);\n        if (lines) {\n            lineno += lines.length;\n        }\n        var i = str.lastIndexOf('\\n');\n        column = i === -1 ? column + str.length : str.length - i;\n    }\n    function position() {\n        var start = { line: lineno, column: column };\n        return function (node) {\n            node.position = new Position(start);\n            whitespace();\n            return node;\n        };\n    }\n    var Position = (function () {\n        function Position(start) {\n            this.start = start;\n            this.end = { line: lineno, column: column };\n            this.source = options.source;\n        }\n        return Position;\n    }());\n    Position.prototype.content = css;\n    var errorsList = [];\n    function error(msg) {\n        var err = new Error(options.source + ':' + lineno + ':' + column + ': ' + msg);\n        err.reason = msg;\n        err.filename = options.source;\n        err.line = lineno;\n        err.column = column;\n        err.source = css;\n        if (options.silent) {\n            errorsList.push(err);\n        }\n        else {\n            throw err;\n        }\n    }\n    function stylesheet() {\n        var rulesList = rules();\n        return {\n            type: 'stylesheet',\n            stylesheet: {\n                source: options.source,\n                rules: rulesList,\n                parsingErrors: errorsList\n            }\n        };\n    }\n    function open() {\n        return match(/^{\\s*/);\n    }\n    function close() {\n        return match(/^}/);\n    }\n    function rules() {\n        var node;\n        var rules = [];\n        whitespace();\n        comments(rules);\n        while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\n            if (node !== false) {\n                rules.push(node);\n                comments(rules);\n            }\n        }\n        return rules;\n    }\n    function match(re) {\n        var m = re.exec(css);\n        if (!m) {\n            return;\n        }\n        var str = m[0];\n        updatePosition(str);\n        css = css.slice(str.length);\n        return m;\n    }\n    function whitespace() {\n        match(/^\\s*/);\n    }\n    function comments(rules) {\n        if (rules === void 0) { rules = []; }\n        var c;\n        while ((c = comment())) {\n            if (c !== false) {\n                rules.push(c);\n            }\n            c = comment();\n        }\n        return rules;\n    }\n    function comment() {\n        var pos = position();\n        if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\n            return;\n        }\n        var i = 2;\n        while ('' !== css.charAt(i) &&\n            ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {\n            ++i;\n        }\n        i += 2;\n        if ('' === css.charAt(i - 1)) {\n            return error('End of comment missing');\n        }\n        var str = css.slice(2, i - 2);\n        column += 2;\n        updatePosition(str);\n        css = css.slice(i);\n        column += 2;\n        return pos({\n            type: 'comment',\n            comment: str\n        });\n    }\n    function selector() {\n        var m = match(/^([^{]+)/);\n        if (!m) {\n            return;\n        }\n        return trim(m[0])\n            .replace(/\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*\\/+/g, '')\n            .replace(/\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'/g, function (m) {\n            return m.replace(/,/g, '\\u200C');\n        })\n            .split(/\\s*(?![^(]*\\)),\\s*/)\n            .map(function (s) {\n            return s.replace(/\\u200C/g, ',');\n        });\n    }\n    function declaration() {\n        var pos = position();\n        var propMatch = match(/^(\\*?[-#\\/\\*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/);\n        if (!propMatch) {\n            return;\n        }\n        var prop = trim(propMatch[0]);\n        if (!match(/^:\\s*/)) {\n            return error(\"property missing ':'\");\n        }\n        var val = match(/^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^\\)]*?\\)|[^};])+)/);\n        var ret = pos({\n            type: 'declaration',\n            property: prop.replace(commentre, ''),\n            value: val ? trim(val[0]).replace(commentre, '') : ''\n        });\n        match(/^[;\\s]*/);\n        return ret;\n    }\n    function declarations() {\n        var decls = [];\n        if (!open()) {\n            return error(\"missing '{'\");\n        }\n        comments(decls);\n        var decl;\n        while ((decl = declaration())) {\n            if (decl !== false) {\n                decls.push(decl);\n                comments(decls);\n            }\n            decl = declaration();\n        }\n        if (!close()) {\n            return error(\"missing '}'\");\n        }\n        return decls;\n    }\n    function keyframe() {\n        var m;\n        var vals = [];\n        var pos = position();\n        while ((m = match(/^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/))) {\n            vals.push(m[1]);\n            match(/^,\\s*/);\n        }\n        if (!vals.length) {\n            return;\n        }\n        return pos({\n            type: 'keyframe',\n            values: vals,\n            declarations: declarations()\n        });\n    }\n    function atkeyframes() {\n        var pos = position();\n        var m = match(/^@([-\\w]+)?keyframes\\s*/);\n        if (!m) {\n            return;\n        }\n        var vendor = m[1];\n        m = match(/^([-\\w]+)\\s*/);\n        if (!m) {\n            return error('@keyframes missing name');\n        }\n        var name = m[1];\n        if (!open()) {\n            return error(\"@keyframes missing '{'\");\n        }\n        var frame;\n        var frames = comments();\n        while ((frame = keyframe())) {\n            frames.push(frame);\n            frames = frames.concat(comments());\n        }\n        if (!close()) {\n            return error(\"@keyframes missing '}'\");\n        }\n        return pos({\n            type: 'keyframes',\n            name: name,\n            vendor: vendor,\n            keyframes: frames\n        });\n    }\n    function atsupports() {\n        var pos = position();\n        var m = match(/^@supports *([^{]+)/);\n        if (!m) {\n            return;\n        }\n        var supports = trim(m[1]);\n        if (!open()) {\n            return error(\"@supports missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@supports missing '}'\");\n        }\n        return pos({\n            type: 'supports',\n            supports: supports,\n            rules: style\n        });\n    }\n    function athost() {\n        var pos = position();\n        var m = match(/^@host\\s*/);\n        if (!m) {\n            return;\n        }\n        if (!open()) {\n            return error(\"@host missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@host missing '}'\");\n        }\n        return pos({\n            type: 'host',\n            rules: style\n        });\n    }\n    function atmedia() {\n        var pos = position();\n        var m = match(/^@media *([^{]+)/);\n        if (!m) {\n            return;\n        }\n        var media = trim(m[1]);\n        if (!open()) {\n            return error(\"@media missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@media missing '}'\");\n        }\n        return pos({\n            type: 'media',\n            media: media,\n            rules: style\n        });\n    }\n    function atcustommedia() {\n        var pos = position();\n        var m = match(/^@custom-media\\s+(--[^\\s]+)\\s*([^{;]+);/);\n        if (!m) {\n            return;\n        }\n        return pos({\n            type: 'custom-media',\n            name: trim(m[1]),\n            media: trim(m[2])\n        });\n    }\n    function atpage() {\n        var pos = position();\n        var m = match(/^@page */);\n        if (!m) {\n            return;\n        }\n        var sel = selector() || [];\n        if (!open()) {\n            return error(\"@page missing '{'\");\n        }\n        var decls = comments();\n        var decl;\n        while ((decl = declaration())) {\n            decls.push(decl);\n            decls = decls.concat(comments());\n        }\n        if (!close()) {\n            return error(\"@page missing '}'\");\n        }\n        return pos({\n            type: 'page',\n            selectors: sel,\n            declarations: decls\n        });\n    }\n    function atdocument() {\n        var pos = position();\n        var m = match(/^@([-\\w]+)?document *([^{]+)/);\n        if (!m) {\n            return;\n        }\n        var vendor = trim(m[1]);\n        var doc = trim(m[2]);\n        if (!open()) {\n            return error(\"@document missing '{'\");\n        }\n        var style = comments().concat(rules());\n        if (!close()) {\n            return error(\"@document missing '}'\");\n        }\n        return pos({\n            type: 'document',\n            document: doc,\n            vendor: vendor,\n            rules: style\n        });\n    }\n    function atfontface() {\n        var pos = position();\n        var m = match(/^@font-face\\s*/);\n        if (!m) {\n            return;\n        }\n        if (!open()) {\n            return error(\"@font-face missing '{'\");\n        }\n        var decls = comments();\n        var decl;\n        while ((decl = declaration())) {\n            decls.push(decl);\n            decls = decls.concat(comments());\n        }\n        if (!close()) {\n            return error(\"@font-face missing '}'\");\n        }\n        return pos({\n            type: 'font-face',\n            declarations: decls\n        });\n    }\n    var atimport = _compileAtrule('import');\n    var atcharset = _compileAtrule('charset');\n    var atnamespace = _compileAtrule('namespace');\n    function _compileAtrule(name) {\n        var re = new RegExp('^@' + name + '\\\\s*([^;]+);');\n        return function () {\n            var pos = position();\n            var m = match(re);\n            if (!m) {\n                return;\n            }\n            var ret = { type: name };\n            ret[name] = m[1].trim();\n            return pos(ret);\n        };\n    }\n    function atrule() {\n        if (css[0] !== '@') {\n            return;\n        }\n        return (atkeyframes() ||\n            atmedia() ||\n            atcustommedia() ||\n            atsupports() ||\n            atimport() ||\n            atcharset() ||\n            atnamespace() ||\n            atdocument() ||\n            atpage() ||\n            athost() ||\n            atfontface());\n    }\n    function rule() {\n        var pos = position();\n        var sel = selector();\n        if (!sel) {\n            return error('selector missing');\n        }\n        comments();\n        return pos({\n            type: 'rule',\n            selectors: sel,\n            declarations: declarations()\n        });\n    }\n    return addParent(stylesheet());\n}\nfunction trim(str) {\n    return str ? str.replace(/^\\s+|\\s+$/g, '') : '';\n}\nfunction addParent(obj, parent) {\n    var isNode = obj && typeof obj.type === 'string';\n    var childParent = isNode ? obj : parent;\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\n        var k = _a[_i];\n        var value = obj[k];\n        if (Array.isArray(value)) {\n            value.forEach(function (v) {\n                addParent(v, childParent);\n            });\n        }\n        else if (value && typeof value === 'object') {\n            addParent(value, childParent);\n        }\n    }\n    if (isNode) {\n        Object.defineProperty(obj, 'parent', {\n            configurable: true,\n            writable: true,\n            enumerable: false,\n            value: parent || null\n        });\n    }\n    return obj;\n}\n\nvar tagMap = {\n    script: 'noscript',\n    altglyph: 'altGlyph',\n    altglyphdef: 'altGlyphDef',\n    altglyphitem: 'altGlyphItem',\n    animatecolor: 'animateColor',\n    animatemotion: 'animateMotion',\n    animatetransform: 'animateTransform',\n    clippath: 'clipPath',\n    feblend: 'feBlend',\n    fecolormatrix: 'feColorMatrix',\n    fecomponenttransfer: 'feComponentTransfer',\n    fecomposite: 'feComposite',\n    feconvolvematrix: 'feConvolveMatrix',\n    fediffuselighting: 'feDiffuseLighting',\n    fedisplacementmap: 'feDisplacementMap',\n    fedistantlight: 'feDistantLight',\n    fedropshadow: 'feDropShadow',\n    feflood: 'feFlood',\n    fefunca: 'feFuncA',\n    fefuncb: 'feFuncB',\n    fefuncg: 'feFuncG',\n    fefuncr: 'feFuncR',\n    fegaussianblur: 'feGaussianBlur',\n    feimage: 'feImage',\n    femerge: 'feMerge',\n    femergenode: 'feMergeNode',\n    femorphology: 'feMorphology',\n    feoffset: 'feOffset',\n    fepointlight: 'fePointLight',\n    fespecularlighting: 'feSpecularLighting',\n    fespotlight: 'feSpotLight',\n    fetile: 'feTile',\n    feturbulence: 'feTurbulence',\n    foreignobject: 'foreignObject',\n    glyphref: 'glyphRef',\n    lineargradient: 'linearGradient',\n    radialgradient: 'radialGradient'\n};\nfunction getTagName(n) {\n    var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;\n    if (tagName === 'link' && n.attributes._cssText) {\n        tagName = 'style';\n    }\n    return tagName;\n}\nfunction escapeRegExp(str) {\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\nvar HOVER_SELECTOR = /([^\\\\]):hover/;\nvar HOVER_SELECTOR_GLOBAL = new RegExp(HOVER_SELECTOR.source, 'g');\nfunction addHoverClass(cssText, cache) {\n    var cachedStyle = cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.get(cssText);\n    if (cachedStyle)\n        return cachedStyle;\n    var ast = parse(cssText, {\n        silent: true\n    });\n    if (!ast.stylesheet) {\n        return cssText;\n    }\n    var selectors = [];\n    ast.stylesheet.rules.forEach(function (rule) {\n        if ('selectors' in rule) {\n            (rule.selectors || []).forEach(function (selector) {\n                if (HOVER_SELECTOR.test(selector)) {\n                    selectors.push(selector);\n                }\n            });\n        }\n    });\n    if (selectors.length === 0) {\n        return cssText;\n    }\n    var selectorMatcher = new RegExp(selectors\n        .filter(function (selector, index) { return selectors.indexOf(selector) === index; })\n        .sort(function (a, b) { return b.length - a.length; })\n        .map(function (selector) {\n        return escapeRegExp(selector);\n    })\n        .join('|'), 'g');\n    var result = cssText.replace(selectorMatcher, function (selector) {\n        var newSelector = selector.replace(HOVER_SELECTOR_GLOBAL, '$1.\\\\:hover');\n        return selector + \", \" + newSelector;\n    });\n    cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.set(cssText, result);\n    return result;\n}\nfunction createCache() {\n    var stylesWithHoverClass = new Map();\n    return {\n        stylesWithHoverClass: stylesWithHoverClass\n    };\n}\nfunction buildNode(n, options) {\n    var doc = options.doc, hackCss = options.hackCss, cache = options.cache;\n    switch (n.type) {\n        case NodeType.Document:\n            return doc.implementation.createDocument(null, '', null);\n        case NodeType.DocumentType:\n            return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);\n        case NodeType.Element:\n            var tagName = getTagName(n);\n            var node_1;\n            if (n.isSVG) {\n                node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);\n            }\n            else {\n                node_1 = doc.createElement(tagName);\n            }\n            var _loop_1 = function (name_1) {\n                if (!n.attributes.hasOwnProperty(name_1)) {\n                    return \"continue\";\n                }\n                var value = n.attributes[name_1];\n                if (tagName === 'option' && name_1 === 'selected' && value === false) {\n                    return \"continue\";\n                }\n                value =\n                    typeof value === 'boolean' || typeof value === 'number' ? '' : value;\n                if (!name_1.startsWith('rr_')) {\n                    var isTextarea = tagName === 'textarea' && name_1 === 'value';\n                    var isRemoteOrDynamicCss = tagName === 'style' && name_1 === '_cssText';\n                    if (isRemoteOrDynamicCss && hackCss) {\n                        value = addHoverClass(value, cache);\n                    }\n                    if (isTextarea || isRemoteOrDynamicCss) {\n                        var child = doc.createTextNode(value);\n                        for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {\n                            var c = _a[_i];\n                            if (c.nodeType === node_1.TEXT_NODE) {\n                                node_1.removeChild(c);\n                            }\n                        }\n                        node_1.appendChild(child);\n                        return \"continue\";\n                    }\n                    try {\n                        if (n.isSVG && name_1 === 'xlink:href') {\n                            node_1.setAttributeNS('http://www.w3.org/1999/xlink', name_1, value);\n                        }\n                        else if (name_1 === 'onload' ||\n                            name_1 === 'onclick' ||\n                            name_1.substring(0, 7) === 'onmouse') {\n                            node_1.setAttribute('_' + name_1, value);\n                        }\n                        else if (tagName === 'meta' &&\n                            n.attributes['http-equiv'] === 'Content-Security-Policy' &&\n                            name_1 === 'content') {\n                            node_1.setAttribute('csp-content', value);\n                            return \"continue\";\n                        }\n                        else if (tagName === 'link' &&\n                            n.attributes.rel === 'preload' &&\n                            n.attributes.as === 'script') {\n                        }\n                        else if (tagName === 'link' &&\n                            n.attributes.rel === 'prefetch' &&\n                            typeof n.attributes.href === 'string' &&\n                            n.attributes.href.endsWith('.js')) {\n                        }\n                        else if (tagName === 'img' &&\n                            n.attributes.srcset &&\n                            n.attributes.rr_dataURL) {\n                            node_1.setAttribute('rrweb-original-srcset', n.attributes.srcset);\n                        }\n                        else {\n                            node_1.setAttribute(name_1, value);\n                        }\n                    }\n                    catch (error) {\n                    }\n                }\n                else {\n                    if (tagName === 'canvas' && name_1 === 'rr_dataURL') {\n                        var image_1 = document.createElement('img');\n                        image_1.src = value;\n                        image_1.onload = function () {\n                            var ctx = node_1.getContext('2d');\n                            if (ctx) {\n                                ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);\n                            }\n                        };\n                    }\n                    else if (tagName === 'img' && name_1 === 'rr_dataURL') {\n                        var image = node_1;\n                        if (!image.currentSrc.startsWith('data:')) {\n                            image.setAttribute('rrweb-original-src', n.attributes.src);\n                            image.src = value;\n                        }\n                    }\n                    if (name_1 === 'rr_width') {\n                        node_1.style.width = value;\n                    }\n                    else if (name_1 === 'rr_height') {\n                        node_1.style.height = value;\n                    }\n                    else if (name_1 === 'rr_mediaCurrentTime') {\n                        node_1.currentTime = n.attributes\n                            .rr_mediaCurrentTime;\n                    }\n                    else if (name_1 === 'rr_mediaState') {\n                        switch (value) {\n                            case 'played':\n                                node_1\n                                    .play()[\"catch\"](function (e) { return console.warn('media playback error', e); });\n                                break;\n                            case 'paused':\n                                node_1.pause();\n                                break;\n                        }\n                    }\n                }\n            };\n            for (var name_1 in n.attributes) {\n                _loop_1(name_1);\n            }\n            if (n.isShadowHost) {\n                if (!node_1.shadowRoot) {\n                    node_1.attachShadow({ mode: 'open' });\n                }\n                else {\n                    while (node_1.shadowRoot.firstChild) {\n                        node_1.shadowRoot.removeChild(node_1.shadowRoot.firstChild);\n                    }\n                }\n            }\n            return node_1;\n        case NodeType.Text:\n            return doc.createTextNode(n.isStyle && hackCss\n                ? addHoverClass(n.textContent, cache)\n                : n.textContent);\n        case NodeType.CDATA:\n            return doc.createCDATASection(n.textContent);\n        case NodeType.Comment:\n            return doc.createComment(n.textContent);\n        default:\n            return null;\n    }\n}\nfunction buildNodeWithSN(n, options) {\n    var doc = options.doc, map = options.map, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.hackCss, hackCss = _b === void 0 ? true : _b, afterAppend = options.afterAppend, cache = options.cache;\n    var node = buildNode(n, { doc: doc, hackCss: hackCss, cache: cache });\n    if (!node) {\n        return null;\n    }\n    if (n.rootId) {\n        console.assert(map[n.rootId] === doc, 'Target document should has the same root id.');\n    }\n    if (n.type === NodeType.Document) {\n        doc.close();\n        doc.open();\n        if (n.compatMode === 'BackCompat' &&\n            n.childNodes &&\n            n.childNodes[0].type !== NodeType.DocumentType) {\n            if (n.childNodes[0].type === NodeType.Element &&\n                'xmlns' in n.childNodes[0].attributes &&\n                n.childNodes[0].attributes.xmlns === 'http://www.w3.org/1999/xhtml') {\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">');\n            }\n            else {\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">');\n            }\n        }\n        node = doc;\n    }\n    node.__sn = n;\n    map[n.id] = node;\n    if ((n.type === NodeType.Document || n.type === NodeType.Element) &&\n        !skipChild) {\n        for (var _i = 0, _c = n.childNodes; _i < _c.length; _i++) {\n            var childN = _c[_i];\n            var childNode = buildNodeWithSN(childN, {\n                doc: doc,\n                map: map,\n                skipChild: false,\n                hackCss: hackCss,\n                afterAppend: afterAppend,\n                cache: cache\n            });\n            if (!childNode) {\n                console.warn('Failed to rebuild', childN);\n                continue;\n            }\n            if (childN.isShadow && isElement(node) && node.shadowRoot) {\n                node.shadowRoot.appendChild(childNode);\n            }\n            else {\n                node.appendChild(childNode);\n            }\n            if (afterAppend) {\n                afterAppend(childNode);\n            }\n        }\n    }\n    return node;\n}\nfunction visit(idNodeMap, onVisit) {\n    function walk(node) {\n        onVisit(node);\n    }\n    for (var key in idNodeMap) {\n        if (idNodeMap[key]) {\n            walk(idNodeMap[key]);\n        }\n    }\n}\nfunction handleScroll(node) {\n    var n = node.__sn;\n    if (n.type !== NodeType.Element) {\n        return;\n    }\n    var el = node;\n    for (var name_2 in n.attributes) {\n        if (!(n.attributes.hasOwnProperty(name_2) && name_2.startsWith('rr_'))) {\n            continue;\n        }\n        var value = n.attributes[name_2];\n        if (name_2 === 'rr_scrollLeft') {\n            el.scrollLeft = value;\n        }\n        if (name_2 === 'rr_scrollTop') {\n            el.scrollTop = value;\n        }\n    }\n}\nfunction rebuild(n, options) {\n    var doc = options.doc, onVisit = options.onVisit, _a = options.hackCss, hackCss = _a === void 0 ? true : _a, afterAppend = options.afterAppend, cache = options.cache;\n    var idNodeMap = {};\n    var node = buildNodeWithSN(n, {\n        doc: doc,\n        map: idNodeMap,\n        skipChild: false,\n        hackCss: hackCss,\n        afterAppend: afterAppend,\n        cache: cache\n    });\n    visit(idNodeMap, function (visitedNode) {\n        if (onVisit) {\n            onVisit(visitedNode);\n        }\n        handleScroll(visitedNode);\n    });\n    return [node, idNodeMap];\n}\n\nexport { IGNORED_NODE, NodeType, addHoverClass, buildNodeWithSN, cleanupSnapshot, createCache, is2DCanvasBlank, isElement, isShadowRoot, maskInputValue, needMaskingText, rebuild, serializeNodeWithId, snapshot, transformAttribute, visitSnapshot };\n", "export function parseCSSText(cssText: string): Record<string, string> {\n\tconst res: Record<string, string> = {};\n\tconst listDelimiter = /;(?![^(]*\\))/g;\n\tconst propertyDelimiter = /:(.+)/;\n\tcssText.split(listDelimiter).forEach(function (item) {\n\t  if (item) {\n\t\tconst tmp = item.split(propertyDelimiter);\n\t\ttmp.length > 1 && (res[camelize(tmp[0].trim())] = tmp[1].trim());\n\t  }\n\t});\n\treturn res;\n  }\n  \n  export function toCSSText(style: Record<string, string>): string {\n\tconst properties = [];\n\tfor (let name in style) {\n\t  const value = style[name];\n\t  if (typeof value !== 'string') continue;\n\t  const normalizedName = hyphenate(name);\n\t  properties.push(`${normalizedName}:${value};`);\n\t}\n\treturn properties.join(' ');\n  }\n  \n  /**\n   * Camelize a hyphen-delimited string.\n   */\n  const camelizeRE = /-(\\w)/g;\n  export const camelize = (str: string): string => {\n\treturn str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''));\n  };\n  \n  /**\n   * Hyphenate a camelCase string.\n   */\n  const hyphenateRE = /\\B([A-Z])/g;\n  export const hyphenate = (str: string): string => {\n\treturn str.replace(hyphenateRE, '-$1').toLowerCase();\n  };\n  ", "import { INode, NodeType, serializedNodeWithId } from 'rrweb-snapshot';\nimport { NWSAPI } from 'nwsapi';\nimport { parseCSSText, camelize, toCSSText } from './style';\nconst nwsapi = require('nwsapi');\nconst cssom = require('cssom');\n\nexport abstract class RRNode {\n  __sn: serializedNodeWithId | undefined;\n  children: Array<RRNode> = [];\n  parentElement: RRElement | null = null;\n  parentNode: RRNode | null = null;\n  ownerDocument: RRDocument | null = null;\n  ELEMENT_NODE = 1;\n  TEXT_NODE = 3;\n\n  get firstChild() {\n    return this.children[0];\n  }\n\n  get nodeType() {\n    if (this instanceof RRDocument) return NodeType.Document;\n    if (this instanceof RRDocumentType) return NodeType.DocumentType;\n    if (this instanceof RRElement) return NodeType.Element;\n    if (this instanceof RRText) return NodeType.Text;\n    if (this instanceof RRCDATASection) return NodeType.CDATA;\n    if (this instanceof RRComment) return NodeType.Comment;\n  }\n\n  get childNodes() {\n    return this.children;\n  }\n\n  appendChild(newChild: RRNode): RRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  insertBefore(newChild: RRNode, refChild: RRNode | null): RRNode {\n    throw new Error(\n      `RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`,\n    );\n  }\n\n  contains(node: RRNode) {\n    if (node === this) return true;\n    for (const child of this.children) {\n      if (child.contains(node)) return true;\n    }\n    return false;\n  }\n\n  removeChild(node: RRNode) {\n    const indexOfChild = this.children.indexOf(node);\n    if (indexOfChild !== -1) {\n      this.children.splice(indexOfChild, 1);\n      node.parentElement = null;\n      node.parentNode = null;\n    }\n  }\n\n  toString(nodeName?: string) {\n    return `${JSON.stringify(this.__sn?.id) || ''} ${nodeName}`;\n  }\n}\n\nexport class RRWindow {\n  scrollLeft = 0;\n  scrollTop = 0;\n  scrollTo(options?: ScrollToOptions) {\n    if (!options) return;\n    if (typeof options.left === 'number') this.scrollLeft = options.left;\n    if (typeof options.top === 'number') this.scrollTop = options.top;\n  }\n}\n\nexport class RRDocument extends RRNode {\n  private mirror: Map<number, RRNode> = new Map();\n  private _nwsapi: NWSAPI;\n  get nwsapi() {\n    if (!this._nwsapi) {\n      this._nwsapi = nwsapi({\n        document: (this as unknown) as Document,\n        DOMException: (null as unknown) as new (\n          message?: string,\n          name?: string,\n        ) => DOMException,\n      });\n      this._nwsapi.configure({\n        LOGERRORS: false,\n        IDS_DUPES: true,\n        MIXEDCASE: true,\n      });\n    }\n    return this._nwsapi;\n  }\n\n  get documentElement(): RRElement {\n    return this.children.find(\n      (node) => node instanceof RRElement && node.tagName === 'HTML',\n    ) as RRElement;\n  }\n\n  get body() {\n    return (\n      this.documentElement?.children.find(\n        (node) => node instanceof RRElement && node.tagName === 'BODY',\n      ) || null\n    );\n  }\n\n  get head() {\n    return (\n      this.documentElement?.children.find(\n        (node) => node instanceof RRElement && node.tagName === 'HEAD',\n      ) || null\n    );\n  }\n\n  get implementation() {\n    return this;\n  }\n\n  get firstElementChild() {\n    return this.documentElement;\n  }\n\n  appendChild(childNode: RRNode) {\n    const nodeType = childNode.nodeType;\n    if (nodeType === NodeType.Element || nodeType === NodeType.DocumentType) {\n      if (this.children.some((s) => s.nodeType === nodeType)) {\n        throw new Error(\n          `RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${\n            nodeType === NodeType.Element ? 'RRElement' : 'RRDoctype'\n          } on RRDocument allowed.`,\n        );\n      }\n    }\n    childNode.parentElement = null;\n    childNode.parentNode = this;\n    childNode.ownerDocument = this;\n    this.children.push(childNode);\n    return childNode;\n  }\n\n  insertBefore(newChild: RRNode, refChild: RRNode | null) {\n    if (refChild === null) return this.appendChild(newChild);\n    const childIndex = this.children.indexOf(refChild);\n    if (childIndex == -1)\n      throw new Error(\n        \"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\",\n      );\n    this.children.splice(childIndex, 0, newChild);\n    newChild.parentElement = null;\n    newChild.parentNode = this;\n    newChild.ownerDocument = this;\n    return newChild;\n  }\n\n  querySelectorAll(selectors: string): RRNode[] {\n    return (this.nwsapi.select(selectors) as unknown) as RRNode[];\n  }\n\n  getElementsByTagName(tagName: string): RRElement[] {\n    if (this.documentElement)\n      return (this.documentElement as RRElement).getElementsByTagName(tagName);\n    return [];\n  }\n\n  getElementsByClassName(className: string): RRElement[] {\n    if (this.documentElement)\n      return (this.documentElement as RRElement).getElementsByClassName(\n        className,\n      );\n    return [];\n  }\n\n  getElementById(elementId: string): RRElement | null {\n    if (this.documentElement)\n      return (this.documentElement as RRElement).getElementById(elementId);\n    return null;\n  }\n\n  createDocument(\n    _namespace: string | null,\n    _qualifiedName: string | null,\n    _doctype?: DocumentType | null,\n  ) {\n    return new RRDocument();\n  }\n\n  createDocumentType(\n    qualifiedName: string,\n    publicId: string,\n    systemId: string,\n  ) {\n    const documentTypeNode = new RRDocumentType(\n      qualifiedName,\n      publicId,\n      systemId,\n    );\n    documentTypeNode.ownerDocument = this;\n    return documentTypeNode;\n  }\n\n  createElement<K extends keyof HTMLElementTagNameMap>(\n    tagName: K,\n  ): RRElementType<K>;\n  createElement(tagName: string): RRElement;\n  createElement(tagName: string) {\n    const upperTagName = tagName.toUpperCase();\n    let element;\n    switch (upperTagName) {\n      case 'AUDIO':\n      case 'VIDEO':\n        element = new RRMediaElement(upperTagName);\n        break;\n      case 'IFRAME':\n        element = new RRIframeElement(upperTagName);\n        break;\n      case 'IMG':\n        element = new RRImageElement('IMG');\n        break;\n      case 'CANVAS':\n        element = new RRCanvasElement('CANVAS');\n        break;\n      case 'STYLE':\n        element = new RRStyleElement('STYLE');\n        break;\n      default:\n        element = new RRElement(upperTagName);\n        break;\n    }\n    element.ownerDocument = this;\n    return element;\n  }\n\n  createElementNS(\n    _namespaceURI: 'http://www.w3.org/2000/svg',\n    qualifiedName: string,\n  ) {\n    return this.createElement(qualifiedName as keyof HTMLElementTagNameMap);\n  }\n\n  createComment(data: string) {\n    const commentNode = new RRComment(data);\n    commentNode.ownerDocument = this;\n    return commentNode;\n  }\n\n  createCDATASection(data: string) {\n    const sectionNode = new RRCDATASection(data);\n    sectionNode.ownerDocument = this;\n    return sectionNode;\n  }\n\n  createTextNode(data: string) {\n    const textNode = new RRText(data);\n    textNode.ownerDocument = this;\n    return textNode;\n  }\n\n  /**\n   * This does come with some side effects. For example:\n   * 1. All event listeners currently registered on the document, nodes inside the document, or the document's window are removed.\n   * 2. All existing nodes are removed from the document.\n   */\n  open() {\n    this.children = [];\n  }\n\n  close() {}\n\n  buildFromDom(dom: Document) {\n    let notSerializedId = -1;\n    const NodeTypeMap: Record<number, number> = {};\n    NodeTypeMap[document.DOCUMENT_NODE] = NodeType.Document;\n    NodeTypeMap[document.DOCUMENT_TYPE_NODE] = NodeType.DocumentType;\n    NodeTypeMap[document.ELEMENT_NODE] = NodeType.Element;\n    NodeTypeMap[document.TEXT_NODE] = NodeType.Text;\n    NodeTypeMap[document.CDATA_SECTION_NODE] = NodeType.CDATA;\n    NodeTypeMap[document.COMMENT_NODE] = NodeType.Comment;\n\n    function getValidTagName(element: HTMLElement): string {\n      if (element instanceof HTMLFormElement) {\n        return 'FORM';\n      }\n      return element.tagName.toUpperCase().trim();\n    }\n\n    const walk = function (node: INode) {\n      let serializedNodeWithId = node.__sn;\n      let rrNode: RRNode;\n      if (!serializedNodeWithId) {\n        serializedNodeWithId = {\n          type: NodeTypeMap[node.nodeType],\n          textContent: '',\n          id: notSerializedId,\n        };\n        notSerializedId -= 1;\n        node.__sn = serializedNodeWithId;\n      }\n      if (!this.mirror.has(serializedNodeWithId.id)) {\n        switch (node.nodeType) {\n          case node.DOCUMENT_NODE:\n            if (\n              serializedNodeWithId.rootId &&\n              serializedNodeWithId.rootId !== serializedNodeWithId.id\n            )\n              rrNode = this.createDocument();\n            else rrNode = this;\n            break;\n          case node.DOCUMENT_TYPE_NODE:\n            const documentType = (node as unknown) as DocumentType;\n            rrNode = this.createDocumentType(\n              documentType.name,\n              documentType.publicId,\n              documentType.systemId,\n            );\n            break;\n          case node.ELEMENT_NODE:\n            const elementNode = (node as unknown) as HTMLElement;\n            const tagName = getValidTagName(elementNode);\n            rrNode = this.createElement(tagName);\n            const rrElement = rrNode as RRElement;\n            for (const { name, value } of Array.from(elementNode.attributes)) {\n              rrElement.attributes[name] = value;\n            }\n            // form fields\n            if (\n              tagName === 'INPUT' ||\n              tagName === 'TEXTAREA' ||\n              tagName === 'SELECT'\n            ) {\n              const value = (elementNode as\n                | HTMLInputElement\n                | HTMLTextAreaElement).value;\n              if (\n                ['RADIO', 'CHECKBOX', 'SUBMIT', 'BUTTON'].includes(\n                  rrElement.attributes.type as string,\n                ) &&\n                value\n              ) {\n                rrElement.attributes.value = value;\n              } else if ((elementNode as HTMLInputElement).checked) {\n                rrElement.attributes.checked = (elementNode as HTMLInputElement).checked;\n              }\n            }\n            if (tagName === 'OPTION') {\n              const selectValue = (elementNode as HTMLOptionElement)\n                .parentElement;\n              if (\n                rrElement.attributes.value ===\n                (selectValue as HTMLSelectElement).value\n              ) {\n                rrElement.attributes.selected = (elementNode as HTMLOptionElement).selected;\n              }\n            }\n            // canvas image data\n            if (tagName === 'CANVAS') {\n              rrElement.attributes.rr_dataURL = (elementNode as HTMLCanvasElement).toDataURL();\n            }\n            // media elements\n            if (tagName === 'AUDIO' || tagName === 'VIDEO') {\n              const rrMediaElement = rrElement as RRMediaElement;\n              rrMediaElement.paused = (elementNode as HTMLMediaElement).paused;\n              rrMediaElement.currentTime = (elementNode as HTMLMediaElement).currentTime;\n            }\n            // scroll\n            if (elementNode.scrollLeft) {\n              rrElement.scrollLeft = elementNode.scrollLeft;\n            }\n            if (elementNode.scrollTop) {\n              rrElement.scrollTop = elementNode.scrollTop;\n            }\n            break;\n          case node.TEXT_NODE:\n            rrNode = this.createTextNode(\n              ((node as unknown) as Text).textContent,\n            );\n            break;\n          case node.CDATA_SECTION_NODE:\n            rrNode = this.createCDATASection();\n            break;\n          case node.COMMENT_NODE:\n            rrNode = this.createComment(\n              ((node as unknown) as Comment).textContent || '',\n            );\n            break;\n          default:\n            return;\n        }\n        rrNode.__sn = serializedNodeWithId;\n        this.mirror.set(serializedNodeWithId.id, rrNode);\n      } else {\n        rrNode = this.mirror.get(serializedNodeWithId.id);\n        rrNode.parentElement = null;\n        rrNode.parentNode = null;\n        rrNode.children = [];\n      }\n      const parentNode = node.parentElement || node.parentNode;\n      if (parentNode) {\n        const parentSN = ((parentNode as unknown) as INode).__sn;\n        const parentRRNode = this.mirror.get(parentSN.id);\n        parentRRNode.appendChild(rrNode);\n        rrNode.parentNode = parentRRNode;\n        rrNode.parentElement =\n          parentRRNode instanceof RRElement ? parentRRNode : null;\n      }\n\n      if (\n        serializedNodeWithId.type === NodeType.Document ||\n        serializedNodeWithId.type === NodeType.Element\n      ) {\n        node.childNodes.forEach((node) => walk((node as unknown) as INode));\n      }\n    }.bind(this);\n\n    if (dom) {\n      this.destroyTree();\n      walk((dom as unknown) as INode);\n    }\n  }\n\n  destroyTree() {\n    this.children = [];\n    this.mirror.clear();\n  }\n\n  toString() {\n    return super.toString('RRDocument');\n  }\n}\n\nexport class RRDocumentType extends RRNode {\n  readonly name: string;\n  readonly publicId: string;\n  readonly systemId: string;\n\n  constructor(qualifiedName: string, publicId: string, systemId: string) {\n    super();\n    this.name = qualifiedName;\n    this.publicId = publicId;\n    this.systemId = systemId;\n  }\n\n  toString() {\n    return super.toString('RRDocumentType');\n  }\n}\n\nexport class RRElement extends RRNode {\n  tagName: string;\n  attributes: Record<string, string | number | boolean> = {};\n  scrollLeft: number = 0;\n  scrollTop: number = 0;\n  shadowRoot: RRElement | null = null;\n\n  constructor(tagName: string) {\n    super();\n    this.tagName = tagName;\n  }\n\n  get classList() {\n    return new ClassList(\n      this.attributes.class as string | undefined,\n      (newClassName) => {\n        this.attributes.class = newClassName;\n      },\n    );\n  }\n\n  get id() {\n    return this.attributes.id;\n  }\n\n  get className() {\n    return this.attributes.class || '';\n  }\n\n  get textContent() {\n    return '';\n  }\n\n  set textContent(newText: string) {}\n\n  get style() {\n    const style = (this.attributes.style\n      ? parseCSSText(this.attributes.style as string)\n      : {}) as Record<string, string> & {\n      setProperty: (\n        name: string,\n        value: string | null,\n        priority?: string | null,\n      ) => void;\n    };\n    style.setProperty = (name: string, value: string | null) => {\n      const normalizedName = camelize(name);\n      if (!value) delete style[normalizedName];\n      else style[normalizedName] = value;\n      this.attributes.style = toCSSText(style);\n    };\n    // This is used to bypass the smoothscroll polyfill in rrweb player.\n    style.scrollBehavior = '';\n    return style;\n  }\n\n  get firstElementChild(): RRElement | null {\n    for (let child of this.children)\n      if (child instanceof RRElement) return child;\n    return null;\n  }\n\n  get nextElementSibling(): RRElement | null {\n    let parentNode = this.parentNode;\n    if (!parentNode) return null;\n    const siblings = parentNode.children;\n    let index = siblings.indexOf(this);\n    for (let i = index + 1; i < siblings.length; i++)\n      if (siblings[i] instanceof RRElement) return siblings[i] as RRElement;\n    return null;\n  }\n\n  getAttribute(name: string) {\n    let upperName = name && name.toLowerCase();\n    if (upperName in this.attributes) return this.attributes[upperName];\n    return null;\n  }\n\n  setAttribute(name: string, attribute: string) {\n    this.attributes[name.toLowerCase()] = attribute;\n  }\n\n  hasAttribute(name: string) {\n    return (name && name.toLowerCase()) in this.attributes;\n  }\n\n  setAttributeNS(\n    _namespace: string | null,\n    qualifiedName: string,\n    value: string,\n  ): void {\n    this.setAttribute(qualifiedName, value);\n  }\n\n  removeAttribute(name: string) {\n    delete this.attributes[name];\n  }\n\n  appendChild(newChild: RRNode): RRNode {\n    this.children.push(newChild);\n    newChild.parentNode = this;\n    newChild.parentElement = this;\n    newChild.ownerDocument = this.ownerDocument;\n    return newChild;\n  }\n\n  insertBefore(newChild: RRNode, refChild: RRNode | null): RRNode {\n    if (refChild === null) return this.appendChild(newChild);\n    const childIndex = this.children.indexOf(refChild);\n    if (childIndex == -1)\n      throw new Error(\n        \"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\",\n      );\n    this.children.splice(childIndex, 0, newChild);\n    newChild.parentElement = null;\n    newChild.parentNode = this;\n    newChild.ownerDocument = this.ownerDocument;\n    return newChild;\n  }\n\n  querySelectorAll(selectors: string): RRNode[] {\n    if (this.ownerDocument !== null) {\n      return (this.ownerDocument.nwsapi.select(\n        selectors,\n        (this as unknown) as Element,\n      ) as unknown) as RRNode[];\n    }\n    return [];\n  }\n\n  getElementById(elementId: string): RRElement | null {\n    if (this instanceof RRElement && this.id === elementId) return this;\n    for (const child of this.children) {\n      if (child instanceof RRElement) {\n        const result = child.getElementById(elementId);\n        if (result !== null) return result;\n      }\n    }\n    return null;\n  }\n\n  getElementsByClassName(className: string): RRElement[] {\n    let elements: RRElement[] = [];\n    const queryClassList = new ClassList(className);\n    // Make sure this element has all queried class names.\n    if (\n      this instanceof RRElement &&\n      queryClassList.filter((queriedClassName) =>\n        this.classList.some((name) => name === queriedClassName),\n      ).length == queryClassList.length\n    )\n      elements.push(this);\n    for (const child of this.children) {\n      if (child instanceof RRElement)\n        elements = elements.concat(child.getElementsByClassName(className));\n    }\n    return elements;\n  }\n\n  getElementsByTagName(tagName: string): RRElement[] {\n    let elements: RRElement[] = [];\n    const normalizedTagName = tagName.toUpperCase();\n    if (this instanceof RRElement && this.tagName === normalizedTagName)\n      elements.push(this);\n    for (const child of this.children) {\n      if (child instanceof RRElement)\n        elements = elements.concat(child.getElementsByTagName(tagName));\n    }\n    return elements;\n  }\n\n  dispatchEvent(_event: Event) {\n    return true;\n  }\n\n  /**\n   * Creates a shadow root for element and returns it.\n   */\n  attachShadow(init: ShadowRootInit): RRElement {\n    this.shadowRoot = init.mode === 'open' ? this : null;\n    return this;\n  }\n\n  toString() {\n    let attributeString = '';\n    for (let attribute in this.attributes) {\n      attributeString += `${attribute}=\"${this.attributes[attribute]}\" `;\n    }\n    return `${super.toString(this.tagName)} ${attributeString}`;\n  }\n}\n\nexport class RRImageElement extends RRElement {\n  src: string;\n  width: number;\n  height: number;\n  onload: ((this: GlobalEventHandlers, ev: Event) => any) | null;\n}\n\nexport class RRMediaElement extends RRElement {\n  currentTime: number = 0;\n  paused: boolean = true;\n  async play() {\n    this.paused = false;\n  }\n  async pause() {\n    this.paused = true;\n  }\n}\n\nexport class RRCanvasElement extends RRElement {\n  /**\n   * This is just a dummy implementation to prevent rrweb replayer from drawing mouse tail. If further analysis of canvas is needed, we may implement it with node-canvas.\n   */\n  getContext(): CanvasRenderingContext2D | null {\n    return null;\n  }\n}\n\nexport class RRStyleElement extends RRElement {\n  private _sheet: CSSStyleSheet | null = null;\n\n  get sheet() {\n    if (!this._sheet) {\n      let result = '';\n      for (let child of this.childNodes)\n        if (child.nodeType === NodeType.Text)\n          result += (child as RRText).textContent;\n      this._sheet = cssom.parse(result);\n    }\n    return this._sheet;\n  }\n}\n\nexport class RRIframeElement extends RRElement {\n  width: string = '';\n  height: string = '';\n  src: string = '';\n  contentDocument: RRDocument = new RRDocument();\n  contentWindow: RRWindow = new RRWindow();\n\n  constructor(tagName: string) {\n    super(tagName);\n    const htmlElement = this.contentDocument.createElement('HTML');\n    this.contentDocument.appendChild(htmlElement);\n    htmlElement.appendChild(this.contentDocument.createElement('HEAD'));\n    htmlElement.appendChild(this.contentDocument.createElement('BODY'));\n  }\n}\n\nexport class RRText extends RRNode {\n  textContent: string;\n\n  constructor(data: string) {\n    super();\n    this.textContent = data;\n  }\n\n  toString() {\n    return `${super.toString('RRText')} text=${JSON.stringify(\n      this.textContent,\n    )}`;\n  }\n}\n\nexport class RRComment extends RRNode {\n  data: string;\n\n  constructor(data: string) {\n    super();\n    this.data = data;\n  }\n\n  toString() {\n    return `${super.toString('RRComment')} data=${JSON.stringify(this.data)}`;\n  }\n}\nexport class RRCDATASection extends RRNode {\n  data: string;\n\n  constructor(data: string) {\n    super();\n    this.data = data;\n  }\n\n  toString() {\n    return `${super.toString('RRCDATASection')} data=${JSON.stringify(\n      this.data,\n    )}`;\n  }\n}\n\ninterface RRElementTagNameMap {\n  img: RRImageElement;\n  audio: RRMediaElement;\n  video: RRMediaElement;\n}\n\ntype RRElementType<\n  K extends keyof HTMLElementTagNameMap\n> = K extends keyof RRElementTagNameMap ? RRElementTagNameMap[K] : RRElement;\n\nclass ClassList extends Array {\n  private onChange: ((newClassText: string) => void) | undefined;\n\n  constructor(\n    classText?: string,\n    onChange?: ((newClassText: string) => void) | undefined,\n  ) {\n    super();\n    if (classText) {\n      const classes = classText.trim().split(/\\s+/);\n      super.push(...classes);\n    }\n    this.onChange = onChange;\n  }\n\n  add = (...classNames: string[]) => {\n    for (const item of classNames) {\n      const className = String(item);\n      if (super.indexOf(className) >= 0) continue;\n      super.push(className);\n    }\n    this.onChange && this.onChange(super.join(' '));\n  };\n\n  remove = (...classNames: string[]) => {\n    for (const item of classNames) {\n      const className = String(item);\n      const index = super.indexOf(className);\n      if (index < 0) continue;\n      super.splice(index, 1);\n    }\n    this.onChange && this.onChange(super.join(' '));\n  };\n}\n"], "names": ["NodeType", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "this", "constructor", "create", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "pop", "length", "push", "camelizeRE", "camelize", "str", "replace", "c", "toUpperCase", "hyphenateRE", "hyphenate", "toLowerCase", "nwsapi", "require", "cssom", "RRNode", "children", "RRDocument", "Document", "RRDocumentType", "DocumentType", "<PERSON><PERSON><PERSON>", "Element", "RRText", "Text", "RRCDATASection", "CDATA", "RRComment", "Comment", "<PERSON><PERSON><PERSON><PERSON>", "Error", "refChild", "node", "_a", "_i", "contains", "indexOfChild", "indexOf", "splice", "parentElement", "parentNode", "nodeName", "JSON", "stringify", "__sn", "id", "RRWindow", "options", "left", "scrollLeft", "top", "scrollTop", "_this", "Map", "_nwsapi", "document", "DOMException", "configure", "LOGERRORS", "IDS_DUPES", "MIXEDCASE", "find", "tagName", "documentElement", "childNode", "nodeType", "some", "s", "ownerDocument", "append<PERSON><PERSON><PERSON>", "childIndex", "selectors", "select", "getElementsByTagName", "className", "getElementsByClassName", "elementId", "getElementById", "_namespace", "_qualifiedName", "_doctype", "qualifiedName", "publicId", "systemId", "documentTypeNode", "element", "upperTagName", "RRMediaElement", "RRIframeElement", "RRImageElement", "RRCanvasElement", "RRStyleElement", "_namespaceURI", "createElement", "data", "commentNode", "sectionNode", "textNode", "dom", "notSerializedId", "NodeTypeMap", "DOCUMENT_NODE", "DOCUMENT_TYPE_NODE", "ELEMENT_NODE", "TEXT_NODE", "CDATA_SECTION_NODE", "COMMENT_NODE", "walk", "rrNode", "serializedNodeWithId", "type", "textContent", "mirror", "has", "get", "rootId", "createDocument", "documentType", "createDocumentType", "name", "elementNode", "HTMLFormElement", "trim", "rr<PERSON><PERSON>", "from", "attributes", "_b", "name_1", "includes", "checked", "selectValue", "selected", "rr_dataURL", "toDataURL", "rrMediaElement", "paused", "currentTime", "createTextNode", "createCDATASection", "createComment", "set", "parentSN", "parentRRNode", "childNodes", "for<PERSON>ach", "bind", "destroyTree", "clear", "_super", "toString", "ClassList", "class", "newClassName", "newText", "cssText", "res", "propertyDelimiter", "style", "split", "item", "tmp", "setProperty", "normalizedName", "properties", "join", "toCSSText", "scroll<PERSON>eh<PERSON>or", "child", "siblings", "i", "upperName", "attribute", "setAttribute", "elements", "queryClassList", "filter", "queriedClassName", "classList", "concat", "normalizedTagName", "_event", "init", "shadowRoot", "mode", "attributeString", "_sheet", "parse", "htmlElement", "contentDocument", "classText", "onChange", "classNames", "classNames_1", "classNames_2", "index", "classes"], "mappings": ";;;;;;;;;;;;;;AAgBA,IChBIA,EDgBAC,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOC,OAAOK,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,MAC3EN,EAAGC,IAGrB,SAASS,EAAUV,EAAGC,GACzB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIU,UAAU,uBAAyBC,OAAOX,GAAK,iCAE7D,SAASY,IAAOC,KAAKC,YAAcf,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEO,UAAkB,OAANN,EAAaC,OAAOc,OAAOf,IAAMY,EAAGN,UAAYN,EAAEM,UAAW,IAAIM,GAyC5E,SAASI,EAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,IACpF,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiB,MAAEK,IAAW,MAAOG,GAAKL,EAAOK,IACvF,SAASF,EAAKI,GAJlB,IAAeL,EAIaK,EAAOC,KAAOT,EAAQQ,EAAOL,QAJ1CA,EAIyDK,EAAOL,MAJhDA,aAAiBN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,OAITO,KAAKR,EAAWK,GAClGH,GAAMN,EAAYA,EAAUa,MAAMhB,EAASC,GAAc,KAAKS,WAI/D,SAASO,EAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,IAAOK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEZ,KAAMkB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOpC,OAAU0B,EACvJ,SAASM,EAAKK,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIhB,EAAG,MAAM,IAAI1B,UAAU,mCAC3B,KAAO8B,OACH,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARc,EAAG,GAASf,EAAU,OAAIe,EAAG,GAAKf,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE9B,KAAK6B,GAAI,GAAKA,EAAEV,SAAWW,EAAIA,EAAE9B,KAAK6B,EAAGe,EAAG,KAAKrB,KAAM,OAAOO,EAE3J,OADID,EAAI,EAAGC,IAAGc,EAAK,CAAS,EAARA,EAAG,GAAQd,EAAEb,QACzB2B,EAAG,IACP,KAAK,EAAG,KAAK,EAAGd,EAAIc,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAEhB,MAAO2B,EAAG,GAAIrB,MAAM,GAChD,KAAK,EAAGS,EAAEC,QAASJ,EAAIe,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAIS,MAAOb,EAAEG,KAAKU,MAAO,SACxC,QACI,KAAMf,EAAIE,EAAEG,MAAML,EAAIA,EAAEgB,OAAS,GAAKhB,EAAEA,EAAEgB,OAAS,KAAkB,IAAVF,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,SACjG,GAAc,IAAVY,EAAG,MAAcd,GAAMc,EAAG,GAAKd,EAAE,IAAMc,EAAG,GAAKd,EAAE,IAAM,CAAEE,EAAEC,MAAQW,EAAG,GAAI,MAC9E,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIc,EAAI,MAC7D,GAAId,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIW,KAAKH,GAAK,MACvDd,EAAE,IAAIE,EAAEI,IAAIS,MAChBb,EAAEG,KAAKU,MAAO,SAEtBD,EAAKjB,EAAK3B,KAAKS,EAASuB,GAC1B,MAAOZ,GAAKwB,EAAK,CAAC,EAAGxB,GAAIS,EAAI,UAAeD,EAAIE,EAAI,EACtD,GAAY,EAARc,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3B,MAAO2B,EAAG,GAAKA,EAAG,QAAK,EAAQrB,MAAM,GArB9BL,CAAK,CAACwB,EAAGC,OCjF7D,SAAWtD,GACPA,EAASA,EAAmB,SAAI,GAAK,WACrCA,EAASA,EAAuB,aAAI,GAAK,eACzCA,EAASA,EAAkB,QAAI,GAAK,UACpCA,EAASA,EAAe,KAAI,GAAK,OACjCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAkB,QAAI,GAAK,UANxC,CAOGA,IAAaA,EAAW,KCmBzB,IAAM2D,EAAa,SACNC,EAAW,SAACC,GAC1B,OAAOA,EAAIC,QAAQH,GAAY,SAAChB,EAAGoB,GAAM,OAACA,EAAIA,EAAEC,cAAgB,OAMzDC,EAAc,aACPC,EAAY,SAACL,GAC3B,OAAOA,EAAIC,QAAQG,EAAa,OAAOE,eClClCC,EAASC,QAAQ,UACjBC,EAAQD,QAAQ,sBAEtB,aAEErD,cAA0B,GAC1BA,mBAAkC,KAClCA,gBAA4B,KAC5BA,mBAAmC,KACnCA,kBAAe,EACfA,eAAY,EAmDd,OAjDEZ,sBAAImE,8BAAJ,WACE,OAAOvD,KAAKwD,SAAS,oCAGvBpE,sBAAImE,4BAAJ,WACE,OAAIvD,gBAAgByD,EAAmBzE,EAAS0E,SAC5C1D,gBAAgB2D,EAAuB3E,EAAS4E,aAChD5D,gBAAgB6D,EAAkB7E,EAAS8E,QAC3C9D,gBAAgB+D,EAAe/E,EAASgF,KACxChE,gBAAgBiE,EAAuBjF,EAASkF,MAChDlE,gBAAgBmE,EAAkBnF,EAASoF,aAA/C,mCAGFhF,sBAAImE,8BAAJ,WACE,OAAOvD,KAAKwD,0CAGdD,wBAAA,SAAYc,GACV,MAAM,IAAIC,MACR,gHAIJf,yBAAA,SAAac,EAAkBE,GAC7B,MAAM,IAAID,MACR,iHAIJf,qBAAA,SAASiB,GACP,GAAIA,IAASxE,KAAM,OAAO,EAC1B,IAAoB,QAAAyE,EAAAzE,KAAKwD,SAALkB,WAAAA,IAAe,CACjC,QAAUC,SAASH,GAAO,OAAO,EAEnC,OAAO,GAGTjB,wBAAA,SAAYiB,GACV,IAAMI,EAAe5E,KAAKwD,SAASqB,QAAQL,IACrB,IAAlBI,IACF5E,KAAKwD,SAASsB,OAAOF,EAAc,GACnCJ,EAAKO,cAAgB,KACrBP,EAAKQ,WAAa,OAItBzB,qBAAA,SAAS0B,SACP,OAAUC,KAAKC,oBAAUnF,KAAKoF,2BAAMC,KAAO,QAAMJ,qBAIrD,aACEjF,gBAAa,EACbA,eAAY,EAMd,OALEsF,qBAAA,SAASC,GACFA,IACuB,iBAAjBA,EAAQC,OAAmBxF,KAAKyF,WAAaF,EAAQC,MACrC,iBAAhBD,EAAQG,MAAkB1F,KAAK2F,UAAYJ,EAAQG,0BAIlE,aAAA,qDACUE,SAA8B,IAAIC,MAmW5C,OApWgCjG,OAG9BR,sBAAIqE,0BAAJ,WAeE,OAdKzD,KAAK8F,UACR9F,KAAK8F,QAAU1C,EAAO,CACpB2C,SAAW/F,KACXgG,aAAe,OAKjBhG,KAAK8F,QAAQG,UAAU,CACrBC,WAAW,EACXC,WAAW,EACXC,WAAW,KAGRpG,KAAK8F,yCAGd1G,sBAAIqE,mCAAJ,WACE,OAAOzD,KAAKwD,SAAS6C,MACnB,SAAC7B,GAAS,OAAAA,aAAgBX,GAA8B,SAAjBW,EAAK8B,4CAIhDlH,sBAAIqE,wBAAJ,iBACE,iBACEzD,KAAKuG,sCAAiB/C,SAAS6C,MAC7B,SAAC7B,GAAS,OAAAA,aAAgBX,GAA8B,SAAjBW,EAAK8B,aACzC,sCAITlH,sBAAIqE,wBAAJ,iBACE,iBACEzD,KAAKuG,sCAAiB/C,SAAS6C,MAC7B,SAAC7B,GAAS,OAAAA,aAAgBX,GAA8B,SAAjBW,EAAK8B,aACzC,sCAITlH,sBAAIqE,kCAAJ,WACE,OAAOzD,sCAGTZ,sBAAIqE,qCAAJ,WACE,OAAOzD,KAAKuG,iDAGd9C,wBAAA,SAAY+C,GACV,IAAMC,EAAWD,EAAUC,SAC3B,IAAIA,IAAazH,EAAS8E,SAAW2C,IAAazH,EAAS4E,eACrD5D,KAAKwD,SAASkD,MAAK,SAACC,GAAM,OAAAA,EAAEF,WAAaA,KAC3C,MAAM,IAAInC,MACR,0EACEmC,IAAazH,EAAS8E,QAAU,YAAc,wCAStD,OAJA0C,EAAUzB,cAAgB,KAC1ByB,EAAUxB,WAAahF,KACvBwG,EAAUI,cAAgB5G,KAC1BA,KAAKwD,SAASd,KAAK8D,GACZA,GAGT/C,yBAAA,SAAaY,EAAkBE,GAC7B,GAAiB,OAAbA,EAAmB,OAAOvE,KAAK6G,YAAYxC,GAC/C,IAAMyC,EAAa9G,KAAKwD,SAASqB,QAAQN,GACzC,IAAmB,GAAfuC,EACF,MAAM,IAAIxC,MACR,uIAMJ,OAJAtE,KAAKwD,SAASsB,OAAOgC,EAAY,EAAGzC,GACpCA,EAASU,cAAgB,KACzBV,EAASW,WAAahF,KACtBqE,EAASuC,cAAgB5G,KAClBqE,GAGTZ,6BAAA,SAAiBsD,GACf,OAAQ/G,KAAKoD,OAAO4D,OAAOD,IAG7BtD,iCAAA,SAAqB6C,GACnB,OAAItG,KAAKuG,gBACCvG,KAAKuG,gBAA8BU,qBAAqBX,GAC3D,IAGT7C,mCAAA,SAAuByD,GACrB,OAAIlH,KAAKuG,gBACCvG,KAAKuG,gBAA8BY,uBACzCD,GAEG,IAGTzD,2BAAA,SAAe2D,GACb,OAAIpH,KAAKuG,gBACCvG,KAAKuG,gBAA8Bc,eAAeD,GACrD,MAGT3D,2BAAA,SACE6D,EACAC,EACAC,GAEA,OAAO,IAAI/D,GAGbA,+BAAA,SACEgE,EACAC,EACAC,GAEA,IAAMC,EAAmB,IAAIjE,EAC3B8D,EACAC,EACAC,GAGF,OADAC,EAAiBhB,cAAgB5G,KAC1B4H,GAOTnE,0BAAA,SAAc6C,GACZ,IACIuB,EADEC,EAAexB,EAAQtD,cAE7B,OAAQ8E,GACN,IAAK,QACL,IAAK,QACHD,EAAU,IAAIE,EAAeD,GAC7B,MACF,IAAK,SACHD,EAAU,IAAIG,EAAgBF,GAC9B,MACF,IAAK,MACHD,EAAU,IAAII,EAAe,OAC7B,MACF,IAAK,SACHJ,EAAU,IAAIK,EAAgB,UAC9B,MACF,IAAK,QACHL,EAAU,IAAIM,EAAe,SAC7B,MACF,QACEN,EAAU,IAAIhE,EAAUiE,GAI5B,OADAD,EAAQjB,cAAgB5G,KACjB6H,GAGTpE,4BAAA,SACE2E,EACAX,GAEA,OAAOzH,KAAKqI,cAAcZ,IAG5BhE,0BAAA,SAAc6E,GACZ,IAAMC,EAAc,IAAIpE,EAAUmE,GAElC,OADAC,EAAY3B,cAAgB5G,KACrBuI,GAGT9E,+BAAA,SAAmB6E,GACjB,IAAME,EAAc,IAAIvE,EAAeqE,GAEvC,OADAE,EAAY5B,cAAgB5G,KACrBwI,GAGT/E,2BAAA,SAAe6E,GACb,IAAMG,EAAW,IAAI1E,EAAOuE,GAE5B,OADAG,EAAS7B,cAAgB5G,KAClByI,GAQThF,iBAAA,WACEzD,KAAKwD,SAAW,IAGlBC,kBAAA,aAEAA,yBAAA,SAAaiF,GACX,IAAIC,GAAmB,EACjBC,EAAsC,GAC5CA,EAAY7C,SAAS8C,eAAiB7J,EAAS0E,SAC/CkF,EAAY7C,SAAS+C,oBAAsB9J,EAAS4E,aACpDgF,EAAY7C,SAASgD,cAAgB/J,EAAS8E,QAC9C8E,EAAY7C,SAASiD,WAAahK,EAASgF,KAC3C4E,EAAY7C,SAASkD,oBAAsBjK,EAASkF,MACpD0E,EAAY7C,SAASmD,cAAgBlK,EAASoF,QAS9C,IAAM+E,EAAO,SAAU3E,GACrB,IACI4E,EATmBvB,EAQnBwB,EAAuB7E,EAAKY,KAWhC,GATKiE,IACHA,EAAuB,CACrBC,KAAMV,EAAYpE,EAAKiC,UACvB8C,YAAa,GACblE,GAAIsD,GAENA,GAAmB,EACnBnE,EAAKY,KAAOiE,GAETrJ,KAAKwJ,OAAOC,IAAIJ,EAAqBhE,KA6FxC+D,EAASpJ,KAAKwJ,OAAOE,IAAIL,EAAqBhE,KACvCN,cAAgB,KACvBqE,EAAOpE,WAAa,KACpBoE,EAAO5F,SAAW,OAhG2B,CAC7C,OAAQgB,EAAKiC,UACX,KAAKjC,EAAKqE,cAKNO,EAHAC,EAAqBM,QACrBN,EAAqBM,SAAWN,EAAqBhE,GAE5CrF,KAAK4J,iBACF5J,KACd,MACF,KAAKwE,EAAKsE,mBACR,IAAMe,EAAgBrF,EACtB4E,EAASpJ,KAAK8J,mBACZD,EAAaE,KACbF,EAAanC,SACbmC,EAAalC,UAEf,MACF,KAAKnD,EAAKuE,aAKR,IAJA,IAAMiB,EAAexF,EACf8B,GAvCWuB,EAuCemC,aAtCfC,gBACd,OAEFpC,EAAQvB,QAAQtD,cAAckH,OAqCzBC,EADNf,EAASpJ,KAAKqI,cAAc/B,OAEE7B,EAAAlF,MAAM6K,KAAKJ,EAAYK,YAAvB3F,WAAAA,IAAoC,CAAvD,IAAA4F,OAAEC,SAAM3J,UACjBuJ,EAAUE,WAAWE,GAAQ3J,EAG/B,GACc,UAAZ0F,GACY,aAAZA,GACY,WAAZA,EACA,CACM1F,EAASoJ,EAEUpJ,MAEvB,CAAC,QAAS,WAAY,SAAU,UAAU4J,SACxCL,EAAUE,WAAWf,OAEvB1I,EAEAuJ,EAAUE,WAAWzJ,MAAQA,EACnBoJ,EAAiCS,UAC3CN,EAAUE,WAAWI,QAAWT,EAAiCS,SAGrE,GAAgB,WAAZnE,EAAsB,CACxB,IAAMoE,EAAeV,EAClBjF,cAEDoF,EAAUE,WAAWzJ,QACpB8J,EAAkC9J,QAEnCuJ,EAAUE,WAAWM,SAAYX,EAAkCW,UAQvE,GAJgB,WAAZrE,IACF6D,EAAUE,WAAWO,WAAcZ,EAAkCa,aAGvD,UAAZvE,GAAmC,UAAZA,EAAqB,CAC9C,IAAMwE,EAAiBX,EACvBW,EAAeC,OAAUf,EAAiCe,OAC1DD,EAAeE,YAAehB,EAAiCgB,YAG7DhB,EAAYvE,aACd0E,EAAU1E,WAAauE,EAAYvE,YAEjCuE,EAAYrE,YACdwE,EAAUxE,UAAYqE,EAAYrE,WAEpC,MACF,KAAKnB,EAAKwE,UACRI,EAASpJ,KAAKiL,eACVzG,EAA0B+E,aAE9B,MACF,KAAK/E,EAAKyE,mBACRG,EAASpJ,KAAKkL,qBACd,MACF,KAAK1G,EAAK0E,aACRE,EAASpJ,KAAKmL,cACV3G,EAA6B+E,aAAe,IAEhD,MACF,QACE,OAEJH,EAAOhE,KAAOiE,EACdrJ,KAAKwJ,OAAO4B,IAAI/B,EAAqBhE,GAAI+D,GAO3C,IAAMpE,EAAaR,EAAKO,eAAiBP,EAAKQ,WAC9C,GAAIA,EAAY,CACd,IAAMqG,EAAarG,EAAiCI,KAC9CkG,EAAetL,KAAKwJ,OAAOE,IAAI2B,EAAShG,IAC9CiG,EAAazE,YAAYuC,GACzBA,EAAOpE,WAAasG,EACpBlC,EAAOrE,cACLuG,aAAwBzH,EAAYyH,EAAe,KAIrDjC,EAAqBC,OAAStK,EAAS0E,UACvC2F,EAAqBC,OAAStK,EAAS8E,SAEvCU,EAAK+G,WAAWC,SAAQ,SAAChH,GAAS,OAAA2E,EAAM3E,OAE1CiH,KAAKzL,MAEH0I,IACF1I,KAAK0L,cACLvC,EAAMT,KAIVjF,wBAAA,WACEzD,KAAKwD,SAAW,GAChBxD,KAAKwJ,OAAOmC,SAGdlI,qBAAA,WACE,OAAOmI,YAAMC,mBAAS,kBAlWMtI,iBA2W9B,WAAYkE,EAAuBC,EAAkBC,GAArD,MACEiE,0BACAhG,EAAKmE,KAAOtC,EACZ7B,EAAK8B,SAAWA,EAChB9B,EAAK+B,SAAWA,IAMpB,OAfoC/H,OAYlC+D,qBAAA,WACE,OAAOiI,YAAMC,mBAAS,sBAbUtI,iBAwBlC,WAAY+C,GAAZ,MACEsF,0BANFhG,aAAwD,GACxDA,aAAqB,EACrBA,YAAoB,EACpBA,aAA+B,KAI7BA,EAAKU,QAAUA,IAqLnB,OA9L+B1G,OAY7BR,sBAAIyE,6BAAJ,WAAA,WACE,OAAO,IAAIiI,EACT9L,KAAKqK,WAAW0B,OAChB,SAACC,GACCpG,EAAKyE,WAAW0B,MAAQC,sCAK9B5M,sBAAIyE,sBAAJ,WACE,OAAO7D,KAAKqK,WAAWhF,oCAGzBjG,sBAAIyE,6BAAJ,WACE,OAAO7D,KAAKqK,WAAW0B,OAAS,oCAGlC3M,sBAAIyE,+BAAJ,WACE,MAAO,QAGT,SAAgBoI,qCAEhB7M,sBAAIyE,yBAAJ,WAAA,IDte2BqI,EACtBC,EAEAC,SCoeGC,EAASrM,KAAKqK,WAAWgC,ODveNH,ECweRlM,KAAKqK,WAAWgC,MDve9BF,EAA8B,GAE9BC,EAAoB,QAC1BF,EAAQI,MAFc,iBAEOd,SAAQ,SAAUe,GAC7C,GAAIA,EAAM,CACX,IAAMC,EAAMD,EAAKD,MAAMF,GACvBI,EAAI/J,OAAS,IAAM0J,EAAIvJ,EAAS4J,EAAI,GAAGtC,SAAWsC,EAAI,GAAGtC,YAGnDiC,GC+dA,GAeJ,OARAE,EAAMI,YAAc,SAAC1C,EAAcnJ,GACjC,IAAM8L,EAAiB9J,EAASmH,GAC3BnJ,EACAyL,EAAMK,GAAkB9L,SADVyL,EAAMK,GAEzB9G,EAAKyE,WAAWgC,eDveMA,GAC3B,IAAMM,EAAa,GACnB,IAAK,IAAIpC,KAAQ8B,EAAO,CACtB,IAAMzL,EAAQyL,EAAM9B,GACpB,GAAqB,iBAAV3J,EAAX,CACA,IAAM8L,EAAiBxJ,EAAUqH,GACjCoC,EAAWjK,KAAQgK,MAAkB9L,QAEvC,OAAO+L,EAAWC,KAAK,KC+dMC,CAAUR,IAGpCA,EAAMS,eAAiB,GAChBT,mCAGTjN,sBAAIyE,qCAAJ,WACE,IAAkB,QAAAY,EAAAzE,KAAKwD,SAALkB,WAAAA,KAAb,IAAIqI,OACP,GAAIA,aAAiBlJ,EAAW,OAAOkJ,EACzC,OAAO,sCAGT3N,sBAAIyE,sCAAJ,WACE,IAAImB,EAAahF,KAAKgF,WACtB,IAAKA,EAAY,OAAO,KAGxB,IAFA,IAAMgI,EAAWhI,EAAWxB,SAEnByJ,EADGD,EAASnI,QAAQ7E,MACR,EAAGiN,EAAID,EAASvK,OAAQwK,IAC3C,GAAID,EAASC,aAAcpJ,EAAW,OAAOmJ,EAASC,GACxD,OAAO,sCAGTpJ,yBAAA,SAAakG,GACX,IAAImD,EAAYnD,GAAQA,EAAK5G,cAC7B,OAAI+J,KAAalN,KAAKqK,WAAmBrK,KAAKqK,WAAW6C,GAClD,MAGTrJ,yBAAA,SAAakG,EAAcoD,GACzBnN,KAAKqK,WAAWN,EAAK5G,eAAiBgK,GAGxCtJ,yBAAA,SAAakG,GACX,OAAQA,GAAQA,EAAK5G,iBAAkBnD,KAAKqK,YAG9CxG,2BAAA,SACEyD,EACAG,EACA7G,GAEAZ,KAAKoN,aAAa3F,EAAe7G,IAGnCiD,4BAAA,SAAgBkG,UACP/J,KAAKqK,WAAWN,IAGzBlG,wBAAA,SAAYQ,GAKV,OAJArE,KAAKwD,SAASd,KAAK2B,GACnBA,EAASW,WAAahF,KACtBqE,EAASU,cAAgB/E,KACzBqE,EAASuC,cAAgB5G,KAAK4G,cACvBvC,GAGTR,yBAAA,SAAaQ,EAAkBE,GAC7B,GAAiB,OAAbA,EAAmB,OAAOvE,KAAK6G,YAAYxC,GAC/C,IAAMyC,EAAa9G,KAAKwD,SAASqB,QAAQN,GACzC,IAAmB,GAAfuC,EACF,MAAM,IAAIxC,MACR,uIAMJ,OAJAtE,KAAKwD,SAASsB,OAAOgC,EAAY,EAAGzC,GACpCA,EAASU,cAAgB,KACzBV,EAASW,WAAahF,KACtBqE,EAASuC,cAAgB5G,KAAK4G,cACvBvC,GAGTR,6BAAA,SAAiBkD,GACf,OAA2B,OAAvB/G,KAAK4G,cACC5G,KAAK4G,cAAcxD,OAAO4D,OAChCD,EACC/G,MAGE,IAGT6D,2BAAA,SAAeuD,GACb,GAAIpH,gBAAgB6D,GAAa7D,KAAKqF,KAAO+B,EAAW,OAAOpH,KAC/D,IAAoB,QAAAyE,EAAAzE,KAAKwD,SAALkB,WAAAA,IAAe,CAA9B,IAAMqI,OACT,GAAIA,aAAiBlJ,EAAW,CAC9B,IAAM5C,EAAS8L,EAAM1F,eAAeD,GACpC,GAAe,OAAXnG,EAAiB,OAAOA,GAGhC,OAAO,MAGT4C,mCAAA,SAAuBqD,GAAvB,WACMmG,EAAwB,GACtBC,EAAiB,IAAIxB,EAAU5E,GAGnClH,gBAAgB6D,GAChByJ,EAAeC,QAAO,SAACC,GACrB,OAAA5H,EAAK6H,UAAU/G,MAAK,SAACqD,GAAS,OAAAA,IAASyD,QACvC/K,QAAU6K,EAAe7K,QAE3B4K,EAAS3K,KAAK1C,MAChB,IAAoB,QAAAyE,EAAAzE,KAAKwD,SAALkB,WAAAA,IAAe,CAA9B,IAAMqI,OACLA,aAAiBlJ,IACnBwJ,EAAWA,EAASK,OAAOX,EAAM5F,uBAAuBD,KAE5D,OAAOmG,GAGTxJ,iCAAA,SAAqByC,GACnB,IAAI+G,EAAwB,GACtBM,EAAoBrH,EAAQtD,cAC9BhD,gBAAgB6D,GAAa7D,KAAKsG,UAAYqH,GAChDN,EAAS3K,KAAK1C,MAChB,IAAoB,QAAAyE,EAAAzE,KAAKwD,SAALkB,WAAAA,IAAe,CAA9B,IAAMqI,OACLA,aAAiBlJ,IACnBwJ,EAAWA,EAASK,OAAOX,EAAM9F,qBAAqBX,KAE1D,OAAO+G,GAGTxJ,0BAAA,SAAc+J,GACZ,OAAO,GAMT/J,yBAAA,SAAagK,GAEX,OADA7N,KAAK8N,WAA2B,SAAdD,EAAKE,KAAkB/N,KAAO,KACzCA,MAGT6D,qBAAA,WACE,IAAImK,EAAkB,GACtB,IAAK,IAAIb,KAAanN,KAAKqK,WACzB2D,GAAsBb,OAAcnN,KAAKqK,WAAW8C,QAEtD,OAAUvB,YAAMC,mBAAS7L,KAAKsG,aAAY0H,MA5LfzK,iBAgM/B,4DAKA,OALoC3D,UAAAiE,iBAOpC,aAAA,qDACE+B,cAAsB,EACtBA,UAAkB,IAOpB,OAToChG,OAG5BmI,iBAAN,qFACE/H,KAAK+K,QAAS,aAEVhD,kBAAN,qFACE/H,KAAK+K,QAAS,gBAPkBlH,iBAWpC,4DAOA,OAPqCjE,OAInCsI,uBAAA,WACE,OAAO,SAL0BrE,iBASrC,aAAA,qDACU+B,SAA+B,OAYzC,OAboChG,OAGlCR,sBAAI+I,yBAAJ,WACE,IAAKnI,KAAKiO,OAAQ,CAEhB,IADA,IAAIhN,EAAS,OACKwD,EAAAzE,KAAKuL,WAAL7G,WAAAA,KAAb,IAAIqI,OACHA,EAAMtG,WAAazH,EAASgF,OAC9B/C,GAAW8L,EAAiBxD,aAChCvJ,KAAKiO,OAAS3K,EAAM4K,MAAMjN,GAE5B,OAAOjB,KAAKiO,2CAXoBpK,iBAsBlC,WAAYyC,GAAZ,MACEsF,YAAMtF,SAPRV,QAAgB,GAChBA,SAAiB,GACjBA,MAAc,GACdA,kBAA8B,IAAInC,EAClCmC,gBAA0B,IAAIN,EAI5B,IAAM6I,EAAcvI,EAAKwI,gBAAgB/F,cAAc,eACvDzC,EAAKwI,gBAAgBvH,YAAYsH,GACjCA,EAAYtH,YAAYjB,EAAKwI,gBAAgB/F,cAAc,SAC3D8F,EAAYtH,YAAYjB,EAAKwI,gBAAgB/F,cAAc,WAE/D,OAdqCzI,UAAAiE,iBAmBnC,WAAYyE,GAAZ,MACEsD,0BACAhG,EAAK2D,YAAcjB,IAQvB,OAb4B1I,OAQ1BmE,qBAAA,WACE,OAAU6H,YAAMC,mBAAS,mBAAkB3G,KAAKC,UAC9CnF,KAAKuJ,iBAViBhG,iBAkB1B,WAAY+E,GAAZ,MACEsD,0BACAhG,EAAK0C,KAAOA,IAMhB,OAX+B1I,OAQ7BuE,qBAAA,WACE,OAAUyH,YAAMC,mBAAS,sBAAqB3G,KAAKC,UAAUnF,KAAKsI,UATvC/E,iBAe7B,WAAY+E,GAAZ,MACEsD,0BACAhG,EAAK0C,KAAOA,IAQhB,OAboC1I,OAQlCqE,qBAAA,WACE,OAAU2H,YAAMC,mBAAS,2BAA0B3G,KAAKC,UACtDnF,KAAKsI,UAVyB/E,iBA4BlC,WACE8K,EACAC,GAFF,MAIE1C,mBACA,GAOFhG,MAAM,eAAC,aAAAlB,mBAAAA,IAAA6J,kBACL,IAAmB,QAAAC,IAAA/J,WAAAA,IAAY,CAA1B,IAAM8H,OACHrF,EAAYpH,OAAOyM,GACrBX,YAAM/G,eAAQqC,IAAc,GAChC0E,YAAMlJ,YAAKwE,GAEbtB,EAAK0I,UAAY1I,EAAK0I,SAAS1C,YAAMgB,YAAK,OAG5ChH,SAAS,eAAC,aAAAlB,mBAAAA,IAAA6J,kBACR,IAAmB,QAAAE,IAAAhK,WAAAA,IAAY,CAA1B,IAAM8H,OACHrF,EAAYpH,OAAOyM,GACnBmC,EAAQ9C,YAAM/G,eAAQqC,GACxBwH,EAAQ,GACZ9C,YAAM9G,cAAO4J,EAAO,GAEtB9I,EAAK0I,UAAY1I,EAAK0I,SAAS1C,YAAMgB,YAAK,OAvBtCyB,EAAW,CACb,IAAMM,EAAUN,EAAUnE,OAAOoC,MAAM,OACvCV,YAAMlJ,aAAQiM,UAEhB/I,EAAK0I,SAAWA,IAqBpB,OAjCwB1O,UAAAL"}