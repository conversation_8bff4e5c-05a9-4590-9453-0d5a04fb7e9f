var RRDocument=function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e,n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function r(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function o(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{a(r.next(t))}catch(t){i(t)}}function c(t){try{a(r.throw(t))}catch(t){i(t)}}function a(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,c)}a((r=r.apply(t,e||[])).next())}))}function i(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}}!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(e||(e={}));var u=/-(\w)/g,c=function(t){return t.replace(u,(function(t,e){return e?e.toUpperCase():""}))},a=/\B([A-Z])/g,s=function(t){return t.replace(a,"-$1").toLowerCase()},l=require("nwsapi"),p=require("cssom"),h=function(){function t(){this.children=[],this.parentElement=null,this.parentNode=null,this.ownerDocument=null,this.ELEMENT_NODE=1,this.TEXT_NODE=3}return Object.defineProperty(t.prototype,"firstChild",{get:function(){return this.children[0]},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"nodeType",{get:function(){return this instanceof d?e.Document:this instanceof m?e.DocumentType:this instanceof y?e.Element:this instanceof D?e.Text:this instanceof w?e.CDATA:this instanceof R?e.Comment:void 0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},enumerable:!1,configurable:!0}),t.prototype.appendChild=function(t){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")},t.prototype.insertBefore=function(t,e){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")},t.prototype.contains=function(t){if(t===this)return!0;for(var e=0,n=this.children;e<n.length;e++){if(n[e].contains(t))return!0}return!1},t.prototype.removeChild=function(t){var e=this.children.indexOf(t);-1!==e&&(this.children.splice(e,1),t.parentElement=null,t.parentNode=null)},t.prototype.toString=function(t){var e;return(JSON.stringify(null===(e=this.__sn)||void 0===e?void 0:e.id)||"")+" "+t},t}(),f=function(){function t(){this.scrollLeft=0,this.scrollTop=0}return t.prototype.scrollTo=function(t){t&&("number"==typeof t.left&&(this.scrollLeft=t.left),"number"==typeof t.top&&(this.scrollTop=t.top))},t}(),d=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.mirror=new Map,e}return r(n,t),Object.defineProperty(n.prototype,"nwsapi",{get:function(){return this._nwsapi||(this._nwsapi=l({document:this,DOMException:null}),this._nwsapi.configure({LOGERRORS:!1,IDS_DUPES:!0,MIXEDCASE:!0})),this._nwsapi},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"documentElement",{get:function(){return this.children.find((function(t){return t instanceof y&&"HTML"===t.tagName}))},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"body",{get:function(){var t;return(null===(t=this.documentElement)||void 0===t?void 0:t.children.find((function(t){return t instanceof y&&"BODY"===t.tagName})))||null},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"head",{get:function(){var t;return(null===(t=this.documentElement)||void 0===t?void 0:t.children.find((function(t){return t instanceof y&&"HEAD"===t.tagName})))||null},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"implementation",{get:function(){return this},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"firstElementChild",{get:function(){return this.documentElement},enumerable:!1,configurable:!0}),n.prototype.appendChild=function(t){var n=t.nodeType;if((n===e.Element||n===e.DocumentType)&&this.children.some((function(t){return t.nodeType===n})))throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one "+(n===e.Element?"RRElement":"RRDoctype")+" on RRDocument allowed.");return t.parentElement=null,t.parentNode=this,t.ownerDocument=this,this.children.push(t),t},n.prototype.insertBefore=function(t,e){if(null===e)return this.appendChild(t);var n=this.children.indexOf(e);if(-1==n)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.children.splice(n,0,t),t.parentElement=null,t.parentNode=this,t.ownerDocument=this,t},n.prototype.querySelectorAll=function(t){return this.nwsapi.select(t)},n.prototype.getElementsByTagName=function(t){return this.documentElement?this.documentElement.getElementsByTagName(t):[]},n.prototype.getElementsByClassName=function(t){return this.documentElement?this.documentElement.getElementsByClassName(t):[]},n.prototype.getElementById=function(t){return this.documentElement?this.documentElement.getElementById(t):null},n.prototype.createDocument=function(t,e,r){return new n},n.prototype.createDocumentType=function(t,e,n){var r=new m(t,e,n);return r.ownerDocument=this,r},n.prototype.createElement=function(t){var e,n=t.toUpperCase();switch(n){case"AUDIO":case"VIDEO":e=new b(n);break;case"IFRAME":e=new T(n);break;case"IMG":e=new E("IMG");break;case"CANVAS":e=new g("CANVAS");break;case"STYLE":e=new v("STYLE");break;default:e=new y(n)}return e.ownerDocument=this,e},n.prototype.createElementNS=function(t,e){return this.createElement(e)},n.prototype.createComment=function(t){var e=new R(t);return e.ownerDocument=this,e},n.prototype.createCDATASection=function(t){var e=new w(t);return e.ownerDocument=this,e},n.prototype.createTextNode=function(t){var e=new D(t);return e.ownerDocument=this,e},n.prototype.open=function(){this.children=[]},n.prototype.close=function(){},n.prototype.buildFromDom=function(t){var n=-1,r={};r[document.DOCUMENT_NODE]=e.Document,r[document.DOCUMENT_TYPE_NODE]=e.DocumentType,r[document.ELEMENT_NODE]=e.Element,r[document.TEXT_NODE]=e.Text,r[document.CDATA_SECTION_NODE]=e.CDATA,r[document.COMMENT_NODE]=e.Comment;var o=function(t){var i,u,c=t.__sn;if(c||(c={type:r[t.nodeType],textContent:"",id:n},n-=1,t.__sn=c),this.mirror.has(c.id))(i=this.mirror.get(c.id)).parentElement=null,i.parentNode=null,i.children=[];else{switch(t.nodeType){case t.DOCUMENT_NODE:i=c.rootId&&c.rootId!==c.id?this.createDocument():this;break;case t.DOCUMENT_TYPE_NODE:var a=t;i=this.createDocumentType(a.name,a.publicId,a.systemId);break;case t.ELEMENT_NODE:for(var s=t,l=(u=s)instanceof HTMLFormElement?"FORM":u.tagName.toUpperCase().trim(),p=i=this.createElement(l),h=0,f=Array.from(s.attributes);h<f.length;h++){var d=f[h],m=d.name,E=d.value;p.attributes[m]=E}if("INPUT"===l||"TEXTAREA"===l||"SELECT"===l){E=s.value;["RADIO","CHECKBOX","SUBMIT","BUTTON"].includes(p.attributes.type)&&E?p.attributes.value=E:s.checked&&(p.attributes.checked=s.checked)}if("OPTION"===l){var b=s.parentElement;p.attributes.value===b.value&&(p.attributes.selected=s.selected)}if("CANVAS"===l&&(p.attributes.rr_dataURL=s.toDataURL()),"AUDIO"===l||"VIDEO"===l){var g=p;g.paused=s.paused,g.currentTime=s.currentTime}s.scrollLeft&&(p.scrollLeft=s.scrollLeft),s.scrollTop&&(p.scrollTop=s.scrollTop);break;case t.TEXT_NODE:i=this.createTextNode(t.textContent);break;case t.CDATA_SECTION_NODE:i=this.createCDATASection();break;case t.COMMENT_NODE:i=this.createComment(t.textContent||"");break;default:return}i.__sn=c,this.mirror.set(c.id,i)}var v=t.parentElement||t.parentNode;if(v){var T=v.__sn,D=this.mirror.get(T.id);D.appendChild(i),i.parentNode=D,i.parentElement=D instanceof y?D:null}c.type!==e.Document&&c.type!==e.Element||t.childNodes.forEach((function(t){return o(t)}))}.bind(this);t&&(this.destroyTree(),o(t))},n.prototype.destroyTree=function(){this.children=[],this.mirror.clear()},n.prototype.toString=function(){return t.prototype.toString.call(this,"RRDocument")},n}(h),m=function(t){function e(e,n,r){var o=t.call(this)||this;return o.name=e,o.publicId=n,o.systemId=r,o}return r(e,t),e.prototype.toString=function(){return t.prototype.toString.call(this,"RRDocumentType")},e}(h),y=function(t){function e(e){var n=t.call(this)||this;return n.attributes={},n.scrollLeft=0,n.scrollTop=0,n.shadowRoot=null,n.tagName=e,n}return r(e,t),Object.defineProperty(e.prototype,"classList",{get:function(){var t=this;return new N(this.attributes.class,(function(e){t.attributes.class=e}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this.attributes.id},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"className",{get:function(){return this.attributes.class||""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textContent",{get:function(){return""},set:function(t){},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"style",{get:function(){var t,e,n,r=this,o=this.attributes.style?(t=this.attributes.style,e={},n=/:(.+)/,t.split(/;(?![^(]*\))/g).forEach((function(t){if(t){var r=t.split(n);r.length>1&&(e[c(r[0].trim())]=r[1].trim())}})),e):{};return o.setProperty=function(t,e){var n=c(t);e?o[n]=e:delete o[n],r.attributes.style=function(t){var e=[];for(var n in t){var r=t[n];if("string"==typeof r){var o=s(n);e.push(o+":"+r+";")}}return e.join(" ")}(o)},o.scrollBehavior="",o},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"firstElementChild",{get:function(){for(var t=0,n=this.children;t<n.length;t++){var r=n[t];if(r instanceof e)return r}return null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextElementSibling",{get:function(){var t=this.parentNode;if(!t)return null;for(var n=t.children,r=n.indexOf(this)+1;r<n.length;r++)if(n[r]instanceof e)return n[r];return null},enumerable:!1,configurable:!0}),e.prototype.getAttribute=function(t){var e=t&&t.toLowerCase();return e in this.attributes?this.attributes[e]:null},e.prototype.setAttribute=function(t,e){this.attributes[t.toLowerCase()]=e},e.prototype.hasAttribute=function(t){return(t&&t.toLowerCase())in this.attributes},e.prototype.setAttributeNS=function(t,e,n){this.setAttribute(e,n)},e.prototype.removeAttribute=function(t){delete this.attributes[t]},e.prototype.appendChild=function(t){return this.children.push(t),t.parentNode=this,t.parentElement=this,t.ownerDocument=this.ownerDocument,t},e.prototype.insertBefore=function(t,e){if(null===e)return this.appendChild(t);var n=this.children.indexOf(e);if(-1==n)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.children.splice(n,0,t),t.parentElement=null,t.parentNode=this,t.ownerDocument=this.ownerDocument,t},e.prototype.querySelectorAll=function(t){return null!==this.ownerDocument?this.ownerDocument.nwsapi.select(t,this):[]},e.prototype.getElementById=function(t){if(this instanceof e&&this.id===t)return this;for(var n=0,r=this.children;n<r.length;n++){var o=r[n];if(o instanceof e){var i=o.getElementById(t);if(null!==i)return i}}return null},e.prototype.getElementsByClassName=function(t){var n=this,r=[],o=new N(t);this instanceof e&&o.filter((function(t){return n.classList.some((function(e){return e===t}))})).length==o.length&&r.push(this);for(var i=0,u=this.children;i<u.length;i++){var c=u[i];c instanceof e&&(r=r.concat(c.getElementsByClassName(t)))}return r},e.prototype.getElementsByTagName=function(t){var n=[],r=t.toUpperCase();this instanceof e&&this.tagName===r&&n.push(this);for(var o=0,i=this.children;o<i.length;o++){var u=i[o];u instanceof e&&(n=n.concat(u.getElementsByTagName(t)))}return n},e.prototype.dispatchEvent=function(t){return!0},e.prototype.attachShadow=function(t){return this.shadowRoot="open"===t.mode?this:null,this},e.prototype.toString=function(){var e="";for(var n in this.attributes)e+=n+'="'+this.attributes[n]+'" ';return t.prototype.toString.call(this,this.tagName)+" "+e},e}(h),E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e}(y),b=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.currentTime=0,e.paused=!0,e}return r(e,t),e.prototype.play=function(){return o(this,void 0,void 0,(function(){return i(this,(function(t){return this.paused=!1,[2]}))}))},e.prototype.pause=function(){return o(this,void 0,void 0,(function(){return i(this,(function(t){return this.paused=!0,[2]}))}))},e}(y),g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.getContext=function(){return null},e}(y),v=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e._sheet=null,e}return r(n,t),Object.defineProperty(n.prototype,"sheet",{get:function(){if(!this._sheet){for(var t="",n=0,r=this.childNodes;n<r.length;n++){var o=r[n];o.nodeType===e.Text&&(t+=o.textContent)}this._sheet=p.parse(t)}return this._sheet},enumerable:!1,configurable:!0}),n}(y),T=function(t){function e(e){var n=t.call(this,e)||this;n.width="",n.height="",n.src="",n.contentDocument=new d,n.contentWindow=new f;var r=n.contentDocument.createElement("HTML");return n.contentDocument.appendChild(r),r.appendChild(n.contentDocument.createElement("HEAD")),r.appendChild(n.contentDocument.createElement("BODY")),n}return r(e,t),e}(y),D=function(t){function e(e){var n=t.call(this)||this;return n.textContent=e,n}return r(e,t),e.prototype.toString=function(){return t.prototype.toString.call(this,"RRText")+" text="+JSON.stringify(this.textContent)},e}(h),R=function(t){function e(e){var n=t.call(this)||this;return n.data=e,n}return r(e,t),e.prototype.toString=function(){return t.prototype.toString.call(this,"RRComment")+" data="+JSON.stringify(this.data)},e}(h),w=function(t){function e(e){var n=t.call(this)||this;return n.data=e,n}return r(e,t),e.prototype.toString=function(){return t.prototype.toString.call(this,"RRCDATASection")+" data="+JSON.stringify(this.data)},e}(h),N=function(t){function e(e,n){var r=t.call(this)||this;if(r.add=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var o=0,i=e;o<i.length;o++){var u=i[o],c=String(u);t.prototype.indexOf.call(r,c)>=0||t.prototype.push.call(r,c)}r.onChange&&r.onChange(t.prototype.join.call(r," "))},r.remove=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var o=0,i=e;o<i.length;o++){var u=i[o],c=String(u),a=t.prototype.indexOf.call(r,c);a<0||t.prototype.splice.call(r,a,1)}r.onChange&&r.onChange(t.prototype.join.call(r," "))},e){var o=e.trim().split(/\s+/);t.prototype.push.apply(r,o)}return r.onChange=n,r}return r(e,t),e}(Array);return t.RRCDATASection=w,t.RRCanvasElement=g,t.RRComment=R,t.RRDocument=d,t.RRDocumentType=m,t.RRElement=y,t.RRIframeElement=T,t.RRImageElement=E,t.RRMediaElement=b,t.RRNode=h,t.RRStyleElement=v,t.RRText=D,t.RRWindow=f,Object.defineProperty(t,"__esModule",{value:!0}),t}({});
//# sourceMappingURL=document-nodejs.min.js.map
