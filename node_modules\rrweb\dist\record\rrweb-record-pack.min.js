var rrwebRecord=function(er){"use strict";var sr;(function(r){r[r.Document=0]="Document",r[r.DocumentType=1]="DocumentType",r[r.Element=2]="Element",r[r.Text=3]="Text",r[r.CDATA=4]="CDATA",r[r.Comment=5]="Comment"})(sr||(sr={}));const N=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`;let cr={map:{},getId(){return console.error(N),-1},getNode(){return console.error(N),null},removeNodeFromMap(){console.error(N)},has(){return console.error(N),!1},reset(){console.error(N)}};typeof window<"u"&&window.Proxy&&window.Reflect&&(cr=new Proxy(cr,{get(r,e,n){return e==="map"&&console.error(N),Reflect.get(r,e,n)}}));for(var Lr=(r=>(r[r.DomContentLoaded=0]="DomContentLoaded",r[r.Load=1]="Load",r[r.FullSnapshot=2]="FullSnapshot",r[r.IncrementalSnapshot=3]="IncrementalSnapshot",r[r.Meta=4]="Meta",r[r.Custom=5]="Custom",r[r.Plugin=6]="Plugin",r))(Lr||{}),Rr=(r=>(r[r.Mutation=0]="Mutation",r[r.MouseMove=1]="MouseMove",r[r.MouseInteraction=2]="MouseInteraction",r[r.Scroll=3]="Scroll",r[r.ViewportResize=4]="ViewportResize",r[r.Input=5]="Input",r[r.TouchMove=6]="TouchMove",r[r.MediaInteraction=7]="MediaInteraction",r[r.StyleSheetRule=8]="StyleSheetRule",r[r.CanvasMutation=9]="CanvasMutation",r[r.Font=10]="Font",r[r.Log=11]="Log",r[r.Drag=12]="Drag",r[r.StyleDeclaration=13]="StyleDeclaration",r[r.Selection=14]="Selection",r[r.AdoptedStyleSheet=15]="AdoptedStyleSheet",r))(Rr||{}),xr=(r=>(r[r.MouseUp=0]="MouseUp",r[r.MouseDown=1]="MouseDown",r[r.Click=2]="Click",r[r.ContextMenu=3]="ContextMenu",r[r.DblClick=4]="DblClick",r[r.Focus=5]="Focus",r[r.Blur=6]="Blur",r[r.TouchStart=7]="TouchStart",r[r.TouchMove_Departed=8]="TouchMove_Departed",r[r.TouchEnd=9]="TouchEnd",r[r.TouchCancel=10]="TouchCancel",r))(xr||{}),Ir=(r=>(r[r["2D"]=0]="2D",r[r.WebGL=1]="WebGL",r[r.WebGL2=2]="WebGL2",r))(Ir||{}),Er=(r=>(r[r.Play=0]="Play",r[r.Pause=1]="Pause",r[r.Seeked=2]="Seeked",r[r.VolumeChange=3]="VolumeChange",r[r.RateChange=4]="RateChange",r))(Er||{}),Or=(r=>(r.Start="start",r.Pause="pause",r.Resume="resume",r.Resize="resize",r.Finish="finish",r.FullsnapshotRebuilded="fullsnapshot-rebuilded",r.LoadStylesheetStart="load-stylesheet-start",r.LoadStylesheetEnd="load-stylesheet-end",r.SkipStart="skip-start",r.SkipEnd="skip-end",r.MouseInteraction="mouse-interaction",r.EventCast="event-cast",r.CustomEvent="custom-event",r.Flush="flush",r.StateChange="state-change",r.PlayBack="play-back",r.Destroy="destroy",r))(Or||{}),wr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",pr=typeof Uint8Array>"u"?[]:new Uint8Array(256),Z=0;Z<wr.length;Z++)pr[wr.charCodeAt(Z)]=Z;var m=Uint8Array,g=Uint16Array,K=Uint32Array,ar=new m([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),nr=new m([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),gr=new m([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),yr=function(r,e){for(var n=new g(31),a=0;a<31;++a)n[a]=e+=1<<r[a-1];for(var o=new K(n[30]),a=1;a<30;++a)for(var t=n[a];t<n[a+1];++t)o[t]=t-n[a]<<5|a;return[n,o]},Mr=yr(ar,2),Ur=Mr[0],or=Mr[1];Ur[28]=258,or[258]=28;for(var zr=yr(nr,0),Cr=zr[1],tr=new g(32768),s=0;s<32768;++s){var U=(s&43690)>>>1|(s&21845)<<1;U=(U&52428)>>>2|(U&13107)<<2,U=(U&61680)>>>4|(U&3855)<<4,tr[s]=((U&65280)>>>8|(U&255)<<8)>>>1}for(var H=function(r,e,n){for(var a=r.length,o=0,t=new g(e);o<a;++o)++t[r[o]-1];var f=new g(e);for(o=0;o<e;++o)f[o]=f[o-1]+t[o-1]<<1;var i;if(n){i=new g(1<<e);var l=15-e;for(o=0;o<a;++o)if(r[o])for(var c=o<<4|r[o],v=e-r[o],y=f[r[o]-1]++<<v,D=y|(1<<v)-1;y<=D;++y)i[tr[y]>>>l]=c}else for(i=new g(a),o=0;o<a;++o)i[o]=tr[f[r[o]-1]++]>>>15-r[o];return i},V=new m(288),s=0;s<144;++s)V[s]=8;for(var s=144;s<256;++s)V[s]=9;for(var s=256;s<280;++s)V[s]=7;for(var s=280;s<288;++s)V[s]=8;for(var $=new m(32),s=0;s<32;++s)$[s]=5;var jr=H(V,9,0),Gr=H($,5,0),Sr=function(r){return(r/8>>0)+(r&7&&1)},mr=function(r,e,n){(e==null||e<0)&&(e=0),(n==null||n>r.length)&&(n=r.length);var a=new(r instanceof g?g:r instanceof K?K:m)(n-e);return a.set(r.subarray(e,n)),a},p=function(r,e,n){n<<=e&7;var a=e/8>>0;r[a]|=n,r[a+1]|=n>>>8},Q=function(r,e,n){n<<=e&7;var a=e/8>>0;r[a]|=n,r[a+1]|=n>>>8,r[a+2]|=n>>>16},vr=function(r,e){for(var n=[],a=0;a<r.length;++a)r[a]&&n.push({s:a,f:r[a]});var o=n.length,t=n.slice();if(!o)return[new m(0),0];if(o==1){var f=new m(n[0].s+1);return f[n[0].s]=1,[f,1]}n.sort(function(x,k){return x.f-k.f}),n.push({s:-1,f:25001});var i=n[0],l=n[1],c=0,v=1,y=2;for(n[0]={s:-1,f:i.f+l.f,l:i,r:l};v!=o-1;)i=n[n[c].f<n[y].f?c++:y++],l=n[c!=v&&n[c].f<n[y].f?c++:y++],n[v++]={s:-1,f:i.f+l.f,l:i,r:l};for(var D=t[0].s,a=1;a<o;++a)t[a].s>D&&(D=t[a].s);var M=new g(D+1),L=fr(n[v-1],M,0);if(L>e){var a=0,S=0,z=L-e,W=1<<z;for(t.sort(function(k,C){return M[C.s]-M[k.s]||k.f-C.f});a<o;++a){var R=t[a].s;if(M[R]>e)S+=W-(1<<L-M[R]),M[R]=e;else break}for(S>>>=z;S>0;){var _=t[a].s;M[_]<e?S-=1<<e-M[_]++-1:++a}for(;a>=0&&S;--a){var j=t[a].s;M[j]==e&&(--M[j],++S)}L=e}return[new m(M),L]},fr=function(r,e,n){return r.s==-1?Math.max(fr(r.l,e,n+1),fr(r.r,e,n+1)):e[r.s]=n},Dr=function(r){for(var e=r.length;e&&!r[--e];);for(var n=new g(++e),a=0,o=r[0],t=1,f=function(l){n[a++]=l},i=1;i<=e;++i)if(r[i]==o&&i!=e)++t;else{if(!o&&t>2){for(;t>138;t-=138)f(32754);t>2&&(f(t>10?t-11<<5|28690:t-3<<5|12305),t=0)}else if(t>3){for(f(o),--t;t>6;t-=6)f(8304);t>2&&(f(t-3<<5|8208),t=0)}for(;t--;)f(o);t=1,o=r[i]}return[n.subarray(0,a),e]},X=function(r,e){for(var n=0,a=0;a<e.length;++a)n+=r[a]*e[a];return n},h=function(r,e,n){var a=n.length,o=Sr(e+2);r[o]=a&255,r[o+1]=a>>>8,r[o+2]=r[o]^255,r[o+3]=r[o+1]^255;for(var t=0;t<a;++t)r[o+t+4]=n[t];return(o+4+a)*8},br=function(r,e,n,a,o,t,f,i,l,c,v){p(e,v++,n),++o[256];for(var y=vr(o,15),D=y[0],M=y[1],L=vr(t,15),S=L[0],z=L[1],W=Dr(D),R=W[0],_=W[1],j=Dr(S),x=j[0],k=j[1],C=new g(19),u=0;u<R.length;++u)C[R[u]&31]++;for(var u=0;u<x.length;++u)C[x[u]&31]++;for(var B=vr(C,7),A=B[0],rr=B[1],b=19;b>4&&!A[gr[b-1]];--b);var q=c+5<<3,F=X(o,V)+X(t,$)+f,P=X(o,D)+X(t,S)+f+14+3*b+X(C,A)+(2*C[16]+3*C[17]+7*C[18]);if(q<=F&&q<=P)return h(e,v,r.subarray(l,l+c));var I,w,d,G;if(p(e,v,1+(P<F)),v+=2,P<F){I=H(D,M,0),w=D,d=H(S,z,0),G=S;var lr=H(A,rr,0);p(e,v,_-257),p(e,v+5,k-1),p(e,v+10,b-4),v+=14;for(var u=0;u<b;++u)p(e,v+3*u,A[gr[u]]);v+=3*b;for(var E=[R,x],Y=0;Y<2;++Y)for(var J=E[Y],u=0;u<J.length;++u){var O=J[u]&31;p(e,v,lr[O]),v+=A[O],O>15&&(p(e,v,J[u]>>>5&127),v+=J[u]>>>12)}}else I=jr,w=V,d=Gr,G=$;for(var u=0;u<i;++u)if(a[u]>255){var O=a[u]>>>18&31;Q(e,v,I[O+257]),v+=w[O+257],O>7&&(p(e,v,a[u]>>>23&31),v+=ar[O]);var T=a[u]&31;Q(e,v,d[T]),v+=G[T],T>3&&(Q(e,v,a[u]>>>5&8191),v+=nr[T])}else Q(e,v,I[a[u]]),v+=w[a[u]];return Q(e,v,I[256]),v+w[256]},Vr=new K([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Wr=new m(0),Br=function(r,e,n,a,o,t){var f=r.length,i=new m(a+f+5*(1+Math.floor(f/7e3))+o),l=i.subarray(a,i.length-o),c=0;if(!e||f<8)for(var v=0;v<=f;v+=65535){var y=v+65535;y<f?c=h(l,c,r.subarray(v,y)):(l[v]=t,c=h(l,c,r.subarray(v,f)))}else{for(var D=Vr[e-1],M=D>>>13,L=D&8191,S=(1<<n)-1,z=new g(32768),W=new g(S+1),R=Math.ceil(n/3),_=2*R,j=function(ir){return(r[ir]^r[ir+1]<<R^r[ir+2]<<_)&S},x=new K(25e3),k=new g(288),C=new g(32),u=0,B=0,v=0,A=0,rr=0,b=0;v<f;++v){var q=j(v),F=v&32767,P=W[q];if(z[F]=P,W[q]=F,rr<=v){var I=f-v;if((u>7e3||A>24576)&&I>423){c=br(r,l,0,x,k,C,B,A,b,v-b,c),A=u=B=0,b=v;for(var w=0;w<286;++w)k[w]=0;for(var w=0;w<30;++w)C[w]=0}var d=2,G=0,lr=L,E=F-P&32767;if(I>2&&q==j(v-E))for(var Y=Math.min(M,I)-1,J=Math.min(32767,v),O=Math.min(258,I);E<=J&&--lr&&F!=P;){if(r[v+d]==r[v+d-E]){for(var T=0;T<O&&r[v+T]==r[v+T-E];++T);if(T>d){if(d=T,G=E,T>Y)break;for(var oe=Math.min(E,T-2),Ar=0,w=0;w<oe;++w){var ur=v-E+w+32768&32767,te=z[ur],Fr=ur-te+32768&32767;Fr>Ar&&(Ar=Fr,P=ur)}}}F=P,P=z[F],E+=F-P+32768&32767}if(G){x[A++]=268435456|or[d]<<18|Cr[G];var Pr=or[d]&31,dr=Cr[G]&31;B+=ar[Pr]+nr[dr],++k[257+Pr],++C[dr],rr=v+d,++u}else x[A++]=r[v],++k[r[v]]}}c=br(r,l,t,x,k,C,B,A,b,v-b,c),t||(c=h(l,c,Wr))}return mr(i,0,a+Sr(c)+o)},Nr=function(){var r=1,e=0;return{p:function(n){for(var a=r,o=e,t=n.length,f=0;f!=t;){for(var i=Math.min(f+5552,t);f<i;++f)a+=n[f],o+=a;a%=65521,o%=65521}r=a,e=o},d:function(){return(r>>>8<<16|(e&255)<<8|e>>>8)+((r&255)<<23)*2}}},_r=function(r,e,n,a,o){return Br(r,e.level==null?6:e.level,e.mem==null?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):12+e.mem,n,a,!o)},qr=function(r,e,n){for(;n;++e)r[e]=n,n>>>=8},Jr=function(r,e){var n=e.level,a=n==0?0:n<6?1:n==9?3:2;r[0]=120,r[1]=a<<6|(a?32-2*a:1)};function Kr(r,e){e===void 0&&(e={});var n=Nr();n.p(r);var a=_r(r,e,2,4);return Jr(a,e),qr(a,a.length-4,n.d()),a}function Hr(r,e){var n=r.length;if(!e&&typeof TextEncoder<"u")return new TextEncoder().encode(r);for(var a=new m(r.length+(r.length>>>1)),o=0,t=function(c){a[o++]=c},f=0;f<n;++f){if(o+5>a.length){var i=new m(o+8+(n-f<<1));i.set(a),a=i}var l=r.charCodeAt(f);l<128||e?t(l):l<2048?(t(192|l>>>6),t(128|l&63)):l>55295&&l<57344?(l=65536+(l&1047552)|r.charCodeAt(++f)&1023,t(240|l>>>18),t(128|l>>>12&63),t(128|l>>>6&63),t(128|l&63)):(t(224|l>>>12),t(128|l>>>6&63),t(128|l&63))}return mr(a,0,o)}function Qr(r,e){var n="";if(!e&&typeof TextDecoder<"u")return new TextDecoder().decode(r);for(var a=0;a<r.length;){var o=r[a++];o<128||e?n+=String.fromCharCode(o):o<224?n+=String.fromCharCode((o&31)<<6|r[a++]&63):o<240?n+=String.fromCharCode((o&15)<<12|(r[a++]&63)<<6|r[a++]&63):(o=((o&15)<<18|(r[a++]&63)<<12|(r[a++]&63)<<6|r[a++]&63)-65536,n+=String.fromCharCode(55296|o>>10,56320|o&1023))}return n}const Xr="v1";var Yr=Object.defineProperty,Zr=Object.defineProperties,$r=Object.getOwnPropertyDescriptors,Tr=Object.getOwnPropertySymbols,hr=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable,kr=(r,e,n)=>e in r?Yr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n,ee=(r,e)=>{for(var n in e||(e={}))hr.call(e,n)&&kr(r,n,e[n]);if(Tr)for(var n of Tr(e))re.call(e,n)&&kr(r,n,e[n]);return r},ae=(r,e)=>Zr(r,$r(e));const ne=r=>{const e=ae(ee({},r),{v:Xr});return Qr(Kr(Hr(JSON.stringify(e))),!0)};return er.pack=ne,Object.defineProperty(er,"__esModule",{value:!0}),er}({});
//# sourceMappingURL=rrweb-record-pack.min.js.map
