var e;function t(e){return e.nodeType===e.ELEMENT_NODE}function r(e){var t=null==e?void 0:e.host;return Boolean((null==t?void 0:t.shadowRoot)===e)}function n(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function a(e){try{var t=e.rules||e.cssRules;return t?((r=Array.from(t).map(o).join("")).includes(" background-clip: text;")&&!r.includes(" -webkit-background-clip: text;")&&(r=r.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),r):null}catch(e){return null}var r}function o(e){var t=e.cssText;if(i(e))try{t=a(e.styleSheet)||t}catch(e){}return t}function i(e){return"styleSheet"in e}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(e||(e={}));var s=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){var t;if(!e)return-1;var r=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=r?r:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,r=this.getId(e);this.idNodeMap.delete(r),e.childNodes&&e.childNodes.forEach((function(e){return t.removeNodeFromMap(e)}))},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){var r=this.getNode(e);if(r){var n=this.nodeMetaMap.get(r);n&&this.nodeMetaMap.set(t,n)}this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function c(){return new s}function l(e){var t=e.maskInputOptions,r=e.tagName,n=e.type,a=e.value,o=e.maskInputFn,i=a||"";return(t[r.toLowerCase()]||t[n])&&(i=o?o(i):"*".repeat(i.length)),i}function u(e){var t=e.getContext("2d");if(!t)return!0;for(var r=0;r<e.width;r+=50)for(var n=0;n<e.height;n+=50){var a=t.getImageData,o="__rrweb_original__"in a?a.__rrweb_original__:a;if(new Uint32Array(o.call(t,r,n,Math.min(50,e.width-r),Math.min(50,e.height-n)).data.buffer).some((function(e){return 0!==e})))return!1}return!0}var d,f,p=1,m=new RegExp("[^a-z0-9-_:]"),h=-2;function v(){return p++}var g=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,y=/^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/|#).*/,k=/^(data:)([^,]*),(.*)/i;function b(e,t){return(e||"").replace(g,(function(e,r,n,a,o,i){var s,c=n||o||i,l=r||a||"";if(!c)return e;if(!y.test(c))return"url(".concat(l).concat(c).concat(l,")");if(k.test(c))return"url(".concat(l).concat(c).concat(l,")");if("/"===c[0])return"url(".concat(l).concat((s=t,(s.indexOf("//")>-1?s.split("/").slice(0,3).join("/"):s.split("/")[0]).split("?")[0]+c)).concat(l,")");var u=t.split("/"),d=c.split("/");u.pop();for(var f=0,p=d;f<p.length;f++){var m=p[f];"."!==m&&(".."===m?u.pop():u.push(m))}return"url(".concat(l).concat(u.join("/")).concat(l,")")}))}var T=/^[^ \t\n\r\u000c]+/,C=/^[, \t\n\r\u000c]+/;function S(e,t){if(!t||""===t.trim())return t;var r=e.createElement("a");return r.href=t,r.href}function w(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function x(){var e=document.createElement("a");return e.href="",e.href}function N(e,t,r,n){return"src"===r||"href"===r&&n&&("use"!==t||"#"!==n[0])||"xlink:href"===r&&n&&"#"!==n[0]?S(e,n):"background"!==r||!n||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===r&&n?function(e,t){if(""===t.trim())return t;var r=0;function n(e){var n,a=e.exec(t.substring(r));return a?(n=a[0],r+=n.length,n):""}for(var a=[];n(C),!(r>=t.length);){var o=n(T);if(","===o.slice(-1))o=S(e,o.substring(0,o.length-1)),a.push(o);else{var i="";o=S(e,o);for(var s=!1;;){var c=t.charAt(r);if(""===c){a.push((o+i).trim());break}if(s)")"===c&&(s=!1);else{if(","===c){r+=1,a.push((o+i).trim());break}"("===c&&(s=!0)}i+=c,r+=1}}}return a.join(", ")}(e,n):"style"===r&&n?b(n,x()):"object"===t&&"data"===r&&n?S(e,n):n:S(e,n)}function E(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!r&&E(e.parentNode,t,r);for(var n=e.classList.length;n--;){var a=e.classList[n];if(t.test(a))return!0}return!!r&&E(e.parentNode,t,r)}function I(e,t,r){var n=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===n)return!1;if("string"==typeof t){if(n.classList.contains(t))return!0;if(n.closest(".".concat(t)))return!0}else if(E(n,t,!0))return!0;if(r){if(n.matches(r))return!0;if(n.closest(r))return!0}return!1}function L(t,r){var n=r.doc,o=r.mirror,i=r.blockClass,s=r.blockSelector,c=r.maskTextClass,p=r.maskTextSelector,h=r.inlineStylesheet,v=r.maskInputOptions,g=void 0===v?{}:v,y=r.maskTextFn,k=r.maskInputFn,T=r.dataURLOptions,C=void 0===T?{}:T,S=r.inlineImages,E=r.recordCanvas,L=r.keepIframeSrcFn,M=r.newlyAddedElement,O=void 0!==M&&M,D=function(e,t){if(!t.hasNode(e))return;var r=t.getId(e);return 1===r?void 0:r}(n,o);switch(t.nodeType){case t.DOCUMENT_NODE:return"CSS1Compat"!==t.compatMode?{type:e.Document,childNodes:[],compatMode:t.compatMode}:{type:e.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:e.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:D};case t.ELEMENT_NODE:return function(t,r){for(var n=r.doc,o=r.blockClass,i=r.blockSelector,s=r.inlineStylesheet,c=r.maskInputOptions,p=void 0===c?{}:c,h=r.maskInputFn,v=r.dataURLOptions,g=void 0===v?{}:v,y=r.inlineImages,k=r.recordCanvas,T=r.keepIframeSrcFn,C=r.newlyAddedElement,S=void 0!==C&&C,E=r.rootId,I=function(e,t,r){if("string"==typeof t){if(e.classList.contains(t))return!0}else for(var n=e.classList.length;n--;){var a=e.classList[n];if(t.test(a))return!0}return!!r&&e.matches(r)}(t,o,i),L=function(e){if(e instanceof HTMLFormElement)return"form";var t=e.tagName.toLowerCase().trim();return m.test(t)?"div":t}(t),M={},O=t.attributes.length,D=0;D<O;D++){var _=t.attributes[D];M[_.name]=N(n,L,_.name,_.value)}if("link"===L&&s){var A=Array.from(n.styleSheets).find((function(e){return e.href===t.href})),R=null;A&&(R=a(A)),R&&(delete M.rel,delete M.href,M._cssText=b(R,A.href))}if("style"===L&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){(R=a(t.sheet))&&(M._cssText=b(R,x()))}if("input"===L||"textarea"===L||"select"===L){var F=t.value,U=t.checked;"radio"!==M.type&&"checkbox"!==M.type&&"submit"!==M.type&&"button"!==M.type&&F?M.value=l({type:M.type,tagName:L,value:F,maskInputOptions:p,maskInputFn:h}):U&&(M.checked=U)}"option"===L&&(t.selected&&!p.select?M.selected=!0:delete M.selected);if("canvas"===L&&k)if("2d"===t.__context)u(t)||(M.rr_dataURL=t.toDataURL(g.type,g.quality));else if(!("__context"in t)){var W=t.toDataURL(g.type,g.quality),j=document.createElement("canvas");j.width=t.width,j.height=t.height,W!==j.toDataURL(g.type,g.quality)&&(M.rr_dataURL=W)}if("img"===L&&y){d||(d=n.createElement("canvas"),f=d.getContext("2d"));var P=t,B=P.crossOrigin;P.crossOrigin="anonymous";var H=function(){try{d.width=P.naturalWidth,d.height=P.naturalHeight,f.drawImage(P,0,0),M.rr_dataURL=d.toDataURL(g.type,g.quality)}catch(e){console.warn("Cannot inline img src=".concat(P.currentSrc,"! Error: ").concat(e))}B?M.crossOrigin=B:P.removeAttribute("crossorigin")};P.complete&&0!==P.naturalWidth?H():P.onload=H}"audio"!==L&&"video"!==L||(M.rr_mediaState=t.paused?"paused":"played",M.rr_mediaCurrentTime=t.currentTime);S||(t.scrollLeft&&(M.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(M.rr_scrollTop=t.scrollTop));if(I){var z=t.getBoundingClientRect(),G=z.width,q=z.height;M={class:M.class,rr_width:"".concat(G,"px"),rr_height:"".concat(q,"px")}}"iframe"!==L||T(M.src)||(t.contentDocument||(M.rr_src=M.src),delete M.src);return{type:e.Element,tagName:L,attributes:M,childNodes:[],isSVG:w(t)||void 0,needBlock:I,rootId:E}}(t,{doc:n,blockClass:i,blockSelector:s,inlineStylesheet:h,maskInputOptions:g,maskInputFn:k,dataURLOptions:C,inlineImages:S,recordCanvas:E,keepIframeSrcFn:L,newlyAddedElement:O,rootId:D});case t.TEXT_NODE:return function(t,r){var n,a=r.maskTextClass,o=r.maskTextSelector,i=r.maskTextFn,s=r.rootId,c=t.parentNode&&t.parentNode.tagName,l=t.textContent,u="STYLE"===c||void 0,d="SCRIPT"===c||void 0;if(u&&l){try{t.nextSibling||t.previousSibling||(null===(n=t.parentNode.sheet)||void 0===n?void 0:n.cssRules)&&(l=(f=t.parentNode.sheet).cssRules?Array.from(f.cssRules).map((function(e){return e.cssText||""})).join(""):"")}catch(e){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(e),t)}l=b(l,x())}var f;d&&(l="SCRIPT_PLACEHOLDER");!u&&!d&&l&&I(t,a,o)&&(l=i?i(l):l.replace(/[\S]/g,"*"));return{type:e.Text,textContent:l||"",isStyle:u,rootId:s}}(t,{maskTextClass:c,maskTextSelector:p,maskTextFn:y,rootId:D});case t.CDATA_SECTION_NODE:return{type:e.CDATA,textContent:"",rootId:D};case t.COMMENT_NODE:return{type:e.Comment,textContent:t.textContent||"",rootId:D};default:return!1}}function M(e){return void 0===e?"":e.toLowerCase()}function O(a,o){var i,s=o.doc,c=o.mirror,l=o.blockClass,u=o.blockSelector,d=o.maskTextClass,f=o.maskTextSelector,p=o.skipChild,m=void 0!==p&&p,h=o.inlineStylesheet,g=void 0===h||h,y=o.maskInputOptions,k=void 0===y?{}:y,b=o.maskTextFn,T=o.maskInputFn,C=o.slimDOMOptions,S=o.dataURLOptions,w=void 0===S?{}:S,x=o.inlineImages,N=void 0!==x&&x,E=o.recordCanvas,I=void 0!==E&&E,D=o.onSerialize,_=o.onIframeLoad,A=o.iframeLoadTimeout,R=void 0===A?5e3:A,F=o.onStylesheetLoad,U=o.stylesheetLoadTimeout,W=void 0===U?5e3:U,j=o.keepIframeSrcFn,P=void 0===j?function(){return!1}:j,B=o.newlyAddedElement,H=void 0!==B&&B,z=o.preserveWhiteSpace,G=void 0===z||z,q=L(a,{doc:s,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:f,inlineStylesheet:g,maskInputOptions:k,maskTextFn:b,maskInputFn:T,dataURLOptions:w,inlineImages:N,recordCanvas:I,keepIframeSrcFn:P,newlyAddedElement:H});if(!q)return console.warn(a,"not serialized"),null;i=c.hasNode(a)?c.getId(a):!function(t,r){if(r.comment&&t.type===e.Comment)return!0;if(t.type===e.Element){if(r.script&&("script"===t.tagName||"link"===t.tagName&&"preload"===t.attributes.rel&&"script"===t.attributes.as||"link"===t.tagName&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&t.attributes.href.endsWith(".js")))return!0;if(r.headFavicon&&("link"===t.tagName&&"shortcut icon"===t.attributes.rel||"meta"===t.tagName&&(M(t.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===M(t.attributes.name)||"icon"===M(t.attributes.rel)||"apple-touch-icon"===M(t.attributes.rel)||"shortcut icon"===M(t.attributes.rel))))return!0;if("meta"===t.tagName){if(r.headMetaDescKeywords&&M(t.attributes.name).match(/^description|keywords$/))return!0;if(r.headMetaSocial&&(M(t.attributes.property).match(/^(og|twitter|fb):/)||M(t.attributes.name).match(/^(og|twitter):/)||"pinterest"===M(t.attributes.name)))return!0;if(r.headMetaRobots&&("robots"===M(t.attributes.name)||"googlebot"===M(t.attributes.name)||"bingbot"===M(t.attributes.name)))return!0;if(r.headMetaHttpEquiv&&void 0!==t.attributes["http-equiv"])return!0;if(r.headMetaAuthorship&&("author"===M(t.attributes.name)||"generator"===M(t.attributes.name)||"framework"===M(t.attributes.name)||"publisher"===M(t.attributes.name)||"progid"===M(t.attributes.name)||M(t.attributes.property).match(/^article:/)||M(t.attributes.property).match(/^product:/)))return!0;if(r.headMetaVerification&&("google-site-verification"===M(t.attributes.name)||"yandex-verification"===M(t.attributes.name)||"csrf-token"===M(t.attributes.name)||"p:domain_verify"===M(t.attributes.name)||"verify-v1"===M(t.attributes.name)||"verification"===M(t.attributes.name)||"shopify-checkout-api-token"===M(t.attributes.name)))return!0}}return!1}(q,C)&&(G||q.type!==e.Text||q.isStyle||q.textContent.replace(/^\s+|\s+$/gm,"").length)?v():-2;var V=Object.assign(q,{id:i});if(c.add(a,V),-2===i)return null;D&&D(a);var $=!m;if(V.type===e.Element){$=$&&!V.needBlock,delete V.needBlock;var Y=a.shadowRoot;Y&&n(Y)&&(V.isShadowHost=!0)}if((V.type===e.Document||V.type===e.Element)&&$){C.headWhitespace&&V.type===e.Element&&"head"===V.tagName&&(G=!1);for(var X={doc:s,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:f,skipChild:m,inlineStylesheet:g,maskInputOptions:k,maskTextFn:b,maskInputFn:T,slimDOMOptions:C,dataURLOptions:w,inlineImages:N,recordCanvas:I,preserveWhiteSpace:G,onSerialize:D,onIframeLoad:_,iframeLoadTimeout:R,onStylesheetLoad:F,stylesheetLoadTimeout:W,keepIframeSrcFn:P},K=0,Z=Array.from(a.childNodes);K<Z.length;K++){(ee=O(Z[K],X))&&V.childNodes.push(ee)}if(t(a)&&a.shadowRoot)for(var J=0,Q=Array.from(a.shadowRoot.childNodes);J<Q.length;J++){var ee;(ee=O(Q[J],X))&&(n(a.shadowRoot)&&(ee.isShadow=!0),V.childNodes.push(ee))}}return a.parentNode&&r(a.parentNode)&&n(a.parentNode)&&(V.isShadow=!0),V.type===e.Element&&"iframe"===V.tagName&&function(e,t,r){var n=e.contentWindow;if(n){var a,o=!1;try{a=n.document.readyState}catch(e){return}if("complete"===a){var i="about:blank";if(n.location.href!==i||e.src===i||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}else{var s=setTimeout((function(){o||(t(),o=!0)}),r);e.addEventListener("load",(function(){clearTimeout(s),o=!0,t()}))}}}(a,(function(){var e=a.contentDocument;if(e&&_){var t=O(e,{doc:e,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:f,skipChild:!1,inlineStylesheet:g,maskInputOptions:k,maskTextFn:b,maskInputFn:T,slimDOMOptions:C,dataURLOptions:w,inlineImages:N,recordCanvas:I,preserveWhiteSpace:G,onSerialize:D,onIframeLoad:_,iframeLoadTimeout:R,onStylesheetLoad:F,stylesheetLoadTimeout:W,keepIframeSrcFn:P});t&&_(a,t)}}),R),V.type===e.Element&&"link"===V.tagName&&"stylesheet"===V.attributes.rel&&function(e,t,r){var n,a=!1;try{n=e.sheet}catch(e){return}if(!n){var o=setTimeout((function(){a||(t(),a=!0)}),r);e.addEventListener("load",(function(){clearTimeout(o),a=!0,t()}))}}(a,(function(){if(F){var e=O(a,{doc:s,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:f,skipChild:!1,inlineStylesheet:g,maskInputOptions:k,maskTextFn:b,maskInputFn:T,slimDOMOptions:C,dataURLOptions:w,inlineImages:N,recordCanvas:I,preserveWhiteSpace:G,onSerialize:D,onIframeLoad:_,iframeLoadTimeout:R,onStylesheetLoad:F,stylesheetLoadTimeout:W,keepIframeSrcFn:P});e&&F(a,e)}}),W),V}function D(e,t){var r=t||{},n=r.mirror,a=void 0===n?new s:n,o=r.blockClass,i=void 0===o?"rr-block":o,c=r.blockSelector,l=void 0===c?null:c,u=r.maskTextClass,d=void 0===u?"rr-mask":u,f=r.maskTextSelector,p=void 0===f?null:f,m=r.inlineStylesheet,h=void 0===m||m,v=r.inlineImages,g=void 0!==v&&v,y=r.recordCanvas,k=void 0!==y&&y,b=r.maskAllInputs,T=void 0!==b&&b,C=r.maskTextFn,S=r.maskInputFn,w=r.slimDOM,x=void 0!==w&&w,N=r.dataURLOptions,E=r.preserveWhiteSpace,I=r.onSerialize,L=r.onIframeLoad,M=r.iframeLoadTimeout,D=r.onStylesheetLoad,_=r.stylesheetLoadTimeout,A=r.keepIframeSrcFn;return O(e,{doc:e,mirror:a,blockClass:i,blockSelector:l,maskTextClass:d,maskTextSelector:p,skipChild:!1,inlineStylesheet:h,maskInputOptions:!0===T?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===T?{password:!0}:T,maskTextFn:C,maskInputFn:S,slimDOMOptions:!0===x||"all"===x?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===x,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===x?{}:x,dataURLOptions:N,inlineImages:g,recordCanvas:k,preserveWhiteSpace:E,onSerialize:I,onIframeLoad:L,iframeLoadTimeout:M,onStylesheetLoad:D,stylesheetLoadTimeout:_,keepIframeSrcFn:void 0===A?function(){return!1}:A,newlyAddedElement:!1})}function _(t,r){!function t(n){r(n),n.type!==e.Document&&n.type!==e.Element||n.childNodes.forEach(t)}(t)}function A(){p=1}var R=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g;function F(e,t){void 0===t&&(t={});var r=1,n=1;function a(e){var t=e.match(/\n/g);t&&(r+=t.length);var a=e.lastIndexOf("\n");n=-1===a?n+e.length:e.length-a}function o(){var e={line:r,column:n};return function(t){return t.position=new i(e),p(),t}}var i=function(e){this.start=e,this.end={line:r,column:n},this.source=t.source};i.prototype.content=e;var s=[];function c(a){var o=new Error("".concat(t.source||"",":").concat(r,":").concat(n,": ").concat(a));if(o.reason=a,o.filename=t.source,o.line=r,o.column=n,o.source=e,!t.silent)throw o;s.push(o)}function l(){return f(/^{\s*/)}function u(){return f(/^}/)}function d(){var t,r=[];for(p(),m(r);e.length&&"}"!==e.charAt(0)&&(t=x()||N());)!1!==t&&(r.push(t),m(r));return r}function f(t){var r=t.exec(e);if(r){var n=r[0];return a(n),e=e.slice(n.length),r}}function p(){f(/^\s*/)}function m(e){var t;for(void 0===e&&(e=[]);t=h();)!1!==t&&e.push(t),t=h();return e}function h(){var t=o();if("/"===e.charAt(0)&&"*"===e.charAt(1)){for(var r=2;""!==e.charAt(r)&&("*"!==e.charAt(r)||"/"!==e.charAt(r+1));)++r;if(r+=2,""===e.charAt(r-1))return c("End of comment missing");var i=e.slice(2,r-2);return n+=2,a(i),e=e.slice(r),n+=2,t({type:"comment",comment:i})}}function v(){var e=f(/^([^{]+)/);if(e)return U(e[0]).replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g,"").replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,(function(e){return e.replace(/,/g,"‌")})).split(/\s*(?![^(]*\)),\s*/).map((function(e){return e.replace(/\u200C/g,",")}))}function g(){var e=o(),t=f(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);if(t){var r=U(t[0]);if(!f(/^:\s*/))return c("property missing ':'");var n=f(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/),a=e({type:"declaration",property:r.replace(R,""),value:n?U(n[0]).replace(R,""):""});return f(/^[;\s]*/),a}}function y(){var e,t=[];if(!l())return c("missing '{'");for(m(t);e=g();)!1!==e&&(t.push(e),m(t)),e=g();return u()?t:c("missing '}'")}function k(){for(var e,t=[],r=o();e=f(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)t.push(e[1]),f(/^,\s*/);if(t.length)return r({type:"keyframe",values:t,declarations:y()})}var b,T=w("import"),C=w("charset"),S=w("namespace");function w(e){var t=new RegExp("^@"+e+"\\s*([^;]+);");return function(){var r=o(),n=f(t);if(n){var a={type:e};return a[e]=n[1].trim(),r(a)}}}function x(){if("@"===e[0])return function(){var e=o(),t=f(/^@([-\w]+)?keyframes\s*/);if(t){var r=t[1];if(!(t=f(/^([-\w]+)\s*/)))return c("@keyframes missing name");var n,a=t[1];if(!l())return c("@keyframes missing '{'");for(var i=m();n=k();)i.push(n),i=i.concat(m());return u()?e({type:"keyframes",name:a,vendor:r,keyframes:i}):c("@keyframes missing '}'")}}()||function(){var e=o(),t=f(/^@media *([^{]+)/);if(t){var r=U(t[1]);if(!l())return c("@media missing '{'");var n=m().concat(d());return u()?e({type:"media",media:r,rules:n}):c("@media missing '}'")}}()||function(){var e=o(),t=f(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);if(t)return e({type:"custom-media",name:U(t[1]),media:U(t[2])})}()||function(){var e=o(),t=f(/^@supports *([^{]+)/);if(t){var r=U(t[1]);if(!l())return c("@supports missing '{'");var n=m().concat(d());return u()?e({type:"supports",supports:r,rules:n}):c("@supports missing '}'")}}()||T()||C()||S()||function(){var e=o(),t=f(/^@([-\w]+)?document *([^{]+)/);if(t){var r=U(t[1]),n=U(t[2]);if(!l())return c("@document missing '{'");var a=m().concat(d());return u()?e({type:"document",document:n,vendor:r,rules:a}):c("@document missing '}'")}}()||function(){var e=o();if(f(/^@page */)){var t=v()||[];if(!l())return c("@page missing '{'");for(var r,n=m();r=g();)n.push(r),n=n.concat(m());return u()?e({type:"page",selectors:t,declarations:n}):c("@page missing '}'")}}()||function(){var e=o();if(f(/^@host\s*/)){if(!l())return c("@host missing '{'");var t=m().concat(d());return u()?e({type:"host",rules:t}):c("@host missing '}'")}}()||function(){var e=o();if(f(/^@font-face\s*/)){if(!l())return c("@font-face missing '{'");for(var t,r=m();t=g();)r.push(t),r=r.concat(m());return u()?e({type:"font-face",declarations:r}):c("@font-face missing '}'")}}()}function N(){var e=o(),t=v();return t?(m(),e({type:"rule",selectors:t,declarations:y()})):c("selector missing")}return W((b=d(),{type:"stylesheet",stylesheet:{source:t.source,rules:b,parsingErrors:s}}))}function U(e){return e?e.replace(/^\s+|\s+$/g,""):""}function W(e,t){for(var r=e&&"string"==typeof e.type,n=r?e:t,a=0,o=Object.keys(e);a<o.length;a++){var i=e[o[a]];Array.isArray(i)?i.forEach((function(e){W(e,n)})):i&&"object"==typeof i&&W(i,n)}return r&&Object.defineProperty(e,"parent",{configurable:!0,writable:!0,enumerable:!1,value:t||null}),e}var j={script:"noscript",altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};var P=/([^\\]):hover/,B=new RegExp(P.source,"g");function H(e,t){var r=null==t?void 0:t.stylesWithHoverClass.get(e);if(r)return r;var n=F(e,{silent:!0});if(!n.stylesheet)return e;var a=[];if(n.stylesheet.rules.forEach((function(e){"selectors"in e&&(e.selectors||[]).forEach((function(e){P.test(e)&&a.push(e)}))})),0===a.length)return e;var o=new RegExp(a.filter((function(e,t){return a.indexOf(e)===t})).sort((function(e,t){return t.length-e.length})).map((function(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")})).join("|"),"g"),i=e.replace(o,(function(e){var t=e.replace(B,"$1.\\:hover");return"".concat(e,", ").concat(t)}));return null==t||t.stylesWithHoverClass.set(e,i),i}function z(){return{stylesWithHoverClass:new Map}}function G(t,r){var n=r.doc,a=r.hackCss,o=r.cache;switch(t.type){case e.Document:return n.implementation.createDocument(null,"",null);case e.DocumentType:return n.implementation.createDocumentType(t.name||"html",t.publicId,t.systemId);case e.Element:var i,s=function(e){var t=j[e.tagName]?j[e.tagName]:e.tagName;return"link"===t&&e.attributes._cssText&&(t="style"),t}(t);i=t.isSVG?n.createElementNS("http://www.w3.org/2000/svg",s):n.createElement(s);var c={};for(var l in t.attributes)if(Object.prototype.hasOwnProperty.call(t.attributes,l)){var u=t.attributes[l];if("option"!==s||"selected"!==l||!1!==u)if(!0===u&&(u=""),l.startsWith("rr_"))c[l]=u;else{var d="textarea"===s&&"value"===l,f="style"===s&&"_cssText"===l;if(f&&a&&"string"==typeof u&&(u=H(u,o)),!d&&!f||"string"!=typeof u)try{if(t.isSVG&&"xlink:href"===l)i.setAttributeNS("http://www.w3.org/1999/xlink",l,u.toString());else if("onload"===l||"onclick"===l||"onmouse"===l.substring(0,7))i.setAttribute("_"+l,u.toString());else{if("meta"===s&&"Content-Security-Policy"===t.attributes["http-equiv"]&&"content"===l){i.setAttribute("csp-content",u.toString());continue}"link"===s&&"preload"===t.attributes.rel&&"script"===t.attributes.as||"link"===s&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&t.attributes.href.endsWith(".js")||("img"===s&&t.attributes.srcset&&t.attributes.rr_dataURL?i.setAttribute("rrweb-original-srcset",t.attributes.srcset):i.setAttribute(l,u.toString()))}}catch(e){}else{for(var p=n.createTextNode(u),m=0,h=Array.from(i.childNodes);m<h.length;m++){var v=h[m];v.nodeType===i.TEXT_NODE&&i.removeChild(v)}i.appendChild(p)}}}var g=function(e){var r=c[e];if("canvas"===s&&"rr_dataURL"===e){var n=document.createElement("img");n.onload=function(){var e=i.getContext("2d");e&&e.drawImage(n,0,0,n.width,n.height)},n.src=r.toString(),i.RRNodeType&&(i.rr_dataURL=r.toString())}else if("img"===s&&"rr_dataURL"===e){var a=i;a.currentSrc.startsWith("data:")||(a.setAttribute("rrweb-original-src",t.attributes.src),a.src=r.toString())}if("rr_width"===e)i.style.width=r.toString();else if("rr_height"===e)i.style.height=r.toString();else if("rr_mediaCurrentTime"===e&&"number"==typeof r)i.currentTime=r;else if("rr_mediaState"===e)switch(r){case"played":i.play().catch((function(e){return console.warn("media playback error",e)}));break;case"paused":i.pause()}};for(var y in c)g(y);if(t.isShadowHost)if(i.shadowRoot)for(;i.shadowRoot.firstChild;)i.shadowRoot.removeChild(i.shadowRoot.firstChild);else i.attachShadow({mode:"open"});return i;case e.Text:return n.createTextNode(t.isStyle&&a?H(t.textContent,o):t.textContent);case e.CDATA:return n.createCDATASection(t.textContent);case e.Comment:return n.createComment(t.textContent);default:return null}}function q(r,n){var a=n.doc,o=n.mirror,i=n.skipChild,s=void 0!==i&&i,c=n.hackCss,l=void 0===c||c,u=n.afterAppend,d=n.cache,f=G(r,{doc:a,hackCss:l,cache:d});if(!f)return null;if(r.rootId&&o.getNode(r.rootId)!==a&&o.replace(r.rootId,a),r.type===e.Document&&(a.close(),a.open(),"BackCompat"===r.compatMode&&r.childNodes&&r.childNodes[0].type!==e.DocumentType&&(r.childNodes[0].type===e.Element&&"xmlns"in r.childNodes[0].attributes&&"http://www.w3.org/1999/xhtml"===r.childNodes[0].attributes.xmlns?a.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'):a.write('<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">')),f=a),o.add(f,r),(r.type===e.Document||r.type===e.Element)&&!s)for(var p=0,m=r.childNodes;p<m.length;p++){var h=m[p],v=q(h,{doc:a,mirror:o,skipChild:!1,hackCss:l,afterAppend:u,cache:d});v?(h.isShadow&&t(f)&&f.shadowRoot?f.shadowRoot.appendChild(v):f.appendChild(v),u&&u(v,h.id)):console.warn("Failed to rebuild",h)}return f}function V(t,r){var n=r.doc,a=r.onVisit,o=r.hackCss,i=void 0===o||o,c=r.afterAppend,l=r.cache,u=r.mirror,d=void 0===u?new s:u,f=q(t,{doc:n,mirror:d,skipChild:!1,hackCss:i,afterAppend:c,cache:l});return function(e,t){for(var r=0,n=e.getIds();r<n.length;r++){var a=n[r];e.has(a)&&t(e.getNode(a))}}(d,(function(t){a&&a(t),function(t,r){var n=r.getMeta(t);if((null==n?void 0:n.type)===e.Element){var a=t;for(var o in n.attributes)if(Object.prototype.hasOwnProperty.call(n.attributes,o)&&o.startsWith("rr_")){var i=n.attributes[o];"rr_scrollLeft"===o&&(a.scrollLeft=i),"rr_scrollTop"===o&&(a.scrollTop=i)}}}(t,d)})),f}export{h as IGNORED_NODE,s as Mirror,e as NodeType,H as addHoverClass,q as buildNodeWithSN,E as classMatchesRegex,A as cleanupSnapshot,z as createCache,c as createMirror,v as genId,o as getCssRuleString,a as getCssRulesString,u as is2DCanvasBlank,i as isCSSImportRule,t as isElement,n as isNativeShadowDom,r as isShadowRoot,l as maskInputValue,I as needMaskingText,V as rebuild,O as serializeNodeWithId,D as snapshot,N as transformAttribute,_ as visitSnapshot};
//# sourceMappingURL=rrweb-snapshot.min.js.map
