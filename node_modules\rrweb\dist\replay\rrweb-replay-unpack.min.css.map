{"version": 3, "sources": ["style.css"], "names": [], "mappings": "AAAA,kBACE,iBACF,CACA,gBACE,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,2CAA+C,CAC/C,uBAAwB,CACxB,uBAAkC,CAClC,2BAA4B,CAC5B,kkBAA+qB,CAC/qB,wBACF,CACA,sBACE,UAAW,CACX,oBAAqB,CACrB,UAAW,CACX,WAAY,CACZ,kBAA4B,CAC5B,kBAAmB,CACnB,8BAAgC,CAChC,UACF,CACA,6BACE,iCACF,CACA,6BACE,qBAAsB,CACtB,UAAW,CACX,WAAY,CAGZ,kBAAmB,CACnB,iBAAkB,CAClB,gBAAiB,CACjB,kCAAkC,CAClC,oEACF,CACA,0CACE,oBAAkC,CAClC,wEACF,CACA,mCACE,SACF,CACA,0CACE,uCACF,CACA,qBACE,iBAAkB,CAClB,mBACF,CAEA,iBACE,GACE,UAAY,CACZ,UAAW,CACX,WACF,CACA,IACE,UAAY,CACZ,UAAW,CACX,WACF,CACF,CAEA,uBACE,GACE,SAAU,CACV,UAAW,CACX,WACF,CACA,IACE,UAAY,CACZ,UAAW,CACX,WACF,CACF", "file": "rrweb-replay-unpack.min.css", "sourcesContent": [".replayer-wrapper {\n  position: relative;\n}\n.replayer-mouse {\n  position: absolute;\n  width: 20px;\n  height: 20px;\n  transition: left 0.05s linear, top 0.05s linear;\n  background-size: contain;\n  background-position: center center;\n  background-repeat: no-repeat;\n  background-image: url('data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9JzMwMHB4JyB3aWR0aD0nMzAwcHgnICBmaWxsPSIjMDAwMDAwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGRhdGEtbmFtZT0iTGF5ZXIgMSIgdmlld0JveD0iMCAwIDUwIDUwIiB4PSIwcHgiIHk9IjBweCI+PHRpdGxlPkRlc2lnbl90bnA8L3RpdGxlPjxwYXRoIGQ9Ik00OC43MSw0Mi45MUwzNC4wOCwyOC4yOSw0NC4zMywxOEExLDEsMCwwLDAsNDQsMTYuMzlMMi4zNSwxLjA2QTEsMSwwLDAsMCwxLjA2LDIuMzVMMTYuMzksNDRhMSwxLDAsMCwwLDEuNjUuMzZMMjguMjksMzQuMDgsNDIuOTEsNDguNzFhMSwxLDAsMCwwLDEuNDEsMGw0LjM4LTQuMzhBMSwxLDAsMCwwLDQ4LjcxLDQyLjkxWm0tNS4wOSwzLjY3TDI5LDMyYTEsMSwwLDAsMC0xLjQxLDBsLTkuODUsOS44NUwzLjY5LDMuNjlsMzguMTIsMTRMMzIsMjcuNThBMSwxLDAsMCwwLDMyLDI5TDQ2LjU5LDQzLjYyWiI+PC9wYXRoPjwvc3ZnPg==');\n  border-color: transparent;  /* otherwise we transition from black when .touch-device class is added */\n}\n.replayer-mouse::after {\n  content: '';\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  background: rgb(73, 80, 246);\n  border-radius: 100%;\n  transform: translate(-50%, -50%);\n  opacity: 0.3;\n}\n.replayer-mouse.active::after {\n  animation: click 0.2s ease-in-out 1;\n}\n.replayer-mouse.touch-device {\n  background-image: none;  /* there's no passive cursor on touch-only screens */\n  width: 70px;\n  height: 70px;\n  border-width: 4px;\n  border-style: solid;\n  border-radius: 100%;\n  margin-left: -37px;\n  margin-top: -37px;\n  border-color: rgba(73, 80, 246, 0);\n  transition: left 0s linear, top 0s linear, border-color 0.2s ease-in-out;\n}\n.replayer-mouse.touch-device.touch-active {\n  border-color: rgba(73, 80, 246, 1);\n  transition: left 0.25s linear, top 0.25s linear, border-color 0.2s ease-in-out;\n}\n.replayer-mouse.touch-device::after {\n  opacity: 0;  /* there's no passive cursor on touch-only screens */\n}\n.replayer-mouse.touch-device.active::after {\n  animation: touch-click 0.2s ease-in-out 1;\n}\n.replayer-mouse-tail {\n  position: absolute;\n  pointer-events: none;\n}\n\n@keyframes click {\n  0% {\n    opacity: 0.3;\n    width: 20px;\n    height: 20px;\n  }\n  50% {\n    opacity: 0.5;\n    width: 10px;\n    height: 10px;\n  }\n}\n\n@keyframes touch-click {\n  0% {\n    opacity: 0;\n    width: 20px;\n    height: 20px;\n  }\n  50% {\n    opacity: 0.5;\n    width: 10px;\n    height: 10px;\n  }\n}\n"]}