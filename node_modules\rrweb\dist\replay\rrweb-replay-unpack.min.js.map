{"version": 3, "file": "rrweb-replay-unpack.min.js", "sources": ["../../../rrweb-snapshot/es/rrweb-snapshot.js", "../../../rrdom/es/rrdom.js", "../../../../node_modules/mitt/dist/mitt.mjs", "../../src/replay/smoothscroll.ts", "../../../types/dist/types.js", "../../src/replay/timer.ts", "../../../../node_modules/@xstate/fsm/es/index.js", "../../src/replay/machine.ts", "../../src/utils.ts", "../../src/replay/styles/inject-style.ts", "../../../../node_modules/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../../src/replay/canvas/deserialize-args.ts", "../../src/replay/canvas/webgl.ts", "../../src/replay/canvas/2d.ts", "../../src/replay/canvas/index.ts", "../../src/replay/index.ts", "../../../../node_modules/fflate/esm/browser.js", "../../src/packer/base.ts", "../../src/packer/unpack.ts"], "sourcesContent": ["var NodeType;\r\n(function (NodeType) {\r\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\r\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\r\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\r\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\r\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\r\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\r\n})(NodeType || (NodeType = {}));\n\nfunction isElement(n) {\r\n    return n.nodeType === n.ELEMENT_NODE;\r\n}\r\nfunction isShadowRoot(n) {\r\n    var host = n === null || n === void 0 ? void 0 : n.host;\r\n    return Boolean((host === null || host === void 0 ? void 0 : host.shadowRoot) === n);\r\n}\r\nfunction isNativeShadowDom(shadowRoot) {\r\n    return Object.prototype.toString.call(shadowRoot) === '[object ShadowRoot]';\r\n}\r\nfunction fixBrowserCompatibilityIssuesInCSS(cssText) {\r\n    if (cssText.includes(' background-clip: text;') &&\r\n        !cssText.includes(' -webkit-background-clip: text;')) {\r\n        cssText = cssText.replace(' background-clip: text;', ' -webkit-background-clip: text; background-clip: text;');\r\n    }\r\n    return cssText;\r\n}\r\nfunction getCssRulesString(s) {\r\n    try {\r\n        var rules = s.rules || s.cssRules;\r\n        return rules\r\n            ? fixBrowserCompatibilityIssuesInCSS(Array.from(rules).map(getCssRuleString).join(''))\r\n            : null;\r\n    }\r\n    catch (error) {\r\n        return null;\r\n    }\r\n}\r\nfunction getCssRuleString(rule) {\r\n    var cssStringified = rule.cssText;\r\n    if (isCSSImportRule(rule)) {\r\n        try {\r\n            cssStringified = getCssRulesString(rule.styleSheet) || cssStringified;\r\n        }\r\n        catch (_a) {\r\n        }\r\n    }\r\n    return cssStringified;\r\n}\r\nfunction isCSSImportRule(rule) {\r\n    return 'styleSheet' in rule;\r\n}\r\nvar Mirror = (function () {\r\n    function Mirror() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n    Mirror.prototype.getId = function (n) {\r\n        var _a;\r\n        if (!n)\r\n            return -1;\r\n        var id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\r\n        return id !== null && id !== void 0 ? id : -1;\r\n    };\r\n    Mirror.prototype.getNode = function (id) {\r\n        return this.idNodeMap.get(id) || null;\r\n    };\r\n    Mirror.prototype.getIds = function () {\r\n        return Array.from(this.idNodeMap.keys());\r\n    };\r\n    Mirror.prototype.getMeta = function (n) {\r\n        return this.nodeMetaMap.get(n) || null;\r\n    };\r\n    Mirror.prototype.removeNodeFromMap = function (n) {\r\n        var _this = this;\r\n        var id = this.getId(n);\r\n        this.idNodeMap[\"delete\"](id);\r\n        if (n.childNodes) {\r\n            n.childNodes.forEach(function (childNode) {\r\n                return _this.removeNodeFromMap(childNode);\r\n            });\r\n        }\r\n    };\r\n    Mirror.prototype.has = function (id) {\r\n        return this.idNodeMap.has(id);\r\n    };\r\n    Mirror.prototype.hasNode = function (node) {\r\n        return this.nodeMetaMap.has(node);\r\n    };\r\n    Mirror.prototype.add = function (n, meta) {\r\n        var id = meta.id;\r\n        this.idNodeMap.set(id, n);\r\n        this.nodeMetaMap.set(n, meta);\r\n    };\r\n    Mirror.prototype.replace = function (id, n) {\r\n        var oldNode = this.getNode(id);\r\n        if (oldNode) {\r\n            var meta = this.nodeMetaMap.get(oldNode);\r\n            if (meta)\r\n                this.nodeMetaMap.set(n, meta);\r\n        }\r\n        this.idNodeMap.set(id, n);\r\n    };\r\n    Mirror.prototype.reset = function () {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    };\r\n    return Mirror;\r\n}());\r\nfunction createMirror() {\r\n    return new Mirror();\r\n}\r\nfunction maskInputValue(_a) {\r\n    var maskInputOptions = _a.maskInputOptions, tagName = _a.tagName, type = _a.type, value = _a.value, maskInputFn = _a.maskInputFn;\r\n    var text = value || '';\r\n    if (maskInputOptions[tagName.toLowerCase()] ||\r\n        maskInputOptions[type]) {\r\n        if (maskInputFn) {\r\n            text = maskInputFn(text);\r\n        }\r\n        else {\r\n            text = '*'.repeat(text.length);\r\n        }\r\n    }\r\n    return text;\r\n}\r\nvar ORIGINAL_ATTRIBUTE_NAME = '__rrweb_original__';\r\nfunction is2DCanvasBlank(canvas) {\r\n    var ctx = canvas.getContext('2d');\r\n    if (!ctx)\r\n        return true;\r\n    var chunkSize = 50;\r\n    for (var x = 0; x < canvas.width; x += chunkSize) {\r\n        for (var y = 0; y < canvas.height; y += chunkSize) {\r\n            var getImageData = ctx.getImageData;\r\n            var originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData\r\n                ? getImageData[ORIGINAL_ATTRIBUTE_NAME]\r\n                : getImageData;\r\n            var pixelBuffer = new Uint32Array(originalGetImageData.call(ctx, x, y, Math.min(chunkSize, canvas.width - x), Math.min(chunkSize, canvas.height - y)).data.buffer);\r\n            if (pixelBuffer.some(function (pixel) { return pixel !== 0; }))\r\n                return false;\r\n        }\r\n    }\r\n    return true;\r\n}\n\nvar _id = 1;\r\nvar tagNameRegex = new RegExp('[^a-z0-9-_:]');\r\nvar IGNORED_NODE = -2;\r\nfunction genId() {\r\n    return _id++;\r\n}\r\nfunction getValidTagName(element) {\r\n    if (element instanceof HTMLFormElement) {\r\n        return 'form';\r\n    }\r\n    var processedTagName = element.tagName.toLowerCase().trim();\r\n    if (tagNameRegex.test(processedTagName)) {\r\n        return 'div';\r\n    }\r\n    return processedTagName;\r\n}\r\nfunction stringifyStyleSheet(sheet) {\r\n    return sheet.cssRules\r\n        ? Array.from(sheet.cssRules)\r\n            .map(function (rule) { return rule.cssText || ''; })\r\n            .join('')\r\n        : '';\r\n}\r\nfunction extractOrigin(url) {\r\n    var origin = '';\r\n    if (url.indexOf('//') > -1) {\r\n        origin = url.split('/').slice(0, 3).join('/');\r\n    }\r\n    else {\r\n        origin = url.split('/')[0];\r\n    }\r\n    origin = origin.split('?')[0];\r\n    return origin;\r\n}\r\nvar canvasService;\r\nvar canvasCtx;\r\nvar URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\r\nvar RELATIVE_PATH = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\\\|\\/\\/|#).*/;\r\nvar DATA_URI = /^(data:)([^,]*),(.*)/i;\r\nfunction absoluteToStylesheet(cssText, href) {\r\n    return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {\r\n        var filePath = path1 || path2 || path3;\r\n        var maybeQuote = quote1 || quote2 || '';\r\n        if (!filePath) {\r\n            return origin;\r\n        }\r\n        if (!RELATIVE_PATH.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (DATA_URI.test(filePath)) {\r\n            return \"url(\".concat(maybeQuote).concat(filePath).concat(maybeQuote, \")\");\r\n        }\r\n        if (filePath[0] === '/') {\r\n            return \"url(\".concat(maybeQuote).concat(extractOrigin(href) + filePath).concat(maybeQuote, \")\");\r\n        }\r\n        var stack = href.split('/');\r\n        var parts = filePath.split('/');\r\n        stack.pop();\r\n        for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\r\n            var part = parts_1[_i];\r\n            if (part === '.') {\r\n                continue;\r\n            }\r\n            else if (part === '..') {\r\n                stack.pop();\r\n            }\r\n            else {\r\n                stack.push(part);\r\n            }\r\n        }\r\n        return \"url(\".concat(maybeQuote).concat(stack.join('/')).concat(maybeQuote, \")\");\r\n    });\r\n}\r\nvar SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\r\nvar SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\r\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\r\n    if (attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var pos = 0;\r\n    function collectCharacters(regEx) {\r\n        var chars;\r\n        var match = regEx.exec(attributeValue.substring(pos));\r\n        if (match) {\r\n            chars = match[0];\r\n            pos += chars.length;\r\n            return chars;\r\n        }\r\n        return '';\r\n    }\r\n    var output = [];\r\n    while (true) {\r\n        collectCharacters(SRCSET_COMMAS_OR_SPACES);\r\n        if (pos >= attributeValue.length) {\r\n            break;\r\n        }\r\n        var url = collectCharacters(SRCSET_NOT_SPACES);\r\n        if (url.slice(-1) === ',') {\r\n            url = absoluteToDoc(doc, url.substring(0, url.length - 1));\r\n            output.push(url);\r\n        }\r\n        else {\r\n            var descriptorsStr = '';\r\n            url = absoluteToDoc(doc, url);\r\n            var inParens = false;\r\n            while (true) {\r\n                var c = attributeValue.charAt(pos);\r\n                if (c === '') {\r\n                    output.push((url + descriptorsStr).trim());\r\n                    break;\r\n                }\r\n                else if (!inParens) {\r\n                    if (c === ',') {\r\n                        pos += 1;\r\n                        output.push((url + descriptorsStr).trim());\r\n                        break;\r\n                    }\r\n                    else if (c === '(') {\r\n                        inParens = true;\r\n                    }\r\n                }\r\n                else {\r\n                    if (c === ')') {\r\n                        inParens = false;\r\n                    }\r\n                }\r\n                descriptorsStr += c;\r\n                pos += 1;\r\n            }\r\n        }\r\n    }\r\n    return output.join(', ');\r\n}\r\nfunction absoluteToDoc(doc, attributeValue) {\r\n    if (!attributeValue || attributeValue.trim() === '') {\r\n        return attributeValue;\r\n    }\r\n    var a = doc.createElement('a');\r\n    a.href = attributeValue;\r\n    return a.href;\r\n}\r\nfunction isSVGElement(el) {\r\n    return Boolean(el.tagName === 'svg' || el.ownerSVGElement);\r\n}\r\nfunction getHref() {\r\n    var a = document.createElement('a');\r\n    a.href = '';\r\n    return a.href;\r\n}\r\nfunction transformAttribute(doc, tagName, name, value) {\r\n    if (name === 'src' ||\r\n        (name === 'href' && value && !(tagName === 'use' && value[0] === '#'))) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'xlink:href' && value && value[0] !== '#') {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'background' &&\r\n        value &&\r\n        (tagName === 'table' || tagName === 'td' || tagName === 'th')) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else if (name === 'srcset' && value) {\r\n        return getAbsoluteSrcsetString(doc, value);\r\n    }\r\n    else if (name === 'style' && value) {\r\n        return absoluteToStylesheet(value, getHref());\r\n    }\r\n    else if (tagName === 'object' && name === 'data' && value) {\r\n        return absoluteToDoc(doc, value);\r\n    }\r\n    else {\r\n        return value;\r\n    }\r\n}\r\nfunction _isBlockedElement(element, blockClass, blockSelector) {\r\n    if (typeof blockClass === 'string') {\r\n        if (element.classList.contains(blockClass)) {\r\n            return true;\r\n        }\r\n    }\r\n    else {\r\n        for (var eIndex = element.classList.length; eIndex--;) {\r\n            var className = element.classList[eIndex];\r\n            if (blockClass.test(className)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    if (blockSelector) {\r\n        return element.matches(blockSelector);\r\n    }\r\n    return false;\r\n}\r\nfunction classMatchesRegex(node, regex, checkAncestors) {\r\n    if (!node)\r\n        return false;\r\n    if (node.nodeType !== node.ELEMENT_NODE) {\r\n        if (!checkAncestors)\r\n            return false;\r\n        return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n    }\r\n    for (var eIndex = node.classList.length; eIndex--;) {\r\n        var className = node.classList[eIndex];\r\n        if (regex.test(className)) {\r\n            return true;\r\n        }\r\n    }\r\n    if (!checkAncestors)\r\n        return false;\r\n    return classMatchesRegex(node.parentNode, regex, checkAncestors);\r\n}\r\nfunction needMaskingText(node, maskTextClass, maskTextSelector) {\r\n    var el = node.nodeType === node.ELEMENT_NODE\r\n        ? node\r\n        : node.parentElement;\r\n    if (el === null)\r\n        return false;\r\n    if (typeof maskTextClass === 'string') {\r\n        if (el.classList.contains(maskTextClass))\r\n            return true;\r\n        if (el.closest(\".\".concat(maskTextClass)))\r\n            return true;\r\n    }\r\n    else {\r\n        if (classMatchesRegex(el, maskTextClass, true))\r\n            return true;\r\n    }\r\n    if (maskTextSelector) {\r\n        if (el.matches(maskTextSelector))\r\n            return true;\r\n        if (el.closest(maskTextSelector))\r\n            return true;\r\n    }\r\n    return false;\r\n}\r\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\r\n    var win = iframeEl.contentWindow;\r\n    if (!win) {\r\n        return;\r\n    }\r\n    var fired = false;\r\n    var readyState;\r\n    try {\r\n        readyState = win.document.readyState;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (readyState !== 'complete') {\r\n        var timer_1 = setTimeout(function () {\r\n            if (!fired) {\r\n                listener();\r\n                fired = true;\r\n            }\r\n        }, iframeLoadTimeout);\r\n        iframeEl.addEventListener('load', function () {\r\n            clearTimeout(timer_1);\r\n            fired = true;\r\n            listener();\r\n        });\r\n        return;\r\n    }\r\n    var blankUrl = 'about:blank';\r\n    if (win.location.href !== blankUrl ||\r\n        iframeEl.src === blankUrl ||\r\n        iframeEl.src === '') {\r\n        setTimeout(listener, 0);\r\n        return iframeEl.addEventListener('load', listener);\r\n    }\r\n    iframeEl.addEventListener('load', listener);\r\n}\r\nfunction onceStylesheetLoaded(link, listener, styleSheetLoadTimeout) {\r\n    var fired = false;\r\n    var styleSheetLoaded;\r\n    try {\r\n        styleSheetLoaded = link.sheet;\r\n    }\r\n    catch (error) {\r\n        return;\r\n    }\r\n    if (styleSheetLoaded)\r\n        return;\r\n    var timer = setTimeout(function () {\r\n        if (!fired) {\r\n            listener();\r\n            fired = true;\r\n        }\r\n    }, styleSheetLoadTimeout);\r\n    link.addEventListener('load', function () {\r\n        clearTimeout(timer);\r\n        fired = true;\r\n        listener();\r\n    });\r\n}\r\nfunction serializeNode(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c;\r\n    var rootId = getRootId(doc, mirror);\r\n    switch (n.nodeType) {\r\n        case n.DOCUMENT_NODE:\r\n            if (n.compatMode !== 'CSS1Compat') {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: [],\r\n                    compatMode: n.compatMode\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    type: NodeType.Document,\r\n                    childNodes: []\r\n                };\r\n            }\r\n        case n.DOCUMENT_TYPE_NODE:\r\n            return {\r\n                type: NodeType.DocumentType,\r\n                name: n.name,\r\n                publicId: n.publicId,\r\n                systemId: n.systemId,\r\n                rootId: rootId\r\n            };\r\n        case n.ELEMENT_NODE:\r\n            return serializeElementNode(n, {\r\n                doc: doc,\r\n                blockClass: blockClass,\r\n                blockSelector: blockSelector,\r\n                inlineStylesheet: inlineStylesheet,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn,\r\n                dataURLOptions: dataURLOptions,\r\n                inlineImages: inlineImages,\r\n                recordCanvas: recordCanvas,\r\n                keepIframeSrcFn: keepIframeSrcFn,\r\n                newlyAddedElement: newlyAddedElement,\r\n                rootId: rootId\r\n            });\r\n        case n.TEXT_NODE:\r\n            return serializeTextNode(n, {\r\n                maskTextClass: maskTextClass,\r\n                maskTextSelector: maskTextSelector,\r\n                maskTextFn: maskTextFn,\r\n                rootId: rootId\r\n            });\r\n        case n.CDATA_SECTION_NODE:\r\n            return {\r\n                type: NodeType.CDATA,\r\n                textContent: '',\r\n                rootId: rootId\r\n            };\r\n        case n.COMMENT_NODE:\r\n            return {\r\n                type: NodeType.Comment,\r\n                textContent: n.textContent || '',\r\n                rootId: rootId\r\n            };\r\n        default:\r\n            return false;\r\n    }\r\n}\r\nfunction getRootId(doc, mirror) {\r\n    if (!mirror.hasNode(doc))\r\n        return undefined;\r\n    var docId = mirror.getId(doc);\r\n    return docId === 1 ? undefined : docId;\r\n}\r\nfunction serializeTextNode(n, options) {\r\n    var _a;\r\n    var maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, maskTextFn = options.maskTextFn, rootId = options.rootId;\r\n    var parentTagName = n.parentNode && n.parentNode.tagName;\r\n    var textContent = n.textContent;\r\n    var isStyle = parentTagName === 'STYLE' ? true : undefined;\r\n    var isScript = parentTagName === 'SCRIPT' ? true : undefined;\r\n    if (isStyle && textContent) {\r\n        try {\r\n            if (n.nextSibling || n.previousSibling) {\r\n            }\r\n            else if ((_a = n.parentNode.sheet) === null || _a === void 0 ? void 0 : _a.cssRules) {\r\n                textContent = stringifyStyleSheet(n.parentNode.sheet);\r\n            }\r\n        }\r\n        catch (err) {\r\n            console.warn(\"Cannot get CSS styles from text's parentNode. Error: \".concat(err), n);\r\n        }\r\n        textContent = absoluteToStylesheet(textContent, getHref());\r\n    }\r\n    if (isScript) {\r\n        textContent = 'SCRIPT_PLACEHOLDER';\r\n    }\r\n    if (!isStyle &&\r\n        !isScript &&\r\n        textContent &&\r\n        needMaskingText(n, maskTextClass, maskTextSelector)) {\r\n        textContent = maskTextFn\r\n            ? maskTextFn(textContent)\r\n            : textContent.replace(/[\\S]/g, '*');\r\n    }\r\n    return {\r\n        type: NodeType.Text,\r\n        textContent: textContent || '',\r\n        isStyle: isStyle,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction serializeElementNode(n, options) {\r\n    var doc = options.doc, blockClass = options.blockClass, blockSelector = options.blockSelector, inlineStylesheet = options.inlineStylesheet, _a = options.maskInputOptions, maskInputOptions = _a === void 0 ? {} : _a, maskInputFn = options.maskInputFn, _b = options.dataURLOptions, dataURLOptions = _b === void 0 ? {} : _b, inlineImages = options.inlineImages, recordCanvas = options.recordCanvas, keepIframeSrcFn = options.keepIframeSrcFn, _c = options.newlyAddedElement, newlyAddedElement = _c === void 0 ? false : _c, rootId = options.rootId;\r\n    var needBlock = _isBlockedElement(n, blockClass, blockSelector);\r\n    var tagName = getValidTagName(n);\r\n    var attributes = {};\r\n    var len = n.attributes.length;\r\n    for (var i = 0; i < len; i++) {\r\n        var attr = n.attributes[i];\r\n        attributes[attr.name] = transformAttribute(doc, tagName, attr.name, attr.value);\r\n    }\r\n    if (tagName === 'link' && inlineStylesheet) {\r\n        var stylesheet = Array.from(doc.styleSheets).find(function (s) {\r\n            return s.href === n.href;\r\n        });\r\n        var cssText = null;\r\n        if (stylesheet) {\r\n            cssText = getCssRulesString(stylesheet);\r\n        }\r\n        if (cssText) {\r\n            delete attributes.rel;\r\n            delete attributes.href;\r\n            attributes._cssText = absoluteToStylesheet(cssText, stylesheet.href);\r\n        }\r\n    }\r\n    if (tagName === 'style' &&\r\n        n.sheet &&\r\n        !(n.innerText || n.textContent || '').trim().length) {\r\n        var cssText = getCssRulesString(n.sheet);\r\n        if (cssText) {\r\n            attributes._cssText = absoluteToStylesheet(cssText, getHref());\r\n        }\r\n    }\r\n    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {\r\n        var value = n.value;\r\n        var checked = n.checked;\r\n        if (attributes.type !== 'radio' &&\r\n            attributes.type !== 'checkbox' &&\r\n            attributes.type !== 'submit' &&\r\n            attributes.type !== 'button' &&\r\n            value) {\r\n            attributes.value = maskInputValue({\r\n                type: attributes.type,\r\n                tagName: tagName,\r\n                value: value,\r\n                maskInputOptions: maskInputOptions,\r\n                maskInputFn: maskInputFn\r\n            });\r\n        }\r\n        else if (checked) {\r\n            attributes.checked = checked;\r\n        }\r\n    }\r\n    if (tagName === 'option') {\r\n        if (n.selected && !maskInputOptions['select']) {\r\n            attributes.selected = true;\r\n        }\r\n        else {\r\n            delete attributes.selected;\r\n        }\r\n    }\r\n    if (tagName === 'canvas' && recordCanvas) {\r\n        if (n.__context === '2d') {\r\n            if (!is2DCanvasBlank(n)) {\r\n                attributes.rr_dataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n        }\r\n        else if (!('__context' in n)) {\r\n            var canvasDataURL = n.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            var blankCanvas = document.createElement('canvas');\r\n            blankCanvas.width = n.width;\r\n            blankCanvas.height = n.height;\r\n            var blankCanvasDataURL = blankCanvas.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            if (canvasDataURL !== blankCanvasDataURL) {\r\n                attributes.rr_dataURL = canvasDataURL;\r\n            }\r\n        }\r\n    }\r\n    if (tagName === 'img' && inlineImages) {\r\n        if (!canvasService) {\r\n            canvasService = doc.createElement('canvas');\r\n            canvasCtx = canvasService.getContext('2d');\r\n        }\r\n        var image_1 = n;\r\n        var oldValue_1 = image_1.crossOrigin;\r\n        image_1.crossOrigin = 'anonymous';\r\n        var recordInlineImage = function () {\r\n            try {\r\n                canvasService.width = image_1.naturalWidth;\r\n                canvasService.height = image_1.naturalHeight;\r\n                canvasCtx.drawImage(image_1, 0, 0);\r\n                attributes.rr_dataURL = canvasService.toDataURL(dataURLOptions.type, dataURLOptions.quality);\r\n            }\r\n            catch (err) {\r\n                console.warn(\"Cannot inline img src=\".concat(image_1.currentSrc, \"! Error: \").concat(err));\r\n            }\r\n            oldValue_1\r\n                ? (attributes.crossOrigin = oldValue_1)\r\n                : image_1.removeAttribute('crossorigin');\r\n        };\r\n        if (image_1.complete && image_1.naturalWidth !== 0)\r\n            recordInlineImage();\r\n        else\r\n            image_1.onload = recordInlineImage;\r\n    }\r\n    if (tagName === 'audio' || tagName === 'video') {\r\n        attributes.rr_mediaState = n.paused\r\n            ? 'paused'\r\n            : 'played';\r\n        attributes.rr_mediaCurrentTime = n.currentTime;\r\n    }\r\n    if (!newlyAddedElement) {\r\n        if (n.scrollLeft) {\r\n            attributes.rr_scrollLeft = n.scrollLeft;\r\n        }\r\n        if (n.scrollTop) {\r\n            attributes.rr_scrollTop = n.scrollTop;\r\n        }\r\n    }\r\n    if (needBlock) {\r\n        var _d = n.getBoundingClientRect(), width = _d.width, height = _d.height;\r\n        attributes = {\r\n            \"class\": attributes[\"class\"],\r\n            rr_width: \"\".concat(width, \"px\"),\r\n            rr_height: \"\".concat(height, \"px\")\r\n        };\r\n    }\r\n    if (tagName === 'iframe' && !keepIframeSrcFn(attributes.src)) {\r\n        if (!n.contentDocument) {\r\n            attributes.rr_src = attributes.src;\r\n        }\r\n        delete attributes.src;\r\n    }\r\n    return {\r\n        type: NodeType.Element,\r\n        tagName: tagName,\r\n        attributes: attributes,\r\n        childNodes: [],\r\n        isSVG: isSVGElement(n) || undefined,\r\n        needBlock: needBlock,\r\n        rootId: rootId\r\n    };\r\n}\r\nfunction lowerIfExists(maybeAttr) {\r\n    if (maybeAttr === undefined) {\r\n        return '';\r\n    }\r\n    else {\r\n        return maybeAttr.toLowerCase();\r\n    }\r\n}\r\nfunction slimDOMExcluded(sn, slimDOMOptions) {\r\n    if (slimDOMOptions.comment && sn.type === NodeType.Comment) {\r\n        return true;\r\n    }\r\n    else if (sn.type === NodeType.Element) {\r\n        if (slimDOMOptions.script &&\r\n            (sn.tagName === 'script' ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'preload' &&\r\n                    sn.attributes.as === 'script') ||\r\n                (sn.tagName === 'link' &&\r\n                    sn.attributes.rel === 'prefetch' &&\r\n                    typeof sn.attributes.href === 'string' &&\r\n                    sn.attributes.href.endsWith('.js')))) {\r\n            return true;\r\n        }\r\n        else if (slimDOMOptions.headFavicon &&\r\n            ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon') ||\r\n                (sn.tagName === 'meta' &&\r\n                    (lowerIfExists(sn.attributes.name).match(/^msapplication-tile(image|color)$/) ||\r\n                        lowerIfExists(sn.attributes.name) === 'application-name' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'apple-touch-icon' ||\r\n                        lowerIfExists(sn.attributes.rel) === 'shortcut icon')))) {\r\n            return true;\r\n        }\r\n        else if (sn.tagName === 'meta') {\r\n            if (slimDOMOptions.headMetaDescKeywords &&\r\n                lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaSocial &&\r\n                (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) ||\r\n                    lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) ||\r\n                    lowerIfExists(sn.attributes.name) === 'pinterest')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaRobots &&\r\n                (lowerIfExists(sn.attributes.name) === 'robots' ||\r\n                    lowerIfExists(sn.attributes.name) === 'googlebot' ||\r\n                    lowerIfExists(sn.attributes.name) === 'bingbot')) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaHttpEquiv &&\r\n                sn.attributes['http-equiv'] !== undefined) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaAuthorship &&\r\n                (lowerIfExists(sn.attributes.name) === 'author' ||\r\n                    lowerIfExists(sn.attributes.name) === 'generator' ||\r\n                    lowerIfExists(sn.attributes.name) === 'framework' ||\r\n                    lowerIfExists(sn.attributes.name) === 'publisher' ||\r\n                    lowerIfExists(sn.attributes.name) === 'progid' ||\r\n                    lowerIfExists(sn.attributes.property).match(/^article:/) ||\r\n                    lowerIfExists(sn.attributes.property).match(/^product:/))) {\r\n                return true;\r\n            }\r\n            else if (slimDOMOptions.headMetaVerification &&\r\n                (lowerIfExists(sn.attributes.name) === 'google-site-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'yandex-verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'csrf-token' ||\r\n                    lowerIfExists(sn.attributes.name) === 'p:domain_verify' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verify-v1' ||\r\n                    lowerIfExists(sn.attributes.name) === 'verification' ||\r\n                    lowerIfExists(sn.attributes.name) === 'shopify-checkout-api-token')) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction serializeNodeWithId(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, blockClass = options.blockClass, blockSelector = options.blockSelector, maskTextClass = options.maskTextClass, maskTextSelector = options.maskTextSelector, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.inlineStylesheet, inlineStylesheet = _b === void 0 ? true : _b, _c = options.maskInputOptions, maskInputOptions = _c === void 0 ? {} : _c, maskTextFn = options.maskTextFn, maskInputFn = options.maskInputFn, slimDOMOptions = options.slimDOMOptions, _d = options.dataURLOptions, dataURLOptions = _d === void 0 ? {} : _d, _e = options.inlineImages, inlineImages = _e === void 0 ? false : _e, _f = options.recordCanvas, recordCanvas = _f === void 0 ? false : _f, onSerialize = options.onSerialize, onIframeLoad = options.onIframeLoad, _g = options.iframeLoadTimeout, iframeLoadTimeout = _g === void 0 ? 5000 : _g, onStylesheetLoad = options.onStylesheetLoad, _h = options.stylesheetLoadTimeout, stylesheetLoadTimeout = _h === void 0 ? 5000 : _h, _j = options.keepIframeSrcFn, keepIframeSrcFn = _j === void 0 ? function () { return false; } : _j, _k = options.newlyAddedElement, newlyAddedElement = _k === void 0 ? false : _k;\r\n    var _l = options.preserveWhiteSpace, preserveWhiteSpace = _l === void 0 ? true : _l;\r\n    var _serializedNode = serializeNode(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: newlyAddedElement\r\n    });\r\n    if (!_serializedNode) {\r\n        console.warn(n, 'not serialized');\r\n        return null;\r\n    }\r\n    var id;\r\n    if (mirror.hasNode(n)) {\r\n        id = mirror.getId(n);\r\n    }\r\n    else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||\r\n        (!preserveWhiteSpace &&\r\n            _serializedNode.type === NodeType.Text &&\r\n            !_serializedNode.isStyle &&\r\n            !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, '').length)) {\r\n        id = IGNORED_NODE;\r\n    }\r\n    else {\r\n        id = genId();\r\n    }\r\n    var serializedNode = Object.assign(_serializedNode, { id: id });\r\n    mirror.add(n, serializedNode);\r\n    if (id === IGNORED_NODE) {\r\n        return null;\r\n    }\r\n    if (onSerialize) {\r\n        onSerialize(n);\r\n    }\r\n    var recordChild = !skipChild;\r\n    if (serializedNode.type === NodeType.Element) {\r\n        recordChild = recordChild && !serializedNode.needBlock;\r\n        delete serializedNode.needBlock;\r\n        var shadowRoot = n.shadowRoot;\r\n        if (shadowRoot && isNativeShadowDom(shadowRoot))\r\n            serializedNode.isShadowHost = true;\r\n    }\r\n    if ((serializedNode.type === NodeType.Document ||\r\n        serializedNode.type === NodeType.Element) &&\r\n        recordChild) {\r\n        if (slimDOMOptions.headWhitespace &&\r\n            serializedNode.type === NodeType.Element &&\r\n            serializedNode.tagName === 'head') {\r\n            preserveWhiteSpace = false;\r\n        }\r\n        var bypassOptions = {\r\n            doc: doc,\r\n            mirror: mirror,\r\n            blockClass: blockClass,\r\n            blockSelector: blockSelector,\r\n            maskTextClass: maskTextClass,\r\n            maskTextSelector: maskTextSelector,\r\n            skipChild: skipChild,\r\n            inlineStylesheet: inlineStylesheet,\r\n            maskInputOptions: maskInputOptions,\r\n            maskTextFn: maskTextFn,\r\n            maskInputFn: maskInputFn,\r\n            slimDOMOptions: slimDOMOptions,\r\n            dataURLOptions: dataURLOptions,\r\n            inlineImages: inlineImages,\r\n            recordCanvas: recordCanvas,\r\n            preserveWhiteSpace: preserveWhiteSpace,\r\n            onSerialize: onSerialize,\r\n            onIframeLoad: onIframeLoad,\r\n            iframeLoadTimeout: iframeLoadTimeout,\r\n            onStylesheetLoad: onStylesheetLoad,\r\n            stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n            keepIframeSrcFn: keepIframeSrcFn\r\n        };\r\n        for (var _i = 0, _m = Array.from(n.childNodes); _i < _m.length; _i++) {\r\n            var childN = _m[_i];\r\n            var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n            if (serializedChildNode) {\r\n                serializedNode.childNodes.push(serializedChildNode);\r\n            }\r\n        }\r\n        if (isElement(n) && n.shadowRoot) {\r\n            for (var _o = 0, _p = Array.from(n.shadowRoot.childNodes); _o < _p.length; _o++) {\r\n                var childN = _p[_o];\r\n                var serializedChildNode = serializeNodeWithId(childN, bypassOptions);\r\n                if (serializedChildNode) {\r\n                    isNativeShadowDom(n.shadowRoot) &&\r\n                        (serializedChildNode.isShadow = true);\r\n                    serializedNode.childNodes.push(serializedChildNode);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (n.parentNode &&\r\n        isShadowRoot(n.parentNode) &&\r\n        isNativeShadowDom(n.parentNode)) {\r\n        serializedNode.isShadow = true;\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'iframe') {\r\n        onceIframeLoaded(n, function () {\r\n            var iframeDoc = n.contentDocument;\r\n            if (iframeDoc && onIframeLoad) {\r\n                var serializedIframeNode = serializeNodeWithId(iframeDoc, {\r\n                    doc: iframeDoc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedIframeNode) {\r\n                    onIframeLoad(n, serializedIframeNode);\r\n                }\r\n            }\r\n        }, iframeLoadTimeout);\r\n    }\r\n    if (serializedNode.type === NodeType.Element &&\r\n        serializedNode.tagName === 'link' &&\r\n        serializedNode.attributes.rel === 'stylesheet') {\r\n        onceStylesheetLoaded(n, function () {\r\n            if (onStylesheetLoad) {\r\n                var serializedLinkNode = serializeNodeWithId(n, {\r\n                    doc: doc,\r\n                    mirror: mirror,\r\n                    blockClass: blockClass,\r\n                    blockSelector: blockSelector,\r\n                    maskTextClass: maskTextClass,\r\n                    maskTextSelector: maskTextSelector,\r\n                    skipChild: false,\r\n                    inlineStylesheet: inlineStylesheet,\r\n                    maskInputOptions: maskInputOptions,\r\n                    maskTextFn: maskTextFn,\r\n                    maskInputFn: maskInputFn,\r\n                    slimDOMOptions: slimDOMOptions,\r\n                    dataURLOptions: dataURLOptions,\r\n                    inlineImages: inlineImages,\r\n                    recordCanvas: recordCanvas,\r\n                    preserveWhiteSpace: preserveWhiteSpace,\r\n                    onSerialize: onSerialize,\r\n                    onIframeLoad: onIframeLoad,\r\n                    iframeLoadTimeout: iframeLoadTimeout,\r\n                    onStylesheetLoad: onStylesheetLoad,\r\n                    stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n                    keepIframeSrcFn: keepIframeSrcFn\r\n                });\r\n                if (serializedLinkNode) {\r\n                    onStylesheetLoad(n, serializedLinkNode);\r\n                }\r\n            }\r\n        }, stylesheetLoadTimeout);\r\n    }\r\n    return serializedNode;\r\n}\r\nfunction snapshot(n, options) {\r\n    var _a = options || {}, _b = _a.mirror, mirror = _b === void 0 ? new Mirror() : _b, _c = _a.blockClass, blockClass = _c === void 0 ? 'rr-block' : _c, _d = _a.blockSelector, blockSelector = _d === void 0 ? null : _d, _e = _a.maskTextClass, maskTextClass = _e === void 0 ? 'rr-mask' : _e, _f = _a.maskTextSelector, maskTextSelector = _f === void 0 ? null : _f, _g = _a.inlineStylesheet, inlineStylesheet = _g === void 0 ? true : _g, _h = _a.inlineImages, inlineImages = _h === void 0 ? false : _h, _j = _a.recordCanvas, recordCanvas = _j === void 0 ? false : _j, _k = _a.maskAllInputs, maskAllInputs = _k === void 0 ? false : _k, maskTextFn = _a.maskTextFn, maskInputFn = _a.maskInputFn, _l = _a.slimDOM, slimDOM = _l === void 0 ? false : _l, dataURLOptions = _a.dataURLOptions, preserveWhiteSpace = _a.preserveWhiteSpace, onSerialize = _a.onSerialize, onIframeLoad = _a.onIframeLoad, iframeLoadTimeout = _a.iframeLoadTimeout, onStylesheetLoad = _a.onStylesheetLoad, stylesheetLoadTimeout = _a.stylesheetLoadTimeout, _m = _a.keepIframeSrcFn, keepIframeSrcFn = _m === void 0 ? function () { return false; } : _m;\r\n    var maskInputOptions = maskAllInputs === true\r\n        ? {\r\n            color: true,\r\n            date: true,\r\n            'datetime-local': true,\r\n            email: true,\r\n            month: true,\r\n            number: true,\r\n            range: true,\r\n            search: true,\r\n            tel: true,\r\n            text: true,\r\n            time: true,\r\n            url: true,\r\n            week: true,\r\n            textarea: true,\r\n            select: true,\r\n            password: true\r\n        }\r\n        : maskAllInputs === false\r\n            ? {\r\n                password: true\r\n            }\r\n            : maskAllInputs;\r\n    var slimDOMOptions = slimDOM === true || slimDOM === 'all'\r\n        ?\r\n            {\r\n                script: true,\r\n                comment: true,\r\n                headFavicon: true,\r\n                headWhitespace: true,\r\n                headMetaDescKeywords: slimDOM === 'all',\r\n                headMetaSocial: true,\r\n                headMetaRobots: true,\r\n                headMetaHttpEquiv: true,\r\n                headMetaAuthorship: true,\r\n                headMetaVerification: true\r\n            }\r\n        : slimDOM === false\r\n            ? {}\r\n            : slimDOM;\r\n    return serializeNodeWithId(n, {\r\n        doc: n,\r\n        mirror: mirror,\r\n        blockClass: blockClass,\r\n        blockSelector: blockSelector,\r\n        maskTextClass: maskTextClass,\r\n        maskTextSelector: maskTextSelector,\r\n        skipChild: false,\r\n        inlineStylesheet: inlineStylesheet,\r\n        maskInputOptions: maskInputOptions,\r\n        maskTextFn: maskTextFn,\r\n        maskInputFn: maskInputFn,\r\n        slimDOMOptions: slimDOMOptions,\r\n        dataURLOptions: dataURLOptions,\r\n        inlineImages: inlineImages,\r\n        recordCanvas: recordCanvas,\r\n        preserveWhiteSpace: preserveWhiteSpace,\r\n        onSerialize: onSerialize,\r\n        onIframeLoad: onIframeLoad,\r\n        iframeLoadTimeout: iframeLoadTimeout,\r\n        onStylesheetLoad: onStylesheetLoad,\r\n        stylesheetLoadTimeout: stylesheetLoadTimeout,\r\n        keepIframeSrcFn: keepIframeSrcFn,\r\n        newlyAddedElement: false\r\n    });\r\n}\r\nfunction visitSnapshot(node, onVisit) {\r\n    function walk(current) {\r\n        onVisit(current);\r\n        if (current.type === NodeType.Document ||\r\n            current.type === NodeType.Element) {\r\n            current.childNodes.forEach(walk);\r\n        }\r\n    }\r\n    walk(node);\r\n}\r\nfunction cleanupSnapshot() {\r\n    _id = 1;\r\n}\n\nvar commentre = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\r\nfunction parse(css, options) {\r\n    if (options === void 0) { options = {}; }\r\n    var lineno = 1;\r\n    var column = 1;\r\n    function updatePosition(str) {\r\n        var lines = str.match(/\\n/g);\r\n        if (lines) {\r\n            lineno += lines.length;\r\n        }\r\n        var i = str.lastIndexOf('\\n');\r\n        column = i === -1 ? column + str.length : str.length - i;\r\n    }\r\n    function position() {\r\n        var start = { line: lineno, column: column };\r\n        return function (node) {\r\n            node.position = new Position(start);\r\n            whitespace();\r\n            return node;\r\n        };\r\n    }\r\n    var Position = (function () {\r\n        function Position(start) {\r\n            this.start = start;\r\n            this.end = { line: lineno, column: column };\r\n            this.source = options.source;\r\n        }\r\n        return Position;\r\n    }());\r\n    Position.prototype.content = css;\r\n    var errorsList = [];\r\n    function error(msg) {\r\n        var err = new Error(\"\".concat(options.source || '', \":\").concat(lineno, \":\").concat(column, \": \").concat(msg));\r\n        err.reason = msg;\r\n        err.filename = options.source;\r\n        err.line = lineno;\r\n        err.column = column;\r\n        err.source = css;\r\n        if (options.silent) {\r\n            errorsList.push(err);\r\n        }\r\n        else {\r\n            throw err;\r\n        }\r\n    }\r\n    function stylesheet() {\r\n        var rulesList = rules();\r\n        return {\r\n            type: 'stylesheet',\r\n            stylesheet: {\r\n                source: options.source,\r\n                rules: rulesList,\r\n                parsingErrors: errorsList\r\n            }\r\n        };\r\n    }\r\n    function open() {\r\n        return match(/^{\\s*/);\r\n    }\r\n    function close() {\r\n        return match(/^}/);\r\n    }\r\n    function rules() {\r\n        var node;\r\n        var rules = [];\r\n        whitespace();\r\n        comments(rules);\r\n        while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\r\n            if (node !== false) {\r\n                rules.push(node);\r\n                comments(rules);\r\n            }\r\n        }\r\n        return rules;\r\n    }\r\n    function match(re) {\r\n        var m = re.exec(css);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var str = m[0];\r\n        updatePosition(str);\r\n        css = css.slice(str.length);\r\n        return m;\r\n    }\r\n    function whitespace() {\r\n        match(/^\\s*/);\r\n    }\r\n    function comments(rules) {\r\n        if (rules === void 0) { rules = []; }\r\n        var c;\r\n        while ((c = comment())) {\r\n            if (c !== false) {\r\n                rules.push(c);\r\n            }\r\n            c = comment();\r\n        }\r\n        return rules;\r\n    }\r\n    function comment() {\r\n        var pos = position();\r\n        if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\r\n            return;\r\n        }\r\n        var i = 2;\r\n        while ('' !== css.charAt(i) &&\r\n            ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {\r\n            ++i;\r\n        }\r\n        i += 2;\r\n        if ('' === css.charAt(i - 1)) {\r\n            return error('End of comment missing');\r\n        }\r\n        var str = css.slice(2, i - 2);\r\n        column += 2;\r\n        updatePosition(str);\r\n        css = css.slice(i);\r\n        column += 2;\r\n        return pos({\r\n            type: 'comment',\r\n            comment: str\r\n        });\r\n    }\r\n    function selector() {\r\n        var m = match(/^([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return trim(m[0])\r\n            .replace(/\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*\\/+/g, '')\r\n            .replace(/\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'/g, function (m) {\r\n            return m.replace(/,/g, '\\u200C');\r\n        })\r\n            .split(/\\s*(?![^(]*\\)),\\s*/)\r\n            .map(function (s) {\r\n            return s.replace(/\\u200C/g, ',');\r\n        });\r\n    }\r\n    function declaration() {\r\n        var pos = position();\r\n        var propMatch = match(/^(\\*?[-#\\/\\*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/);\r\n        if (!propMatch) {\r\n            return;\r\n        }\r\n        var prop = trim(propMatch[0]);\r\n        if (!match(/^:\\s*/)) {\r\n            return error(\"property missing ':'\");\r\n        }\r\n        var val = match(/^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^\\)]*?\\)|[^};])+)/);\r\n        var ret = pos({\r\n            type: 'declaration',\r\n            property: prop.replace(commentre, ''),\r\n            value: val ? trim(val[0]).replace(commentre, '') : ''\r\n        });\r\n        match(/^[;\\s]*/);\r\n        return ret;\r\n    }\r\n    function declarations() {\r\n        var decls = [];\r\n        if (!open()) {\r\n            return error(\"missing '{'\");\r\n        }\r\n        comments(decls);\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            if (decl !== false) {\r\n                decls.push(decl);\r\n                comments(decls);\r\n            }\r\n            decl = declaration();\r\n        }\r\n        if (!close()) {\r\n            return error(\"missing '}'\");\r\n        }\r\n        return decls;\r\n    }\r\n    function keyframe() {\r\n        var m;\r\n        var vals = [];\r\n        var pos = position();\r\n        while ((m = match(/^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/))) {\r\n            vals.push(m[1]);\r\n            match(/^,\\s*/);\r\n        }\r\n        if (!vals.length) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'keyframe',\r\n            values: vals,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    function atkeyframes() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?keyframes\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = m[1];\r\n        m = match(/^([-\\w]+)\\s*/);\r\n        if (!m) {\r\n            return error('@keyframes missing name');\r\n        }\r\n        var name = m[1];\r\n        if (!open()) {\r\n            return error(\"@keyframes missing '{'\");\r\n        }\r\n        var frame;\r\n        var frames = comments();\r\n        while ((frame = keyframe())) {\r\n            frames.push(frame);\r\n            frames = frames.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@keyframes missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'keyframes',\r\n            name: name,\r\n            vendor: vendor,\r\n            keyframes: frames\r\n        });\r\n    }\r\n    function atsupports() {\r\n        var pos = position();\r\n        var m = match(/^@supports *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var supports = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@supports missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@supports missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'supports',\r\n            supports: supports,\r\n            rules: style\r\n        });\r\n    }\r\n    function athost() {\r\n        var pos = position();\r\n        var m = match(/^@host\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@host missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@host missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'host',\r\n            rules: style\r\n        });\r\n    }\r\n    function atmedia() {\r\n        var pos = position();\r\n        var m = match(/^@media *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var media = trim(m[1]);\r\n        if (!open()) {\r\n            return error(\"@media missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@media missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'media',\r\n            media: media,\r\n            rules: style\r\n        });\r\n    }\r\n    function atcustommedia() {\r\n        var pos = position();\r\n        var m = match(/^@custom-media\\s+(--[^\\s]+)\\s*([^{;]+);/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        return pos({\r\n            type: 'custom-media',\r\n            name: trim(m[1]),\r\n            media: trim(m[2])\r\n        });\r\n    }\r\n    function atpage() {\r\n        var pos = position();\r\n        var m = match(/^@page */);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var sel = selector() || [];\r\n        if (!open()) {\r\n            return error(\"@page missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@page missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'page',\r\n            selectors: sel,\r\n            declarations: decls\r\n        });\r\n    }\r\n    function atdocument() {\r\n        var pos = position();\r\n        var m = match(/^@([-\\w]+)?document *([^{]+)/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        var vendor = trim(m[1]);\r\n        var doc = trim(m[2]);\r\n        if (!open()) {\r\n            return error(\"@document missing '{'\");\r\n        }\r\n        var style = comments().concat(rules());\r\n        if (!close()) {\r\n            return error(\"@document missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'document',\r\n            document: doc,\r\n            vendor: vendor,\r\n            rules: style\r\n        });\r\n    }\r\n    function atfontface() {\r\n        var pos = position();\r\n        var m = match(/^@font-face\\s*/);\r\n        if (!m) {\r\n            return;\r\n        }\r\n        if (!open()) {\r\n            return error(\"@font-face missing '{'\");\r\n        }\r\n        var decls = comments();\r\n        var decl;\r\n        while ((decl = declaration())) {\r\n            decls.push(decl);\r\n            decls = decls.concat(comments());\r\n        }\r\n        if (!close()) {\r\n            return error(\"@font-face missing '}'\");\r\n        }\r\n        return pos({\r\n            type: 'font-face',\r\n            declarations: decls\r\n        });\r\n    }\r\n    var atimport = _compileAtrule('import');\r\n    var atcharset = _compileAtrule('charset');\r\n    var atnamespace = _compileAtrule('namespace');\r\n    function _compileAtrule(name) {\r\n        var re = new RegExp('^@' + name + '\\\\s*([^;]+);');\r\n        return function () {\r\n            var pos = position();\r\n            var m = match(re);\r\n            if (!m) {\r\n                return;\r\n            }\r\n            var ret = { type: name };\r\n            ret[name] = m[1].trim();\r\n            return pos(ret);\r\n        };\r\n    }\r\n    function atrule() {\r\n        if (css[0] !== '@') {\r\n            return;\r\n        }\r\n        return (atkeyframes() ||\r\n            atmedia() ||\r\n            atcustommedia() ||\r\n            atsupports() ||\r\n            atimport() ||\r\n            atcharset() ||\r\n            atnamespace() ||\r\n            atdocument() ||\r\n            atpage() ||\r\n            athost() ||\r\n            atfontface());\r\n    }\r\n    function rule() {\r\n        var pos = position();\r\n        var sel = selector();\r\n        if (!sel) {\r\n            return error('selector missing');\r\n        }\r\n        comments();\r\n        return pos({\r\n            type: 'rule',\r\n            selectors: sel,\r\n            declarations: declarations()\r\n        });\r\n    }\r\n    return addParent(stylesheet());\r\n}\r\nfunction trim(str) {\r\n    return str ? str.replace(/^\\s+|\\s+$/g, '') : '';\r\n}\r\nfunction addParent(obj, parent) {\r\n    var isNode = obj && typeof obj.type === 'string';\r\n    var childParent = isNode ? obj : parent;\r\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\r\n        var k = _a[_i];\r\n        var value = obj[k];\r\n        if (Array.isArray(value)) {\r\n            value.forEach(function (v) {\r\n                addParent(v, childParent);\r\n            });\r\n        }\r\n        else if (value && typeof value === 'object') {\r\n            addParent(value, childParent);\r\n        }\r\n    }\r\n    if (isNode) {\r\n        Object.defineProperty(obj, 'parent', {\r\n            configurable: true,\r\n            writable: true,\r\n            enumerable: false,\r\n            value: parent || null\r\n        });\r\n    }\r\n    return obj;\r\n}\n\nvar tagMap = {\r\n    script: 'noscript',\r\n    altglyph: 'altGlyph',\r\n    altglyphdef: 'altGlyphDef',\r\n    altglyphitem: 'altGlyphItem',\r\n    animatecolor: 'animateColor',\r\n    animatemotion: 'animateMotion',\r\n    animatetransform: 'animateTransform',\r\n    clippath: 'clipPath',\r\n    feblend: 'feBlend',\r\n    fecolormatrix: 'feColorMatrix',\r\n    fecomponenttransfer: 'feComponentTransfer',\r\n    fecomposite: 'feComposite',\r\n    feconvolvematrix: 'feConvolveMatrix',\r\n    fediffuselighting: 'feDiffuseLighting',\r\n    fedisplacementmap: 'feDisplacementMap',\r\n    fedistantlight: 'feDistantLight',\r\n    fedropshadow: 'feDropShadow',\r\n    feflood: 'feFlood',\r\n    fefunca: 'feFuncA',\r\n    fefuncb: 'feFuncB',\r\n    fefuncg: 'feFuncG',\r\n    fefuncr: 'feFuncR',\r\n    fegaussianblur: 'feGaussianBlur',\r\n    feimage: 'feImage',\r\n    femerge: 'feMerge',\r\n    femergenode: 'feMergeNode',\r\n    femorphology: 'feMorphology',\r\n    feoffset: 'feOffset',\r\n    fepointlight: 'fePointLight',\r\n    fespecularlighting: 'feSpecularLighting',\r\n    fespotlight: 'feSpotLight',\r\n    fetile: 'feTile',\r\n    feturbulence: 'feTurbulence',\r\n    foreignobject: 'foreignObject',\r\n    glyphref: 'glyphRef',\r\n    lineargradient: 'linearGradient',\r\n    radialgradient: 'radialGradient'\r\n};\r\nfunction getTagName(n) {\r\n    var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;\r\n    if (tagName === 'link' && n.attributes._cssText) {\r\n        tagName = 'style';\r\n    }\r\n    return tagName;\r\n}\r\nfunction escapeRegExp(str) {\r\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n}\r\nvar HOVER_SELECTOR = /([^\\\\]):hover/;\r\nvar HOVER_SELECTOR_GLOBAL = new RegExp(HOVER_SELECTOR.source, 'g');\r\nfunction addHoverClass(cssText, cache) {\r\n    var cachedStyle = cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.get(cssText);\r\n    if (cachedStyle)\r\n        return cachedStyle;\r\n    var ast = parse(cssText, {\r\n        silent: true\r\n    });\r\n    if (!ast.stylesheet) {\r\n        return cssText;\r\n    }\r\n    var selectors = [];\r\n    ast.stylesheet.rules.forEach(function (rule) {\r\n        if ('selectors' in rule) {\r\n            (rule.selectors || []).forEach(function (selector) {\r\n                if (HOVER_SELECTOR.test(selector)) {\r\n                    selectors.push(selector);\r\n                }\r\n            });\r\n        }\r\n    });\r\n    if (selectors.length === 0) {\r\n        return cssText;\r\n    }\r\n    var selectorMatcher = new RegExp(selectors\r\n        .filter(function (selector, index) { return selectors.indexOf(selector) === index; })\r\n        .sort(function (a, b) { return b.length - a.length; })\r\n        .map(function (selector) {\r\n        return escapeRegExp(selector);\r\n    })\r\n        .join('|'), 'g');\r\n    var result = cssText.replace(selectorMatcher, function (selector) {\r\n        var newSelector = selector.replace(HOVER_SELECTOR_GLOBAL, '$1.\\\\:hover');\r\n        return \"\".concat(selector, \", \").concat(newSelector);\r\n    });\r\n    cache === null || cache === void 0 ? void 0 : cache.stylesWithHoverClass.set(cssText, result);\r\n    return result;\r\n}\r\nfunction createCache() {\r\n    var stylesWithHoverClass = new Map();\r\n    return {\r\n        stylesWithHoverClass: stylesWithHoverClass\r\n    };\r\n}\r\nfunction buildNode(n, options) {\r\n    var doc = options.doc, hackCss = options.hackCss, cache = options.cache;\r\n    switch (n.type) {\r\n        case NodeType.Document:\r\n            return doc.implementation.createDocument(null, '', null);\r\n        case NodeType.DocumentType:\r\n            return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);\r\n        case NodeType.Element: {\r\n            var tagName = getTagName(n);\r\n            var node_1;\r\n            if (n.isSVG) {\r\n                node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);\r\n            }\r\n            else {\r\n                node_1 = doc.createElement(tagName);\r\n            }\r\n            var specialAttributes = {};\r\n            for (var name_1 in n.attributes) {\r\n                if (!Object.prototype.hasOwnProperty.call(n.attributes, name_1)) {\r\n                    continue;\r\n                }\r\n                var value = n.attributes[name_1];\r\n                if (tagName === 'option' &&\r\n                    name_1 === 'selected' &&\r\n                    value === false) {\r\n                    continue;\r\n                }\r\n                if (value === true)\r\n                    value = '';\r\n                if (name_1.startsWith('rr_')) {\r\n                    specialAttributes[name_1] = value;\r\n                    continue;\r\n                }\r\n                var isTextarea = tagName === 'textarea' && name_1 === 'value';\r\n                var isRemoteOrDynamicCss = tagName === 'style' && name_1 === '_cssText';\r\n                if (isRemoteOrDynamicCss && hackCss && typeof value === 'string') {\r\n                    value = addHoverClass(value, cache);\r\n                }\r\n                if ((isTextarea || isRemoteOrDynamicCss) && typeof value === 'string') {\r\n                    var child = doc.createTextNode(value);\r\n                    for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {\r\n                        var c = _a[_i];\r\n                        if (c.nodeType === node_1.TEXT_NODE) {\r\n                            node_1.removeChild(c);\r\n                        }\r\n                    }\r\n                    node_1.appendChild(child);\r\n                    continue;\r\n                }\r\n                try {\r\n                    if (n.isSVG && name_1 === 'xlink:href') {\r\n                        node_1.setAttributeNS('http://www.w3.org/1999/xlink', name_1, value.toString());\r\n                    }\r\n                    else if (name_1 === 'onload' ||\r\n                        name_1 === 'onclick' ||\r\n                        name_1.substring(0, 7) === 'onmouse') {\r\n                        node_1.setAttribute('_' + name_1, value.toString());\r\n                    }\r\n                    else if (tagName === 'meta' &&\r\n                        n.attributes['http-equiv'] === 'Content-Security-Policy' &&\r\n                        name_1 === 'content') {\r\n                        node_1.setAttribute('csp-content', value.toString());\r\n                        continue;\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'preload' &&\r\n                        n.attributes.as === 'script') {\r\n                    }\r\n                    else if (tagName === 'link' &&\r\n                        n.attributes.rel === 'prefetch' &&\r\n                        typeof n.attributes.href === 'string' &&\r\n                        n.attributes.href.endsWith('.js')) {\r\n                    }\r\n                    else if (tagName === 'img' &&\r\n                        n.attributes.srcset &&\r\n                        n.attributes.rr_dataURL) {\r\n                        node_1.setAttribute('rrweb-original-srcset', n.attributes.srcset);\r\n                    }\r\n                    else {\r\n                        node_1.setAttribute(name_1, value.toString());\r\n                    }\r\n                }\r\n                catch (error) {\r\n                }\r\n            }\r\n            var _loop_1 = function (name_2) {\r\n                var value = specialAttributes[name_2];\r\n                if (tagName === 'canvas' && name_2 === 'rr_dataURL') {\r\n                    var image_1 = document.createElement('img');\r\n                    image_1.onload = function () {\r\n                        var ctx = node_1.getContext('2d');\r\n                        if (ctx) {\r\n                            ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);\r\n                        }\r\n                    };\r\n                    image_1.src = value.toString();\r\n                    if (node_1.RRNodeType)\r\n                        node_1.rr_dataURL = value.toString();\r\n                }\r\n                else if (tagName === 'img' && name_2 === 'rr_dataURL') {\r\n                    var image = node_1;\r\n                    if (!image.currentSrc.startsWith('data:')) {\r\n                        image.setAttribute('rrweb-original-src', n.attributes.src);\r\n                        image.src = value.toString();\r\n                    }\r\n                }\r\n                if (name_2 === 'rr_width') {\r\n                    node_1.style.width = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_height') {\r\n                    node_1.style.height = value.toString();\r\n                }\r\n                else if (name_2 === 'rr_mediaCurrentTime' &&\r\n                    typeof value === 'number') {\r\n                    node_1.currentTime = value;\r\n                }\r\n                else if (name_2 === 'rr_mediaState') {\r\n                    switch (value) {\r\n                        case 'played':\r\n                            node_1\r\n                                .play()[\"catch\"](function (e) { return console.warn('media playback error', e); });\r\n                            break;\r\n                        case 'paused':\r\n                            node_1.pause();\r\n                            break;\r\n                    }\r\n                }\r\n            };\r\n            for (var name_2 in specialAttributes) {\r\n                _loop_1(name_2);\r\n            }\r\n            if (n.isShadowHost) {\r\n                if (!node_1.shadowRoot) {\r\n                    node_1.attachShadow({ mode: 'open' });\r\n                }\r\n                else {\r\n                    while (node_1.shadowRoot.firstChild) {\r\n                        node_1.shadowRoot.removeChild(node_1.shadowRoot.firstChild);\r\n                    }\r\n                }\r\n            }\r\n            return node_1;\r\n        }\r\n        case NodeType.Text:\r\n            return doc.createTextNode(n.isStyle && hackCss\r\n                ? addHoverClass(n.textContent, cache)\r\n                : n.textContent);\r\n        case NodeType.CDATA:\r\n            return doc.createCDATASection(n.textContent);\r\n        case NodeType.Comment:\r\n            return doc.createComment(n.textContent);\r\n        default:\r\n            return null;\r\n    }\r\n}\r\nfunction buildNodeWithSN(n, options) {\r\n    var doc = options.doc, mirror = options.mirror, _a = options.skipChild, skipChild = _a === void 0 ? false : _a, _b = options.hackCss, hackCss = _b === void 0 ? true : _b, afterAppend = options.afterAppend, cache = options.cache;\r\n    var node = buildNode(n, { doc: doc, hackCss: hackCss, cache: cache });\r\n    if (!node) {\r\n        return null;\r\n    }\r\n    if (n.rootId && mirror.getNode(n.rootId) !== doc) {\r\n        mirror.replace(n.rootId, doc);\r\n    }\r\n    if (n.type === NodeType.Document) {\r\n        doc.close();\r\n        doc.open();\r\n        if (n.compatMode === 'BackCompat' &&\r\n            n.childNodes &&\r\n            n.childNodes[0].type !== NodeType.DocumentType) {\r\n            if (n.childNodes[0].type === NodeType.Element &&\r\n                'xmlns' in n.childNodes[0].attributes &&\r\n                n.childNodes[0].attributes.xmlns === 'http://www.w3.org/1999/xhtml') {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">');\r\n            }\r\n            else {\r\n                doc.write('<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">');\r\n            }\r\n        }\r\n        node = doc;\r\n    }\r\n    mirror.add(node, n);\r\n    if ((n.type === NodeType.Document || n.type === NodeType.Element) &&\r\n        !skipChild) {\r\n        for (var _i = 0, _c = n.childNodes; _i < _c.length; _i++) {\r\n            var childN = _c[_i];\r\n            var childNode = buildNodeWithSN(childN, {\r\n                doc: doc,\r\n                mirror: mirror,\r\n                skipChild: false,\r\n                hackCss: hackCss,\r\n                afterAppend: afterAppend,\r\n                cache: cache\r\n            });\r\n            if (!childNode) {\r\n                console.warn('Failed to rebuild', childN);\r\n                continue;\r\n            }\r\n            if (childN.isShadow && isElement(node) && node.shadowRoot) {\r\n                node.shadowRoot.appendChild(childNode);\r\n            }\r\n            else {\r\n                node.appendChild(childNode);\r\n            }\r\n            if (afterAppend) {\r\n                afterAppend(childNode, childN.id);\r\n            }\r\n        }\r\n    }\r\n    return node;\r\n}\r\nfunction visit(mirror, onVisit) {\r\n    function walk(node) {\r\n        onVisit(node);\r\n    }\r\n    for (var _i = 0, _a = mirror.getIds(); _i < _a.length; _i++) {\r\n        var id = _a[_i];\r\n        if (mirror.has(id)) {\r\n            walk(mirror.getNode(id));\r\n        }\r\n    }\r\n}\r\nfunction handleScroll(node, mirror) {\r\n    var n = mirror.getMeta(node);\r\n    if ((n === null || n === void 0 ? void 0 : n.type) !== NodeType.Element) {\r\n        return;\r\n    }\r\n    var el = node;\r\n    for (var name_3 in n.attributes) {\r\n        if (!(Object.prototype.hasOwnProperty.call(n.attributes, name_3) &&\r\n            name_3.startsWith('rr_'))) {\r\n            continue;\r\n        }\r\n        var value = n.attributes[name_3];\r\n        if (name_3 === 'rr_scrollLeft') {\r\n            el.scrollLeft = value;\r\n        }\r\n        if (name_3 === 'rr_scrollTop') {\r\n            el.scrollTop = value;\r\n        }\r\n    }\r\n}\r\nfunction rebuild(n, options) {\r\n    var doc = options.doc, onVisit = options.onVisit, _a = options.hackCss, hackCss = _a === void 0 ? true : _a, afterAppend = options.afterAppend, cache = options.cache, _b = options.mirror, mirror = _b === void 0 ? new Mirror() : _b;\r\n    var node = buildNodeWithSN(n, {\r\n        doc: doc,\r\n        mirror: mirror,\r\n        skipChild: false,\r\n        hackCss: hackCss,\r\n        afterAppend: afterAppend,\r\n        cache: cache\r\n    });\r\n    visit(mirror, function (visitedNode) {\r\n        if (onVisit) {\r\n            onVisit(visitedNode);\r\n        }\r\n        handleScroll(visitedNode, mirror);\r\n    });\r\n    return node;\r\n}\n\nexport { IGNORED_NODE, Mirror, NodeType, addHoverClass, buildNodeWithSN, classMatchesRegex, cleanupSnapshot, createCache, createMirror, genId, getCssRuleString, getCssRulesString, is2DCanvasBlank, isCSSImportRule, isElement, isNativeShadowDom, isShadowRoot, maskInputValue, needMaskingText, rebuild, serializeNodeWithId, snapshot, transformAttribute, visitSnapshot };\n", "var NodeType$1;\r\n(function (NodeType) {\r\n    NodeType[NodeType[\"Document\"] = 0] = \"Document\";\r\n    NodeType[NodeType[\"DocumentType\"] = 1] = \"DocumentType\";\r\n    NodeType[NodeType[\"Element\"] = 2] = \"Element\";\r\n    NodeType[NodeType[\"Text\"] = 3] = \"Text\";\r\n    NodeType[NodeType[\"CDATA\"] = 4] = \"CDATA\";\r\n    NodeType[NodeType[\"Comment\"] = 5] = \"Comment\";\r\n})(NodeType$1 || (NodeType$1 = {}));\nvar Mirror$1 = (function () {\r\n    function Mirror() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n    Mirror.prototype.getId = function (n) {\r\n        var _a;\r\n        if (!n)\r\n            return -1;\r\n        var id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\r\n        return id !== null && id !== void 0 ? id : -1;\r\n    };\r\n    Mirror.prototype.getNode = function (id) {\r\n        return this.idNodeMap.get(id) || null;\r\n    };\r\n    Mirror.prototype.getIds = function () {\r\n        return Array.from(this.idNodeMap.keys());\r\n    };\r\n    Mirror.prototype.getMeta = function (n) {\r\n        return this.nodeMetaMap.get(n) || null;\r\n    };\r\n    Mirror.prototype.removeNodeFromMap = function (n) {\r\n        var _this = this;\r\n        var id = this.getId(n);\r\n        this.idNodeMap[\"delete\"](id);\r\n        if (n.childNodes) {\r\n            n.childNodes.forEach(function (childNode) {\r\n                return _this.removeNodeFromMap(childNode);\r\n            });\r\n        }\r\n    };\r\n    Mirror.prototype.has = function (id) {\r\n        return this.idNodeMap.has(id);\r\n    };\r\n    Mirror.prototype.hasNode = function (node) {\r\n        return this.nodeMetaMap.has(node);\r\n    };\r\n    Mirror.prototype.add = function (n, meta) {\r\n        var id = meta.id;\r\n        this.idNodeMap.set(id, n);\r\n        this.nodeMetaMap.set(n, meta);\r\n    };\r\n    Mirror.prototype.replace = function (id, n) {\r\n        var oldNode = this.getNode(id);\r\n        if (oldNode) {\r\n            var meta = this.nodeMetaMap.get(oldNode);\r\n            if (meta)\r\n                this.nodeMetaMap.set(n, meta);\r\n        }\r\n        this.idNodeMap.set(id, n);\r\n    };\r\n    Mirror.prototype.reset = function () {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    };\r\n    return Mirror;\r\n}());\r\nfunction createMirror$1() {\r\n    return new Mirror$1();\r\n}\n\nfunction parseCSSText(cssText) {\r\n    const res = {};\r\n    const listDelimiter = /;(?![^(]*\\))/g;\r\n    const propertyDelimiter = /:(.+)/;\r\n    const comment = /\\/\\*.*?\\*\\//g;\r\n    cssText\r\n        .replace(comment, '')\r\n        .split(listDelimiter)\r\n        .forEach(function (item) {\r\n        if (item) {\r\n            const tmp = item.split(propertyDelimiter);\r\n            tmp.length > 1 && (res[camelize(tmp[0].trim())] = tmp[1].trim());\r\n        }\r\n    });\r\n    return res;\r\n}\r\nfunction toCSSText(style) {\r\n    const properties = [];\r\n    for (const name in style) {\r\n        const value = style[name];\r\n        if (typeof value !== 'string')\r\n            continue;\r\n        const normalizedName = hyphenate(name);\r\n        properties.push(`${normalizedName}: ${value};`);\r\n    }\r\n    return properties.join(' ');\r\n}\r\nconst camelizeRE = /-([a-z])/g;\r\nconst CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9-]+$/;\r\nconst camelize = (str) => {\r\n    if (CUSTOM_PROPERTY_REGEX.test(str))\r\n        return str;\r\n    return str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''));\r\n};\r\nconst hyphenateRE = /\\B([A-Z])/g;\r\nconst hyphenate = (str) => {\r\n    return str.replace(hyphenateRE, '-$1').toLowerCase();\r\n};\n\nclass BaseRRNode {\r\n    constructor(..._args) {\r\n        this.childNodes = [];\r\n        this.parentElement = null;\r\n        this.parentNode = null;\r\n        this.ELEMENT_NODE = NodeType.ELEMENT_NODE;\r\n        this.TEXT_NODE = NodeType.TEXT_NODE;\r\n    }\r\n    get firstChild() {\r\n        return this.childNodes[0] || null;\r\n    }\r\n    get lastChild() {\r\n        return this.childNodes[this.childNodes.length - 1] || null;\r\n    }\r\n    get nextSibling() {\r\n        const parentNode = this.parentNode;\r\n        if (!parentNode)\r\n            return null;\r\n        const siblings = parentNode.childNodes;\r\n        const index = siblings.indexOf(this);\r\n        return siblings[index + 1] || null;\r\n    }\r\n    contains(node) {\r\n        if (node === this)\r\n            return true;\r\n        for (const child of this.childNodes) {\r\n            if (child.contains(node))\r\n                return true;\r\n        }\r\n        return false;\r\n    }\r\n    appendChild(_newChild) {\r\n        throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`);\r\n    }\r\n    insertBefore(_newChild, _refChild) {\r\n        throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`);\r\n    }\r\n    removeChild(_node) {\r\n        throw new Error(`RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.`);\r\n    }\r\n    toString() {\r\n        return 'RRNode';\r\n    }\r\n}\r\nfunction BaseRRDocumentImpl(RRNodeClass) {\r\n    return class BaseRRDocument extends RRNodeClass {\r\n        constructor() {\r\n            super(...arguments);\r\n            this.nodeType = NodeType.DOCUMENT_NODE;\r\n            this.nodeName = '#document';\r\n            this.compatMode = 'CSS1Compat';\r\n            this.RRNodeType = NodeType$1.Document;\r\n            this.textContent = null;\r\n        }\r\n        get documentElement() {\r\n            return (this.childNodes.find((node) => node.RRNodeType === NodeType$1.Element &&\r\n                node.tagName === 'HTML') || null);\r\n        }\r\n        get body() {\r\n            var _a;\r\n            return (((_a = this.documentElement) === null || _a === void 0 ? void 0 : _a.childNodes.find((node) => node.RRNodeType === NodeType$1.Element &&\r\n                node.tagName === 'BODY')) || null);\r\n        }\r\n        get head() {\r\n            var _a;\r\n            return (((_a = this.documentElement) === null || _a === void 0 ? void 0 : _a.childNodes.find((node) => node.RRNodeType === NodeType$1.Element &&\r\n                node.tagName === 'HEAD')) || null);\r\n        }\r\n        get implementation() {\r\n            return this;\r\n        }\r\n        get firstElementChild() {\r\n            return this.documentElement;\r\n        }\r\n        appendChild(childNode) {\r\n            const nodeType = childNode.RRNodeType;\r\n            if (nodeType === NodeType$1.Element ||\r\n                nodeType === NodeType$1.DocumentType) {\r\n                if (this.childNodes.some((s) => s.RRNodeType === nodeType)) {\r\n                    throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${nodeType === NodeType$1.Element ? 'RRElement' : 'RRDoctype'} on RRDocument allowed.`);\r\n                }\r\n            }\r\n            childNode.parentElement = null;\r\n            childNode.parentNode = this;\r\n            this.childNodes.push(childNode);\r\n            return childNode;\r\n        }\r\n        insertBefore(newChild, refChild) {\r\n            const nodeType = newChild.RRNodeType;\r\n            if (nodeType === NodeType$1.Element ||\r\n                nodeType === NodeType$1.DocumentType) {\r\n                if (this.childNodes.some((s) => s.RRNodeType === nodeType)) {\r\n                    throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${nodeType === NodeType$1.Element ? 'RRElement' : 'RRDoctype'} on RRDocument allowed.`);\r\n                }\r\n            }\r\n            if (refChild === null)\r\n                return this.appendChild(newChild);\r\n            const childIndex = this.childNodes.indexOf(refChild);\r\n            if (childIndex == -1)\r\n                throw new Error(\"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\");\r\n            this.childNodes.splice(childIndex, 0, newChild);\r\n            newChild.parentElement = null;\r\n            newChild.parentNode = this;\r\n            return newChild;\r\n        }\r\n        removeChild(node) {\r\n            const indexOfChild = this.childNodes.indexOf(node);\r\n            if (indexOfChild === -1)\r\n                throw new Error(\"Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.\");\r\n            this.childNodes.splice(indexOfChild, 1);\r\n            node.parentElement = null;\r\n            node.parentNode = null;\r\n            return node;\r\n        }\r\n        open() {\r\n            this.childNodes = [];\r\n        }\r\n        close() {\r\n        }\r\n        write(content) {\r\n            let publicId;\r\n            if (content ===\r\n                '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"\">')\r\n                publicId = '-//W3C//DTD XHTML 1.0 Transitional//EN';\r\n            else if (content ===\r\n                '<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"\">')\r\n                publicId = '-//W3C//DTD HTML 4.0 Transitional//EN';\r\n            if (publicId) {\r\n                const doctype = this.createDocumentType('html', publicId, '');\r\n                this.open();\r\n                this.appendChild(doctype);\r\n            }\r\n        }\r\n        createDocument(_namespace, _qualifiedName, _doctype) {\r\n            return new BaseRRDocument();\r\n        }\r\n        createDocumentType(qualifiedName, publicId, systemId) {\r\n            const doctype = new (BaseRRDocumentTypeImpl(BaseRRNode))(qualifiedName, publicId, systemId);\r\n            doctype.ownerDocument = this;\r\n            return doctype;\r\n        }\r\n        createElement(tagName) {\r\n            const element = new (BaseRRElementImpl(BaseRRNode))(tagName);\r\n            element.ownerDocument = this;\r\n            return element;\r\n        }\r\n        createElementNS(_namespaceURI, qualifiedName) {\r\n            return this.createElement(qualifiedName);\r\n        }\r\n        createTextNode(data) {\r\n            const text = new (BaseRRTextImpl(BaseRRNode))(data);\r\n            text.ownerDocument = this;\r\n            return text;\r\n        }\r\n        createComment(data) {\r\n            const comment = new (BaseRRCommentImpl(BaseRRNode))(data);\r\n            comment.ownerDocument = this;\r\n            return comment;\r\n        }\r\n        createCDATASection(data) {\r\n            const CDATASection = new (BaseRRCDATASectionImpl(BaseRRNode))(data);\r\n            CDATASection.ownerDocument = this;\r\n            return CDATASection;\r\n        }\r\n        toString() {\r\n            return 'RRDocument';\r\n        }\r\n    };\r\n}\r\nfunction BaseRRDocumentTypeImpl(RRNodeClass) {\r\n    return class BaseRRDocumentType extends RRNodeClass {\r\n        constructor(qualifiedName, publicId, systemId) {\r\n            super();\r\n            this.nodeType = NodeType.DOCUMENT_TYPE_NODE;\r\n            this.RRNodeType = NodeType$1.DocumentType;\r\n            this.textContent = null;\r\n            this.name = qualifiedName;\r\n            this.publicId = publicId;\r\n            this.systemId = systemId;\r\n            this.nodeName = qualifiedName;\r\n        }\r\n        toString() {\r\n            return 'RRDocumentType';\r\n        }\r\n    };\r\n}\r\nfunction BaseRRElementImpl(RRNodeClass) {\r\n    return class BaseRRElement extends RRNodeClass {\r\n        constructor(tagName) {\r\n            super();\r\n            this.nodeType = NodeType.ELEMENT_NODE;\r\n            this.RRNodeType = NodeType$1.Element;\r\n            this.attributes = {};\r\n            this.shadowRoot = null;\r\n            this.tagName = tagName.toUpperCase();\r\n            this.nodeName = tagName.toUpperCase();\r\n        }\r\n        get textContent() {\r\n            let result = '';\r\n            this.childNodes.forEach((node) => (result += node.textContent));\r\n            return result;\r\n        }\r\n        set textContent(textContent) {\r\n            this.childNodes = [this.ownerDocument.createTextNode(textContent)];\r\n        }\r\n        get classList() {\r\n            return new ClassList(this.attributes.class, (newClassName) => {\r\n                this.attributes.class = newClassName;\r\n            });\r\n        }\r\n        get id() {\r\n            return this.attributes.id || '';\r\n        }\r\n        get className() {\r\n            return this.attributes.class || '';\r\n        }\r\n        get style() {\r\n            const style = (this.attributes.style\r\n                ? parseCSSText(this.attributes.style)\r\n                : {});\r\n            const hyphenateRE = /\\B([A-Z])/g;\r\n            style.setProperty = (name, value, priority) => {\r\n                if (hyphenateRE.test(name))\r\n                    return;\r\n                const normalizedName = camelize(name);\r\n                if (!value)\r\n                    delete style[normalizedName];\r\n                else\r\n                    style[normalizedName] = value;\r\n                if (priority === 'important')\r\n                    style[normalizedName] += ' !important';\r\n                this.attributes.style = toCSSText(style);\r\n            };\r\n            style.removeProperty = (name) => {\r\n                if (hyphenateRE.test(name))\r\n                    return '';\r\n                const normalizedName = camelize(name);\r\n                const value = style[normalizedName] || '';\r\n                delete style[normalizedName];\r\n                this.attributes.style = toCSSText(style);\r\n                return value;\r\n            };\r\n            return style;\r\n        }\r\n        getAttribute(name) {\r\n            return this.attributes[name] || null;\r\n        }\r\n        setAttribute(name, attribute) {\r\n            this.attributes[name] = attribute;\r\n        }\r\n        setAttributeNS(_namespace, qualifiedName, value) {\r\n            this.setAttribute(qualifiedName, value);\r\n        }\r\n        removeAttribute(name) {\r\n            delete this.attributes[name];\r\n        }\r\n        appendChild(newChild) {\r\n            this.childNodes.push(newChild);\r\n            newChild.parentNode = this;\r\n            newChild.parentElement = this;\r\n            return newChild;\r\n        }\r\n        insertBefore(newChild, refChild) {\r\n            if (refChild === null)\r\n                return this.appendChild(newChild);\r\n            const childIndex = this.childNodes.indexOf(refChild);\r\n            if (childIndex == -1)\r\n                throw new Error(\"Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.\");\r\n            this.childNodes.splice(childIndex, 0, newChild);\r\n            newChild.parentElement = this;\r\n            newChild.parentNode = this;\r\n            return newChild;\r\n        }\r\n        removeChild(node) {\r\n            const indexOfChild = this.childNodes.indexOf(node);\r\n            if (indexOfChild === -1)\r\n                throw new Error(\"Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.\");\r\n            this.childNodes.splice(indexOfChild, 1);\r\n            node.parentElement = null;\r\n            node.parentNode = null;\r\n            return node;\r\n        }\r\n        attachShadow(_init) {\r\n            const shadowRoot = this.ownerDocument.createElement('SHADOWROOT');\r\n            this.shadowRoot = shadowRoot;\r\n            return shadowRoot;\r\n        }\r\n        dispatchEvent(_event) {\r\n            return true;\r\n        }\r\n        toString() {\r\n            let attributeString = '';\r\n            for (const attribute in this.attributes) {\r\n                attributeString += `${attribute}=\"${this.attributes[attribute]}\" `;\r\n            }\r\n            return `${this.tagName} ${attributeString}`;\r\n        }\r\n    };\r\n}\r\nfunction BaseRRMediaElementImpl(RRElementClass) {\r\n    return class BaseRRMediaElement extends RRElementClass {\r\n        attachShadow(_init) {\r\n            throw new Error(`RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow`);\r\n        }\r\n        play() {\r\n            this.paused = false;\r\n        }\r\n        pause() {\r\n            this.paused = true;\r\n        }\r\n    };\r\n}\r\nfunction BaseRRTextImpl(RRNodeClass) {\r\n    return class BaseRRText extends RRNodeClass {\r\n        constructor(data) {\r\n            super();\r\n            this.nodeType = NodeType.TEXT_NODE;\r\n            this.nodeName = '#text';\r\n            this.RRNodeType = NodeType$1.Text;\r\n            this.data = data;\r\n        }\r\n        get textContent() {\r\n            return this.data;\r\n        }\r\n        set textContent(textContent) {\r\n            this.data = textContent;\r\n        }\r\n        toString() {\r\n            return `RRText text=${JSON.stringify(this.data)}`;\r\n        }\r\n    };\r\n}\r\nfunction BaseRRCommentImpl(RRNodeClass) {\r\n    return class BaseRRComment extends RRNodeClass {\r\n        constructor(data) {\r\n            super();\r\n            this.nodeType = NodeType.COMMENT_NODE;\r\n            this.nodeName = '#comment';\r\n            this.RRNodeType = NodeType$1.Comment;\r\n            this.data = data;\r\n        }\r\n        get textContent() {\r\n            return this.data;\r\n        }\r\n        set textContent(textContent) {\r\n            this.data = textContent;\r\n        }\r\n        toString() {\r\n            return `RRComment text=${JSON.stringify(this.data)}`;\r\n        }\r\n    };\r\n}\r\nfunction BaseRRCDATASectionImpl(RRNodeClass) {\r\n    return class BaseRRCDATASection extends RRNodeClass {\r\n        constructor(data) {\r\n            super();\r\n            this.nodeName = '#cdata-section';\r\n            this.nodeType = NodeType.CDATA_SECTION_NODE;\r\n            this.RRNodeType = NodeType$1.CDATA;\r\n            this.data = data;\r\n        }\r\n        get textContent() {\r\n            return this.data;\r\n        }\r\n        set textContent(textContent) {\r\n            this.data = textContent;\r\n        }\r\n        toString() {\r\n            return `RRCDATASection data=${JSON.stringify(this.data)}`;\r\n        }\r\n    };\r\n}\r\nclass ClassList {\r\n    constructor(classText, onChange) {\r\n        this.classes = [];\r\n        this.add = (...classNames) => {\r\n            for (const item of classNames) {\r\n                const className = String(item);\r\n                if (this.classes.indexOf(className) >= 0)\r\n                    continue;\r\n                this.classes.push(className);\r\n            }\r\n            this.onChange && this.onChange(this.classes.join(' '));\r\n        };\r\n        this.remove = (...classNames) => {\r\n            this.classes = this.classes.filter((item) => classNames.indexOf(item) === -1);\r\n            this.onChange && this.onChange(this.classes.join(' '));\r\n        };\r\n        if (classText) {\r\n            const classes = classText.trim().split(/\\s+/);\r\n            this.classes.push(...classes);\r\n        }\r\n        this.onChange = onChange;\r\n    }\r\n}\r\nvar NodeType;\r\n(function (NodeType) {\r\n    NodeType[NodeType[\"PLACEHOLDER\"] = 0] = \"PLACEHOLDER\";\r\n    NodeType[NodeType[\"ELEMENT_NODE\"] = 1] = \"ELEMENT_NODE\";\r\n    NodeType[NodeType[\"ATTRIBUTE_NODE\"] = 2] = \"ATTRIBUTE_NODE\";\r\n    NodeType[NodeType[\"TEXT_NODE\"] = 3] = \"TEXT_NODE\";\r\n    NodeType[NodeType[\"CDATA_SECTION_NODE\"] = 4] = \"CDATA_SECTION_NODE\";\r\n    NodeType[NodeType[\"ENTITY_REFERENCE_NODE\"] = 5] = \"ENTITY_REFERENCE_NODE\";\r\n    NodeType[NodeType[\"ENTITY_NODE\"] = 6] = \"ENTITY_NODE\";\r\n    NodeType[NodeType[\"PROCESSING_INSTRUCTION_NODE\"] = 7] = \"PROCESSING_INSTRUCTION_NODE\";\r\n    NodeType[NodeType[\"COMMENT_NODE\"] = 8] = \"COMMENT_NODE\";\r\n    NodeType[NodeType[\"DOCUMENT_NODE\"] = 9] = \"DOCUMENT_NODE\";\r\n    NodeType[NodeType[\"DOCUMENT_TYPE_NODE\"] = 10] = \"DOCUMENT_TYPE_NODE\";\r\n    NodeType[NodeType[\"DOCUMENT_FRAGMENT_NODE\"] = 11] = \"DOCUMENT_FRAGMENT_NODE\";\r\n})(NodeType || (NodeType = {}));\n\nconst NAMESPACES = {\r\n    svg: 'http://www.w3.org/2000/svg',\r\n    'xlink:href': 'http://www.w3.org/1999/xlink',\r\n    xmlns: 'http://www.w3.org/2000/xmlns/',\r\n};\r\nconst SVGTagMap = {\r\n    altglyph: 'altGlyph',\r\n    altglyphdef: 'altGlyphDef',\r\n    altglyphitem: 'altGlyphItem',\r\n    animatecolor: 'animateColor',\r\n    animatemotion: 'animateMotion',\r\n    animatetransform: 'animateTransform',\r\n    clippath: 'clipPath',\r\n    feblend: 'feBlend',\r\n    fecolormatrix: 'feColorMatrix',\r\n    fecomponenttransfer: 'feComponentTransfer',\r\n    fecomposite: 'feComposite',\r\n    feconvolvematrix: 'feConvolveMatrix',\r\n    fediffuselighting: 'feDiffuseLighting',\r\n    fedisplacementmap: 'feDisplacementMap',\r\n    fedistantlight: 'feDistantLight',\r\n    fedropshadow: 'feDropShadow',\r\n    feflood: 'feFlood',\r\n    fefunca: 'feFuncA',\r\n    fefuncb: 'feFuncB',\r\n    fefuncg: 'feFuncG',\r\n    fefuncr: 'feFuncR',\r\n    fegaussianblur: 'feGaussianBlur',\r\n    feimage: 'feImage',\r\n    femerge: 'feMerge',\r\n    femergenode: 'feMergeNode',\r\n    femorphology: 'feMorphology',\r\n    feoffset: 'feOffset',\r\n    fepointlight: 'fePointLight',\r\n    fespecularlighting: 'feSpecularLighting',\r\n    fespotlight: 'feSpotLight',\r\n    fetile: 'feTile',\r\n    feturbulence: 'feTurbulence',\r\n    foreignobject: 'foreignObject',\r\n    glyphref: 'glyphRef',\r\n    lineargradient: 'linearGradient',\r\n    radialgradient: 'radialGradient',\r\n};\r\nfunction diff(oldTree, newTree, replayer, rrnodeMirror) {\r\n    const oldChildren = oldTree.childNodes;\r\n    const newChildren = newTree.childNodes;\r\n    rrnodeMirror =\r\n        rrnodeMirror ||\r\n            newTree.mirror ||\r\n            newTree.ownerDocument.mirror;\r\n    if (oldChildren.length > 0 || newChildren.length > 0) {\r\n        diffChildren(Array.from(oldChildren), newChildren, oldTree, replayer, rrnodeMirror);\r\n    }\r\n    let inputDataToApply = null, scrollDataToApply = null;\r\n    switch (newTree.RRNodeType) {\r\n        case NodeType$1.Document: {\r\n            const newRRDocument = newTree;\r\n            scrollDataToApply = newRRDocument.scrollData;\r\n            break;\r\n        }\r\n        case NodeType$1.Element: {\r\n            const oldElement = oldTree;\r\n            const newRRElement = newTree;\r\n            diffProps(oldElement, newRRElement, rrnodeMirror);\r\n            scrollDataToApply = newRRElement.scrollData;\r\n            inputDataToApply = newRRElement.inputData;\r\n            switch (newRRElement.tagName) {\r\n                case 'AUDIO':\r\n                case 'VIDEO': {\r\n                    const oldMediaElement = oldTree;\r\n                    const newMediaRRElement = newRRElement;\r\n                    if (newMediaRRElement.paused !== undefined)\r\n                        newMediaRRElement.paused\r\n                            ? void oldMediaElement.pause()\r\n                            : void oldMediaElement.play();\r\n                    if (newMediaRRElement.muted !== undefined)\r\n                        oldMediaElement.muted = newMediaRRElement.muted;\r\n                    if (newMediaRRElement.volume !== undefined)\r\n                        oldMediaElement.volume = newMediaRRElement.volume;\r\n                    if (newMediaRRElement.currentTime !== undefined)\r\n                        oldMediaElement.currentTime = newMediaRRElement.currentTime;\r\n                    if (newMediaRRElement.playbackRate !== undefined)\r\n                        oldMediaElement.playbackRate = newMediaRRElement.playbackRate;\r\n                    break;\r\n                }\r\n                case 'CANVAS':\r\n                    {\r\n                        const rrCanvasElement = newTree;\r\n                        if (rrCanvasElement.rr_dataURL !== null) {\r\n                            const image = document.createElement('img');\r\n                            image.onload = () => {\r\n                                const ctx = oldElement.getContext('2d');\r\n                                if (ctx) {\r\n                                    ctx.drawImage(image, 0, 0, image.width, image.height);\r\n                                }\r\n                            };\r\n                            image.src = rrCanvasElement.rr_dataURL;\r\n                        }\r\n                        rrCanvasElement.canvasMutations.forEach((canvasMutation) => replayer.applyCanvas(canvasMutation.event, canvasMutation.mutation, oldTree));\r\n                    }\r\n                    break;\r\n                case 'STYLE':\r\n                    {\r\n                        const styleSheet = oldElement.sheet;\r\n                        styleSheet &&\r\n                            newTree.rules.forEach((data) => replayer.applyStyleSheetMutation(data, styleSheet));\r\n                    }\r\n                    break;\r\n            }\r\n            if (newRRElement.shadowRoot) {\r\n                if (!oldElement.shadowRoot)\r\n                    oldElement.attachShadow({ mode: 'open' });\r\n                const oldChildren = oldElement.shadowRoot.childNodes;\r\n                const newChildren = newRRElement.shadowRoot.childNodes;\r\n                if (oldChildren.length > 0 || newChildren.length > 0)\r\n                    diffChildren(Array.from(oldChildren), newChildren, oldElement.shadowRoot, replayer, rrnodeMirror);\r\n            }\r\n            break;\r\n        }\r\n        case NodeType$1.Text:\r\n        case NodeType$1.Comment:\r\n        case NodeType$1.CDATA:\r\n            if (oldTree.textContent !==\r\n                newTree.data)\r\n                oldTree.textContent = newTree.data;\r\n            break;\r\n    }\r\n    scrollDataToApply && replayer.applyScroll(scrollDataToApply, true);\r\n    inputDataToApply && replayer.applyInput(inputDataToApply);\r\n    if (newTree.nodeName === 'IFRAME') {\r\n        const oldContentDocument = oldTree.contentDocument;\r\n        const newIFrameElement = newTree;\r\n        if (oldContentDocument) {\r\n            const sn = rrnodeMirror.getMeta(newIFrameElement.contentDocument);\r\n            if (sn) {\r\n                replayer.mirror.add(oldContentDocument, Object.assign({}, sn));\r\n            }\r\n            diff(oldContentDocument, newIFrameElement.contentDocument, replayer, rrnodeMirror);\r\n        }\r\n    }\r\n}\r\nfunction diffProps(oldTree, newTree, rrnodeMirror) {\r\n    const oldAttributes = oldTree.attributes;\r\n    const newAttributes = newTree.attributes;\r\n    for (const name in newAttributes) {\r\n        const newValue = newAttributes[name];\r\n        const sn = rrnodeMirror.getMeta(newTree);\r\n        if (sn && 'isSVG' in sn && sn.isSVG && NAMESPACES[name])\r\n            oldTree.setAttributeNS(NAMESPACES[name], name, newValue);\r\n        else if (newTree.tagName === 'CANVAS' && name === 'rr_dataURL') {\r\n            const image = document.createElement('img');\r\n            image.src = newValue;\r\n            image.onload = () => {\r\n                const ctx = oldTree.getContext('2d');\r\n                if (ctx) {\r\n                    ctx.drawImage(image, 0, 0, image.width, image.height);\r\n                }\r\n            };\r\n        }\r\n        else\r\n            oldTree.setAttribute(name, newValue);\r\n    }\r\n    for (const { name } of Array.from(oldAttributes))\r\n        if (!(name in newAttributes))\r\n            oldTree.removeAttribute(name);\r\n    newTree.scrollLeft && (oldTree.scrollLeft = newTree.scrollLeft);\r\n    newTree.scrollTop && (oldTree.scrollTop = newTree.scrollTop);\r\n}\r\nfunction diffChildren(oldChildren, newChildren, parentNode, replayer, rrnodeMirror) {\r\n    var _a;\r\n    let oldStartIndex = 0, oldEndIndex = oldChildren.length - 1, newStartIndex = 0, newEndIndex = newChildren.length - 1;\r\n    let oldStartNode = oldChildren[oldStartIndex], oldEndNode = oldChildren[oldEndIndex], newStartNode = newChildren[newStartIndex], newEndNode = newChildren[newEndIndex];\r\n    let oldIdToIndex = undefined, indexInOld;\r\n    while (oldStartIndex <= oldEndIndex && newStartIndex <= newEndIndex) {\r\n        const oldStartId = replayer.mirror.getId(oldStartNode);\r\n        const oldEndId = replayer.mirror.getId(oldEndNode);\r\n        const newStartId = rrnodeMirror.getId(newStartNode);\r\n        const newEndId = rrnodeMirror.getId(newEndNode);\r\n        if (oldStartNode === undefined) {\r\n            oldStartNode = oldChildren[++oldStartIndex];\r\n        }\r\n        else if (oldEndNode === undefined) {\r\n            oldEndNode = oldChildren[--oldEndIndex];\r\n        }\r\n        else if (oldStartId !== -1 &&\r\n            oldStartId === newStartId) {\r\n            diff(oldStartNode, newStartNode, replayer, rrnodeMirror);\r\n            oldStartNode = oldChildren[++oldStartIndex];\r\n            newStartNode = newChildren[++newStartIndex];\r\n        }\r\n        else if (oldEndId !== -1 &&\r\n            oldEndId === newEndId) {\r\n            diff(oldEndNode, newEndNode, replayer, rrnodeMirror);\r\n            oldEndNode = oldChildren[--oldEndIndex];\r\n            newEndNode = newChildren[--newEndIndex];\r\n        }\r\n        else if (oldStartId !== -1 &&\r\n            oldStartId === newEndId) {\r\n            parentNode.insertBefore(oldStartNode, oldEndNode.nextSibling);\r\n            diff(oldStartNode, newEndNode, replayer, rrnodeMirror);\r\n            oldStartNode = oldChildren[++oldStartIndex];\r\n            newEndNode = newChildren[--newEndIndex];\r\n        }\r\n        else if (oldEndId !== -1 &&\r\n            oldEndId === newStartId) {\r\n            parentNode.insertBefore(oldEndNode, oldStartNode);\r\n            diff(oldEndNode, newStartNode, replayer, rrnodeMirror);\r\n            oldEndNode = oldChildren[--oldEndIndex];\r\n            newStartNode = newChildren[++newStartIndex];\r\n        }\r\n        else {\r\n            if (!oldIdToIndex) {\r\n                oldIdToIndex = {};\r\n                for (let i = oldStartIndex; i <= oldEndIndex; i++) {\r\n                    const oldChild = oldChildren[i];\r\n                    if (oldChild && replayer.mirror.hasNode(oldChild))\r\n                        oldIdToIndex[replayer.mirror.getId(oldChild)] = i;\r\n                }\r\n            }\r\n            indexInOld = oldIdToIndex[rrnodeMirror.getId(newStartNode)];\r\n            if (indexInOld) {\r\n                const nodeToMove = oldChildren[indexInOld];\r\n                parentNode.insertBefore(nodeToMove, oldStartNode);\r\n                diff(nodeToMove, newStartNode, replayer, rrnodeMirror);\r\n                oldChildren[indexInOld] = undefined;\r\n            }\r\n            else {\r\n                const newNode = createOrGetNode(newStartNode, replayer.mirror, rrnodeMirror);\r\n                if (parentNode.nodeName === '#document' &&\r\n                    ((_a = replayer.mirror.getMeta(newNode)) === null || _a === void 0 ? void 0 : _a.type) === NodeType$1.Element &&\r\n                    parentNode.documentElement) {\r\n                    parentNode.removeChild(parentNode.documentElement);\r\n                    oldChildren[oldStartIndex] = undefined;\r\n                    oldStartNode = undefined;\r\n                }\r\n                parentNode.insertBefore(newNode, oldStartNode || null);\r\n                diff(newNode, newStartNode, replayer, rrnodeMirror);\r\n            }\r\n            newStartNode = newChildren[++newStartIndex];\r\n        }\r\n    }\r\n    if (oldStartIndex > oldEndIndex) {\r\n        const referenceRRNode = newChildren[newEndIndex + 1];\r\n        let referenceNode = null;\r\n        if (referenceRRNode)\r\n            parentNode.childNodes.forEach((child) => {\r\n                if (replayer.mirror.getId(child) === rrnodeMirror.getId(referenceRRNode))\r\n                    referenceNode = child;\r\n            });\r\n        for (; newStartIndex <= newEndIndex; ++newStartIndex) {\r\n            const newNode = createOrGetNode(newChildren[newStartIndex], replayer.mirror, rrnodeMirror);\r\n            parentNode.insertBefore(newNode, referenceNode);\r\n            diff(newNode, newChildren[newStartIndex], replayer, rrnodeMirror);\r\n        }\r\n    }\r\n    else if (newStartIndex > newEndIndex) {\r\n        for (; oldStartIndex <= oldEndIndex; oldStartIndex++) {\r\n            const node = oldChildren[oldStartIndex];\r\n            if (node) {\r\n                parentNode.removeChild(node);\r\n                replayer.mirror.removeNodeFromMap(node);\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction createOrGetNode(rrNode, domMirror, rrnodeMirror) {\r\n    const nodeId = rrnodeMirror.getId(rrNode);\r\n    const sn = rrnodeMirror.getMeta(rrNode);\r\n    let node = null;\r\n    if (nodeId > -1)\r\n        node = domMirror.getNode(nodeId);\r\n    if (node !== null)\r\n        return node;\r\n    switch (rrNode.RRNodeType) {\r\n        case NodeType$1.Document:\r\n            node = new Document();\r\n            break;\r\n        case NodeType$1.DocumentType:\r\n            node = document.implementation.createDocumentType(rrNode.name, rrNode.publicId, rrNode.systemId);\r\n            break;\r\n        case NodeType$1.Element: {\r\n            let tagName = rrNode.tagName.toLowerCase();\r\n            tagName = SVGTagMap[tagName] || tagName;\r\n            if (sn && 'isSVG' in sn && (sn === null || sn === void 0 ? void 0 : sn.isSVG)) {\r\n                node = document.createElementNS(NAMESPACES['svg'], tagName);\r\n            }\r\n            else\r\n                node = document.createElement(rrNode.tagName);\r\n            break;\r\n        }\r\n        case NodeType$1.Text:\r\n            node = document.createTextNode(rrNode.data);\r\n            break;\r\n        case NodeType$1.Comment:\r\n            node = document.createComment(rrNode.data);\r\n            break;\r\n        case NodeType$1.CDATA:\r\n            node = document.createCDATASection(rrNode.data);\r\n            break;\r\n    }\r\n    if (sn)\r\n        domMirror.add(node, Object.assign({}, sn));\r\n    return node;\r\n}\n\nclass RRDocument extends BaseRRDocumentImpl(BaseRRNode) {\r\n    constructor(mirror) {\r\n        super();\r\n        this.UNSERIALIZED_STARTING_ID = -2;\r\n        this._unserializedId = this.UNSERIALIZED_STARTING_ID;\r\n        this.mirror = createMirror();\r\n        this.scrollData = null;\r\n        if (mirror) {\r\n            this.mirror = mirror;\r\n        }\r\n    }\r\n    get unserializedId() {\r\n        return this._unserializedId--;\r\n    }\r\n    createDocument(_namespace, _qualifiedName, _doctype) {\r\n        return new RRDocument();\r\n    }\r\n    createDocumentType(qualifiedName, publicId, systemId) {\r\n        const documentTypeNode = new RRDocumentType(qualifiedName, publicId, systemId);\r\n        documentTypeNode.ownerDocument = this;\r\n        return documentTypeNode;\r\n    }\r\n    createElement(tagName) {\r\n        const upperTagName = tagName.toUpperCase();\r\n        let element;\r\n        switch (upperTagName) {\r\n            case 'AUDIO':\r\n            case 'VIDEO':\r\n                element = new RRMediaElement(upperTagName);\r\n                break;\r\n            case 'IFRAME':\r\n                element = new RRIFrameElement(upperTagName, this.mirror);\r\n                break;\r\n            case 'CANVAS':\r\n                element = new RRCanvasElement(upperTagName);\r\n                break;\r\n            case 'STYLE':\r\n                element = new RRStyleElement(upperTagName);\r\n                break;\r\n            default:\r\n                element = new RRElement(upperTagName);\r\n                break;\r\n        }\r\n        element.ownerDocument = this;\r\n        return element;\r\n    }\r\n    createComment(data) {\r\n        const commentNode = new RRComment(data);\r\n        commentNode.ownerDocument = this;\r\n        return commentNode;\r\n    }\r\n    createCDATASection(data) {\r\n        const sectionNode = new RRCDATASection(data);\r\n        sectionNode.ownerDocument = this;\r\n        return sectionNode;\r\n    }\r\n    createTextNode(data) {\r\n        const textNode = new RRText(data);\r\n        textNode.ownerDocument = this;\r\n        return textNode;\r\n    }\r\n    destroyTree() {\r\n        this.childNodes = [];\r\n        this.mirror.reset();\r\n    }\r\n    open() {\r\n        super.open();\r\n        this._unserializedId = this.UNSERIALIZED_STARTING_ID;\r\n    }\r\n}\r\nconst RRDocumentType = BaseRRDocumentTypeImpl(BaseRRNode);\r\nclass RRElement extends BaseRRElementImpl(BaseRRNode) {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.inputData = null;\r\n        this.scrollData = null;\r\n    }\r\n}\r\nclass RRMediaElement extends BaseRRMediaElementImpl(RRElement) {\r\n}\r\nclass RRCanvasElement extends RRElement {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.rr_dataURL = null;\r\n        this.canvasMutations = [];\r\n    }\r\n    getContext() {\r\n        return null;\r\n    }\r\n}\r\nclass RRStyleElement extends RRElement {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.rules = [];\r\n    }\r\n}\r\nclass RRIFrameElement extends RRElement {\r\n    constructor(upperTagName, mirror) {\r\n        super(upperTagName);\r\n        this.contentDocument = new RRDocument();\r\n        this.contentDocument.mirror = mirror;\r\n    }\r\n}\r\nconst RRText = BaseRRTextImpl(BaseRRNode);\r\nconst RRComment = BaseRRCommentImpl(BaseRRNode);\r\nconst RRCDATASection = BaseRRCDATASectionImpl(BaseRRNode);\r\nfunction getValidTagName(element) {\r\n    if (element instanceof HTMLFormElement) {\r\n        return 'FORM';\r\n    }\r\n    return element.tagName.toUpperCase();\r\n}\r\nfunction buildFromNode(node, rrdom, domMirror, parentRRNode) {\r\n    let rrNode;\r\n    switch (node.nodeType) {\r\n        case NodeType.DOCUMENT_NODE:\r\n            if (parentRRNode && parentRRNode.nodeName === 'IFRAME')\r\n                rrNode = parentRRNode.contentDocument;\r\n            else {\r\n                rrNode = rrdom;\r\n                rrNode.compatMode = node.compatMode;\r\n            }\r\n            break;\r\n        case NodeType.DOCUMENT_TYPE_NODE: {\r\n            const documentType = node;\r\n            rrNode = rrdom.createDocumentType(documentType.name, documentType.publicId, documentType.systemId);\r\n            break;\r\n        }\r\n        case NodeType.ELEMENT_NODE: {\r\n            const elementNode = node;\r\n            const tagName = getValidTagName(elementNode);\r\n            rrNode = rrdom.createElement(tagName);\r\n            const rrElement = rrNode;\r\n            for (const { name, value } of Array.from(elementNode.attributes)) {\r\n                rrElement.attributes[name] = value;\r\n            }\r\n            elementNode.scrollLeft && (rrElement.scrollLeft = elementNode.scrollLeft);\r\n            elementNode.scrollTop && (rrElement.scrollTop = elementNode.scrollTop);\r\n            break;\r\n        }\r\n        case NodeType.TEXT_NODE:\r\n            rrNode = rrdom.createTextNode(node.textContent || '');\r\n            break;\r\n        case NodeType.CDATA_SECTION_NODE:\r\n            rrNode = rrdom.createCDATASection(node.data);\r\n            break;\r\n        case NodeType.COMMENT_NODE:\r\n            rrNode = rrdom.createComment(node.textContent || '');\r\n            break;\r\n        case NodeType.DOCUMENT_FRAGMENT_NODE:\r\n            rrNode = parentRRNode.attachShadow({ mode: 'open' });\r\n            break;\r\n        default:\r\n            return null;\r\n    }\r\n    let sn = domMirror.getMeta(node);\r\n    if (rrdom instanceof RRDocument) {\r\n        if (!sn) {\r\n            sn = getDefaultSN(rrNode, rrdom.unserializedId);\r\n            domMirror.add(node, sn);\r\n        }\r\n        rrdom.mirror.add(rrNode, Object.assign({}, sn));\r\n    }\r\n    return rrNode;\r\n}\r\nfunction buildFromDom(dom, domMirror = createMirror$1(), rrdom = new RRDocument()) {\r\n    function walk(node, parentRRNode) {\r\n        const rrNode = buildFromNode(node, rrdom, domMirror, parentRRNode);\r\n        if (rrNode === null)\r\n            return;\r\n        if ((parentRRNode === null || parentRRNode === void 0 ? void 0 : parentRRNode.nodeName) !== 'IFRAME' &&\r\n            node.nodeType !== NodeType.DOCUMENT_FRAGMENT_NODE) {\r\n            parentRRNode === null || parentRRNode === void 0 ? void 0 : parentRRNode.appendChild(rrNode);\r\n            rrNode.parentNode = parentRRNode;\r\n            rrNode.parentElement = parentRRNode;\r\n        }\r\n        if (node.nodeName === 'IFRAME') {\r\n            const iframeDoc = node.contentDocument;\r\n            iframeDoc && walk(iframeDoc, rrNode);\r\n        }\r\n        else if (node.nodeType === NodeType.DOCUMENT_NODE ||\r\n            node.nodeType === NodeType.ELEMENT_NODE ||\r\n            node.nodeType === NodeType.DOCUMENT_FRAGMENT_NODE) {\r\n            if (node.nodeType === NodeType.ELEMENT_NODE &&\r\n                node.shadowRoot)\r\n                walk(node.shadowRoot, rrNode);\r\n            node.childNodes.forEach((childNode) => walk(childNode, rrNode));\r\n        }\r\n    }\r\n    walk(dom, null);\r\n    return rrdom;\r\n}\r\nfunction createMirror() {\r\n    return new Mirror();\r\n}\r\nclass Mirror {\r\n    constructor() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n    getId(n) {\r\n        var _a;\r\n        if (!n)\r\n            return -1;\r\n        const id = (_a = this.getMeta(n)) === null || _a === void 0 ? void 0 : _a.id;\r\n        return id !== null && id !== void 0 ? id : -1;\r\n    }\r\n    getNode(id) {\r\n        return this.idNodeMap.get(id) || null;\r\n    }\r\n    getIds() {\r\n        return Array.from(this.idNodeMap.keys());\r\n    }\r\n    getMeta(n) {\r\n        return this.nodeMetaMap.get(n) || null;\r\n    }\r\n    removeNodeFromMap(n) {\r\n        const id = this.getId(n);\r\n        this.idNodeMap.delete(id);\r\n        if (n.childNodes) {\r\n            n.childNodes.forEach((childNode) => this.removeNodeFromMap(childNode));\r\n        }\r\n    }\r\n    has(id) {\r\n        return this.idNodeMap.has(id);\r\n    }\r\n    hasNode(node) {\r\n        return this.nodeMetaMap.has(node);\r\n    }\r\n    add(n, meta) {\r\n        const id = meta.id;\r\n        this.idNodeMap.set(id, n);\r\n        this.nodeMetaMap.set(n, meta);\r\n    }\r\n    replace(id, n) {\r\n        const oldNode = this.getNode(id);\r\n        if (oldNode) {\r\n            const meta = this.nodeMetaMap.get(oldNode);\r\n            if (meta)\r\n                this.nodeMetaMap.set(n, meta);\r\n        }\r\n        this.idNodeMap.set(id, n);\r\n    }\r\n    reset() {\r\n        this.idNodeMap = new Map();\r\n        this.nodeMetaMap = new WeakMap();\r\n    }\r\n}\r\nfunction getDefaultSN(node, id) {\r\n    switch (node.RRNodeType) {\r\n        case NodeType$1.Document:\r\n            return {\r\n                id,\r\n                type: node.RRNodeType,\r\n                childNodes: [],\r\n            };\r\n        case NodeType$1.DocumentType: {\r\n            const doctype = node;\r\n            return {\r\n                id,\r\n                type: node.RRNodeType,\r\n                name: doctype.name,\r\n                publicId: doctype.publicId,\r\n                systemId: doctype.systemId,\r\n            };\r\n        }\r\n        case NodeType$1.Element:\r\n            return {\r\n                id,\r\n                type: node.RRNodeType,\r\n                tagName: node.tagName.toLowerCase(),\r\n                attributes: {},\r\n                childNodes: [],\r\n            };\r\n        case NodeType$1.Text:\r\n            return {\r\n                id,\r\n                type: node.RRNodeType,\r\n                textContent: node.textContent || '',\r\n            };\r\n        case NodeType$1.Comment:\r\n            return {\r\n                id,\r\n                type: node.RRNodeType,\r\n                textContent: node.textContent || '',\r\n            };\r\n        case NodeType$1.CDATA:\r\n            return {\r\n                id,\r\n                type: node.RRNodeType,\r\n                textContent: '',\r\n            };\r\n    }\r\n}\r\nfunction printRRDom(rootNode, mirror) {\r\n    return walk(rootNode, mirror, '');\r\n}\r\nfunction walk(node, mirror, blankSpace) {\r\n    let printText = `${blankSpace}${mirror.getId(node)} ${node.toString()}\\n`;\r\n    if (node.RRNodeType === NodeType$1.Element) {\r\n        const element = node;\r\n        if (element.shadowRoot)\r\n            printText += walk(element.shadowRoot, mirror, blankSpace + '  ');\r\n    }\r\n    for (const child of node.childNodes)\r\n        printText += walk(child, mirror, blankSpace + '  ');\r\n    if (node.nodeName === 'IFRAME')\r\n        printText += walk(node.contentDocument, mirror, blankSpace + '  ');\r\n    return printText;\r\n}\n\nexport { BaseRRCDATASectionImpl, BaseRRCommentImpl, BaseRRDocumentImpl, BaseRRDocumentTypeImpl, BaseRRElementImpl, BaseRRMediaElementImpl, BaseRRNode, BaseRRTextImpl, ClassList, Mirror, NodeType, RRCDATASection, RRCanvasElement, RRComment, RRDocument, RRDocumentType, RRElement, RRIFrameElement, RRMediaElement, BaseRRNode as RRNode, RRStyleElement, RRText, buildFromDom, buildFromNode, createMirror, createOrGetNode, diff, getDefaultSN, printRRDom };\n", "export default function(n){return{all:n=n||new Map,on:function(t,e){var i=n.get(t);i?i.push(e):n.set(t,[e])},off:function(t,e){var i=n.get(t);i&&(e?i.splice(i.indexOf(e)>>>0,1):n.set(t,[]))},emit:function(t,e){var i=n.get(t);i&&i.slice().map(function(n){n(e)}),(i=n.get(\"*\"))&&i.slice().map(function(n){n(t,e)})}}}\n//# sourceMappingURL=mitt.mjs.map\n", "/**\n * A fork version of https://github.com/iamdustan/smoothscroll\n * Add support of customize target window and document\n */\n\n/* eslint-disable */\n// @ts-nocheck\nexport function polyfill(w: Window = window, d = document) {\n  // return if scroll behavior is supported and polyfill is not forced\n  if (\n    'scrollBehavior' in d.documentElement.style &&\n    w.__forceSmoothScrollPolyfill__ !== true\n  ) {\n    return;\n  }\n\n  // globals\n  const Element = w.HTMLElement || w.Element;\n  const SCROLL_TIME = 468;\n\n  // object gathering original scroll methods\n  const original = {\n    scroll: w.scroll || w.scrollTo,\n    scrollBy: w.scrollBy,\n    elementScroll: Element.prototype.scroll || scrollElement,\n    scrollIntoView: Element.prototype.scrollIntoView,\n  };\n\n  // define timing method\n  const now =\n    w.performance && w.performance.now\n      ? w.performance.now.bind(w.performance)\n      : Date.now;\n\n  /**\n   * indicates if a the current browser is made by Microsoft\n   * @method isMicrosoftBrowser\n   * @param {String} userAgent\n   * @returns {Boolean}\n   */\n  function isMicrosoftBrowser(userAgent) {\n    const userAgentPatterns = ['MSIE ', 'Trident/', 'Edge/'];\n\n    return new RegExp(userAgentPatterns.join('|')).test(userAgent);\n  }\n\n  /*\n   * IE has rounding bug rounding down clientHeight and clientWidth and\n   * rounding up scrollHeight and scrollWidth causing false positives\n   * on hasScrollableSpace\n   */\n  const ROUNDING_TOLERANCE = isMicrosoftBrowser(w.navigator.userAgent) ? 1 : 0;\n\n  /**\n   * changes scroll position inside an element\n   * @method scrollElement\n   * @param {Number} x\n   * @param {Number} y\n   * @returns {undefined}\n   */\n  function scrollElement(x, y) {\n    this.scrollLeft = x;\n    this.scrollTop = y;\n  }\n\n  /**\n   * returns result of applying ease math function to a number\n   * @method ease\n   * @param {Number} k\n   * @returns {Number}\n   */\n  function ease(k) {\n    return 0.5 * (1 - Math.cos(Math.PI * k));\n  }\n\n  /**\n   * indicates if a smooth behavior should be applied\n   * @method shouldBailOut\n   * @param {Number|Object} firstArg\n   * @returns {Boolean}\n   */\n  function shouldBailOut(firstArg) {\n    if (\n      firstArg === null ||\n      typeof firstArg !== 'object' ||\n      firstArg.behavior === undefined ||\n      firstArg.behavior === 'auto' ||\n      firstArg.behavior === 'instant'\n    ) {\n      // first argument is not an object/null\n      // or behavior is auto, instant or undefined\n      return true;\n    }\n\n    if (typeof firstArg === 'object' && firstArg.behavior === 'smooth') {\n      // first argument is an object and behavior is smooth\n      return false;\n    }\n\n    // throw error when behavior is not supported\n    throw new TypeError(\n      'behavior member of ScrollOptions ' +\n        firstArg.behavior +\n        ' is not a valid value for enumeration ScrollBehavior.',\n    );\n  }\n\n  /**\n   * indicates if an element has scrollable space in the provided axis\n   * @method hasScrollableSpace\n   * @param {Node} el\n   * @param {String} axis\n   * @returns {Boolean}\n   */\n  function hasScrollableSpace(el, axis) {\n    if (axis === 'Y') {\n      return el.clientHeight + ROUNDING_TOLERANCE < el.scrollHeight;\n    }\n\n    if (axis === 'X') {\n      return el.clientWidth + ROUNDING_TOLERANCE < el.scrollWidth;\n    }\n  }\n\n  /**\n   * indicates if an element has a scrollable overflow property in the axis\n   * @method canOverflow\n   * @param {Node} el\n   * @param {String} axis\n   * @returns {Boolean}\n   */\n  function canOverflow(el, axis) {\n    const overflowValue = w.getComputedStyle(el, null)['overflow' + axis];\n\n    return overflowValue === 'auto' || overflowValue === 'scroll';\n  }\n\n  /**\n   * indicates if an element can be scrolled in either axis\n   * @method isScrollable\n   * @param {Node} el\n   * @param {String} axis\n   * @returns {Boolean}\n   */\n  function isScrollable(el) {\n    const isScrollableY = hasScrollableSpace(el, 'Y') && canOverflow(el, 'Y');\n    const isScrollableX = hasScrollableSpace(el, 'X') && canOverflow(el, 'X');\n\n    return isScrollableY || isScrollableX;\n  }\n\n  /**\n   * finds scrollable parent of an element\n   * @method findScrollableParent\n   * @param {Node} el\n   * @returns {Node} el\n   */\n  function findScrollableParent(el) {\n    while (el !== d.body && isScrollable(el) === false) {\n      el = el.parentNode || el.host;\n    }\n\n    return el;\n  }\n\n  /**\n   * self invoked function that, given a context, steps through scrolling\n   * @method step\n   * @param {Object} context\n   * @returns {undefined}\n   */\n  function step(context) {\n    const time = now();\n    let value;\n    let currentX;\n    let currentY;\n    let elapsed = (time - context.startTime) / SCROLL_TIME;\n\n    // avoid elapsed times higher than one\n    elapsed = elapsed > 1 ? 1 : elapsed;\n\n    // apply easing to elapsed time\n    value = ease(elapsed);\n\n    currentX = context.startX + (context.x - context.startX) * value;\n    currentY = context.startY + (context.y - context.startY) * value;\n\n    context.method.call(context.scrollable, currentX, currentY);\n\n    // scroll more if we have not reached our destination\n    if (currentX !== context.x || currentY !== context.y) {\n      w.requestAnimationFrame(step.bind(w, context));\n    }\n  }\n\n  /**\n   * scrolls window or element with a smooth behavior\n   * @method smoothScroll\n   * @param {Object|Node} el\n   * @param {Number} x\n   * @param {Number} y\n   * @returns {undefined}\n   */\n  function smoothScroll(el, x, y) {\n    let scrollable;\n    let startX;\n    let startY;\n    let method;\n    const startTime = now();\n\n    // define scroll context\n    if (el === d.body) {\n      scrollable = w;\n      startX = w.scrollX || w.pageXOffset;\n      startY = w.scrollY || w.pageYOffset;\n      method = original.scroll;\n    } else {\n      scrollable = el;\n      startX = el.scrollLeft;\n      startY = el.scrollTop;\n      method = scrollElement;\n    }\n\n    // scroll looping over a frame\n    step({\n      scrollable: scrollable,\n      method: method,\n      startTime: startTime,\n      startX: startX,\n      startY: startY,\n      x: x,\n      y: y,\n    });\n  }\n\n  // ORIGINAL METHODS OVERRIDES\n  // w.scroll and w.scrollTo\n  w.scroll = w.scrollTo = function () {\n    // avoid action when no arguments are passed\n    if (arguments[0] === undefined) {\n      return;\n    }\n\n    // avoid smooth behavior if not required\n    if (shouldBailOut(arguments[0]) === true) {\n      original.scroll.call(\n        w,\n        arguments[0].left !== undefined\n          ? arguments[0].left\n          : typeof arguments[0] !== 'object'\n          ? arguments[0]\n          : w.scrollX || w.pageXOffset,\n        // use top prop, second argument if present or fallback to scrollY\n        arguments[0].top !== undefined\n          ? arguments[0].top\n          : arguments[1] !== undefined\n          ? arguments[1]\n          : w.scrollY || w.pageYOffset,\n      );\n\n      return;\n    }\n\n    // LET THE SMOOTHNESS BEGIN!\n    smoothScroll.call(\n      w,\n      d.body,\n      arguments[0].left !== undefined\n        ? ~~arguments[0].left\n        : w.scrollX || w.pageXOffset,\n      arguments[0].top !== undefined\n        ? ~~arguments[0].top\n        : w.scrollY || w.pageYOffset,\n    );\n  };\n\n  // w.scrollBy\n  w.scrollBy = function () {\n    // avoid action when no arguments are passed\n    if (arguments[0] === undefined) {\n      return;\n    }\n\n    // avoid smooth behavior if not required\n    if (shouldBailOut(arguments[0])) {\n      original.scrollBy.call(\n        w,\n        arguments[0].left !== undefined\n          ? arguments[0].left\n          : typeof arguments[0] !== 'object'\n          ? arguments[0]\n          : 0,\n        arguments[0].top !== undefined\n          ? arguments[0].top\n          : arguments[1] !== undefined\n          ? arguments[1]\n          : 0,\n      );\n\n      return;\n    }\n\n    // LET THE SMOOTHNESS BEGIN!\n    smoothScroll.call(\n      w,\n      d.body,\n      ~~arguments[0].left + (w.scrollX || w.pageXOffset),\n      ~~arguments[0].top + (w.scrollY || w.pageYOffset),\n    );\n  };\n\n  // Element.prototype.scroll and Element.prototype.scrollTo\n  Element.prototype.scroll = Element.prototype.scrollTo = function () {\n    // avoid action when no arguments are passed\n    if (arguments[0] === undefined) {\n      return;\n    }\n\n    // avoid smooth behavior if not required\n    if (shouldBailOut(arguments[0]) === true) {\n      // if one number is passed, throw error to match Firefox implementation\n      if (typeof arguments[0] === 'number' && arguments[1] === undefined) {\n        throw new SyntaxError('Value could not be converted');\n      }\n\n      original.elementScroll.call(\n        this,\n        // use left prop, first number argument or fallback to scrollLeft\n        arguments[0].left !== undefined\n          ? ~~arguments[0].left\n          : typeof arguments[0] !== 'object'\n          ? ~~arguments[0]\n          : this.scrollLeft,\n        // use top prop, second argument or fallback to scrollTop\n        arguments[0].top !== undefined\n          ? ~~arguments[0].top\n          : arguments[1] !== undefined\n          ? ~~arguments[1]\n          : this.scrollTop,\n      );\n\n      return;\n    }\n\n    const left = arguments[0].left;\n    const top = arguments[0].top;\n\n    // LET THE SMOOTHNESS BEGIN!\n    smoothScroll.call(\n      this,\n      this,\n      typeof left === 'undefined' ? this.scrollLeft : ~~left,\n      typeof top === 'undefined' ? this.scrollTop : ~~top,\n    );\n  };\n\n  // Element.prototype.scrollBy\n  Element.prototype.scrollBy = function () {\n    // avoid action when no arguments are passed\n    if (arguments[0] === undefined) {\n      return;\n    }\n\n    // avoid smooth behavior if not required\n    if (shouldBailOut(arguments[0]) === true) {\n      original.elementScroll.call(\n        this,\n        arguments[0].left !== undefined\n          ? ~~arguments[0].left + this.scrollLeft\n          : ~~arguments[0] + this.scrollLeft,\n        arguments[0].top !== undefined\n          ? ~~arguments[0].top + this.scrollTop\n          : ~~arguments[1] + this.scrollTop,\n      );\n\n      return;\n    }\n\n    this.scroll({\n      left: ~~arguments[0].left + this.scrollLeft,\n      top: ~~arguments[0].top + this.scrollTop,\n      behavior: arguments[0].behavior,\n    });\n  };\n\n  // Element.prototype.scrollIntoView\n  Element.prototype.scrollIntoView = function () {\n    // avoid smooth behavior if not required\n    if (shouldBailOut(arguments[0]) === true) {\n      original.scrollIntoView.call(\n        this,\n        arguments[0] === undefined ? true : arguments[0],\n      );\n\n      return;\n    }\n\n    // LET THE SMOOTHNESS BEGIN!\n    const scrollableParent = findScrollableParent(this);\n    const parentRects = scrollableParent.getBoundingClientRect();\n    const clientRects = this.getBoundingClientRect();\n\n    if (scrollableParent !== d.body) {\n      // reveal element inside parent\n      smoothScroll.call(\n        this,\n        scrollableParent,\n        scrollableParent.scrollLeft + clientRects.left - parentRects.left,\n        scrollableParent.scrollTop + clientRects.top - parentRects.top,\n      );\n\n      // reveal parent in viewport unless is fixed\n      if (w.getComputedStyle(scrollableParent).position !== 'fixed') {\n        w.scrollBy({\n          left: parentRects.left,\n          top: parentRects.top,\n          behavior: 'smooth',\n        });\n      }\n    } else {\n      // reveal element in viewport\n      w.scrollBy({\n        left: clientRects.left,\n        top: clientRects.top,\n        behavior: 'smooth',\n      });\n    }\n  };\n}\n", "var EventType = /* @__PURE__ */ ((EventType2) => {\n  EventType2[EventType2[\"DomContentLoaded\"] = 0] = \"DomContentLoaded\";\n  EventType2[EventType2[\"Load\"] = 1] = \"Load\";\n  EventType2[EventType2[\"FullSnapshot\"] = 2] = \"FullSnapshot\";\n  EventType2[EventType2[\"IncrementalSnapshot\"] = 3] = \"IncrementalSnapshot\";\n  EventType2[EventType2[\"Meta\"] = 4] = \"Meta\";\n  EventType2[EventType2[\"Custom\"] = 5] = \"Custom\";\n  EventType2[EventType2[\"Plugin\"] = 6] = \"Plugin\";\n  return EventType2;\n})(EventType || {});\nvar IncrementalSource = /* @__PURE__ */ ((IncrementalSource2) => {\n  IncrementalSource2[IncrementalSource2[\"Mutation\"] = 0] = \"Mutation\";\n  IncrementalSource2[IncrementalSource2[\"MouseMove\"] = 1] = \"MouseMove\";\n  IncrementalSource2[IncrementalSource2[\"MouseInteraction\"] = 2] = \"MouseInteraction\";\n  IncrementalSource2[IncrementalSource2[\"Scroll\"] = 3] = \"Scroll\";\n  IncrementalSource2[IncrementalSource2[\"ViewportResize\"] = 4] = \"ViewportResize\";\n  IncrementalSource2[IncrementalSource2[\"Input\"] = 5] = \"Input\";\n  IncrementalSource2[IncrementalSource2[\"TouchMove\"] = 6] = \"TouchMove\";\n  IncrementalSource2[IncrementalSource2[\"MediaInteraction\"] = 7] = \"MediaInteraction\";\n  IncrementalSource2[IncrementalSource2[\"StyleSheetRule\"] = 8] = \"StyleSheetRule\";\n  IncrementalSource2[IncrementalSource2[\"CanvasMutation\"] = 9] = \"CanvasMutation\";\n  IncrementalSource2[IncrementalSource2[\"Font\"] = 10] = \"Font\";\n  IncrementalSource2[IncrementalSource2[\"Log\"] = 11] = \"Log\";\n  IncrementalSource2[IncrementalSource2[\"Drag\"] = 12] = \"Drag\";\n  IncrementalSource2[IncrementalSource2[\"StyleDeclaration\"] = 13] = \"StyleDeclaration\";\n  IncrementalSource2[IncrementalSource2[\"Selection\"] = 14] = \"Selection\";\n  IncrementalSource2[IncrementalSource2[\"AdoptedStyleSheet\"] = 15] = \"AdoptedStyleSheet\";\n  return IncrementalSource2;\n})(IncrementalSource || {});\nvar MouseInteractions = /* @__PURE__ */ ((MouseInteractions2) => {\n  MouseInteractions2[MouseInteractions2[\"MouseUp\"] = 0] = \"MouseUp\";\n  MouseInteractions2[MouseInteractions2[\"MouseDown\"] = 1] = \"MouseDown\";\n  MouseInteractions2[MouseInteractions2[\"Click\"] = 2] = \"Click\";\n  MouseInteractions2[MouseInteractions2[\"ContextMenu\"] = 3] = \"ContextMenu\";\n  MouseInteractions2[MouseInteractions2[\"DblClick\"] = 4] = \"DblClick\";\n  MouseInteractions2[MouseInteractions2[\"Focus\"] = 5] = \"Focus\";\n  MouseInteractions2[MouseInteractions2[\"Blur\"] = 6] = \"Blur\";\n  MouseInteractions2[MouseInteractions2[\"TouchStart\"] = 7] = \"TouchStart\";\n  MouseInteractions2[MouseInteractions2[\"TouchMove_Departed\"] = 8] = \"TouchMove_Departed\";\n  MouseInteractions2[MouseInteractions2[\"TouchEnd\"] = 9] = \"TouchEnd\";\n  MouseInteractions2[MouseInteractions2[\"TouchCancel\"] = 10] = \"TouchCancel\";\n  return MouseInteractions2;\n})(MouseInteractions || {});\nvar CanvasContext = /* @__PURE__ */ ((CanvasContext2) => {\n  CanvasContext2[CanvasContext2[\"2D\"] = 0] = \"2D\";\n  CanvasContext2[CanvasContext2[\"WebGL\"] = 1] = \"WebGL\";\n  CanvasContext2[CanvasContext2[\"WebGL2\"] = 2] = \"WebGL2\";\n  return CanvasContext2;\n})(CanvasContext || {});\nvar MediaInteractions = /* @__PURE__ */ ((MediaInteractions2) => {\n  MediaInteractions2[MediaInteractions2[\"Play\"] = 0] = \"Play\";\n  MediaInteractions2[MediaInteractions2[\"Pause\"] = 1] = \"Pause\";\n  MediaInteractions2[MediaInteractions2[\"Seeked\"] = 2] = \"Seeked\";\n  MediaInteractions2[MediaInteractions2[\"VolumeChange\"] = 3] = \"VolumeChange\";\n  MediaInteractions2[MediaInteractions2[\"RateChange\"] = 4] = \"RateChange\";\n  return MediaInteractions2;\n})(MediaInteractions || {});\nvar ReplayerEvents = /* @__PURE__ */ ((ReplayerEvents2) => {\n  ReplayerEvents2[\"Start\"] = \"start\";\n  ReplayerEvents2[\"Pause\"] = \"pause\";\n  ReplayerEvents2[\"Resume\"] = \"resume\";\n  ReplayerEvents2[\"Resize\"] = \"resize\";\n  ReplayerEvents2[\"Finish\"] = \"finish\";\n  ReplayerEvents2[\"FullsnapshotRebuilded\"] = \"fullsnapshot-rebuilded\";\n  ReplayerEvents2[\"LoadStylesheetStart\"] = \"load-stylesheet-start\";\n  ReplayerEvents2[\"LoadStylesheetEnd\"] = \"load-stylesheet-end\";\n  ReplayerEvents2[\"SkipStart\"] = \"skip-start\";\n  ReplayerEvents2[\"SkipEnd\"] = \"skip-end\";\n  ReplayerEvents2[\"MouseInteraction\"] = \"mouse-interaction\";\n  ReplayerEvents2[\"EventCast\"] = \"event-cast\";\n  ReplayerEvents2[\"CustomEvent\"] = \"custom-event\";\n  ReplayerEvents2[\"Flush\"] = \"flush\";\n  ReplayerEvents2[\"StateChange\"] = \"state-change\";\n  ReplayerEvents2[\"PlayBack\"] = \"play-back\";\n  ReplayerEvents2[\"Destroy\"] = \"destroy\";\n  return ReplayerEvents2;\n})(ReplayerEvents || {});\nexport {\n  CanvasContext,\n  EventType,\n  IncrementalSource,\n  MediaInteractions,\n  MouseInteractions,\n  ReplayerEvents\n};\n//# sourceMappingURL=types.js.map\n", "import {\n  actionWithDelay,\n  eventWithTime,\n  EventType,\n  IncrementalSource,\n} from '@rrweb/types';\n\nexport class Timer {\n  public timeOffset = 0;\n  public speed: number;\n\n  private actions: actionWithDelay[];\n  private raf: number | null = null;\n  private liveMode: boolean;\n\n  constructor(\n    actions: actionWithDelay[] = [],\n    config: {\n      speed: number;\n      liveMode: boolean;\n    },\n  ) {\n    this.actions = actions;\n    this.speed = config.speed;\n    this.liveMode = config.liveMode;\n  }\n  /**\n   * Add an action, possibly after the timer starts.\n   */\n  public addAction(action: actionWithDelay) {\n    if (\n      !this.actions.length ||\n      this.actions[this.actions.length - 1].delay <= action.delay\n    ) {\n      // 'fast track'\n      this.actions.push(action);\n      return;\n    }\n    // binary search - events can arrive out of order in a realtime context\n    const index = this.findActionIndex(action);\n    this.actions.splice(index, 0, action);\n  }\n\n  public start() {\n    this.timeOffset = 0;\n    let lastTimestamp = performance.now();\n    const check = () => {\n      const time = performance.now();\n      this.timeOffset += (time - lastTimestamp) * this.speed;\n      lastTimestamp = time;\n      while (this.actions.length) {\n        const action = this.actions[0];\n\n        if (this.timeOffset >= action.delay) {\n          this.actions.shift();\n          action.doAction();\n        } else {\n          break;\n        }\n      }\n      if (this.actions.length > 0 || this.liveMode) {\n        this.raf = requestAnimationFrame(check);\n      }\n    };\n    this.raf = requestAnimationFrame(check);\n  }\n\n  public clear() {\n    if (this.raf) {\n      cancelAnimationFrame(this.raf);\n      this.raf = null;\n    }\n    this.actions.length = 0;\n  }\n\n  public setSpeed(speed: number) {\n    this.speed = speed;\n  }\n\n  public toggleLiveMode(mode: boolean) {\n    this.liveMode = mode;\n  }\n\n  public isActive() {\n    return this.raf !== null;\n  }\n\n  private findActionIndex(action: actionWithDelay): number {\n    let start = 0;\n    let end = this.actions.length - 1;\n    while (start <= end) {\n      const mid = Math.floor((start + end) / 2);\n      if (this.actions[mid].delay < action.delay) {\n        start = mid + 1;\n      } else if (this.actions[mid].delay > action.delay) {\n        end = mid - 1;\n      } else {\n        // already an action with same delay (timestamp)\n        // the plus one will splice the new one after the existing one\n        return mid + 1;\n      }\n    }\n    return start;\n  }\n}\n\n// TODO: add speed to mouse move timestamp calculation\nexport function addDelay(event: eventWithTime, baselineTime: number): number {\n  // Mouse move events was recorded in a throttle function,\n  // so we need to find the real timestamp by traverse the time offsets.\n  if (\n    event.type === EventType.IncrementalSnapshot &&\n    event.data.source === IncrementalSource.MouseMove &&\n    event.data.positions &&\n    event.data.positions.length\n  ) {\n    const firstOffset = event.data.positions[0].timeOffset;\n    // timeOffset is a negative offset to event.timestamp\n    const firstTimestamp = event.timestamp + firstOffset;\n    event.delay = firstTimestamp - baselineTime;\n    return firstTimestamp - baselineTime;\n  }\n\n  event.delay = event.timestamp - baselineTime;\n  return event.delay;\n}\n", "/*! *****************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\nfunction t(t,n){var e=\"function\"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var r,o,i=e.call(t),a=[];try{for(;(void 0===n||n-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(e=i.return)&&e.call(i)}finally{if(o)throw o.error}}return a}var n;!function(t){t[t.NotStarted=0]=\"NotStarted\",t[t.Running=1]=\"Running\",t[t.Stopped=2]=\"Stopped\"}(n||(n={}));var e={type:\"xstate.init\"};function r(t){return void 0===t?[]:[].concat(t)}function o(t){return{type:\"xstate.assign\",assignment:t}}function i(t,n){return\"string\"==typeof(t=\"string\"==typeof t&&n&&n[t]?n[t]:t)?{type:t}:\"function\"==typeof t?{type:t.name,exec:t}:t}function a(t){return function(n){return t===n}}function u(t){return\"string\"==typeof t?{type:t}:t}function c(t,n){return{value:t,context:n,actions:[],changed:!1,matches:a(t)}}function f(t,n,e){var r=n,o=!1;return[t.filter((function(t){if(\"xstate.assign\"===t.type){o=!0;var n=Object.assign({},r);return\"function\"==typeof t.assignment?n=t.assignment(r,e):Object.keys(t.assignment).forEach((function(o){n[o]=\"function\"==typeof t.assignment[o]?t.assignment[o](r,e):t.assignment[o]})),r=n,!1}return!0})),r,o]}function s(n,o){void 0===o&&(o={});var s=t(f(r(n.states[n.initial].entry).map((function(t){return i(t,o.actions)})),n.context,e),2),l=s[0],v=s[1],y={config:n,_options:o,initialState:{value:n.initial,actions:l,context:v,matches:a(n.initial)},transition:function(e,o){var s,l,v=\"string\"==typeof e?{value:e,context:n.context}:e,p=v.value,g=v.context,d=u(o),x=n.states[p];if(x.on){var m=r(x.on[d.type]);try{for(var h=function(t){var n=\"function\"==typeof Symbol&&Symbol.iterator,e=n&&t[n],r=0;if(e)return e.call(t);if(t&&\"number\"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(n?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")}(m),b=h.next();!b.done;b=h.next()){var S=b.value;if(void 0===S)return c(p,g);var w=\"string\"==typeof S?{target:S}:S,j=w.target,E=w.actions,R=void 0===E?[]:E,N=w.cond,O=void 0===N?function(){return!0}:N,_=void 0===j,k=null!=j?j:p,T=n.states[k];if(O(g,d)){var q=t(f((_?r(R):[].concat(x.exit,R,T.entry).filter((function(t){return t}))).map((function(t){return i(t,y._options.actions)})),g,d),3),z=q[0],A=q[1],B=q[2],C=null!=j?j:p;return{value:C,context:A,actions:z,changed:j!==p||z.length>0||B,matches:a(C)}}}}catch(t){s={error:t}}finally{try{b&&!b.done&&(l=h.return)&&l.call(h)}finally{if(s)throw s.error}}}return c(p,g)}};return y}var l=function(t,n){return t.actions.forEach((function(e){var r=e.exec;return r&&r(t.context,n)}))};function v(t){var r=t.initialState,o=n.NotStarted,i=new Set,c={_machine:t,send:function(e){o===n.Running&&(r=t.transition(r,e),l(r,u(e)),i.forEach((function(t){return t(r)})))},subscribe:function(t){return i.add(t),t(r),{unsubscribe:function(){return i.delete(t)}}},start:function(i){if(i){var u=\"object\"==typeof i?i:{context:t.config.context,value:i};r={value:u.value,actions:[],context:u.context,matches:a(u.value)}}return o=n.Running,l(r,e),c},stop:function(){return o=n.Stopped,i.clear(),c},get state(){return r},get status(){return o}};return c}export{n as InterpreterStatus,o as assign,s as createMachine,v as interpret};\n", "import { createMachine, interpret, assign, StateMachine } from '@xstate/fsm';\nimport type { playerConfig } from '../types';\nimport {\n  eventWithTime,\n  ReplayerEvents,\n  EventType,\n  Emitter,\n  IncrementalSource,\n} from '@rrweb/types';\nimport { Timer, addDelay } from './timer';\n\nexport type PlayerContext = {\n  events: eventWithTime[];\n  timer: Timer;\n  timeOffset: number;\n  baselineTime: number;\n  lastPlayedEvent: eventWithTime | null;\n};\nexport type PlayerEvent =\n  | {\n      type: 'PLAY';\n      payload: {\n        timeOffset: number;\n      };\n    }\n  | {\n      type: 'CAST_EVENT';\n      payload: {\n        event: eventWithTime;\n      };\n    }\n  | { type: 'PAUSE' }\n  | { type: 'TO_LIVE'; payload: { baselineTime?: number } }\n  | {\n      type: 'ADD_EVENT';\n      payload: {\n        event: eventWithTime;\n      };\n    }\n  | {\n      type: 'END';\n    };\nexport type PlayerState =\n  | {\n      value: 'playing';\n      context: PlayerContext;\n    }\n  | {\n      value: 'paused';\n      context: PlayerContext;\n    }\n  | {\n      value: 'live';\n      context: PlayerContext;\n    };\n\n/**\n * If the array have multiple meta and fullsnapshot events,\n * return the events from last meta to the end.\n */\nexport function discardPriorSnapshots(\n  events: eventWithTime[],\n  baselineTime: number,\n): eventWithTime[] {\n  for (let idx = events.length - 1; idx >= 0; idx--) {\n    const event = events[idx];\n    if (event.type === EventType.Meta) {\n      if (event.timestamp <= baselineTime) {\n        return events.slice(idx);\n      }\n    }\n  }\n  return events;\n}\n\ntype PlayerAssets = {\n  emitter: Emitter;\n  applyEventsSynchronously(events: Array<eventWithTime>): void;\n  getCastFn(event: eventWithTime, isSync: boolean): () => void;\n};\nexport function createPlayerService(\n  context: PlayerContext,\n  { getCastFn, applyEventsSynchronously, emitter }: PlayerAssets,\n) {\n  const playerMachine = createMachine<PlayerContext, PlayerEvent, PlayerState>(\n    {\n      id: 'player',\n      context,\n      initial: 'paused',\n      states: {\n        playing: {\n          on: {\n            PAUSE: {\n              target: 'paused',\n              actions: ['pause'],\n            },\n            CAST_EVENT: {\n              target: 'playing',\n              actions: 'castEvent',\n            },\n            END: {\n              target: 'paused',\n              actions: ['resetLastPlayedEvent', 'pause'],\n            },\n            ADD_EVENT: {\n              target: 'playing',\n              actions: ['addEvent'],\n            },\n          },\n        },\n        paused: {\n          on: {\n            PLAY: {\n              target: 'playing',\n              actions: ['recordTimeOffset', 'play'],\n            },\n            CAST_EVENT: {\n              target: 'paused',\n              actions: 'castEvent',\n            },\n            TO_LIVE: {\n              target: 'live',\n              actions: ['startLive'],\n            },\n            ADD_EVENT: {\n              target: 'paused',\n              actions: ['addEvent'],\n            },\n          },\n        },\n        live: {\n          on: {\n            ADD_EVENT: {\n              target: 'live',\n              actions: ['addEvent'],\n            },\n            CAST_EVENT: {\n              target: 'live',\n              actions: ['castEvent'],\n            },\n          },\n        },\n      },\n    },\n    {\n      actions: {\n        castEvent: assign({\n          lastPlayedEvent: (ctx, event) => {\n            if (event.type === 'CAST_EVENT') {\n              return event.payload.event;\n            }\n            return ctx.lastPlayedEvent;\n          },\n        }),\n        recordTimeOffset: assign((ctx, event) => {\n          let timeOffset = ctx.timeOffset;\n          if ('payload' in event && 'timeOffset' in event.payload) {\n            timeOffset = event.payload.timeOffset;\n          }\n          return {\n            ...ctx,\n            timeOffset,\n            baselineTime: ctx.events[0].timestamp + timeOffset,\n          };\n        }),\n        play(ctx) {\n          const { timer, events, baselineTime, lastPlayedEvent } = ctx;\n          timer.clear();\n\n          for (const event of events) {\n            // TODO: improve this API\n            addDelay(event, baselineTime);\n          }\n          const neededEvents = discardPriorSnapshots(events, baselineTime);\n\n          let lastPlayedTimestamp = lastPlayedEvent?.timestamp;\n          if (\n            lastPlayedEvent?.type === EventType.IncrementalSnapshot &&\n            lastPlayedEvent.data.source === IncrementalSource.MouseMove\n          ) {\n            lastPlayedTimestamp =\n              lastPlayedEvent.timestamp +\n              lastPlayedEvent.data.positions[0]?.timeOffset;\n          }\n          if (baselineTime < (lastPlayedTimestamp || 0)) {\n            emitter.emit(ReplayerEvents.PlayBack);\n          }\n\n          const syncEvents = new Array<eventWithTime>();\n          for (const event of neededEvents) {\n            if (\n              lastPlayedTimestamp &&\n              lastPlayedTimestamp < baselineTime &&\n              (event.timestamp <= lastPlayedTimestamp ||\n                event === lastPlayedEvent)\n            ) {\n              continue;\n            }\n            if (event.timestamp < baselineTime) {\n              syncEvents.push(event);\n            } else {\n              const castFn = getCastFn(event, false);\n              timer.addAction({\n                doAction: () => {\n                  castFn();\n                },\n                delay: event.delay!,\n              });\n            }\n          }\n          applyEventsSynchronously(syncEvents);\n          emitter.emit(ReplayerEvents.Flush);\n          timer.start();\n        },\n        pause(ctx) {\n          ctx.timer.clear();\n        },\n        resetLastPlayedEvent: assign((ctx) => {\n          return {\n            ...ctx,\n            lastPlayedEvent: null,\n          };\n        }),\n        startLive: assign({\n          baselineTime: (ctx, event) => {\n            ctx.timer.toggleLiveMode(true);\n            ctx.timer.start();\n            if (event.type === 'TO_LIVE' && event.payload.baselineTime) {\n              return event.payload.baselineTime;\n            }\n            return Date.now();\n          },\n        }),\n        addEvent: assign((ctx, machineEvent) => {\n          const { baselineTime, timer, events } = ctx;\n          if (machineEvent.type === 'ADD_EVENT') {\n            const { event } = machineEvent.payload;\n            addDelay(event, baselineTime);\n\n            let end = events.length - 1;\n            if (!events[end] || events[end].timestamp <= event.timestamp) {\n              // fast track\n              events.push(event);\n            } else {\n              let insertionIndex = -1;\n              let start = 0;\n              while (start <= end) {\n                const mid = Math.floor((start + end) / 2);\n                if (events[mid].timestamp <= event.timestamp) {\n                  start = mid + 1;\n                } else {\n                  end = mid - 1;\n                }\n              }\n              if (insertionIndex === -1) {\n                insertionIndex = start;\n              }\n              events.splice(insertionIndex, 0, event);\n            }\n\n            const isSync = event.timestamp < baselineTime;\n            const castFn = getCastFn(event, isSync);\n            if (isSync) {\n              castFn();\n            } else if (timer.isActive()) {\n              timer.addAction({\n                doAction: () => {\n                  castFn();\n                },\n                delay: event.delay!,\n              });\n            }\n          }\n          return { ...ctx, events };\n        }),\n      },\n    },\n  );\n  return interpret(playerMachine);\n}\n\nexport type SpeedContext = {\n  normalSpeed: playerConfig['speed'];\n  timer: Timer;\n};\n\nexport type SpeedEvent =\n  | {\n      type: 'FAST_FORWARD';\n      payload: { speed: playerConfig['speed'] };\n    }\n  | {\n      type: 'BACK_TO_NORMAL';\n    }\n  | {\n      type: 'SET_SPEED';\n      payload: { speed: playerConfig['speed'] };\n    };\n\nexport type SpeedState =\n  | {\n      value: 'normal';\n      context: SpeedContext;\n    }\n  | {\n      value: 'skipping';\n      context: SpeedContext;\n    };\n\nexport function createSpeedService(context: SpeedContext) {\n  const speedMachine = createMachine<SpeedContext, SpeedEvent, SpeedState>(\n    {\n      id: 'speed',\n      context,\n      initial: 'normal',\n      states: {\n        normal: {\n          on: {\n            FAST_FORWARD: {\n              target: 'skipping',\n              actions: ['recordSpeed', 'setSpeed'],\n            },\n            SET_SPEED: {\n              target: 'normal',\n              actions: ['setSpeed'],\n            },\n          },\n        },\n        skipping: {\n          on: {\n            BACK_TO_NORMAL: {\n              target: 'normal',\n              actions: ['restoreSpeed'],\n            },\n            SET_SPEED: {\n              target: 'normal',\n              actions: ['setSpeed'],\n            },\n          },\n        },\n      },\n    },\n    {\n      actions: {\n        setSpeed: (ctx, event) => {\n          if ('payload' in event) {\n            ctx.timer.setSpeed(event.payload.speed);\n          }\n        },\n        recordSpeed: assign({\n          normalSpeed: (ctx) => ctx.timer.speed,\n        }),\n        restoreSpeed: (ctx) => {\n          ctx.timer.setSpeed(ctx.normalSpeed);\n        },\n      },\n    },\n  );\n\n  return interpret(speedMachine);\n}\n\nexport type PlayerMachineState = StateMachine.State<\n  PlayerContext,\n  PlayerEvent,\n  PlayerState\n>;\n\nexport type SpeedMachineState = StateMachine.State<\n  SpeedContext,\n  SpeedEvent,\n  SpeedState\n>;\n", "import type {\n  throttleOptions,\n  listenerHandler,\n  hookR<PERSON>tter,\n  blockClass,\n  addedNodeMutation,\n  DocumentDimension,\n  IWindow,\n  DeprecatedMirror,\n  textMutation,\n} from '@rrweb/types';\nimport type { I<PERSON>ir<PERSON>r, Mirror } from 'rrweb-snapshot';\nimport { isShadowRoot, IGNORED_NODE, classMatchesRegex } from 'rrweb-snapshot';\nimport type { RRNode, RRIFrameElement } from 'rrdom';\n\nexport function on(\n  type: string,\n  fn: EventListenerOrEventListenerObject,\n  target: Document | IWindow = document,\n): listenerHandler {\n  const options = { capture: true, passive: true };\n  target.addEventListener(type, fn, options);\n  return () => target.removeEventListener(type, fn, options);\n}\n\n// https://github.com/rrweb-io/rrweb/pull/407\nconst DEPARTED_MIRROR_ACCESS_WARNING =\n  'Please stop import mirror directly. Instead of that,' +\n  '\\r\\n' +\n  'now you can use replayer.getMirror() to access the mirror instance of a replayer,' +\n  '\\r\\n' +\n  'or you can use record.mirror to access the mirror instance during recording.';\nexport let _mirror: DeprecatedMirror = {\n  map: {},\n  getId() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return -1;\n  },\n  getNode() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return null;\n  },\n  removeNodeFromMap() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n  has() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return false;\n  },\n  reset() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n};\nif (typeof window !== 'undefined' && window.Proxy && window.Reflect) {\n  _mirror = new Proxy(_mirror, {\n    get(target, prop, receiver) {\n      if (prop === 'map') {\n        console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n      }\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      return Reflect.get(target, prop, receiver);\n    },\n  });\n}\n\n// copy from underscore and modified\nexport function throttle<T>(\n  func: (arg: T) => void,\n  wait: number,\n  options: throttleOptions = {},\n) {\n  let timeout: ReturnType<typeof setTimeout> | null = null;\n  let previous = 0;\n  return function (...args: T[]) {\n    const now = Date.now();\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n    const remaining = wait - (now - previous);\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-this-alias\n    const context = this;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(() => {\n        previous = options.leading === false ? 0 : Date.now();\n        timeout = null;\n        func.apply(context, args);\n      }, remaining);\n    }\n  };\n}\n\nexport function hookSetter<T>(\n  target: T,\n  key: string | number | symbol,\n  d: PropertyDescriptor,\n  isRevoked?: boolean,\n  win = window,\n): hookResetter {\n  const original = win.Object.getOwnPropertyDescriptor(target, key);\n  win.Object.defineProperty(\n    target,\n    key,\n    isRevoked\n      ? d\n      : {\n          set(value) {\n            // put hooked setter into event loop to avoid of set latency\n            setTimeout(() => {\n              d.set!.call(this, value);\n            }, 0);\n            if (original && original.set) {\n              original.set.call(this, value);\n            }\n          },\n        },\n  );\n  return () => hookSetter(target, key, original || {}, true);\n}\n\n// copy from https://github.com/getsentry/sentry-javascript/blob/b2109071975af8bf0316d3b5b38f519bdaf5dc15/packages/utils/src/object.ts\nexport function patch(\n  source: { [key: string]: any },\n  name: string,\n  replacement: (...args: unknown[]) => unknown,\n): () => void {\n  try {\n    if (!(name in source)) {\n      return () => {\n        //\n      };\n    }\n\n    const original = source[name] as () => unknown;\n    const wrapped = replacement(original);\n\n    // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n    // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n    if (typeof wrapped === 'function') {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __rrweb_original__: {\n          enumerable: false,\n          value: original,\n        },\n      });\n    }\n\n    source[name] = wrapped;\n\n    return () => {\n      source[name] = original;\n    };\n  } catch {\n    return () => {\n      //\n    };\n    // This can throw if multiple fill happens on a global object like XMLHttpRequest\n    // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n  }\n}\n\nexport function getWindowHeight(): number {\n  return (\n    window.innerHeight ||\n    (document.documentElement && document.documentElement.clientHeight) ||\n    (document.body && document.body.clientHeight)\n  );\n}\n\nexport function getWindowWidth(): number {\n  return (\n    window.innerWidth ||\n    (document.documentElement && document.documentElement.clientWidth) ||\n    (document.body && document.body.clientWidth)\n  );\n}\n\n/**\n * Checks if the given element set to be blocked by rrweb\n * @param node - node to check\n * @param blockClass - class name to check\n * @param blockSelector - css selectors to check\n * @param checkAncestors - whether to search through parent nodes for the block class\n * @returns true/false if the node was blocked or not\n */\nexport function isBlocked(\n  node: Node | null,\n  blockClass: blockClass,\n  blockSelector: string | null,\n  checkAncestors: boolean,\n): boolean {\n  if (!node) {\n    return false;\n  }\n  const el: HTMLElement | null =\n    node.nodeType === node.ELEMENT_NODE\n      ? (node as HTMLElement)\n      : node.parentElement;\n  if (!el) return false;\n\n  if (typeof blockClass === 'string') {\n    if (el.classList.contains(blockClass)) return true;\n    if (checkAncestors && el.closest('.' + blockClass) !== null) return true;\n  } else {\n    if (classMatchesRegex(el, blockClass, checkAncestors)) return true;\n  }\n  if (blockSelector) {\n    if ((node as HTMLElement).matches(blockSelector)) return true;\n    if (checkAncestors && el.closest(blockSelector) !== null) return true;\n  }\n  return false;\n}\n\nexport function isSerialized(n: Node, mirror: Mirror): boolean {\n  return mirror.getId(n) !== -1;\n}\n\nexport function isIgnored(n: Node, mirror: Mirror): boolean {\n  // The main part of the slimDOM check happens in\n  // rrweb-snapshot::serializeNodeWithId\n  return mirror.getId(n) === IGNORED_NODE;\n}\n\nexport function isAncestorRemoved(target: Node, mirror: Mirror): boolean {\n  if (isShadowRoot(target)) {\n    return false;\n  }\n  const id = mirror.getId(target);\n  if (!mirror.has(id)) {\n    return true;\n  }\n  if (\n    target.parentNode &&\n    target.parentNode.nodeType === target.DOCUMENT_NODE\n  ) {\n    return false;\n  }\n  // if the root is not document, it means the node is not in the DOM tree anymore\n  if (!target.parentNode) {\n    return true;\n  }\n  return isAncestorRemoved(target.parentNode, mirror);\n}\n\nexport function isTouchEvent(\n  event: MouseEvent | TouchEvent,\n): event is TouchEvent {\n  return Boolean((event as TouchEvent).changedTouches);\n}\n\nexport function polyfill(win = window) {\n  if ('NodeList' in win && !win.NodeList.prototype.forEach) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    win.NodeList.prototype.forEach = (Array.prototype\n      .forEach as unknown) as NodeList['forEach'];\n  }\n\n  if ('DOMTokenList' in win && !win.DOMTokenList.prototype.forEach) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    win.DOMTokenList.prototype.forEach = (Array.prototype\n      .forEach as unknown) as DOMTokenList['forEach'];\n  }\n\n  // https://github.com/Financial-Times/polyfill-service/pull/183\n  if (!Node.prototype.contains) {\n    Node.prototype.contains = (...args: unknown[]) => {\n      let node = args[0] as Node | null;\n      if (!(0 in args)) {\n        throw new TypeError('1 argument is required');\n      }\n\n      do {\n        if (this === node) {\n          return true;\n        }\n      } while ((node = node && node.parentNode));\n\n      return false;\n    };\n  }\n}\n\ntype ResolveTree = {\n  value: addedNodeMutation;\n  children: ResolveTree[];\n  parent: ResolveTree | null;\n};\n\nexport function queueToResolveTrees(queue: addedNodeMutation[]): ResolveTree[] {\n  const queueNodeMap: Record<number, ResolveTree> = {};\n  const putIntoMap = (\n    m: addedNodeMutation,\n    parent: ResolveTree | null,\n  ): ResolveTree => {\n    const nodeInTree: ResolveTree = {\n      value: m,\n      parent,\n      children: [],\n    };\n    queueNodeMap[m.node.id] = nodeInTree;\n    return nodeInTree;\n  };\n\n  const queueNodeTrees: ResolveTree[] = [];\n  for (const mutation of queue) {\n    const { nextId, parentId } = mutation;\n    if (nextId && nextId in queueNodeMap) {\n      const nextInTree = queueNodeMap[nextId];\n      if (nextInTree.parent) {\n        const idx = nextInTree.parent.children.indexOf(nextInTree);\n        nextInTree.parent.children.splice(\n          idx,\n          0,\n          putIntoMap(mutation, nextInTree.parent),\n        );\n      } else {\n        const idx = queueNodeTrees.indexOf(nextInTree);\n        queueNodeTrees.splice(idx, 0, putIntoMap(mutation, null));\n      }\n      continue;\n    }\n    if (parentId in queueNodeMap) {\n      const parentInTree = queueNodeMap[parentId];\n      parentInTree.children.push(putIntoMap(mutation, parentInTree));\n      continue;\n    }\n    queueNodeTrees.push(putIntoMap(mutation, null));\n  }\n\n  return queueNodeTrees;\n}\n\nexport function iterateResolveTree(\n  tree: ResolveTree,\n  cb: (mutation: addedNodeMutation) => unknown,\n) {\n  cb(tree.value);\n  /**\n   * The resolve tree was designed to reflect the DOM layout,\n   * but we need append next sibling first, so we do a reverse\n   * loop here.\n   */\n  for (let i = tree.children.length - 1; i >= 0; i--) {\n    iterateResolveTree(tree.children[i], cb);\n  }\n}\n\nexport type AppendedIframe = {\n  mutationInQueue: addedNodeMutation;\n  builtNode: HTMLIFrameElement | RRIFrameElement;\n};\n\nexport function isSerializedIframe<TNode extends Node | RRNode>(\n  n: TNode,\n  mirror: IMirror<TNode>,\n): boolean {\n  return Boolean(n.nodeName === 'IFRAME' && mirror.getMeta(n));\n}\n\nexport function isSerializedStylesheet<TNode extends Node | RRNode>(\n  n: TNode,\n  mirror: IMirror<TNode>,\n): boolean {\n  return Boolean(\n    n.nodeName === 'LINK' &&\n      n.nodeType === n.ELEMENT_NODE &&\n      (n as HTMLElement).getAttribute &&\n      (n as HTMLElement).getAttribute('rel') === 'stylesheet' &&\n      mirror.getMeta(n),\n  );\n}\n\nexport function getBaseDimension(\n  node: Node,\n  rootIframe: Node,\n): DocumentDimension {\n  const frameElement = node.ownerDocument?.defaultView?.frameElement;\n  if (!frameElement || frameElement === rootIframe) {\n    return {\n      x: 0,\n      y: 0,\n      relativeScale: 1,\n      absoluteScale: 1,\n    };\n  }\n\n  const frameDimension = frameElement.getBoundingClientRect();\n  const frameBaseDimension = getBaseDimension(frameElement, rootIframe);\n  // the iframe element may have a scale transform\n  const relativeScale = frameDimension.height / frameElement.clientHeight;\n  return {\n    x:\n      frameDimension.x * frameBaseDimension.relativeScale +\n      frameBaseDimension.x,\n    y:\n      frameDimension.y * frameBaseDimension.relativeScale +\n      frameBaseDimension.y,\n    relativeScale,\n    absoluteScale: frameBaseDimension.absoluteScale * relativeScale,\n  };\n}\n\nexport function hasShadowRoot<T extends Node | RRNode>(\n  n: T,\n): n is T & { shadowRoot: ShadowRoot } {\n  return Boolean(((n as unknown) as Element)?.shadowRoot);\n}\n\nexport function getNestedRule(\n  rules: CSSRuleList,\n  position: number[],\n): CSSGroupingRule {\n  const rule = rules[position[0]] as CSSGroupingRule;\n  if (position.length === 1) {\n    return rule;\n  } else {\n    return getNestedRule(\n      (rule.cssRules[position[1]] as CSSGroupingRule).cssRules,\n      position.slice(2),\n    );\n  }\n}\n\nexport function getPositionsAndIndex(nestedIndex: number[]) {\n  const positions = [...nestedIndex];\n  const index = positions.pop();\n  return { positions, index };\n}\n\n/**\n * Returns the latest mutation in the queue for each node.\n * @param mutations - mutations The text mutations to filter.\n * @returns The filtered text mutations.\n */\nexport function uniqueTextMutations(mutations: textMutation[]): textMutation[] {\n  const idSet = new Set<number>();\n  const uniqueMutations: textMutation[] = [];\n\n  for (let i = mutations.length; i--; ) {\n    const mutation = mutations[i];\n    if (!idSet.has(mutation.id)) {\n      uniqueMutations.push(mutation);\n      idSet.add(mutation.id);\n    }\n  }\n\n  return uniqueMutations;\n}\n\nexport class StyleSheetMirror {\n  private id = 1;\n  private styleIDMap = new WeakMap<CSSStyleSheet, number>();\n  private idStyleMap = new Map<number, CSSStyleSheet>();\n\n  getId(stylesheet: CSSStyleSheet): number {\n    return this.styleIDMap.get(stylesheet) ?? -1;\n  }\n\n  has(stylesheet: CSSStyleSheet): boolean {\n    return this.styleIDMap.has(stylesheet);\n  }\n\n  /**\n   * @returns If the stylesheet is in the mirror, returns the id of the stylesheet. If not, return the new assigned id.\n   */\n  add(stylesheet: CSSStyleSheet, id?: number): number {\n    if (this.has(stylesheet)) return this.getId(stylesheet);\n    let newId: number;\n    if (id === undefined) {\n      newId = this.id++;\n    } else newId = id;\n    this.styleIDMap.set(stylesheet, newId);\n    this.idStyleMap.set(newId, stylesheet);\n    return newId;\n  }\n\n  getStyle(id: number): CSSStyleSheet | null {\n    return this.idStyleMap.get(id) || null;\n  }\n\n  reset(): void {\n    this.styleIDMap = new WeakMap();\n    this.idStyleMap = new Map();\n    this.id = 1;\n  }\n\n  generateId(): number {\n    return this.id++;\n  }\n}\n", "const rules: (blockClass: string) => string[] = (blockClass: string) => [\n  `.${blockClass} { background: currentColor }`,\n  'noscript { display: none !important; }',\n];\n\nexport default rules;\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2021 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { decode } from 'base64-arraybuffer';\nimport type { Replayer } from '../';\nimport type { CanvasArg, SerializedCanvasArg } from '@rrweb/types';\n\n// TODO: add ability to wipe this list\ntype GLVarMap = Map<string, any[]>;\nconst webGLVarMap: Map<\n  CanvasRenderingContext2D | WebGLRenderingContext | WebGL2RenderingContext,\n  GLVarMap\n> = new Map();\nexport function variableListFor(\n  ctx:\n    | CanvasRenderingContext2D\n    | WebGLRenderingContext\n    | WebGL2RenderingContext,\n  ctor: string,\n) {\n  let contextMap = webGLVarMap.get(ctx);\n  if (!contextMap) {\n    contextMap = new Map();\n    webGLVarMap.set(ctx, contextMap);\n  }\n  if (!contextMap.has(ctor)) {\n    contextMap.set(ctor, []);\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n  return contextMap.get(ctor) as any[];\n}\n\nexport function isSerializedArg(arg: unknown): arg is SerializedCanvasArg {\n  return Boolean(arg && typeof arg === 'object' && 'rr_type' in arg);\n}\n\nexport function deserializeArg(\n  imageMap: Replayer['imageMap'],\n  ctx:\n    | CanvasRenderingContext2D\n    | WebGLRenderingContext\n    | WebGL2RenderingContext\n    | null,\n  preload?: {\n    isUnchanged: boolean;\n  },\n): (arg: CanvasArg) => Promise<any> {\n  return async (arg: CanvasArg): Promise<any> => {\n    if (arg && typeof arg === 'object' && 'rr_type' in arg) {\n      if (preload) preload.isUnchanged = false;\n      if (arg.rr_type === 'ImageBitmap' && 'args' in arg) {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        const args = await deserializeArg(imageMap, ctx, preload)(arg.args);\n        // eslint-disable-next-line prefer-spread\n        return await createImageBitmap.apply(null, args);\n      } else if ('index' in arg) {\n        if (preload || ctx === null) return arg; // we are preloading, ctx is unknown\n        const { rr_type: name, index } = arg;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return variableListFor(ctx, name)[index];\n      } else if ('args' in arg) {\n        const { rr_type: name, args } = arg;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        const ctor = window[name as keyof Window];\n\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call\n        return new ctor(\n          ...(await Promise.all(\n            args.map(deserializeArg(imageMap, ctx, preload)),\n          )),\n        );\n      } else if ('base64' in arg) {\n        return decode(arg.base64);\n      } else if ('src' in arg) {\n        const image = imageMap.get(arg.src);\n        if (image) {\n          return image;\n        } else {\n          const image = new Image();\n          image.src = arg.src;\n          imageMap.set(arg.src, image);\n          return image;\n        }\n      } else if ('data' in arg && arg.rr_type === 'Blob') {\n        const blobContents = await Promise.all(\n          arg.data.map(deserializeArg(imageMap, ctx, preload)),\n        );\n        const blob = new Blob(blobContents, {\n          type: arg.type,\n        });\n        return blob;\n      }\n    } else if (Array.isArray(arg)) {\n      const result = await Promise.all(\n        arg.map(deserializeArg(imageMap, ctx, preload)),\n      );\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      return result;\n    }\n    return arg;\n  };\n}\n", "import type { Replayer } from '../';\nimport { CanvasContext, canvasMutationCommand } from '@rrweb/types';\nimport { deserializeArg, variableListFor } from './deserialize-args';\n\nfunction getContext(\n  target: HTMLCanvasElement,\n  type: CanvasContext,\n): WebGLRenderingContext | WebGL2RenderingContext | null {\n  // Note to whomever is going to implement support for `contextAttributes`:\n  // if `preserveDrawingBuffer` is set to true,\n  // you might have to do `ctx.flush()` before every webgl canvas event\n  try {\n    if (type === CanvasContext.WebGL) {\n      return (\n        target.getContext('webgl')! || target.getContext('experimental-webgl')\n      );\n    }\n    return target.getContext('webgl2')!;\n  } catch (e) {\n    return null;\n  }\n}\n\nconst WebGLVariableConstructorsNames = [\n  'WebGLActiveInfo',\n  'WebGLBuffer',\n  'WebGLFramebuffer',\n  'WebGLProgram',\n  'WebGLRenderbuffer',\n  'WebGLShader',\n  'WebGLShaderPrecisionFormat',\n  'WebGLTexture',\n  'WebGLUniformLocation',\n  'WebGLVertexArrayObject',\n];\n\nfunction saveToWebGLVarMap(\n  ctx: WebGLRenderingContext | WebGL2RenderingContext,\n  result: any,\n) {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  if (!result?.constructor) return; // probably null or undefined\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n  const { name } = result.constructor;\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n  if (!WebGLVariableConstructorsNames.includes(name)) return; // not a WebGL variable\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n  const variables = variableListFor(ctx, name);\n  if (!variables.includes(result)) variables.push(result);\n}\n\nexport default async function webglMutation({\n  mutation,\n  target,\n  type,\n  imageMap,\n  errorHandler,\n}: {\n  mutation: canvasMutationCommand;\n  target: HTMLCanvasElement;\n  type: CanvasContext;\n  imageMap: Replayer['imageMap'];\n  errorHandler: Replayer['warnCanvasMutationFailed'];\n}): Promise<void> {\n  try {\n    const ctx = getContext(target, type);\n    if (!ctx) return;\n\n    // NOTE: if `preserveDrawingBuffer` is set to true,\n    // we must flush the buffers on every new canvas event\n    // if (mutation.newFrame) ctx.flush();\n\n    if (mutation.setter) {\n      // skip some read-only type checks\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      (ctx as any)[mutation.property] = mutation.args[0];\n      return;\n    }\n    const original = ctx[\n      mutation.property as Exclude<keyof typeof ctx, 'canvas'>\n    ] as (\n      ctx: WebGLRenderingContext | WebGL2RenderingContext,\n      args: unknown[],\n    ) => void;\n\n    const args = await Promise.all(\n      mutation.args.map(deserializeArg(imageMap, ctx)),\n    );\n    const result = original.apply(ctx, args);\n    saveToWebGLVarMap(ctx, result);\n\n    // Slows down replay considerably, only use for debugging\n    const debugMode = false;\n    if (debugMode) {\n      if (mutation.property === 'compileShader') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        if (!ctx.getShaderParameter(args[0], ctx.COMPILE_STATUS))\n          console.warn(\n            'something went wrong in replay',\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            ctx.getShaderInfoLog(args[0]),\n          );\n      } else if (mutation.property === 'linkProgram') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        ctx.validateProgram(args[0]);\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        if (!ctx.getProgramParameter(args[0], ctx.LINK_STATUS))\n          console.warn(\n            'something went wrong in replay',\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            ctx.getProgramInfoLog(args[0]),\n          );\n      }\n      const webglError = ctx.getError();\n      if (webglError !== ctx.NO_ERROR) {\n        console.warn(\n          'WEBGL ERROR',\n          webglError,\n          'on command:',\n          mutation.property,\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n          ...args,\n        );\n      }\n    }\n  } catch (error) {\n    errorHandler(mutation, error);\n  }\n}\n", "import type { Replayer } from '../';\nimport type { canvasMutationCommand } from '@rrweb/types';\nimport { deserializeArg } from './deserialize-args';\n\nexport default async function canvasMutation({\n  event,\n  mutation,\n  target,\n  imageMap,\n  errorHandler,\n}: {\n  event: Parameters<Replayer['applyIncremental']>[0];\n  mutation: canvasMutationCommand;\n  target: HTMLCanvasElement;\n  imageMap: Replayer['imageMap'];\n  errorHandler: Replayer['warnCanvasMutationFailed'];\n}): Promise<void> {\n  try {\n    const ctx = target.getContext('2d')!;\n\n    if (mutation.setter) {\n      // skip some read-only type checks\n      ((ctx as unknown) as Record<string, unknown>)[mutation.property] =\n        mutation.args[0];\n      return;\n    }\n    const original = ctx[\n      mutation.property as Exclude<keyof typeof ctx, 'canvas'>\n    ] as (ctx: CanvasRenderingContext2D, args: unknown[]) => void;\n\n    /**\n     * We have serialized the image source into base64 string during recording,\n     * which has been preloaded before replay.\n     * So we can get call drawImage SYNCHRONOUSLY which avoid some fragile cast.\n     */\n    if (\n      mutation.property === 'drawImage' &&\n      typeof mutation.args[0] === 'string'\n    ) {\n      imageMap.get(event);\n      original.apply(ctx, mutation.args);\n    } else {\n      const args = await Promise.all(\n        mutation.args.map(deserializeArg(imageMap, ctx)),\n      );\n      original.apply(ctx, args);\n    }\n  } catch (error) {\n    errorHandler(mutation, error);\n  }\n}\n", "import type { Replayer } from '..';\nimport {\n  CanvasContext,\n  canvasMutationCommand,\n  canvasMutationData,\n  canvasMutationParam,\n} from '@rrweb/types';\nimport webglMutation from './webgl';\nimport canvas2DMutation from './2d';\n\nexport default async function canvasMutation({\n  event,\n  mutation,\n  target,\n  imageMap,\n  canvasEventMap,\n  errorHandler,\n}: {\n  event: Parameters<Replayer['applyIncremental']>[0];\n  mutation: canvasMutationData;\n  target: HTMLCanvasElement;\n  imageMap: Replayer['imageMap'];\n  canvasEventMap: Replayer['canvasEventMap'];\n  errorHandler: Replayer['warnCanvasMutationFailed'];\n}): Promise<void> {\n  try {\n    const precomputedMutation: canvasMutationParam =\n      canvasEventMap.get(event) || mutation;\n\n    const commands: canvasMutationCommand[] =\n      'commands' in precomputedMutation\n        ? precomputedMutation.commands\n        : [precomputedMutation];\n\n    if ([CanvasContext.WebGL, CanvasContext.WebGL2].includes(mutation.type)) {\n      for (let i = 0; i < commands.length; i++) {\n        const command = commands[i];\n        await webglMutation({\n          mutation: command,\n          type: mutation.type,\n          target,\n          imageMap,\n          errorHandler,\n        });\n      }\n      return;\n    }\n    // default is '2d' for backwards compatibility (rrweb below 1.1.x)\n    for (let i = 0; i < commands.length; i++) {\n      const command = commands[i];\n      await canvas2DMutation({\n        event,\n        mutation: command,\n        target,\n        imageMap,\n        errorHandler,\n      });\n    }\n  } catch (error) {\n    errorHandler(mutation, error);\n  }\n}\n", "import {\n  rebuild,\n  buildNodeWithSN,\n  NodeType,\n  BuildCache,\n  createCache,\n  Mirror,\n  createMirror,\n  attributes,\n  serializedElementNodeWithId,\n} from 'rrweb-snapshot';\nimport {\n  RRDocument,\n  createOrGetNode,\n  buildFromNode,\n  buildFromDom,\n  diff,\n  getDefaultSN,\n} from 'rrdom';\nimport type {\n  RRNode,\n  RRElement,\n  RRStyleElement,\n  RRIFrameElement,\n  RRMediaElement,\n  RRCanvasElement,\n  ReplayerHandler,\n  Mirror as RRDOMMirror,\n} from 'rrdom';\nimport * as mittProxy from 'mitt';\nimport { polyfill as smoothscrollPolyfill } from './smoothscroll';\nimport { Timer } from './timer';\nimport { createPlayerService, createSpeedService } from './machine';\nimport type { playerConfig, missingNodeMap } from '../types';\nimport {\n  EventType,\n  IncrementalSource,\n  fullSnapshotEvent,\n  eventWithTime,\n  MouseInteractions,\n  playerMetaData,\n  viewportResizeDimension,\n  addedNodeMutation,\n  incrementalSnapshotEvent,\n  incrementalData,\n  ReplayerEvents,\n  Handler,\n  Emitter,\n  MediaInteractions,\n  metaEvent,\n  mutationData,\n  scrollData,\n  inputData,\n  canvasMutationData,\n  styleValueWithPriority,\n  mouseMovePos,\n  IWindow,\n  canvasMutationCommand,\n  canvasMutationParam,\n  canvasEventWithTime,\n  selectionData,\n  styleSheetRuleData,\n  styleDeclarationData,\n  adoptedStyleSheetData,\n} from '@rrweb/types';\nimport {\n  polyfill,\n  queueToResolveTrees,\n  iterateResolveTree,\n  AppendedIframe,\n  getBaseDimension,\n  hasShadowRoot,\n  isSerializedIframe,\n  getNestedRule,\n  getPositionsAndIndex,\n  uniqueTextMutations,\n  StyleSheetMirror,\n} from '../utils';\nimport getInjectStyleRules from './styles/inject-style';\nimport './styles/style.css';\nimport canvasMutation from './canvas';\nimport { deserializeArg } from './canvas/deserialize-args';\n\nconst SKIP_TIME_THRESHOLD = 10 * 1000;\nconst SKIP_TIME_INTERVAL = 5 * 1000;\n\n// https://github.com/rollup/rollup/issues/1267#issuecomment-296395734\nconst mitt = mittProxy.default || mittProxy;\n\nconst REPLAY_CONSOLE_PREFIX = '[replayer]';\n\nconst defaultMouseTailConfig = {\n  duration: 500,\n  lineCap: 'round',\n  lineWidth: 3,\n  strokeStyle: 'red',\n} as const;\n\nfunction indicatesTouchDevice(e: eventWithTime) {\n  return (\n    e.type == EventType.IncrementalSnapshot &&\n    (e.data.source == IncrementalSource.TouchMove ||\n      (e.data.source == IncrementalSource.MouseInteraction &&\n        e.data.type == MouseInteractions.TouchStart))\n  );\n}\n\nexport class Replayer {\n  public wrapper: HTMLDivElement;\n  public iframe: HTMLIFrameElement;\n\n  public service: ReturnType<typeof createPlayerService>;\n  public speedService: ReturnType<typeof createSpeedService>;\n  public get timer() {\n    return this.service.state.context.timer;\n  }\n\n  public config: playerConfig;\n\n  // In the fast-forward process, if the virtual-dom optimization is used, this flag value is true.\n  public usingVirtualDom = false;\n  public virtualDom: RRDocument = new RRDocument();\n\n  private mouse: HTMLDivElement;\n  private mouseTail: HTMLCanvasElement | null = null;\n  private tailPositions: Array<{ x: number; y: number }> = [];\n\n  private emitter: Emitter = mitt();\n\n  private nextUserInteractionEvent: eventWithTime | null;\n\n  private legacy_missingNodeRetryMap: missingNodeMap = {};\n\n  // The replayer uses the cache to speed up replay and scrubbing.\n  private cache: BuildCache = createCache();\n\n  private imageMap: Map<eventWithTime | string, HTMLImageElement> = new Map();\n  private canvasEventMap: Map<eventWithTime, canvasMutationParam> = new Map();\n\n  private mirror: Mirror = createMirror();\n\n  // Used to track StyleSheetObjects adopted on multiple document hosts.\n  private styleMirror: StyleSheetMirror = new StyleSheetMirror();\n\n  private firstFullSnapshot: eventWithTime | true | null = null;\n\n  private newDocumentQueue: addedNodeMutation[] = [];\n\n  private mousePos: mouseMovePos | null = null;\n  private touchActive: boolean | null = null;\n\n  // In the fast-forward mode, only the last selection data needs to be applied.\n  private lastSelectionData: selectionData | null = null;\n\n  // In the fast-forward mode using VirtualDom optimization, all stylesheetRule, and styleDeclaration events on constructed StyleSheets will be delayed to get applied until the flush stage.\n  private constructedStyleMutations: (\n    | styleSheetRuleData\n    | styleDeclarationData\n  )[] = [];\n\n  // Similar to the reason for constructedStyleMutations.\n  private adoptedStyleSheets: adoptedStyleSheetData[] = [];\n\n  constructor(\n    events: Array<eventWithTime | string>,\n    config?: Partial<playerConfig>,\n  ) {\n    if (!config?.liveMode && events.length < 2) {\n      throw new Error('Replayer need at least 2 events.');\n    }\n    const defaultConfig: playerConfig = {\n      speed: 1,\n      maxSpeed: 360,\n      root: document.body,\n      loadTimeout: 0,\n      skipInactive: false,\n      showWarning: true,\n      showDebug: false,\n      blockClass: 'rr-block',\n      liveMode: false,\n      insertStyleRules: [],\n      triggerFocus: true,\n      UNSAFE_replayCanvas: false,\n      pauseAnimation: true,\n      mouseTail: defaultMouseTailConfig,\n      useVirtualDom: true, // Virtual-dom optimization is enabled by default.\n    };\n    this.config = Object.assign({}, defaultConfig, config);\n\n    this.handleResize = this.handleResize.bind(this);\n    this.getCastFn = this.getCastFn.bind(this);\n    this.applyEventsSynchronously = this.applyEventsSynchronously.bind(this);\n    this.emitter.on(ReplayerEvents.Resize, this.handleResize as Handler);\n\n    this.setupDom();\n\n    /**\n     * Exposes mirror to the plugins\n     */\n    for (const plugin of this.config.plugins || []) {\n      if (plugin.getMirror) plugin.getMirror({ nodeMirror: this.mirror });\n    }\n\n    this.emitter.on(ReplayerEvents.Flush, () => {\n      if (this.usingVirtualDom) {\n        const replayerHandler: ReplayerHandler = {\n          mirror: this.mirror,\n          applyCanvas: (\n            canvasEvent: canvasEventWithTime,\n            canvasMutationData: canvasMutationData,\n            target: HTMLCanvasElement,\n          ) => {\n            void canvasMutation({\n              event: canvasEvent,\n              mutation: canvasMutationData,\n              target,\n              imageMap: this.imageMap,\n              canvasEventMap: this.canvasEventMap,\n              errorHandler: this.warnCanvasMutationFailed.bind(this),\n            });\n          },\n          applyInput: this.applyInput.bind(this),\n          applyScroll: this.applyScroll.bind(this),\n          applyStyleSheetMutation: (\n            data: styleDeclarationData | styleSheetRuleData,\n            styleSheet: CSSStyleSheet,\n          ) => {\n            if (data.source === IncrementalSource.StyleSheetRule)\n              this.applyStyleSheetRule(data, styleSheet);\n            else if (data.source === IncrementalSource.StyleDeclaration)\n              this.applyStyleDeclaration(data, styleSheet);\n          },\n        };\n        this.iframe.contentDocument &&\n          diff(\n            this.iframe.contentDocument,\n            this.virtualDom,\n            replayerHandler,\n            this.virtualDom.mirror,\n          );\n        this.virtualDom.destroyTree();\n        this.usingVirtualDom = false;\n\n        // If these legacy missing nodes haven't been resolved, they should be converted to real Nodes.\n        if (Object.keys(this.legacy_missingNodeRetryMap).length) {\n          for (const key in this.legacy_missingNodeRetryMap) {\n            try {\n              const value = this.legacy_missingNodeRetryMap[key];\n              const realNode = createOrGetNode(\n                value.node as RRNode,\n                this.mirror,\n                this.virtualDom.mirror,\n              );\n              diff(\n                realNode,\n                value.node as RRNode,\n                replayerHandler,\n                this.virtualDom.mirror,\n              );\n              value.node = realNode;\n            } catch (error) {\n              if (this.config.showWarning) {\n                console.warn(error);\n              }\n            }\n          }\n        }\n\n        this.constructedStyleMutations.forEach((data) => {\n          this.applyStyleSheetMutation(data);\n        });\n        this.constructedStyleMutations = [];\n\n        this.adoptedStyleSheets.forEach((data) => {\n          this.applyAdoptedStyleSheet(data);\n        });\n        this.adoptedStyleSheets = [];\n      }\n\n      if (this.mousePos) {\n        this.moveAndHover(\n          this.mousePos.x,\n          this.mousePos.y,\n          this.mousePos.id,\n          true,\n          this.mousePos.debugData,\n        );\n        this.mousePos = null;\n      }\n      if (this.lastSelectionData) {\n        this.applySelection(this.lastSelectionData);\n        this.lastSelectionData = null;\n      }\n    });\n    this.emitter.on(ReplayerEvents.PlayBack, () => {\n      this.firstFullSnapshot = null;\n      this.mirror.reset();\n      this.styleMirror.reset();\n    });\n\n    const timer = new Timer([], {\n      speed: this.config.speed,\n      liveMode: this.config.liveMode,\n    });\n    this.service = createPlayerService(\n      {\n        events: events\n          .map((e) => {\n            if (config && config.unpackFn) {\n              return config.unpackFn(e as string);\n            }\n            return e as eventWithTime;\n          })\n          .sort((a1, a2) => a1.timestamp - a2.timestamp),\n        timer,\n        timeOffset: 0,\n        baselineTime: 0,\n        lastPlayedEvent: null,\n      },\n      {\n        getCastFn: this.getCastFn,\n        applyEventsSynchronously: this.applyEventsSynchronously,\n        emitter: this.emitter,\n      },\n    );\n    this.service.start();\n    this.service.subscribe((state) => {\n      this.emitter.emit(ReplayerEvents.StateChange, {\n        player: state,\n      });\n    });\n    this.speedService = createSpeedService({\n      normalSpeed: -1,\n      timer,\n    });\n    this.speedService.start();\n    this.speedService.subscribe((state) => {\n      this.emitter.emit(ReplayerEvents.StateChange, {\n        speed: state,\n      });\n    });\n\n    // rebuild first full snapshot as the poster of the player\n    // maybe we can cache it for performance optimization\n    const firstMeta = this.service.state.context.events.find(\n      (e) => e.type === EventType.Meta,\n    );\n    const firstFullsnapshot = this.service.state.context.events.find(\n      (e) => e.type === EventType.FullSnapshot,\n    );\n    if (firstMeta) {\n      const { width, height } = firstMeta.data as metaEvent['data'];\n      setTimeout(() => {\n        this.emitter.emit(ReplayerEvents.Resize, {\n          width,\n          height,\n        });\n      }, 0);\n    }\n    if (firstFullsnapshot) {\n      setTimeout(() => {\n        // when something has been played, there is no need to rebuild poster\n        if (this.firstFullSnapshot) {\n          // true if any other fullSnapshot has been executed by Timer already\n          return;\n        }\n        this.firstFullSnapshot = firstFullsnapshot;\n        this.rebuildFullSnapshot(\n          firstFullsnapshot as fullSnapshotEvent & { timestamp: number },\n        );\n        this.iframe.contentWindow?.scrollTo(\n          (firstFullsnapshot as fullSnapshotEvent).data.initialOffset,\n        );\n      }, 1);\n    }\n    if (this.service.state.context.events.find(indicatesTouchDevice)) {\n      this.mouse.classList.add('touch-device');\n    }\n  }\n\n  public on(event: string, handler: Handler) {\n    this.emitter.on(event, handler);\n    return this;\n  }\n\n  public off(event: string, handler: Handler) {\n    this.emitter.off(event, handler);\n    return this;\n  }\n\n  public setConfig(config: Partial<playerConfig>) {\n    Object.keys(config).forEach((key) => {\n      const newConfigValue = config[key as keyof playerConfig];\n      (this.config as Record<keyof playerConfig, typeof newConfigValue>)[\n        key as keyof playerConfig\n      ] = config[key as keyof playerConfig];\n    });\n    if (!this.config.skipInactive) {\n      this.backToNormal();\n    }\n    if (typeof config.speed !== 'undefined') {\n      this.speedService.send({\n        type: 'SET_SPEED',\n        payload: {\n          speed: config.speed,\n        },\n      });\n    }\n    if (typeof config.mouseTail !== 'undefined') {\n      if (config.mouseTail === false) {\n        if (this.mouseTail) {\n          this.mouseTail.style.display = 'none';\n        }\n      } else {\n        if (!this.mouseTail) {\n          this.mouseTail = document.createElement('canvas');\n          this.mouseTail.width = Number.parseFloat(this.iframe.width);\n          this.mouseTail.height = Number.parseFloat(this.iframe.height);\n          this.mouseTail.classList.add('replayer-mouse-tail');\n          this.wrapper.insertBefore(this.mouseTail, this.iframe);\n        }\n        this.mouseTail.style.display = 'inherit';\n      }\n    }\n  }\n\n  public getMetaData(): playerMetaData {\n    const firstEvent = this.service.state.context.events[0];\n    const lastEvent = this.service.state.context.events[\n      this.service.state.context.events.length - 1\n    ];\n    return {\n      startTime: firstEvent.timestamp,\n      endTime: lastEvent.timestamp,\n      totalTime: lastEvent.timestamp - firstEvent.timestamp,\n    };\n  }\n\n  public getCurrentTime(): number {\n    return this.timer.timeOffset + this.getTimeOffset();\n  }\n\n  public getTimeOffset(): number {\n    const { baselineTime, events } = this.service.state.context;\n    return baselineTime - events[0].timestamp;\n  }\n\n  public getMirror(): Mirror {\n    return this.mirror;\n  }\n\n  /**\n   * This API was designed to be used as play at any time offset.\n   * Since we minimized the data collected from recorder, we do not\n   * have the ability of undo an event.\n   * So the implementation of play at any time offset will always iterate\n   * all of the events, cast event before the offset synchronously\n   * and cast event after the offset asynchronously with timer.\n   * @param timeOffset - number\n   */\n  public play(timeOffset = 0) {\n    if (this.service.state.matches('paused')) {\n      this.service.send({ type: 'PLAY', payload: { timeOffset } });\n    } else {\n      this.service.send({ type: 'PAUSE' });\n      this.service.send({ type: 'PLAY', payload: { timeOffset } });\n    }\n    this.iframe.contentDocument\n      ?.getElementsByTagName('html')[0]\n      ?.classList.remove('rrweb-paused');\n    this.emitter.emit(ReplayerEvents.Start);\n  }\n\n  public pause(timeOffset?: number) {\n    if (timeOffset === undefined && this.service.state.matches('playing')) {\n      this.service.send({ type: 'PAUSE' });\n    }\n    if (typeof timeOffset === 'number') {\n      this.play(timeOffset);\n      this.service.send({ type: 'PAUSE' });\n    }\n    this.iframe.contentDocument\n      ?.getElementsByTagName('html')[0]\n      ?.classList.add('rrweb-paused');\n    this.emitter.emit(ReplayerEvents.Pause);\n  }\n\n  public resume(timeOffset = 0) {\n    console.warn(\n      `The 'resume' was deprecated in 1.0. Please use 'play' method which has the same interface.`,\n    );\n    this.play(timeOffset);\n    this.emitter.emit(ReplayerEvents.Resume);\n  }\n\n  /**\n   * Totally destroy this replayer and please be careful that this operation is irreversible.\n   * Memory occupation can be released by removing all references to this replayer.\n   */\n  public destroy() {\n    this.pause();\n    this.config.root.removeChild(this.wrapper);\n    this.emitter.emit(ReplayerEvents.Destroy);\n  }\n\n  public startLive(baselineTime?: number) {\n    this.service.send({ type: 'TO_LIVE', payload: { baselineTime } });\n  }\n\n  public addEvent(rawEvent: eventWithTime | string) {\n    const event = this.config.unpackFn\n      ? this.config.unpackFn(rawEvent as string)\n      : (rawEvent as eventWithTime);\n    if (indicatesTouchDevice(event)) {\n      this.mouse.classList.add('touch-device');\n    }\n    void Promise.resolve().then(() =>\n      this.service.send({ type: 'ADD_EVENT', payload: { event } }),\n    );\n  }\n\n  public enableInteract() {\n    this.iframe.setAttribute('scrolling', 'auto');\n    this.iframe.style.pointerEvents = 'auto';\n  }\n\n  public disableInteract() {\n    this.iframe.setAttribute('scrolling', 'no');\n    this.iframe.style.pointerEvents = 'none';\n  }\n\n  /**\n   * Empties the replayer's cache and reclaims memory.\n   * The replayer will use this cache to speed up the playback.\n   */\n  public resetCache() {\n    this.cache = createCache();\n  }\n\n  private setupDom() {\n    this.wrapper = document.createElement('div');\n    this.wrapper.classList.add('replayer-wrapper');\n    this.config.root.appendChild(this.wrapper);\n\n    this.mouse = document.createElement('div');\n    this.mouse.classList.add('replayer-mouse');\n    this.wrapper.appendChild(this.mouse);\n\n    if (this.config.mouseTail !== false) {\n      this.mouseTail = document.createElement('canvas');\n      this.mouseTail.classList.add('replayer-mouse-tail');\n      this.mouseTail.style.display = 'inherit';\n      this.wrapper.appendChild(this.mouseTail);\n    }\n\n    this.iframe = document.createElement('iframe');\n    const attributes = ['allow-same-origin'];\n    if (this.config.UNSAFE_replayCanvas) {\n      attributes.push('allow-scripts');\n    }\n    // hide iframe before first meta event\n    this.iframe.style.display = 'none';\n    this.iframe.setAttribute('sandbox', attributes.join(' '));\n    this.disableInteract();\n    this.wrapper.appendChild(this.iframe);\n    if (this.iframe.contentWindow && this.iframe.contentDocument) {\n      smoothscrollPolyfill(\n        this.iframe.contentWindow,\n        this.iframe.contentDocument,\n      );\n\n      polyfill(this.iframe.contentWindow as IWindow);\n    }\n  }\n\n  private handleResize = (dimension: viewportResizeDimension) => {\n    this.iframe.style.display = 'inherit';\n    for (const el of [this.mouseTail, this.iframe]) {\n      if (!el) {\n        continue;\n      }\n      el.setAttribute('width', String(dimension.width));\n      el.setAttribute('height', String(dimension.height));\n    }\n  };\n\n  private applyEventsSynchronously = (events: Array<eventWithTime>) => {\n    for (const event of events) {\n      switch (event.type) {\n        case EventType.DomContentLoaded:\n        case EventType.Load:\n        case EventType.Custom:\n          continue;\n        case EventType.FullSnapshot:\n        case EventType.Meta:\n        case EventType.Plugin:\n        case EventType.IncrementalSnapshot:\n          break;\n        default:\n          break;\n      }\n      const castFn = this.getCastFn(event, true);\n      castFn();\n    }\n    if (this.touchActive === true) {\n      this.mouse.classList.add('touch-active');\n    } else if (this.touchActive === false) {\n      this.mouse.classList.remove('touch-active');\n    }\n    this.touchActive = null;\n  };\n\n  private getCastFn = (event: eventWithTime, isSync = false) => {\n    let castFn: undefined | (() => void);\n    switch (event.type) {\n      case EventType.DomContentLoaded:\n      case EventType.Load:\n        break;\n      case EventType.Custom:\n        castFn = () => {\n          /**\n           * emit custom-event and pass the event object.\n           *\n           * This will add more value to the custom event and allows the client to react for custom-event.\n           */\n          this.emitter.emit(ReplayerEvents.CustomEvent, event);\n        };\n        break;\n      case EventType.Meta:\n        castFn = () =>\n          this.emitter.emit(ReplayerEvents.Resize, {\n            width: event.data.width,\n            height: event.data.height,\n          });\n        break;\n      case EventType.FullSnapshot:\n        castFn = () => {\n          if (this.firstFullSnapshot) {\n            if (this.firstFullSnapshot === event) {\n              // we've already built this exact FullSnapshot when the player was mounted, and haven't built any other FullSnapshot since\n              this.firstFullSnapshot = true; // forget as we might need to re-execute this FullSnapshot later e.g. to rebuild after scrubbing\n              return;\n            }\n          } else {\n            // Timer (requestAnimationFrame) can be faster than setTimeout(..., 1)\n            this.firstFullSnapshot = true;\n          }\n          this.rebuildFullSnapshot(event, isSync);\n          this.iframe.contentWindow?.scrollTo(event.data.initialOffset);\n          this.styleMirror.reset();\n        };\n        break;\n      case EventType.IncrementalSnapshot:\n        castFn = () => {\n          this.applyIncremental(event, isSync);\n          if (isSync) {\n            // do not check skip in sync\n            return;\n          }\n          if (event === this.nextUserInteractionEvent) {\n            this.nextUserInteractionEvent = null;\n            this.backToNormal();\n          }\n          if (this.config.skipInactive && !this.nextUserInteractionEvent) {\n            for (const _event of this.service.state.context.events) {\n              if (_event.timestamp <= event.timestamp) {\n                continue;\n              }\n              if (this.isUserInteraction(_event)) {\n                if (\n                  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                  _event.delay! - event.delay! >\n                  SKIP_TIME_THRESHOLD *\n                    this.speedService.state.context.timer.speed\n                ) {\n                  this.nextUserInteractionEvent = _event;\n                }\n                break;\n              }\n            }\n            if (this.nextUserInteractionEvent) {\n              const skipTime =\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                this.nextUserInteractionEvent.delay! - event.delay!;\n              const payload = {\n                speed: Math.min(\n                  Math.round(skipTime / SKIP_TIME_INTERVAL),\n                  this.config.maxSpeed,\n                ),\n              };\n              this.speedService.send({ type: 'FAST_FORWARD', payload });\n              this.emitter.emit(ReplayerEvents.SkipStart, payload);\n            }\n          }\n        };\n        break;\n      default:\n    }\n    const wrappedCastFn = () => {\n      if (castFn) {\n        castFn();\n      }\n\n      for (const plugin of this.config.plugins || []) {\n        if (plugin.handler) plugin.handler(event, isSync, { replayer: this });\n      }\n\n      this.service.send({ type: 'CAST_EVENT', payload: { event } });\n\n      // events are kept sorted by timestamp, check if this is the last event\n      const last_index = this.service.state.context.events.length - 1;\n      if (event === this.service.state.context.events[last_index]) {\n        const finish = () => {\n          if (last_index < this.service.state.context.events.length - 1) {\n            // more events have been added since the setTimeout\n            return;\n          }\n          this.backToNormal();\n          this.service.send('END');\n          this.emitter.emit(ReplayerEvents.Finish);\n        };\n        if (\n          event.type === EventType.IncrementalSnapshot &&\n          event.data.source === IncrementalSource.MouseMove &&\n          event.data.positions.length\n        ) {\n          // defer finish event if the last event is a mouse move\n          setTimeout(() => {\n            finish();\n          }, Math.max(0, -event.data.positions[0].timeOffset + 50)); // Add 50 to make sure the timer would check the last mousemove event. Otherwise, the timer may be stopped by the service before checking the last event.\n        } else {\n          finish();\n        }\n      }\n\n      this.emitter.emit(ReplayerEvents.EventCast, event);\n    };\n    return wrappedCastFn;\n  };\n\n  private rebuildFullSnapshot(\n    event: fullSnapshotEvent & { timestamp: number },\n    isSync = false,\n  ) {\n    if (!this.iframe.contentDocument) {\n      return console.warn('Looks like your replayer has been destroyed.');\n    }\n    if (Object.keys(this.legacy_missingNodeRetryMap).length) {\n      console.warn(\n        'Found unresolved missing node map',\n        this.legacy_missingNodeRetryMap,\n      );\n    }\n    this.legacy_missingNodeRetryMap = {};\n    const collected: AppendedIframe[] = [];\n    const afterAppend = (builtNode: Node, id: number) => {\n      this.collectIframeAndAttachDocument(collected, builtNode);\n      for (const plugin of this.config.plugins || []) {\n        if (plugin.onBuild)\n          plugin.onBuild(builtNode, {\n            id,\n            replayer: this,\n          });\n      }\n    };\n\n    rebuild(event.data.node, {\n      doc: this.iframe.contentDocument,\n      afterAppend,\n      cache: this.cache,\n      mirror: this.mirror,\n    });\n    afterAppend(this.iframe.contentDocument, event.data.node.id);\n\n    for (const { mutationInQueue, builtNode } of collected) {\n      this.attachDocumentToIframe(mutationInQueue, builtNode);\n      this.newDocumentQueue = this.newDocumentQueue.filter(\n        (m) => m !== mutationInQueue,\n      );\n    }\n    const { documentElement, head } = this.iframe.contentDocument;\n    this.insertStyleRules(documentElement, head);\n    if (!this.service.state.matches('playing')) {\n      this.iframe.contentDocument\n        .getElementsByTagName('html')[0]\n        .classList.add('rrweb-paused');\n    }\n    this.emitter.emit(ReplayerEvents.FullsnapshotRebuilded, event);\n    if (!isSync) {\n      this.waitForStylesheetLoad();\n    }\n    if (this.config.UNSAFE_replayCanvas) {\n      void this.preloadAllImages();\n    }\n  }\n\n  private insertStyleRules(\n    documentElement: HTMLElement | RRElement,\n    head: HTMLHeadElement | RRElement,\n  ) {\n    const injectStylesRules = getInjectStyleRules(\n      this.config.blockClass,\n    ).concat(this.config.insertStyleRules);\n    if (this.config.pauseAnimation) {\n      injectStylesRules.push(\n        'html.rrweb-paused *, html.rrweb-paused *:before, html.rrweb-paused *:after { animation-play-state: paused !important; }',\n      );\n    }\n    if (this.usingVirtualDom) {\n      const styleEl = this.virtualDom.createElement('style');\n      this.virtualDom.mirror.add(\n        styleEl,\n        getDefaultSN(styleEl, this.virtualDom.unserializedId),\n      );\n      (documentElement as RRElement).insertBefore(styleEl, head as RRElement);\n      styleEl.rules.push({\n        source: IncrementalSource.StyleSheetRule,\n        adds: injectStylesRules.map((cssText, index) => ({\n          rule: cssText,\n          index,\n        })),\n      });\n    } else {\n      const styleEl = document.createElement('style');\n      (documentElement as HTMLElement).insertBefore(\n        styleEl,\n        head as HTMLHeadElement,\n      );\n      for (let idx = 0; idx < injectStylesRules.length; idx++) {\n        styleEl.sheet?.insertRule(injectStylesRules[idx], idx);\n      }\n    }\n  }\n\n  private attachDocumentToIframe(\n    mutation: addedNodeMutation,\n    iframeEl: HTMLIFrameElement | RRIFrameElement,\n  ) {\n    const mirror: RRDOMMirror | Mirror = this.usingVirtualDom\n      ? this.virtualDom.mirror\n      : this.mirror;\n    type TNode = typeof mirror extends Mirror ? Node : RRNode;\n    type TMirror = typeof mirror extends Mirror ? Mirror : RRDOMMirror;\n\n    const collected: AppendedIframe[] = [];\n    const afterAppend = (builtNode: Node, id: number) => {\n      this.collectIframeAndAttachDocument(collected, builtNode);\n      const sn = (mirror as TMirror).getMeta((builtNode as unknown) as TNode);\n      if (\n        sn?.type === NodeType.Element &&\n        sn?.tagName.toUpperCase() === 'HTML'\n      ) {\n        const { documentElement, head } = iframeEl.contentDocument!;\n        this.insertStyleRules(\n          documentElement as HTMLElement | RRElement,\n          head as HTMLElement | RRElement,\n        );\n      }\n\n      for (const plugin of this.config.plugins || []) {\n        if (plugin.onBuild)\n          plugin.onBuild(builtNode, {\n            id,\n            replayer: this,\n          });\n      }\n    };\n\n    buildNodeWithSN(mutation.node, {\n      doc: iframeEl.contentDocument! as Document,\n      mirror: mirror as Mirror,\n      hackCss: true,\n      skipChild: false,\n      afterAppend,\n      cache: this.cache,\n    });\n    afterAppend(iframeEl.contentDocument! as Document, mutation.node.id);\n\n    for (const { mutationInQueue, builtNode } of collected) {\n      this.attachDocumentToIframe(mutationInQueue, builtNode);\n      this.newDocumentQueue = this.newDocumentQueue.filter(\n        (m) => m !== mutationInQueue,\n      );\n    }\n  }\n\n  private collectIframeAndAttachDocument(\n    collected: AppendedIframe[],\n    builtNode: Node,\n  ) {\n    if (isSerializedIframe(builtNode, this.mirror)) {\n      const mutationInQueue = this.newDocumentQueue.find(\n        (m) => m.parentId === this.mirror.getId(builtNode),\n      );\n      if (mutationInQueue) {\n        collected.push({\n          mutationInQueue,\n          builtNode: builtNode as HTMLIFrameElement,\n        });\n      }\n    }\n  }\n\n  /**\n   * pause when loading style sheet, resume when loaded all timeout exceed\n   */\n  private waitForStylesheetLoad() {\n    const head = this.iframe.contentDocument?.head;\n    if (head) {\n      const unloadSheets: Set<HTMLLinkElement> = new Set();\n      let timer: ReturnType<typeof setTimeout> | -1;\n      let beforeLoadState = this.service.state;\n      const stateHandler = () => {\n        beforeLoadState = this.service.state;\n      };\n      this.emitter.on(ReplayerEvents.Start, stateHandler);\n      this.emitter.on(ReplayerEvents.Pause, stateHandler);\n      const unsubscribe = () => {\n        this.emitter.off(ReplayerEvents.Start, stateHandler);\n        this.emitter.off(ReplayerEvents.Pause, stateHandler);\n      };\n      head\n        .querySelectorAll('link[rel=\"stylesheet\"]')\n        .forEach((css: HTMLLinkElement) => {\n          if (!css.sheet) {\n            unloadSheets.add(css);\n            css.addEventListener('load', () => {\n              unloadSheets.delete(css);\n              // all loaded and timer not released yet\n              if (unloadSheets.size === 0 && timer !== -1) {\n                if (beforeLoadState.matches('playing')) {\n                  this.play(this.getCurrentTime());\n                }\n                this.emitter.emit(ReplayerEvents.LoadStylesheetEnd);\n                if (timer) {\n                  clearTimeout(timer);\n                }\n                unsubscribe();\n              }\n            });\n          }\n        });\n\n      if (unloadSheets.size > 0) {\n        // find some unload sheets after iterate\n        this.service.send({ type: 'PAUSE' });\n        this.emitter.emit(ReplayerEvents.LoadStylesheetStart);\n        timer = setTimeout(() => {\n          if (beforeLoadState.matches('playing')) {\n            this.play(this.getCurrentTime());\n          }\n          // mark timer was called\n          timer = -1;\n          unsubscribe();\n        }, this.config.loadTimeout);\n      }\n    }\n  }\n\n  /**\n   * pause when there are some canvas drawImage args need to be loaded\n   */\n  private async preloadAllImages(): Promise<void[]> {\n    let beforeLoadState = this.service.state;\n    const stateHandler = () => {\n      beforeLoadState = this.service.state;\n    };\n    this.emitter.on(ReplayerEvents.Start, stateHandler);\n    this.emitter.on(ReplayerEvents.Pause, stateHandler);\n    const promises: Promise<void>[] = [];\n    for (const event of this.service.state.context.events) {\n      if (\n        event.type === EventType.IncrementalSnapshot &&\n        event.data.source === IncrementalSource.CanvasMutation\n      ) {\n        promises.push(\n          this.deserializeAndPreloadCanvasEvents(event.data, event),\n        );\n        const commands =\n          'commands' in event.data ? event.data.commands : [event.data];\n        commands.forEach((c) => {\n          this.preloadImages(c, event);\n        });\n      }\n    }\n    return Promise.all(promises);\n  }\n\n  private preloadImages(data: canvasMutationCommand, event: eventWithTime) {\n    if (\n      data.property === 'drawImage' &&\n      typeof data.args[0] === 'string' &&\n      !this.imageMap.has(event)\n    ) {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const imgd = ctx?.createImageData(canvas.width, canvas.height);\n      let d = imgd?.data;\n      d = JSON.parse(data.args[0]) as Uint8ClampedArray;\n      ctx?.putImageData(imgd!, 0, 0);\n    }\n  }\n  private async deserializeAndPreloadCanvasEvents(\n    data: canvasMutationData,\n    event: eventWithTime,\n  ) {\n    if (!this.canvasEventMap.has(event)) {\n      const status = {\n        isUnchanged: true,\n      };\n      if ('commands' in data) {\n        const commands = await Promise.all(\n          data.commands.map(async (c) => {\n            const args = await Promise.all(\n              c.args.map(deserializeArg(this.imageMap, null, status)),\n            );\n            return { ...c, args };\n          }),\n        );\n        if (status.isUnchanged === false)\n          this.canvasEventMap.set(event, { ...data, commands });\n      } else {\n        const args = await Promise.all(\n          data.args.map(deserializeArg(this.imageMap, null, status)),\n        );\n        if (status.isUnchanged === false)\n          this.canvasEventMap.set(event, { ...data, args });\n      }\n    }\n  }\n\n  private applyIncremental(\n    e: incrementalSnapshotEvent & { timestamp: number; delay?: number },\n    isSync: boolean,\n  ) {\n    const { data: d } = e;\n    switch (d.source) {\n      case IncrementalSource.Mutation: {\n        try {\n          this.applyMutation(d, isSync);\n        } catch (error) {\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/restrict-template-expressions\n          this.warn(`Exception in mutation ${error.message || error}`, d);\n        }\n        break;\n      }\n      case IncrementalSource.Drag:\n      case IncrementalSource.TouchMove:\n      case IncrementalSource.MouseMove:\n        if (isSync) {\n          const lastPosition = d.positions[d.positions.length - 1];\n          this.mousePos = {\n            x: lastPosition.x,\n            y: lastPosition.y,\n            id: lastPosition.id,\n            debugData: d,\n          };\n        } else {\n          d.positions.forEach((p) => {\n            const action = {\n              doAction: () => {\n                this.moveAndHover(p.x, p.y, p.id, isSync, d);\n              },\n              delay:\n                p.timeOffset +\n                e.timestamp -\n                this.service.state.context.baselineTime,\n            };\n            this.timer.addAction(action);\n          });\n          // add a dummy action to keep timer alive\n          this.timer.addAction({\n            doAction() {\n              //\n            },\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            delay: e.delay! - d.positions[0]?.timeOffset,\n          });\n        }\n        break;\n      case IncrementalSource.MouseInteraction: {\n        /**\n         * Same as the situation of missing input target.\n         */\n        if (d.id === -1 || isSync) {\n          break;\n        }\n        const event = new Event(MouseInteractions[d.type].toLowerCase());\n        const target = this.mirror.getNode(d.id);\n        if (!target) {\n          return this.debugNodeNotFound(d, d.id);\n        }\n        this.emitter.emit(ReplayerEvents.MouseInteraction, {\n          type: d.type,\n          target,\n        });\n        const { triggerFocus } = this.config;\n        switch (d.type) {\n          case MouseInteractions.Blur:\n            if ('blur' in (target as HTMLElement)) {\n              (target as HTMLElement).blur();\n            }\n            break;\n          case MouseInteractions.Focus:\n            if (triggerFocus && (target as HTMLElement).focus) {\n              (target as HTMLElement).focus({\n                preventScroll: true,\n              });\n            }\n            break;\n          case MouseInteractions.Click:\n          case MouseInteractions.TouchStart:\n          case MouseInteractions.TouchEnd:\n            if (isSync) {\n              if (d.type === MouseInteractions.TouchStart) {\n                this.touchActive = true;\n              } else if (d.type === MouseInteractions.TouchEnd) {\n                this.touchActive = false;\n              }\n              this.mousePos = {\n                x: d.x,\n                y: d.y,\n                id: d.id,\n                debugData: d,\n              };\n            } else {\n              if (d.type === MouseInteractions.TouchStart) {\n                // don't draw a trail as user has lifted finger and is placing at a new point\n                this.tailPositions.length = 0;\n              }\n              this.moveAndHover(d.x, d.y, d.id, isSync, d);\n              if (d.type === MouseInteractions.Click) {\n                /*\n                 * don't want target.click() here as could trigger an iframe navigation\n                 * instead any effects of the click should already be covered by mutations\n                 */\n                /*\n                 * removal and addition of .active class (along with void line to trigger repaint)\n                 * triggers the 'click' css animation in styles/style.css\n                 */\n                this.mouse.classList.remove('active');\n                void this.mouse.offsetWidth;\n                this.mouse.classList.add('active');\n              } else if (d.type === MouseInteractions.TouchStart) {\n                void this.mouse.offsetWidth; // needed for the position update of moveAndHover to apply without the .touch-active transition\n                this.mouse.classList.add('touch-active');\n              } else if (d.type === MouseInteractions.TouchEnd) {\n                this.mouse.classList.remove('touch-active');\n              }\n            }\n            break;\n          case MouseInteractions.TouchCancel:\n            if (isSync) {\n              this.touchActive = false;\n            } else {\n              this.mouse.classList.remove('touch-active');\n            }\n            break;\n          default:\n            target.dispatchEvent(event);\n        }\n        break;\n      }\n      case IncrementalSource.Scroll: {\n        /**\n         * Same as the situation of missing input target.\n         */\n        if (d.id === -1) {\n          break;\n        }\n        if (this.usingVirtualDom) {\n          const target = this.virtualDom.mirror.getNode(d.id) as RRElement;\n          if (!target) {\n            return this.debugNodeNotFound(d, d.id);\n          }\n          target.scrollData = d;\n          break;\n        }\n        // Use isSync rather than this.usingVirtualDom because not every fast-forward process uses virtual dom optimization.\n        this.applyScroll(d, isSync);\n        break;\n      }\n      case IncrementalSource.ViewportResize:\n        this.emitter.emit(ReplayerEvents.Resize, {\n          width: d.width,\n          height: d.height,\n        });\n        break;\n      case IncrementalSource.Input: {\n        /**\n         * Input event on an unserialized node usually means the event\n         * was synchrony triggered programmatically after the node was\n         * created. This means there was not an user observable interaction\n         * and we do not need to replay it.\n         */\n        if (d.id === -1) {\n          break;\n        }\n        if (this.usingVirtualDom) {\n          const target = this.virtualDom.mirror.getNode(d.id) as RRElement;\n          if (!target) {\n            return this.debugNodeNotFound(d, d.id);\n          }\n          target.inputData = d;\n          break;\n        }\n        this.applyInput(d);\n        break;\n      }\n      case IncrementalSource.MediaInteraction: {\n        const target = this.usingVirtualDom\n          ? this.virtualDom.mirror.getNode(d.id)\n          : this.mirror.getNode(d.id);\n        if (!target) {\n          return this.debugNodeNotFound(d, d.id);\n        }\n        const mediaEl = target as HTMLMediaElement | RRMediaElement;\n        try {\n          if (d.currentTime) {\n            mediaEl.currentTime = d.currentTime;\n          }\n          if (d.volume) {\n            mediaEl.volume = d.volume;\n          }\n          if (d.muted) {\n            mediaEl.muted = d.muted;\n          }\n          if (d.type === MediaInteractions.Pause) {\n            mediaEl.pause();\n          }\n          if (d.type === MediaInteractions.Play) {\n            // remove listener for 'canplay' event because play() is async and returns a promise\n            // i.e. media will evntualy start to play when data is loaded\n            // 'canplay' event fires even when currentTime attribute changes which may lead to\n            // unexpeted behavior\n            void mediaEl.play();\n          }\n          if (d.type === MediaInteractions.RateChange) {\n            mediaEl.playbackRate = d.playbackRate;\n          }\n        } catch (error) {\n          if (this.config.showWarning) {\n            console.warn(\n              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/restrict-template-expressions\n              `Failed to replay media interactions: ${error.message || error}`,\n            );\n          }\n        }\n        break;\n      }\n      case IncrementalSource.StyleSheetRule:\n      case IncrementalSource.StyleDeclaration: {\n        if (this.usingVirtualDom) {\n          if (d.styleId) this.constructedStyleMutations.push(d);\n          else if (d.id)\n            (this.virtualDom.mirror.getNode(\n              d.id,\n            ) as RRStyleElement | null)?.rules.push(d);\n        } else this.applyStyleSheetMutation(d);\n        break;\n      }\n      case IncrementalSource.CanvasMutation: {\n        if (!this.config.UNSAFE_replayCanvas) {\n          return;\n        }\n        if (this.usingVirtualDom) {\n          const target = this.virtualDom.mirror.getNode(\n            d.id,\n          ) as RRCanvasElement;\n          if (!target) {\n            return this.debugNodeNotFound(d, d.id);\n          }\n          target.canvasMutations.push({\n            event: e as canvasEventWithTime,\n            mutation: d,\n          });\n        } else {\n          const target = this.mirror.getNode(d.id);\n          if (!target) {\n            return this.debugNodeNotFound(d, d.id);\n          }\n          void canvasMutation({\n            event: e,\n            mutation: d,\n            target: target as HTMLCanvasElement,\n            imageMap: this.imageMap,\n            canvasEventMap: this.canvasEventMap,\n            errorHandler: this.warnCanvasMutationFailed.bind(this),\n          });\n        }\n        break;\n      }\n      case IncrementalSource.Font: {\n        try {\n          const fontFace = new FontFace(\n            d.family,\n            d.buffer\n              ? new Uint8Array(JSON.parse(d.fontSource) as Iterable<number>)\n              : d.fontSource,\n            d.descriptors,\n          );\n          this.iframe.contentDocument?.fonts.add(fontFace);\n        } catch (error) {\n          if (this.config.showWarning) {\n            console.warn(error);\n          }\n        }\n        break;\n      }\n      case IncrementalSource.Selection: {\n        if (isSync) {\n          this.lastSelectionData = d;\n          break;\n        }\n        this.applySelection(d);\n        break;\n      }\n      case IncrementalSource.AdoptedStyleSheet: {\n        if (this.usingVirtualDom) this.adoptedStyleSheets.push(d);\n        else this.applyAdoptedStyleSheet(d);\n        break;\n      }\n      default:\n    }\n  }\n\n  private applyMutation(d: mutationData, isSync: boolean) {\n    // Only apply virtual dom optimization if the fast-forward process has node mutation. Because the cost of creating a virtual dom tree and executing the diff algorithm is usually higher than directly applying other kind of events.\n    if (this.config.useVirtualDom && !this.usingVirtualDom && isSync) {\n      this.usingVirtualDom = true;\n      buildFromDom(this.iframe.contentDocument!, this.mirror, this.virtualDom);\n      // If these legacy missing nodes haven't been resolved, they should be converted to virtual nodes.\n      if (Object.keys(this.legacy_missingNodeRetryMap).length) {\n        for (const key in this.legacy_missingNodeRetryMap) {\n          try {\n            const value = this.legacy_missingNodeRetryMap[key];\n            const virtualNode = buildFromNode(\n              value.node as Node,\n              this.virtualDom,\n              this.mirror,\n            );\n            if (virtualNode) value.node = virtualNode;\n          } catch (error) {\n            if (this.config.showWarning) {\n              console.warn(error);\n            }\n          }\n        }\n      }\n    }\n    const mirror = this.usingVirtualDom ? this.virtualDom.mirror : this.mirror;\n    type TNode = typeof mirror extends Mirror ? Node : RRNode;\n\n    d.removes.forEach((mutation) => {\n      const target = mirror.getNode(mutation.id);\n      if (!target) {\n        if (d.removes.find((r) => r.id === mutation.parentId)) {\n          // no need to warn, parent was already removed\n          return;\n        }\n        return this.warnNodeNotFound(d, mutation.id);\n      }\n      let parent: Node | null | ShadowRoot | RRNode = mirror.getNode(\n        mutation.parentId,\n      );\n      if (!parent) {\n        return this.warnNodeNotFound(d, mutation.parentId);\n      }\n      if (mutation.isShadow && hasShadowRoot(parent as Node)) {\n        parent = (parent as Element | RRElement).shadowRoot;\n      }\n      // target may be removed with its parents before\n      mirror.removeNodeFromMap(target as Node & RRNode);\n      if (parent)\n        try {\n          parent.removeChild(target as Node & RRNode);\n          /**\n           * https://github.com/rrweb-io/rrweb/pull/887\n           * Remove any virtual style rules for stylesheets if a child text node is removed.\n           */\n          if (\n            this.usingVirtualDom &&\n            target.nodeName === '#text' &&\n            parent.nodeName === 'STYLE' &&\n            (parent as RRStyleElement).rules?.length > 0\n          )\n            (parent as RRStyleElement).rules = [];\n        } catch (error) {\n          if (error instanceof DOMException) {\n            this.warn(\n              'parent could not remove child in mutation',\n              parent,\n              target,\n              d,\n            );\n          } else {\n            throw error;\n          }\n        }\n    });\n\n    const legacy_missingNodeMap: missingNodeMap = {\n      ...this.legacy_missingNodeRetryMap,\n    };\n    const queue: addedNodeMutation[] = [];\n\n    // next not present at this moment\n    const nextNotInDOM = (mutation: addedNodeMutation) => {\n      let next: TNode | null = null;\n      if (mutation.nextId) {\n        next = mirror.getNode(mutation.nextId) as TNode | null;\n      }\n      // next not present at this moment\n      if (\n        mutation.nextId !== null &&\n        mutation.nextId !== undefined &&\n        mutation.nextId !== -1 &&\n        !next\n      ) {\n        return true;\n      }\n      return false;\n    };\n\n    const appendNode = (mutation: addedNodeMutation) => {\n      if (!this.iframe.contentDocument) {\n        return console.warn('Looks like your replayer has been destroyed.');\n      }\n      let parent: Node | null | ShadowRoot | RRNode = mirror.getNode(\n        mutation.parentId,\n      );\n      if (!parent) {\n        if (mutation.node.type === NodeType.Document) {\n          // is newly added document, maybe the document node of an iframe\n          return this.newDocumentQueue.push(mutation);\n        }\n        return queue.push(mutation);\n      }\n\n      if (mutation.node.isShadow) {\n        // If the parent is attached a shadow dom after it's created, it won't have a shadow root.\n        if (!hasShadowRoot(parent)) {\n          (parent as Element | RRElement).attachShadow({ mode: 'open' });\n          parent = (parent as Element | RRElement).shadowRoot! as Node | RRNode;\n        } else parent = parent.shadowRoot as Node | RRNode;\n      }\n\n      let previous: Node | RRNode | null = null;\n      let next: Node | RRNode | null = null;\n      if (mutation.previousId) {\n        previous = mirror.getNode(mutation.previousId);\n      }\n      if (mutation.nextId) {\n        next = mirror.getNode(mutation.nextId);\n      }\n      if (nextNotInDOM(mutation)) {\n        return queue.push(mutation);\n      }\n\n      if (mutation.node.rootId && !mirror.getNode(mutation.node.rootId)) {\n        return;\n      }\n\n      const targetDoc = mutation.node.rootId\n        ? mirror.getNode(mutation.node.rootId)\n        : this.usingVirtualDom\n        ? this.virtualDom\n        : this.iframe.contentDocument;\n      if (isSerializedIframe<typeof parent>(parent, mirror)) {\n        this.attachDocumentToIframe(\n          mutation,\n          parent as HTMLIFrameElement | RRIFrameElement,\n        );\n        return;\n      }\n      const afterAppend = (node: Node | RRNode, id: number) => {\n        for (const plugin of this.config.plugins || []) {\n          if (plugin.onBuild) plugin.onBuild(node, { id, replayer: this });\n        }\n      };\n\n      const target = buildNodeWithSN(mutation.node, {\n        doc: targetDoc as Document, // can be Document or RRDocument\n        mirror: mirror as Mirror, // can be this.mirror or virtualDom.mirror\n        skipChild: true,\n        hackCss: true,\n        cache: this.cache,\n        /**\n         * caveat: `afterAppend` only gets called on child nodes of target\n         * we have to call it again below when this target was added to the DOM\n         */\n        afterAppend,\n      }) as Node | RRNode;\n\n      // legacy data, we should not have -1 siblings any more\n      if (mutation.previousId === -1 || mutation.nextId === -1) {\n        legacy_missingNodeMap[mutation.node.id] = {\n          node: target,\n          mutation,\n        };\n        return;\n      }\n\n      // Typescripts type system is not smart enough\n      // to understand what is going on with the types below\n      type TNode = typeof mirror extends Mirror ? Node : RRNode;\n      type TMirror = typeof mirror extends Mirror ? Mirror : RRDOMMirror;\n\n      const parentSn = (mirror as TMirror).getMeta(parent as TNode);\n      if (\n        parentSn &&\n        parentSn.type === NodeType.Element &&\n        parentSn.tagName === 'textarea' &&\n        mutation.node.type === NodeType.Text\n      ) {\n        const childNodeArray = Array.isArray(parent.childNodes)\n          ? parent.childNodes\n          : Array.from(parent.childNodes);\n\n        // https://github.com/rrweb-io/rrweb/issues/745\n        // parent is textarea, will only keep one child node as the value\n        for (const c of childNodeArray) {\n          if (c.nodeType === parent.TEXT_NODE) {\n            parent.removeChild(c as Node & RRNode);\n          }\n        }\n      }\n\n      if (previous && previous.nextSibling && previous.nextSibling.parentNode) {\n        (parent as TNode).insertBefore(\n          target as TNode,\n          previous.nextSibling as TNode,\n        );\n      } else if (next && next.parentNode) {\n        // making sure the parent contains the reference nodes\n        // before we insert target before next.\n        (parent as TNode).contains(next as TNode)\n          ? (parent as TNode).insertBefore(target as TNode, next as TNode)\n          : (parent as TNode).insertBefore(target as TNode, null);\n      } else {\n        /**\n         * Sometimes the document changes and the MutationObserver is disconnected, so the removal of child elements can't be detected and recorded. After the change of document, we may get another mutation which adds a new html element, while the old html element still exists in the dom, and we need to remove the old html element first to avoid collision.\n         */\n        if (parent === targetDoc) {\n          while (targetDoc.firstChild) {\n            (targetDoc as TNode).removeChild(targetDoc.firstChild as TNode);\n          }\n        }\n\n        (parent as TNode).appendChild(target as TNode);\n      }\n      /**\n       * target was added, execute plugin hooks\n       */\n      afterAppend(target, mutation.node.id);\n\n      /**\n       * https://github.com/rrweb-io/rrweb/pull/887\n       * Remove any virtual style rules for stylesheets if a new text node is appended.\n       */\n      if (\n        this.usingVirtualDom &&\n        target.nodeName === '#text' &&\n        parent.nodeName === 'STYLE' &&\n        (parent as RRStyleElement).rules?.length > 0\n      )\n        (parent as RRStyleElement).rules = [];\n\n      if (isSerializedIframe(target, this.mirror)) {\n        const targetId = this.mirror.getId(target as HTMLIFrameElement);\n        const mutationInQueue = this.newDocumentQueue.find(\n          (m) => m.parentId === targetId,\n        );\n        if (mutationInQueue) {\n          this.attachDocumentToIframe(\n            mutationInQueue,\n            target as HTMLIFrameElement,\n          );\n          this.newDocumentQueue = this.newDocumentQueue.filter(\n            (m) => m !== mutationInQueue,\n          );\n        }\n      }\n\n      if (mutation.previousId || mutation.nextId) {\n        this.legacy_resolveMissingNode(\n          legacy_missingNodeMap,\n          parent,\n          target,\n          mutation,\n        );\n      }\n    };\n\n    d.adds.forEach((mutation) => {\n      appendNode(mutation);\n    });\n\n    const startTime = Date.now();\n    while (queue.length) {\n      // transform queue to resolve tree\n      const resolveTrees = queueToResolveTrees(queue);\n      queue.length = 0;\n      if (Date.now() - startTime > 500) {\n        this.warn(\n          'Timeout in the loop, please check the resolve tree data:',\n          resolveTrees,\n        );\n        break;\n      }\n      for (const tree of resolveTrees) {\n        const parent = mirror.getNode(tree.value.parentId);\n        if (!parent) {\n          this.debug(\n            'Drop resolve tree since there is no parent for the root node.',\n            tree,\n          );\n        } else {\n          iterateResolveTree(tree, (mutation) => {\n            appendNode(mutation);\n          });\n        }\n      }\n    }\n\n    if (Object.keys(legacy_missingNodeMap).length) {\n      Object.assign(this.legacy_missingNodeRetryMap, legacy_missingNodeMap);\n    }\n\n    uniqueTextMutations(d.texts).forEach((mutation) => {\n      const target = mirror.getNode(mutation.id);\n      if (!target) {\n        if (d.removes.find((r) => r.id === mutation.id)) {\n          // no need to warn, element was already removed\n          return;\n        }\n        return this.warnNodeNotFound(d, mutation.id);\n      }\n      target.textContent = mutation.value;\n\n      /**\n       * https://github.com/rrweb-io/rrweb/pull/865\n       * Remove any virtual style rules for stylesheets whose contents are replaced.\n       */\n      if (this.usingVirtualDom) {\n        const parent = target.parentNode as RRStyleElement;\n        if (parent?.rules?.length > 0) parent.rules = [];\n      }\n    });\n    d.attributes.forEach((mutation) => {\n      const target = mirror.getNode(mutation.id);\n      if (!target) {\n        if (d.removes.find((r) => r.id === mutation.id)) {\n          // no need to warn, element was already removed\n          return;\n        }\n        return this.warnNodeNotFound(d, mutation.id);\n      }\n      for (const attributeName in mutation.attributes) {\n        if (typeof attributeName === 'string') {\n          const value = mutation.attributes[attributeName];\n          if (value === null) {\n            (target as Element | RRElement).removeAttribute(attributeName);\n          } else if (typeof value === 'string') {\n            try {\n              // When building snapshot, some link styles haven't loaded. Then they are loaded, they will be inlined as incremental mutation change of attribute. We need to replace the old elements whose styles aren't inlined.\n              if (\n                attributeName === '_cssText' &&\n                (target.nodeName === 'LINK' || target.nodeName === 'STYLE')\n              ) {\n                try {\n                  const newSn = mirror.getMeta(\n                    target as Node & RRNode,\n                  ) as serializedElementNodeWithId;\n                  Object.assign(\n                    newSn.attributes,\n                    mutation.attributes as attributes,\n                  );\n                  const newNode = buildNodeWithSN(newSn, {\n                    doc: target.ownerDocument as Document, // can be Document or RRDocument\n                    mirror: mirror as Mirror,\n                    skipChild: true,\n                    hackCss: true,\n                    cache: this.cache,\n                  });\n                  const siblingNode = target.nextSibling;\n                  const parentNode = target.parentNode;\n                  if (newNode && parentNode) {\n                    parentNode.removeChild(target as Node & RRNode);\n                    parentNode.insertBefore(\n                      newNode as Node & RRNode,\n                      siblingNode as (Node & RRNode) | null,\n                    );\n                    mirror.replace(mutation.id, newNode as Node & RRNode);\n                    break;\n                  }\n                } catch (e) {\n                  // for safe\n                }\n              }\n              (target as Element | RRElement).setAttribute(\n                attributeName,\n                value,\n              );\n            } catch (error) {\n              if (this.config.showWarning) {\n                console.warn(\n                  'An error occurred may due to the checkout feature.',\n                  error,\n                );\n              }\n            }\n          } else if (attributeName === 'style') {\n            const styleValues = value;\n            const targetEl = target as HTMLElement | RRElement;\n            for (const s in styleValues) {\n              if (styleValues[s] === false) {\n                targetEl.style.removeProperty(s);\n              } else if (styleValues[s] instanceof Array) {\n                const svp = styleValues[s] as styleValueWithPriority;\n                targetEl.style.setProperty(s, svp[0], svp[1]);\n              } else {\n                const svs = styleValues[s] as string;\n                targetEl.style.setProperty(s, svs);\n              }\n            }\n          }\n        }\n      }\n    });\n  }\n\n  /**\n   * Apply the scroll data on real elements.\n   * If the replayer is in sync mode, smooth scroll behavior should be disabled.\n   * @param d - the scroll data\n   * @param isSync - whether the replayer is in sync mode(fast-forward)\n   */\n  private applyScroll(d: scrollData, isSync: boolean) {\n    const target = this.mirror.getNode(d.id);\n    if (!target) {\n      return this.debugNodeNotFound(d, d.id);\n    }\n    const sn = this.mirror.getMeta(target);\n    if (target === this.iframe.contentDocument) {\n      this.iframe.contentWindow?.scrollTo({\n        top: d.y,\n        left: d.x,\n        behavior: isSync ? 'auto' : 'smooth',\n      });\n    } else if (sn?.type === NodeType.Document) {\n      // nest iframe content document\n      (target as Document).defaultView?.scrollTo({\n        top: d.y,\n        left: d.x,\n        behavior: isSync ? 'auto' : 'smooth',\n      });\n    } else {\n      try {\n        (target as Element).scrollTo({\n          top: d.y,\n          left: d.x,\n          behavior: isSync ? 'auto' : 'smooth',\n        });\n      } catch (error) {\n        /**\n         * Seldomly we may found scroll target was removed before\n         * its last scroll event.\n         */\n      }\n    }\n  }\n\n  private applyInput(d: inputData) {\n    const target = this.mirror.getNode(d.id);\n    if (!target) {\n      return this.debugNodeNotFound(d, d.id);\n    }\n    try {\n      (target as HTMLInputElement).checked = d.isChecked;\n      (target as HTMLInputElement).value = d.text;\n    } catch (error) {\n      // for safe\n    }\n  }\n\n  private applySelection(d: selectionData) {\n    try {\n      const selectionSet = new Set<Selection>();\n      const ranges = d.ranges.map(({ start, startOffset, end, endOffset }) => {\n        const startContainer = this.mirror.getNode(start);\n        const endContainer = this.mirror.getNode(end);\n\n        if (!startContainer || !endContainer) return;\n\n        const result = new Range();\n\n        result.setStart(startContainer, startOffset);\n        result.setEnd(endContainer, endOffset);\n        const doc = startContainer.ownerDocument;\n        const selection = doc?.getSelection();\n        selection && selectionSet.add(selection);\n\n        return {\n          range: result,\n          selection,\n        };\n      });\n\n      selectionSet.forEach((s) => s.removeAllRanges());\n\n      ranges.forEach((r) => r && r.selection?.addRange(r.range));\n    } catch (error) {\n      // for safe\n    }\n  }\n\n  private applyStyleSheetMutation(\n    data: styleDeclarationData | styleSheetRuleData,\n  ) {\n    let styleSheet: CSSStyleSheet | null = null;\n    if (data.styleId) styleSheet = this.styleMirror.getStyle(data.styleId);\n    else if (data.id)\n      styleSheet =\n        (this.mirror.getNode(data.id) as HTMLStyleElement)?.sheet || null;\n    if (!styleSheet) return;\n    if (data.source === IncrementalSource.StyleSheetRule)\n      this.applyStyleSheetRule(data, styleSheet);\n    else if (data.source === IncrementalSource.StyleDeclaration)\n      this.applyStyleDeclaration(data, styleSheet);\n  }\n\n  private applyStyleSheetRule(\n    data: styleSheetRuleData,\n    styleSheet: CSSStyleSheet,\n  ) {\n    data.adds?.forEach(({ rule, index: nestedIndex }) => {\n      try {\n        if (Array.isArray(nestedIndex)) {\n          const { positions, index } = getPositionsAndIndex(nestedIndex);\n          const nestedRule = getNestedRule(styleSheet.cssRules, positions);\n          nestedRule.insertRule(rule, index);\n        } else {\n          const index =\n            nestedIndex === undefined\n              ? undefined\n              : Math.min(nestedIndex, styleSheet.cssRules.length);\n          styleSheet?.insertRule(rule, index);\n        }\n      } catch (e) {\n        /**\n         * sometimes we may capture rules with browser prefix\n         * insert rule with prefixs in other browsers may cause Error\n         */\n        /**\n         * accessing styleSheet rules may cause SecurityError\n         * for specific access control settings\n         */\n      }\n    });\n\n    data.removes?.forEach(({ index: nestedIndex }) => {\n      try {\n        if (Array.isArray(nestedIndex)) {\n          const { positions, index } = getPositionsAndIndex(nestedIndex);\n          const nestedRule = getNestedRule(styleSheet.cssRules, positions);\n          nestedRule.deleteRule(index || 0);\n        } else {\n          styleSheet?.deleteRule(nestedIndex);\n        }\n      } catch (e) {\n        /**\n         * same as insertRule\n         */\n      }\n    });\n\n    if (data.replace)\n      try {\n        void styleSheet.replace?.(data.replace);\n      } catch (e) {\n        // for safety\n      }\n\n    if (data.replaceSync)\n      try {\n        styleSheet.replaceSync?.(data.replaceSync);\n      } catch (e) {\n        // for safety\n      }\n  }\n\n  private applyStyleDeclaration(\n    data: styleDeclarationData,\n    styleSheet: CSSStyleSheet,\n  ) {\n    if (data.set) {\n      const rule = (getNestedRule(\n        styleSheet.rules,\n        data.index,\n      ) as unknown) as CSSStyleRule;\n      rule.style.setProperty(\n        data.set.property,\n        data.set.value,\n        data.set.priority,\n      );\n    }\n\n    if (data.remove) {\n      const rule = (getNestedRule(\n        styleSheet.rules,\n        data.index,\n      ) as unknown) as CSSStyleRule;\n      rule.style.removeProperty(data.remove.property);\n    }\n  }\n\n  private applyAdoptedStyleSheet(data: adoptedStyleSheetData) {\n    const targetHost = this.mirror.getNode(data.id);\n    if (!targetHost) return;\n    // Create StyleSheet objects which will be adopted after.\n    data.styles?.forEach((style) => {\n      let newStyleSheet: CSSStyleSheet | null = null;\n      /**\n       * Constructed StyleSheet can't share across multiple documents.\n       * The replayer has to get the correct host window to recreate a StyleSheetObject.\n       */\n      let hostWindow: IWindow | null = null;\n      if (hasShadowRoot(targetHost))\n        hostWindow = targetHost.ownerDocument?.defaultView || null;\n      else if (targetHost.nodeName === '#document')\n        hostWindow = (targetHost as Document).defaultView;\n\n      if (!hostWindow) return;\n      try {\n        newStyleSheet = new hostWindow.CSSStyleSheet();\n        this.styleMirror.add(newStyleSheet, style.styleId);\n        // To reuse the code of applying stylesheet rules\n        this.applyStyleSheetRule(\n          {\n            source: IncrementalSource.StyleSheetRule,\n            adds: style.rules,\n          },\n          newStyleSheet,\n        );\n      } catch (e) {\n        // In case some browsers don't support constructing StyleSheet.\n      }\n    });\n\n    const MAX_RETRY_TIME = 10;\n    let count = 0;\n    const adoptStyleSheets = (targetHost: Node, styleIds: number[]) => {\n      const stylesToAdopt = styleIds\n        .map((styleId) => this.styleMirror.getStyle(styleId))\n        .filter((style) => style !== null) as CSSStyleSheet[];\n      if (hasShadowRoot(targetHost))\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        (targetHost as HTMLElement).shadowRoot!.adoptedStyleSheets = stylesToAdopt;\n      else if (targetHost.nodeName === '#document')\n        (targetHost as Document).adoptedStyleSheets = stylesToAdopt;\n\n      /**\n       * In the live mode where events are transferred over network without strict order guarantee, some newer events are applied before some old events and adopted stylesheets may haven't been created.\n       * This retry mechanism can help resolve this situation.\n       */\n      if (stylesToAdopt.length !== styleIds.length && count < MAX_RETRY_TIME) {\n        setTimeout(\n          () => adoptStyleSheets(targetHost, styleIds),\n          0 + 100 * count,\n        );\n        count++;\n      }\n    };\n    adoptStyleSheets(targetHost, data.styleIds);\n  }\n\n  private legacy_resolveMissingNode(\n    map: missingNodeMap,\n    parent: Node | RRNode,\n    target: Node | RRNode,\n    targetMutation: addedNodeMutation,\n  ) {\n    const { previousId, nextId } = targetMutation;\n    const previousInMap = previousId && map[previousId];\n    const nextInMap = nextId && map[nextId];\n    if (previousInMap) {\n      const { node, mutation } = previousInMap;\n      parent.insertBefore(node as Node & RRNode, target as Node & RRNode);\n      delete map[mutation.node.id];\n      delete this.legacy_missingNodeRetryMap[mutation.node.id];\n      if (mutation.previousId || mutation.nextId) {\n        this.legacy_resolveMissingNode(map, parent, node, mutation);\n      }\n    }\n    if (nextInMap) {\n      const { node, mutation } = nextInMap;\n      parent.insertBefore(\n        node as Node & RRNode,\n        target.nextSibling as Node & RRNode,\n      );\n      delete map[mutation.node.id];\n      delete this.legacy_missingNodeRetryMap[mutation.node.id];\n      if (mutation.previousId || mutation.nextId) {\n        this.legacy_resolveMissingNode(map, parent, node, mutation);\n      }\n    }\n  }\n\n  private moveAndHover(\n    x: number,\n    y: number,\n    id: number,\n    isSync: boolean,\n    debugData: incrementalData,\n  ) {\n    const target = this.mirror.getNode(id);\n    if (!target) {\n      return this.debugNodeNotFound(debugData, id);\n    }\n\n    const base = getBaseDimension(target, this.iframe);\n    const _x = x * base.absoluteScale + base.x;\n    const _y = y * base.absoluteScale + base.y;\n\n    this.mouse.style.left = `${_x}px`;\n    this.mouse.style.top = `${_y}px`;\n    if (!isSync) {\n      this.drawMouseTail({ x: _x, y: _y });\n    }\n    this.hoverElements(target as Element);\n  }\n\n  private drawMouseTail(position: { x: number; y: number }) {\n    if (!this.mouseTail) {\n      return;\n    }\n\n    const { lineCap, lineWidth, strokeStyle, duration } =\n      this.config.mouseTail === true\n        ? defaultMouseTailConfig\n        : Object.assign({}, defaultMouseTailConfig, this.config.mouseTail);\n\n    const draw = () => {\n      if (!this.mouseTail) {\n        return;\n      }\n      const ctx = this.mouseTail.getContext('2d');\n      if (!ctx || !this.tailPositions.length) {\n        return;\n      }\n      ctx.clearRect(0, 0, this.mouseTail.width, this.mouseTail.height);\n      ctx.beginPath();\n      ctx.lineWidth = lineWidth;\n      ctx.lineCap = lineCap;\n      ctx.strokeStyle = strokeStyle;\n      ctx.moveTo(this.tailPositions[0].x, this.tailPositions[0].y);\n      this.tailPositions.forEach((p) => ctx.lineTo(p.x, p.y));\n      ctx.stroke();\n    };\n\n    this.tailPositions.push(position);\n    draw();\n    setTimeout(() => {\n      this.tailPositions = this.tailPositions.filter((p) => p !== position);\n      draw();\n    }, duration / this.speedService.state.context.timer.speed);\n  }\n\n  private hoverElements(el: Element) {\n    this.iframe.contentDocument\n      ?.querySelectorAll('.\\\\:hover')\n      .forEach((hoveredEl) => {\n        hoveredEl.classList.remove(':hover');\n      });\n    let currentEl: Element | null = el;\n    while (currentEl) {\n      if (currentEl.classList) {\n        currentEl.classList.add(':hover');\n      }\n      currentEl = currentEl.parentElement;\n    }\n  }\n\n  private isUserInteraction(event: eventWithTime): boolean {\n    if (event.type !== EventType.IncrementalSnapshot) {\n      return false;\n    }\n    return (\n      event.data.source > IncrementalSource.Mutation &&\n      event.data.source <= IncrementalSource.Input\n    );\n  }\n\n  private backToNormal() {\n    this.nextUserInteractionEvent = null;\n    if (this.speedService.state.matches('normal')) {\n      return;\n    }\n    this.speedService.send({ type: 'BACK_TO_NORMAL' });\n    this.emitter.emit(ReplayerEvents.SkipEnd, {\n      speed: this.speedService.state.context.normalSpeed,\n    });\n  }\n\n  private warnNodeNotFound(d: incrementalData, id: number) {\n    this.warn(`Node with id '${id}' not found. `, d);\n  }\n\n  private warnCanvasMutationFailed(\n    d: canvasMutationData | canvasMutationCommand,\n    error: unknown,\n  ) {\n    this.warn(`Has error on canvas update`, error, 'canvas mutation:', d);\n  }\n\n  private debugNodeNotFound(d: incrementalData, id: number) {\n    /**\n     * There maybe some valid scenes of node not being found.\n     * Because DOM events are macrotask and MutationObserver callback\n     * is microtask, so events fired on a removed DOM may emit\n     * snapshots in the reverse order.\n     */\n    this.debug(REPLAY_CONSOLE_PREFIX, `Node with id '${id}' not found. `, d);\n  }\n\n  private warn(...args: Parameters<typeof console.warn>) {\n    if (!this.config.showWarning) {\n      return;\n    }\n    console.warn(REPLAY_CONSOLE_PREFIX, ...args);\n  }\n\n  private debug(...args: Parameters<typeof console.log>) {\n    if (!this.config.showDebug) {\n      return;\n    }\n    console.log(REPLAY_CONSOLE_PREFIX, ...args);\n  }\n}\n", "// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Much of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// Many optimizations have been made, so the bundle size is ultimately smaller but performance is similar.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = (function (c, id, msg, transfer, cb) {\n    var u = ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c], { type: 'text/javascript' })));\n    var w = new Worker(u);\n    w.onerror = function (e) { return cb(e.error, null); };\n    w.onmessage = function (e) { return cb(null, e.data); };\n    w.postMessage(msg, transfer);\n    return w;\n});\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i)\n            co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) >> 0;\n    return ((d[o] | (d[o + 1] << 8)) >>> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) >> 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >>> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) >> 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                if (!noSt && pos + tl * (clb + 7) > tbts)\n                    break;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts)\n                throw 'unexpected EOF';\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var mxa = lbt + dbt + 18;\n        while (noSt || pos + mxa < tbts) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts)\n                throw 'unexpected EOF';\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts)\n                    throw 'unexpected EOF';\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = pos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) >> 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) >> 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [new u8(0), 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.floor(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            var hv = hsh(i);\n            // index mod 32768\n            var imod = i & 32767;\n            // previous index with this value\n            var pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst)\n            pos = wfblk(w, pos, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new u32(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && 0xEDB88320) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = 0xFFFFFFFF;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return c ^ 0xFFFFFFFF; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 5552, l);\n                for (; i < e; ++i)\n                    n += d[i], m += n;\n                n %= 65521, m %= 65521;\n            }\n            a = n, b = m;\n        },\n        d: function () { return ((a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8)) + ((a & 255) << 23) * 2; }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    if (!opts.consume)\n        dat = new u8(dat);\n    w.postMessage([dat, opts], [dat.buffer]);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16)) + (d[b + 3] << 23) * 2; };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) + (2 * (d[l - 1] << 23));\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    return dopt(data, opts, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) >> 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = gzs(this.p);\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    var l = str.length;\n    if (!latin1 && typeof TextEncoder != 'undefined')\n        return new TextEncoder().encode(str);\n    var ar = new u8(str.length + (str.length >>> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >>> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >>> 18)), w(128 | ((c >>> 12) & 63)), w(128 | ((c >>> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >>> 12)), w(128 | ((c >>> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    var r = '';\n    if (!latin1 && typeof TextDecoder != 'undefined')\n        return new TextDecoder().decode(dat);\n    for (var i = 0; i < dat.length;) {\n        var c = dat[i++];\n        if (c < 128 || latin1)\n            r += String.fromCharCode(c);\n        else if (c < 224)\n            r += String.fromCharCode((c & 31) << 6 | (dat[i++] & 63));\n        else if (c < 240)\n            r += String.fromCharCode((c & 15) << 12 | (dat[i++] & 63) << 6 | (dat[i++] & 63));\n        else\n            c = ((c & 15) << 18 | (dat[i++] & 63) << 12 | (dat[i++] & 63) << 6 | (dat[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n    }\n    return r;\n}\n;\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl;\n    var _a = z ? z64e(d, es) : [b4(d, b + 20), b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b4(d, b + 12), b4(d, b + 4), b4(d, b + 20)];\n};\n// write zip header\nvar wzh = function (d, b, c, cmp, su, fn, u, o, ce, t) {\n    var fl = fn.length, l = cmp.length;\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b] = 20, b += 2;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (t == 8 && (o.level == 1 ? 6 : o.level < 6 ? 4 : o.level == 9 ? 2 : 0)), d[b++] = u && 8;\n    d[b] = t, b += 2;\n    var dt = new Date(o.mtime || Date.now()), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, ((y << 24) * 2) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1));\n    b += 4;\n    wbytes(d, b, c);\n    wbytes(d, b + 4, l);\n    wbytes(d, b + 8, su);\n    wbytes(d, b + 12, fl), b += 16; // skip extra field, comment\n    if (ce != null)\n        wbytes(d, b += 10, ce), b += 4;\n    d.set(fn, b);\n    b += fl;\n    if (ce == null)\n        d.set(cmp, b);\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                wzh(out, tot, f.c, f.d, f.m, f.n, f.u, f.p, null, f.t);\n                wzh(out, o, f.c, f.d, f.m, f.n, f.u, f.p, tot, f.t), o += 46 + f.n.length, tot += 30 + f.n.length + f.d.length;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), m = file.length;\n        c.p(file);\n        var n = strToU8(fn), s = n.length;\n        var t = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = {\n                    t: t,\n                    d: d,\n                    m: m,\n                    c: c.d(),\n                    u: fn.length != l,\n                    n: n,\n                    p: p\n                };\n                o += 30 + s + l;\n                tot += 76 + 2 * s + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (n.length > 65535)\n            cbl('filename too long', null);\n        if (!t)\n            cbl(null, file);\n        else if (m < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var t = p.level == 0 ? 0 : 8;\n        var n = strToU8(fn), s = n.length;\n        if (n.length > 65535)\n            throw 'filename too long';\n        var d = t ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push({\n            t: t,\n            d: d,\n            m: file.length,\n            c: c.d(),\n            u: fn.length != s,\n            n: n,\n            o: o,\n            p: p\n        });\n        o += 30 + s + l;\n        tot += 76 + 2 * s + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f.c, f.d, f.m, f.n, f.u, f.p, null, f.t);\n        wzh(out, o, f.c, f.d, f.m, f.n, f.u, f.p, f.o, f.t), o += 46 + f.n.length;\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_2 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_2(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n", "import type { eventWithTime } from '@rrweb/types';\n\nexport type PackFn = (event: eventWithTime) => string;\nexport type UnpackFn = (raw: string) => eventWithTime;\n\nexport type eventWithTimeAndPacker = eventWithTime & {\n  v: string;\n};\n\nexport const MARK = 'v1';\n", "import { strFromU8, strToU8, unzlibSync } from 'fflate';\nimport { UnpackFn, eventWithTimeAndPacker, MARK } from './base';\nimport type { eventWithTime } from '@rrweb/types';\n\nexport const unpack: UnpackFn = (raw: string) => {\n  if (typeof raw !== 'string') {\n    return raw;\n  }\n  try {\n    const e: eventWithTime = JSON.parse(raw) as eventWithTime;\n    if (e.timestamp) {\n      return e;\n    }\n  } catch (error) {\n    // ignore and continue\n  }\n  try {\n    const e: eventWithTimeAndPacker = JSON.parse(\n      strFromU8(unzlibSync(strToU8(raw, true))),\n    ) as eventWithTimeAndPacker;\n    if (e.v === MARK) {\n      return e;\n    }\n    throw new Error(\n      `These events were packed with packer ${e.v} which is incompatible with current packer ${MARK}.`,\n    );\n  } catch (error) {\n    console.error(error);\n    throw new Error('Unknown data format.');\n  }\n};\n"], "names": ["k", "Q", "z", "et", "rt", "at", "it", "nt", "c", "p", "m", "y", "b", "v", "L", "$", "F", "C", "q", "Y", "W", "H", "j", "K", "polyfill", "D", "g", "n", "o", "i", "a", "u", "f", "l", "d", "x", "A", "V", "h", "P", "M", "O.default", "O", "G", "_", "J", "ue", "U", "Z", "ae", "oe", "re", "se", "ne", "X", "me", "ie", "te", "ee", "I", "le", "ce", "he", "B", "de", "t"], "mappings": ";;;IAAA,IAAIA,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAS,CAAC,EAAEA,GAAC,GAAGA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAA2mB,IAAIC,GAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,OAAO,IAAIA,GAAC,CAA2yb,IAAI,EAAE,CAAC,iCAAiC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AACp1f,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAOC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,0CAA0C,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,uDAAuD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,qCAAqC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,SAASA,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,cAAc,CAAC,gBAAgB,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,eAAe,CAACC,IAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAACA,IAAE,CAAC,aAAa,CAAC,CAAC,OAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,OAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,SAASC,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAKJ,GAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,KAAKA,GAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,yBAAyB,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,SAAS,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,UAAU,EAAE,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAC,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAC,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,qBAAqB,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,KAAKA,GAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,KAAKA,GAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAACI,IAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGJ,GAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,YAAY,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,YAAY,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,8BAA8B,CAAC,CAAC,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,mEAAmE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,OAAO,CAAC,CAAC,SAASK,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAASC,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAIN,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,eAAe,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,SAASO,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAIN,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOI,IAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACC,IAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;;ICDpiV,IAAIE,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAS,CAAC,EAAEA,GAAC,GAAGA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,MAAMC,GAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAACC,GAAC,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAACA,GAAC,CAAC,UAAS,CAAC,IAAI,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,6GAA6G,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,8GAA8G,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,6GAA6G,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAM,QAAQ,CAAC,CAAC,SAASV,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,EAAE,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAACU,GAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAACF,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAI,CAAC,IAAI,eAAe,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,cAAc,EAAE,CAAC,OAAO,IAAI,CAAC,IAAI,iBAAiB,EAAE,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAGA,GAAC,CAAC,OAAO,EAAE,CAAC,GAAGA,GAAC,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,sEAAsE,EAAE,CAAC,GAAGA,GAAC,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAGA,GAAC,CAAC,OAAO,EAAE,CAAC,GAAGA,GAAC,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,uEAAuE,EAAE,CAAC,GAAGA,GAAC,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,qIAAqI,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,0GAA0G,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAE,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,oEAAoE,CAAC,CAAC,CAAC,wCAAwC,CAAC,CAAC,GAAG,mEAAmE,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAACC,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIE,GAAC,CAACF,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIG,GAAC,CAACH,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAACA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAII,GAAC,CAACJ,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAM,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAACC,GAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAACF,GAAC,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,OAAM,gBAAgB,CAAC,CAAC,CAAC,SAASG,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAACD,GAAC,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAACF,GAAC,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,GAAE,CAAC,IAAI,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,qIAAqI,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,yGAAyG,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,+GAA+G,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,SAASI,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAACF,GAAC,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAACF,GAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,IAAI,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,OAAM,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAACE,GAAC,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAACF,GAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,IAAI,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,OAAM,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASK,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAACH,GAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAACF,GAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,IAAI,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,OAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,IAAIE,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,yBAAwB,CAAC,EAAEA,GAAC,GAAGA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAMI,GAAC,CAAC,CAAC,GAAG,CAAC,4BAA4B,CAAC,YAAY,CAAC,8BAA8B,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,cAAc,CAAC,gBAAgB,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAGC,GAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAKP,GAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAU,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAGO,GAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,KAAKP,GAAC,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,KAAKA,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,EAAEM,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAC,EAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAC,CAAC,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAIR,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAACQ,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAASA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAKR,GAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAKA,GAAC,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAACM,GAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAKN,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAMS,GAAC,SAASjB,GAAC,CAACS,GAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAACS,GAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,IAAI,cAAc,EAAE,CAAC,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAID,GAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIE,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAIC,GAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,IAAIC,GAAC,CAAC,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIC,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAwB,CAAC,CAAC,MAAMH,GAAC,CAAC,CAAC,CAACV,GAAC,CAAC,CAAC,MAAM,CAAC,SAASE,GAAC,CAACF,GAAC,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAE,CAAC,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,CAAC,MAAMY,GAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAE,CAAC,CAAC,MAAMD,GAAC,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAIH,GAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAACL,GAAC,CAACH,GAAC,CAAC,CAACa,GAAC,CAAC,CAAC,CAACb,GAAC,CAAC,CAAC,CAAC,CAACI,GAAC,CAACJ,GAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAKC,GAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAYO,GAAC,GAAG,CAAC,GAAG,CAAC,CAAChB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAIgB,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC,QAAQ,GAAGP,GAAC,CAAC,sBAAsB,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,CAAC,CAAC,QAAQ,GAAGA,GAAC,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,GAAGA,GAAC,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,GAAGA,GAAC,CAAC,sBAAsB,IAAI,CAAC,CAAC,QAAQ,GAAGA,GAAC,CAAC,YAAY,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAASQ,GAAC,EAAE,CAAC,OAAO,IAAIK,GAAC,CAAC,MAAMA,GAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,QAAO,CAAC,CAAC,SAAStB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAKO,GAAC,CAAC,QAAQ,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,KAAKA,GAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,KAAKA,GAAC,CAAC,IAAI,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,KAAKA,GAAC,CAAC,KAAK,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;;ICAjykB,aAAQ,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;;;;;;;ICAlT,SAASgB,UAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,6BAA6B,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC,QAAQ,CAAC,uDAAuD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,IAAI,WAAW,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAC,EAAC;;ICAj9H,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAACZ,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC,CAACG,GAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAEA,GAAC,EAAE,EAAE,CAAC;;ICAprD,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAQ,SAAS,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAGU,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAGC,GAAC,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;;ICAn1C;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,IAAID,GAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAS,CAAC,CAACA,GAAC,GAAGA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAASE,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,OAAM,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAACD,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASE,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAM,UAAU,EAAE,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,OAAOH,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAACC,GAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,OAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,yBAAyB,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,OAAOH,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAIG,GAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAASpB,GAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAACc,GAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,GAAC,CAAC,CAAC,CAACF,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAACD,GAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC,CAAC,OAAO,CAAC,CAACH,GAAC,CAAC,OAAO,CAACM,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAACN,GAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;;ICdzkG,IAAIF,GAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAIC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAACD,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAACM,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAsM,SAAS,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAME,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAC,CAAC,IAAI,GAAGC,CAAC,CAAC,IAAI,EAAED,GAAC,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAQ,SAAS,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAACA,GAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC5B,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAM,SAAS,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAACwB,GAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAACf,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAIkB,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAGE,GAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEH,GAAC,CAAC,IAAI,CAACI,GAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACJ,GAAC,CAAC,IAAI,CAACI,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAE,CAAC,CAAC,oBAAoB,CAAC9B,CAAC,CAAC,CAAC,EAAEwB,GAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAACxB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAACS,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC,CAAC,OAAOe,GAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOO,GAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,kBAAkB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACH,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAC,CAAC,CAAC,WAAW,CAAC5B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO+B,GAAC,CAAC,CAAC,CAAC;;ICAvvG,MAAM,CAAC,CAAC,CAAC;AACrO;AACA,4EAA4E,CAAC,CAAQ,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,OAAO,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAyvD,SAAS,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,OAAM,CAAC,CAAC,CAAC,EAAC,CAAQ,SAAS,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,OAAO,CAAC,CAAQ,SAAS,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAQ,SAAS,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAyL,SAAS,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAQ,SAAS,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAQ,SAAS,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAQ,SAAS,oBAAoB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAQ,SAAS,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,OAAO,CAAC,CAAQ,MAAM,gBAAgB,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC,CAAC,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC;;ICF/rJ,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,6BAA6B,CAAC,CAAC,wCAAwC,CAAC;;ICA1F;IACA;IACA;IACA;IACA;IACA,IAAI,KAAK,GAAG,kEAAkE,CAAC;IAC/E;IACA,IAAI,MAAM,GAAG,OAAO,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IAC1E,KAAK,IAAIV,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAG,KAAK,CAAC,MAAM,EAAEA,GAAC,EAAE,EAAE;IACvC,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAACA,GAAC,CAAC,CAAC,GAAGA,GAAC,CAAC;IACpC,CAAC;IAiBD,IAAI,MAAM,GAAG,UAAU,MAAM,EAAE;IAC/B,IAAI,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACnH,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3C,QAAQ,YAAY,EAAE,CAAC;IACvB,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IAC/C,YAAY,YAAY,EAAE,CAAC;IAC3B,SAAS;IACT,KAAK;IACL,IAAI,IAAI,WAAW,GAAG,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IACzF,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;IACjC,QAAQ,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,QAAQ,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,QAAQ,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,QAAQ,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC;IACvD,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC;IAC9D,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC;IAC7D,KAAK;IACL,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC;;IC9CD,IAAIlB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAA6C,MAAM,CAAC,CAAC,IAAI,GAAG,CAAQ,SAAS,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAgG,SAAS,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAEA,GAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,aAAa,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,MAAM,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,OAAOC,MAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;ICAlzC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAuH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGoB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,MAAMlB,GAAC,CAAC,CAAC,iBAAiB,CAAC,aAAa,CAAC,kBAAkB,CAAC,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,4BAA4B,CAAC,cAAc,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAACA,GAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAACoB,eAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAgB,SAASC,GAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC1B,cAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAA8Z,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;;ICA1/C,IAAIE,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAoE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOA,GAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACkB,cAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;;ICAhoB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAwG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACE,CAAC,CAAC,KAAK,CAACA,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAME,GAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAMO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;;ICAhwB,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAu6B,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAACC,IAAS,EAAEC,GAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE/B,CAAC,CAAC,mBAAmB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE2B,GAAC,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,EAAEA,GAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAEZ,GAAC,CAAC,UAAU,CAAC,CAAQ,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAIiB,GAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAACC,EAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAACC,EAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAIC,gBAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,IAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAKnC,CAAC,CAAC,gBAAgB,CAAC,KAAKA,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,CAAC,MAAM,CAAC,SAAS,KAAKA,CAAC,CAAC,YAAY,CAAC,KAAKA,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,CAAC,MAAM,CAAC,KAAKA,CAAC,CAAC,mBAAmB,CAAC,MAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAKA,CAAC,CAAC,gBAAgB,CAAC,KAAKA,CAAC,CAAC,IAAI,CAAC,MAAM,KAAKA,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACoB,GAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,KAAKpB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAACoB,GAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,KAAKpB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAE,CAAC,CAAC,MAAM,KAAKA,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACoB,GAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAc,CAAC,OAAM,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGpB,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG2B,GAAC,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACP,GAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACA,GAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACA,GAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAGT,GAAC,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAGA,GAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAED,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAACW,GAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAE,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACN,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIkB,KAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAACC,mBAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACnB,GAAC,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAACoB,kBAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACpB,GAAC,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGpB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGA,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACoB,GAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,EAAC,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,WAAW,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,KAAK,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,KAAK,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,4FAA4F,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,MAAM,EAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,KAAK,CAACa,EAAC,GAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,GAAGQ,UAAE,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAACC,QAAE,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,CAAC,CAACC,IAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACvB,GAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,GAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIJ,GAAC,CAAC,MAAM,CAAC,CAAC4B,CAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yHAAyH,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAACC,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAClB,GAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAACX,GAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,EAAEA,GAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAIf,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,CAAC,CAACuB,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACL,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACA,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAACA,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAACA,GAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,GAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAO,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,IAAI,CAAG,IAAI,CAAC,OAAO,CAAC,MAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACA,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAACA,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAGpB,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG2B,GAAC,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACpC,cAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACA,cAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAKoC,GAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC,IAAI,KAAK,CAACZ,GAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACK,GAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAKL,GAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,KAAKA,GAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,UAAU,CAAC,KAAKA,GAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,KAAKA,GAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,KAAKY,GAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACP,GAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,KAAKO,GAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAGtC,GAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,GAAGA,GAAC,CAAC,UAAU,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,KAAKsC,GAAC,CAAC,cAAc,CAAC,KAAKA,GAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAACS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,KAAKT,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAKA,GAAC,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAS,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAACmB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,CAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAEC,aAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG/C,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG+C,aAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAGvB,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,CAACD,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,GAAGvB,GAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAACwB,kBAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAACwB,mBAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAACC,kBAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAACC,mBAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC3B,EAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC,EAAC,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAIvB,GAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG0B,GAAC,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAGA,GAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAACyB,oBAAC,CAAC,CAAC,CAAC,CAAC9C,aAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC8C,oBAAC,CAAC,CAAC,CAAC,CAAC9C,aAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAEA,aAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,EAAEA,aAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG0C,aAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAACrB,GAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAACqB,aAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACK,gBAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAGrD,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC2B,GAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,EAAEA,GAAC,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAACP,GAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC;;ICA/w5B;AAkBA;IACA;IACA,IAAI,EAAE,GAAG,UAAU,EAAE,GAAG,GAAG,WAAW,EAAE,GAAG,GAAG,WAAW,CAAC;IAC1D;IACA,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAClJ;IACA;IACA,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzI;IACA,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACtF;IACA,IAAI,IAAI,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE;IAChC,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;IACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;IACjC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvC,KAAK;IACL;IACA,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;IACjC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC9C,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzC,SAAS;IACT,KAAK;IACL,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;IACF,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAClD;IACA,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC3B,QAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAgB;IAClD;IACA,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;IAChC;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;IACvD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;IACnD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;IACnD,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;IAChE,CAAC;IACD;IACA;IACA;IACA,IAAI,IAAI,IAAI,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IACtB;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;IACd;IACA,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;IACxB;IACA,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;IACrB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvB;IACA,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;IACzB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;IAC7B,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAC5C,KAAK;IACL,IAAI,IAAI,EAAE,CAAC;IACX,IAAI,IAAI,CAAC,EAAE;IACX;IACA,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9B;IACA,QAAQ,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IAC1B,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;IAChC;IACA,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;IACvB;IACA,gBAAgB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1C;IACA,gBAAgB,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC;IACA,gBAAgB,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC;IAC/C;IACA,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;IAChE;IACA,oBAAoB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;IAC5C,iBAAiB;IACjB,aAAa;IACb,SAAS;IACT,KAAK;IACL,SAAS;IACT,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9B,YAAY,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,KAAK;IACL,IAAI,OAAO,EAAE,CAAC;IACd,CAAC,CAAC,CAAC;IACH;IACA,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC;IAC5B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC;IAC9B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC;IAC9B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC;IAC9B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACf;IACA,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC;IAC3B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACf;AACG,QAAsC,IAAI,iBAAiB,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;IAC9E;AACG,QAAsC,IAAI,iBAAiB,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;IAC9E;IACA,IAAI,GAAG,GAAG,UAAU,CAAC,EAAE;IACvB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;IACvC,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACpB,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,KAAK;IACL,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACF;IACA,IAAI,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAC9B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACzB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC,CAAC;IACF;IACA,IAAI,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACzB,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACrE,CAAC,CAAC;IACF;IACA,IAAI,IAAI,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE;IACA;IACA,IAAI,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAC7B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC;IAC1B,QAAQ,CAAC,GAAG,CAAC,CAAC;IACd,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM;IACjC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACrB;IACA,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACF;IACA,IAAI,KAAK,GAAG,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE;IACpC;IACA,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACxB;IACA,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;IAC3B;IACA,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC3B,IAAI,IAAI,CAAC,EAAE;IACX,QAAQ,EAAE,GAAG,EAAE,CAAC;IAChB;IACA,IAAI,IAAI,CAAC,GAAG;IACZ,QAAQ,GAAG,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7B;IACA,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,EAAE;IAC5B,QAAQ,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC5B;IACA,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;IACpB;IACA,YAAY,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B,YAAY,GAAG,GAAG,IAAI,CAAC;IACvB,SAAS;IACT,KAAK,CAAC;IACN;IACA,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IACzG;IACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IACtB,IAAI,GAAG;IACP,QAAQ,IAAI,CAAC,EAAE,EAAE;IACjB;IACA,YAAY,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC7C;IACA,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,YAAY,GAAG,IAAI,CAAC,CAAC;IACrB,YAAY,IAAI,CAAC,IAAI,EAAE;IACvB;IACA,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrF,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;IAC5B,oBAAoB,IAAI,IAAI;IAC5B,wBAAwB,MAAM,gBAAgB,CAAC;IAC/C,oBAAoB,MAAM;IAC1B,iBAAiB;IACjB;IACA,gBAAgB,IAAI,KAAK;IACzB,oBAAoB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACjC;IACA,gBAAgB,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAChD;IACA,gBAAgB,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACnD,gBAAgB,SAAS;IACzB,aAAa;IACb,iBAAiB,IAAI,IAAI,IAAI,CAAC;IAC9B,gBAAgB,EAAE,GAAG,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IACvD,iBAAiB,IAAI,IAAI,IAAI,CAAC,EAAE;IAChC;IACA,gBAAgB,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACzF,gBAAgB,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IAC3D,gBAAgB,GAAG,IAAI,EAAE,CAAC;IAC1B;IACA,gBAAgB,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACrC;IACA,gBAAgB,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACrC,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;IAChD;IACA,oBAAoB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D,iBAAiB;IACjB,gBAAgB,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC;IACjC;IACA,gBAAgB,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAC5D,gBAAgB,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;IACxD,oBAAoB,MAAM;IAC1B;IACA,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5C,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG;IACzC,oBAAoB,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;IACxD;IACA,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAClC;IACA,oBAAoB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpC;IACA,oBAAoB,IAAI,CAAC,GAAG,EAAE,EAAE;IAChC,wBAAwB,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACrC,qBAAqB;IACrB,yBAAyB;IACzB;IACA,wBAAwB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzC,wBAAwB,IAAI,CAAC,IAAI,EAAE;IACnC,4BAA4B,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChF,6BAA6B,IAAI,CAAC,IAAI,EAAE;IACxC,4BAA4B,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IAChE,6BAA6B,IAAI,CAAC,IAAI,EAAE;IACxC,4BAA4B,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IACnE,wBAAwB,OAAO,CAAC,EAAE;IAClC,4BAA4B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzC,qBAAqB;IACrB,iBAAiB;IACjB;IACA,gBAAgB,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxE;IACA,gBAAgB,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B;IACA,gBAAgB,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B,gBAAgB,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACtC,gBAAgB,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACtC,aAAa;IACb;IACA,gBAAgB,MAAM,oBAAoB,CAAC;IAC3C,YAAY,IAAI,GAAG,GAAG,IAAI;IAC1B,gBAAgB,MAAM,gBAAgB,CAAC;IACvC,SAAS;IACT;IACA;IACA,QAAQ,IAAI,KAAK;IACjB,YAAY,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC;IAC9B,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IACvD,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;IACjC,QAAQ,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;IACzC;IACA,YAAY,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC9D,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC1B,YAAY,IAAI,GAAG,GAAG,IAAI;IAC1B,gBAAgB,MAAM,gBAAgB,CAAC;IACvC,YAAY,IAAI,CAAC,CAAC;IAClB,gBAAgB,MAAM,wBAAwB,CAAC;IAC/C,YAAY,IAAI,GAAG,GAAG,GAAG;IACzB,gBAAgB,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IAChC,iBAAiB,IAAI,GAAG,IAAI,GAAG,EAAE;IACjC,gBAAgB,EAAE,GAAG,IAAI,CAAC;IAC1B,gBAAgB,MAAM;IACtB,aAAa;IACb,iBAAiB;IACjB,gBAAgB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACpC;IACA,gBAAgB,IAAI,GAAG,GAAG,GAAG,EAAE;IAC/B;IACA,oBAAoB,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,oBAAoB,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/D,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAC7B,iBAAiB;IACjB;IACA,gBAAgB,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IACnE,gBAAgB,IAAI,CAAC,CAAC;IACtB,oBAAoB,MAAM,kBAAkB,CAAC;IAC7C,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9B,gBAAgB,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IAClC,gBAAgB,IAAI,IAAI,GAAG,CAAC,EAAE;IAC9B,oBAAoB,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,oBAAoB,EAAE,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IACtE,iBAAiB;IACjB,gBAAgB,IAAI,GAAG,GAAG,IAAI;IAC9B,oBAAoB,MAAM,gBAAgB,CAAC;IAC3C,gBAAgB,IAAI,KAAK;IACzB,oBAAoB,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC;IACtC,gBAAgB,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;IACnC,gBAAgB,OAAO,EAAE,GAAG,GAAG,EAAE,EAAE,IAAI,CAAC,EAAE;IAC1C,oBAAoB,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3C,oBAAoB,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IACnD,oBAAoB,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IACnD,oBAAoB,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IACnD,iBAAiB;IACjB,gBAAgB,EAAE,GAAG,GAAG,CAAC;IACzB,aAAa;IACb,SAAS;IACT,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;IACzC,QAAQ,IAAI,EAAE;IACd,YAAY,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;IACzD,KAAK,QAAQ,CAAC,KAAK,EAAE;IACrB,IAAI,OAAO,EAAE,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC,CAAC;IAojBF;IACA,IAAI,GAAG,GAAG,UAAU,CAAC,EAAE;IACvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACzE,QAAQ,MAAM,mBAAmB,CAAC;IAClC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;IACjB,QAAQ,MAAM,sDAAsD,CAAC;IACrE,CAAC,CAAC;IA2bF;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;IACtC,IAAI,OAAO,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACzD,CAAC;IAiHD;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE;IACrC,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,WAAW,IAAI,WAAW;IACpD,QAAQ,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC;IACf,IAAI,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;IAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;IAChC,QAAQ,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;IAChC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,YAAY,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACtB,YAAY,EAAE,GAAG,CAAC,CAAC;IACnB,SAAS;IACT,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAClC,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM;IAC7B,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,aAAa,IAAI,CAAC,GAAG,IAAI;IACzB,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAClD,aAAa,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK;IACvC,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;IACvE,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9G;IACA,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9E,KAAK;IACL,IAAI,OAAO,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1B,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE;IACvC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,WAAW,IAAI,WAAW;IACpD,QAAQ,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG;IACrC,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM;IAC7B,YAAY,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACxC,aAAa,IAAI,CAAC,GAAG,GAAG;IACxB,YAAY,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtE,aAAa,IAAI,CAAC,GAAG,GAAG;IACxB,YAAY,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9F;IACA,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK;IACzG,gBAAgB,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAChF,KAAK;IACL,IAAI,OAAO,CAAC,CAAC;IACb;;IC1+CO,MAAM,IAAI,CAAC,IAAI;;ACAoF,UAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAACJ,SAAC,CAACC,UAAC,CAACC,OAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGoC,IAAC,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,qCAAqC,EAAE,CAAC,CAAC,CAAC,CAAC,2CAA2C,EAAEA,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;;;;;;;;;;;;;"}