{"version": 3, "file": "sequential-id-record.min.js", "sources": ["../../src/plugins/sequential-id/record/index.ts"], "sourcesContent": ["import type { RecordPlugin } from '@rrweb/types';\n\nexport type SequentialIdOptions = {\n  key: string;\n};\n\nconst defaultOptions: SequentialIdOptions = {\n  key: '_sid',\n};\n\nexport const PLUGIN_NAME = 'rrweb/sequential-id@1';\n\nexport const getRecordSequentialIdPlugin: (\n  options?: Partial<SequentialIdOptions>,\n) => RecordPlugin = (options) => {\n  const _options = options\n    ? Object.assign({}, defaultOptions, options)\n    : defaultOptions;\n  let id = 0;\n\n  return {\n    name: PLUGIN_NAME,\n    eventProcessor(event) {\n      Object.assign(event, {\n        [_options.key]: ++id,\n      });\n      return event;\n    },\n    options: _options,\n  };\n};\n"], "names": [], "mappings": ";;;EAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAa,MAAC,WAAW,CAAC,uBAAuB,CAAC,2BAA2B,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;;;;;;;;;;;;"}