var vdom=function(e){"use strict";var t;!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(t||(t={}));var o=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){var t;if(!e)return-1;var o=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=o?o:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,o=this.getId(e);this.idNodeMap.delete(o),e.childNodes&&e.childNodes.forEach((function(e){return t.removeNodeFromMap(e)}))},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var o=t.id;this.idNodeMap.set(o,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function n(e){const t=[];for(const o in e){const n=e[o];if("string"!=typeof n)continue;const s=d(o);t.push(`${s}: ${n};`)}return t.join(" ")}const s=/-([a-z])/g,r=/^--[a-zA-Z0-9-]+$/,i=e=>r.test(e)?e:e.replace(s,((e,t)=>t?t.toUpperCase():"")),a=/\B([A-Z])/g,d=e=>e.replace(a,"-$1").toLowerCase();class c{constructor(...t){this.childNodes=[],this.parentElement=null,this.parentNode=null,this.ELEMENT_NODE=e.NodeType.ELEMENT_NODE,this.TEXT_NODE=e.NodeType.TEXT_NODE}get firstChild(){return this.childNodes[0]||null}get lastChild(){return this.childNodes[this.childNodes.length-1]||null}get nextSibling(){const e=this.parentNode;if(!e)return null;const t=e.childNodes,o=t.indexOf(this);return t[o+1]||null}contains(e){if(e===this)return!0;for(const t of this.childNodes)if(t.contains(e))return!0;return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}function l(o){return class n extends o{constructor(){super(...arguments),this.nodeType=e.NodeType.DOCUMENT_NODE,this.nodeName="#document",this.compatMode="CSS1Compat",this.RRNodeType=t.Document,this.textContent=null}get documentElement(){return this.childNodes.find((e=>e.RRNodeType===t.Element&&"HTML"===e.tagName))||null}get body(){var e;return(null===(e=this.documentElement)||void 0===e?void 0:e.childNodes.find((e=>e.RRNodeType===t.Element&&"BODY"===e.tagName)))||null}get head(){var e;return(null===(e=this.documentElement)||void 0===e?void 0:e.childNodes.find((e=>e.RRNodeType===t.Element&&"HEAD"===e.tagName)))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(e){const o=e.RRNodeType;if((o===t.Element||o===t.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===o)))throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${o===t.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);return e.parentElement=null,e.parentNode=this,this.childNodes.push(e),e}insertBefore(e,o){const n=e.RRNodeType;if((n===t.Element||n===t.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===n)))throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${n===t.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);if(null===o)return this.appendChild(e);const s=this.childNodes.indexOf(o);if(-1==s)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(s,0,e),e.parentElement=null,e.parentNode=this,e}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}open(){this.childNodes=[]}close(){}write(e){let t;if('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'===e?t="-//W3C//DTD XHTML 1.0 Transitional//EN":'<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">'===e&&(t="-//W3C//DTD HTML 4.0 Transitional//EN"),t){const e=this.createDocumentType("html",t,"");this.open(),this.appendChild(e)}}createDocument(e,t,o){return new n}createDocumentType(e,t,o){const n=new(u(c))(e,t,o);return n.ownerDocument=this,n}createElement(e){const t=new(h(c))(e);return t.ownerDocument=this,t}createElementNS(e,t){return this.createElement(t)}createTextNode(e){const t=new(m(c))(e);return t.ownerDocument=this,t}createComment(e){const t=new(N(c))(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new(E(c))(e);return t.ownerDocument=this,t}toString(){return"RRDocument"}}}function u(o){return class extends o{constructor(o,n,s){super(),this.nodeType=e.NodeType.DOCUMENT_TYPE_NODE,this.RRNodeType=t.DocumentType,this.textContent=null,this.name=o,this.publicId=n,this.systemId=s,this.nodeName=o}toString(){return"RRDocumentType"}}}function h(o){return class extends o{constructor(o){super(),this.nodeType=e.NodeType.ELEMENT_NODE,this.RRNodeType=t.Element,this.attributes={},this.shadowRoot=null,this.tagName=o.toUpperCase(),this.nodeName=o.toUpperCase()}get textContent(){let e="";return this.childNodes.forEach((t=>e+=t.textContent)),e}set textContent(e){this.childNodes=[this.ownerDocument.createTextNode(e)]}get classList(){return new T(this.attributes.class,(e=>{this.attributes.class=e}))}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const e=this.attributes.style?function(e){const t={},o=/:(.+)/;return e.replace(/\/\*.*?\*\//g,"").split(/;(?![^(]*\))/g).forEach((function(e){if(e){const n=e.split(o);n.length>1&&(t[i(n[0].trim())]=n[1].trim())}})),t}(this.attributes.style):{},t=/\B([A-Z])/g;return e.setProperty=(o,s,r)=>{if(t.test(o))return;const a=i(o);s?e[a]=s:delete e[a],"important"===r&&(e[a]+=" !important"),this.attributes.style=n(e)},e.removeProperty=o=>{if(t.test(o))return"";const s=i(o),r=e[s]||"";return delete e[s],this.attributes.style=n(e),r},e}getAttribute(e){return this.attributes[e]||null}setAttribute(e,t){this.attributes[e]=t}setAttributeNS(e,t,o){this.setAttribute(t,o)}removeAttribute(e){delete this.attributes[e]}appendChild(e){return this.childNodes.push(e),e.parentNode=this,e.parentElement=this,e}insertBefore(e,t){if(null===t)return this.appendChild(e);const o=this.childNodes.indexOf(t);if(-1==o)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(o,0,e),e.parentElement=this,e.parentNode=this,e}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}attachShadow(e){const t=this.ownerDocument.createElement("SHADOWROOT");return this.shadowRoot=t,t}dispatchEvent(e){return!0}toString(){let e="";for(const t in this.attributes)e+=`${t}="${this.attributes[t]}" `;return`${this.tagName} ${e}`}}}function p(e){return class extends e{attachShadow(e){throw new Error("RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow")}play(){this.paused=!1}pause(){this.paused=!0}}}function m(o){return class extends o{constructor(o){super(),this.nodeType=e.NodeType.TEXT_NODE,this.nodeName="#text",this.RRNodeType=t.Text,this.data=o}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRText text=${JSON.stringify(this.data)}`}}}function N(o){return class extends o{constructor(o){super(),this.nodeType=e.NodeType.COMMENT_NODE,this.nodeName="#comment",this.RRNodeType=t.Comment,this.data=o}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}}function E(o){return class extends o{constructor(o){super(),this.nodeName="#cdata-section",this.nodeType=e.NodeType.CDATA_SECTION_NODE,this.RRNodeType=t.CDATA,this.data=o}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}}class T{constructor(e,t){if(this.classes=[],this.add=(...e)=>{for(const t of e){const e=String(t);this.classes.indexOf(e)>=0||this.classes.push(e)}this.onChange&&this.onChange(this.classes.join(" "))},this.remove=(...e)=>{this.classes=this.classes.filter((t=>-1===e.indexOf(t))),this.onChange&&this.onChange(this.classes.join(" "))},e){const t=e.trim().split(/\s+/);this.classes.push(...t)}this.onChange=t}}e.NodeType=void 0,function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(e.NodeType||(e.NodeType={}));const R={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"};function y(o,n,s,r){const i=o.childNodes,a=n.childNodes;r=r||n.mirror||n.ownerDocument.mirror,(i.length>0||a.length>0)&&D(Array.from(i),a,o,s,r);let d=null,c=null;switch(n.RRNodeType){case t.Document:c=n.scrollData;break;case t.Element:{const t=o,i=n;switch(function(e,t,o){const n=e.attributes,s=t.attributes;for(const n in s){const r=s[n],i=o.getMeta(t);if(i&&"isSVG"in i&&i.isSVG&&R[n])e.setAttributeNS(R[n],n,r);else if("CANVAS"===t.tagName&&"rr_dataURL"===n){const t=document.createElement("img");t.src=r,t.onload=()=>{const o=e.getContext("2d");o&&o.drawImage(t,0,0,t.width,t.height)}}else e.setAttribute(n,r)}for(const{name:t}of Array.from(n))t in s||e.removeAttribute(t);t.scrollLeft&&(e.scrollLeft=t.scrollLeft),t.scrollTop&&(e.scrollTop=t.scrollTop)}(t,i,r),c=i.scrollData,d=i.inputData,i.tagName){case"AUDIO":case"VIDEO":{const e=o,t=i;void 0!==t.paused&&(t.paused?e.pause():e.play()),void 0!==t.muted&&(e.muted=t.muted),void 0!==t.volume&&(e.volume=t.volume),void 0!==t.currentTime&&(e.currentTime=t.currentTime);break}case"CANVAS":n.canvasMutations.forEach((e=>s.applyCanvas(e.event,e.mutation,o)));break;case"STYLE":!function(t,o){const n=t.sheet;o.forEach((t=>{if(t.type===e.StyleRuleType.Insert)try{if(Array.isArray(t.index)){const{positions:e,index:o}=g(t.index);C(n.cssRules,e).insertRule(t.cssText,o)}else n.insertRule(t.cssText,t.index)}catch(e){}else if(t.type===e.StyleRuleType.Remove)try{if(Array.isArray(t.index)){const{positions:e,index:o}=g(t.index);C(n.cssRules,e).deleteRule(o||0)}else n.deleteRule(t.index)}catch(e){}else if(t.type===e.StyleRuleType.SetProperty){C(n.cssRules,t.index).style.setProperty(t.property,t.value,t.priority)}else if(t.type===e.StyleRuleType.RemoveProperty){C(n.cssRules,t.index).style.removeProperty(t.property)}}))}(t,n.rules)}if(i.shadowRoot){t.shadowRoot||t.attachShadow({mode:"open"});const e=t.shadowRoot.childNodes,o=i.shadowRoot.childNodes;(e.length>0||o.length>0)&&D(Array.from(e),o,t.shadowRoot,s,r)}break}case t.Text:case t.Comment:case t.CDATA:o.textContent!==n.data&&(o.textContent=n.data)}if(c&&s.applyScroll(c,!0),d&&s.applyInput(d),"IFRAME"===n.nodeName){const e=o.contentDocument,t=n;if(e){const o=r.getMeta(t.contentDocument);o&&s.mirror.add(e,Object.assign({},o)),y(e,t.contentDocument,s,r)}}}function D(e,o,n,s,r){var i,a;let d,c,l=0,u=e.length-1,h=0,p=o.length-1,m=e[l],N=e[u],E=o[h],T=o[p];for(;l<=u&&h<=p;)if(void 0===m)m=e[++l];else if(void 0===N)N=e[--u];else if(s.mirror.getId(m)===r.getId(E))y(m,E,s,r),m=e[++l],E=o[++h];else if(s.mirror.getId(N)===r.getId(T))y(N,T,s,r),N=e[--u],T=o[--p];else if(s.mirror.getId(m)===r.getId(T))n.insertBefore(m,N.nextSibling),y(m,T,s,r),m=e[++l],T=o[--p];else if(s.mirror.getId(N)===r.getId(E))n.insertBefore(N,m),y(N,E,s,r),N=e[--u],E=o[++h];else{if(!d){d={};for(let t=l;t<=u;t++){const o=e[t];o&&s.mirror.hasNode(o)&&(d[s.mirror.getId(o)]=t)}}if(c=d[r.getId(E)],c){const t=e[c];n.insertBefore(t,m),y(t,E,s,r),e[c]=void 0}else{const o=f(E,s.mirror,r);(null===(i=s.mirror.getMeta(n))||void 0===i?void 0:i.type)===t.Document&&(null===(a=s.mirror.getMeta(o))||void 0===a?void 0:a.type)===t.Element&&n.documentElement&&(n.removeChild(n.documentElement),e[l]=void 0,m=void 0),n.insertBefore(o,m||null),y(o,E,s,r)}E=o[++h]}if(l>u){const e=o[p+1];let t=null;for(e&&n.childNodes.forEach((o=>{s.mirror.getId(o)===r.getId(e)&&(t=o)}));h<=p;++h){const e=f(o[h],s.mirror,r);n.insertBefore(e,t),y(e,o[h],s,r)}}else if(h>p)for(;l<=u;l++){const t=e[l];t&&(n.removeChild(t),s.mirror.removeNodeFromMap(t))}}function f(e,o,n){let s=o.getNode(n.getId(e));const r=n.getMeta(e);if(null!==s)return s;switch(e.RRNodeType){case t.Document:s=new Document;break;case t.DocumentType:s=document.implementation.createDocumentType(e.name,e.publicId,e.systemId);break;case t.Element:e.tagName.toLowerCase(),s=r&&"isSVG"in r&&(null==r?void 0:r.isSVG)?document.createElementNS(R.svg,e.tagName.toLowerCase()):document.createElement(e.tagName);break;case t.Text:s=document.createTextNode(e.data);break;case t.Comment:s=document.createComment(e.data);break;case t.CDATA:s=document.createCDATASection(e.data)}return r&&o.add(s,Object.assign({},r)),s}function C(e,t){const o=e[t[0]];return 1===t.length?o:C(o.cssRules[t[1]].cssRules,t.slice(2))}var M;function g(e){const t=[...e],o=t.pop();return{positions:t,index:o}}e.StyleRuleType=void 0,(M=e.StyleRuleType||(e.StyleRuleType={}))[M.Insert=0]="Insert",M[M.Remove=1]="Remove",M[M.Snapshot=2]="Snapshot",M[M.SetProperty=3]="SetProperty",M[M.RemoveProperty=4]="RemoveProperty";class w extends(l(c)){constructor(e){super(),this._unserializedId=-1,this.mirror=k(),this.scrollData=null,e&&(this.mirror=e)}get unserializedId(){return this._unserializedId--}createDocument(e,t,o){return new w}createDocumentType(e,t,o){const n=new O(e,t,o);return n.ownerDocument=this,n}createElement(e){const t=e.toUpperCase();let o;switch(t){case"AUDIO":case"VIDEO":o=new v(t);break;case"IFRAME":o=new b(t,this.mirror);break;case"CANVAS":o=new A(t);break;case"STYLE":o=new I(t);break;default:o=new x(t)}return o.ownerDocument=this,o}createComment(e){const t=new _(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new L(e);return t.ownerDocument=this,t}createTextNode(e){const t=new S(e);return t.ownerDocument=this,t}destroyTree(){this.childNodes=[],this.mirror.reset()}open(){super.open(),this._unserializedId=-1}}const O=u(c);class x extends(h(c)){constructor(){super(...arguments),this.inputData=null,this.scrollData=null}}class v extends(p(x)){}class A extends x{constructor(){super(...arguments),this.canvasMutations=[]}getContext(){return null}}class I extends x{constructor(){super(...arguments),this.rules=[]}}class b extends x{constructor(e,t){super(e),this.contentDocument=new w,this.contentDocument.mirror=t}}const S=m(c),_=N(c),L=E(c);function F(t,o,n,s){let r;switch(t.nodeType){case e.NodeType.DOCUMENT_NODE:s&&"IFRAME"===s.nodeName?r=s.contentDocument:(r=o,r.compatMode=t.compatMode);break;case e.NodeType.DOCUMENT_TYPE_NODE:const n=t;r=o.createDocumentType(n.name,n.publicId,n.systemId);break;case e.NodeType.ELEMENT_NODE:const a=t,d=(i=a)instanceof HTMLFormElement?"FORM":i.tagName.toUpperCase();r=o.createElement(d);const c=r;for(const{name:e,value:t}of Array.from(a.attributes))c.attributes[e]=t;a.scrollLeft&&(c.scrollLeft=a.scrollLeft),a.scrollTop&&(c.scrollTop=a.scrollTop);break;case e.NodeType.TEXT_NODE:r=o.createTextNode(t.textContent||"");break;case e.NodeType.CDATA_SECTION_NODE:r=o.createCDATASection(t.data);break;case e.NodeType.COMMENT_NODE:r=o.createComment(t.textContent||"");break;case e.NodeType.DOCUMENT_FRAGMENT_NODE:r=s.attachShadow({mode:"open"});break;default:return null}var i;let a=n.getMeta(t);return o instanceof w&&(a||(a=B(r,o.unserializedId),n.add(t,a)),o.mirror.add(r,Object.assign({},a))),r}function k(){return new U}class U{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){var t;if(!e)return-1;const o=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=o?o:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const o=t.id;this.idNodeMap.set(o,e),this.nodeMetaMap.set(e,t)}replace(e,t){this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function B(e,o){switch(e.RRNodeType){case t.Document:return{id:o,type:e.RRNodeType,childNodes:[]};case t.DocumentType:const n=e;return{id:o,type:e.RRNodeType,name:n.name,publicId:n.publicId,systemId:n.systemId};case t.Element:return{id:o,type:e.RRNodeType,tagName:e.tagName.toLowerCase(),attributes:{},childNodes:[]};case t.Text:case t.Comment:return{id:o,type:e.RRNodeType,textContent:e.textContent||""};case t.CDATA:return{id:o,type:e.RRNodeType,textContent:""}}}return e.BaseRRCDATASectionImpl=E,e.BaseRRCommentImpl=N,e.BaseRRDocumentImpl=l,e.BaseRRDocumentTypeImpl=u,e.BaseRRElementImpl=h,e.BaseRRMediaElementImpl=p,e.BaseRRNode=c,e.BaseRRTextImpl=m,e.ClassList=T,e.Mirror=U,e.RRCDATASection=L,e.RRCanvasElement=A,e.RRComment=_,e.RRDocument=w,e.RRDocumentType=O,e.RRElement=x,e.RRIFrameElement=b,e.RRMediaElement=v,e.RRStyleElement=I,e.RRText=S,e.buildFromDom=function(t,n=function(){return new o}(),s=new w){return function t(o,r){const i=F(o,s,n,r);null!==i&&("IFRAME"!==(null==r?void 0:r.nodeName)&&o.nodeType!==e.NodeType.DOCUMENT_FRAGMENT_NODE&&(null==r||r.appendChild(i),i.parentNode=r,i.parentElement=r),"IFRAME"===o.nodeName?t(o.contentDocument,i):o.nodeType!==e.NodeType.DOCUMENT_NODE&&o.nodeType!==e.NodeType.ELEMENT_NODE&&o.nodeType!==e.NodeType.DOCUMENT_FRAGMENT_NODE||(o.nodeType===e.NodeType.ELEMENT_NODE&&o.shadowRoot&&t(o.shadowRoot,i),o.childNodes.forEach((e=>t(e,i)))))}(t,null),s},e.buildFromNode=F,e.createMirror=k,e.createOrGetNode=f,e.diff=y,e.getDefaultSN=B,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
//# sourceMappingURL=vdom.min.js.map
