{"name": "rrweb-snapshot", "version": "2.0.0-alpha.4", "description": "rrweb's component to take a snapshot of DOM, aka DOM serializer", "scripts": {"prepare": "npm run prepack", "prepack": "npm run bundle && npm run typings", "test": "jest", "test:watch": "jest --watch", "bundle": "rollup --config", "bundle:es-only": "cross-env ES_ONLY=true rollup --config", "dev": "yarn bundle:es-only --watch", "typings": "tsc -d --declarationDir typings", "prepublish": "npm run typings && npm run bundle", "lint": "yarn eslint src"}, "repository": {"type": "git", "url": "git+https://github.com/rrweb-io/rrweb.git"}, "keywords": ["rrweb", "snapshot", "DOM"], "main": "lib/rrweb-snapshot.js", "module": "es/rrweb-snapshot.js", "unpkg": "dist/rrweb-snapshot.js", "typings": "typings/index.d.ts", "files": ["dist", "lib", "es", "typings"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/rrweb-io/rrweb/issues"}, "homepage": "https://github.com/rrweb-io/rrweb/tree/master/packages/rrweb-snapshot#readme", "devDependencies": {"@types/chai": "^4.1.4", "@types/jest": "^27.0.2", "@types/jsdom": "^20.0.0", "@types/node": "^10.11.3", "@types/puppeteer": "^1.12.4", "cross-env": "^5.2.0", "jest": "^27.2.4", "jest-snapshot": "^23.6.0", "jsdom": "^16.4.0", "puppeteer": "^1.15.0", "rollup": "^2.45.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "ts-jest": "^27.0.5", "ts-node": "^7.0.1", "tslib": "^1.9.3", "typescript": "^4.7.3"}, "gitHead": "7bb68625e3ff39258a8cc8614f0691f265cb5bee"}