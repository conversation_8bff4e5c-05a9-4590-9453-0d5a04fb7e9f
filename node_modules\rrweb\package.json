{"name": "rrweb", "version": "2.0.0-alpha.4", "description": "record and replay the web", "scripts": {"prepare": "npm run prepack", "prepack": "npm run bundle", "test": "npm run bundle:browser && jest --testPathIgnorePatterns test/benchmark", "test:headless": "PUPPETEER_HEADLESS=true npm run test", "test:watch": "PUPPETEER_HEADLESS=true npm run test -- --watch", "repl": "npm run bundle:browser && node scripts/repl.js", "live-stream": "yarn bundle:browser && node scripts/stream.js", "dev": "yarn bundle:browser --watch", "bundle:browser": "cross-env BROWSER_ONLY=true rollup --config", "bundle": "rollup --config", "typings": "tsc -d --declarationDir typings", "check-types": "tsc -noEmit", "prepublish": "npm run typings && npm run bundle", "lint": "yarn eslint src", "benchmark": "jest test/benchmark"}, "type": "module", "repository": {"type": "git", "url": "git+ssh://**************/rrweb-io/rrweb.git"}, "keywords": ["rrweb"], "main": "lib/rrweb-all.js", "module": "es/rrweb/packages/rrweb/src/entries/all.js", "unpkg": "dist/rrweb.js", "sideEffects": false, "typings": "typings/entries/all.d.ts", "files": ["dist", "lib", "es", "typings"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/rrweb-io/rrweb/issues"}, "homepage": "https://github.com/rrweb-io/rrweb#readme", "devDependencies": {"@rollup/plugin-node-resolve": "^13.1.3", "@types/chai": "^4.1.6", "@types/dom-mediacapture-transform": "^0.1.3", "@types/inquirer": "^8.2.1", "@types/jest": "^27.4.1", "@types/jest-image-snapshot": "^5.1.0", "@types/node": "^17.0.21", "@types/offscreencanvas": "^2019.6.4", "@types/prettier": "^2.3.2", "@types/puppeteer": "^5.4.4", "construct-style-sheets-polyfill": "^3.1.0", "cross-env": "^5.2.0", "esbuild": "^0.14.38", "fast-mhtml": "^1.1.9", "identity-obj-proxy": "^3.0.0", "ignore-styles": "^5.0.1", "inquirer": "^9.0.0", "jest": "^27.5.1", "jest-image-snapshot": "^5.2.0", "jest-snapshot": "^23.6.0", "prettier": "2.2.1", "puppeteer": "^11.0.0", "rollup": "^2.68.0", "rollup-plugin-esbuild": "^4.9.1", "rollup-plugin-postcss": "^3.1.1", "rollup-plugin-rename-node-modules": "^1.3.1", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-web-worker-loader": "^1.6.1", "simple-peer-light": "^9.10.0", "ts-jest": "^27.1.3", "ts-node": "^10.9.1", "tslib": "^2.3.1", "typescript": "^4.7.3"}, "dependencies": {"@rrweb/types": "^2.0.0-alpha.4", "@types/css-font-loading-module": "0.0.7", "@xstate/fsm": "^1.4.0", "base64-arraybuffer": "^1.0.1", "fflate": "^0.4.4", "mitt": "^3.0.0", "rrdom": "^0.1.7", "rrweb-snapshot": "^2.0.0-alpha.4"}}