var rrwebSnapshot=function(e){"use strict";var t;function r(e){return e.nodeType===e.ELEMENT_NODE}function n(e){var t=null==e?void 0:e.host;return Boolean((null==t?void 0:t.shadowRoot)===e)}function a(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function o(e){try{var t=e.rules||e.cssRules;return t?((r=Array.from(t).map(i).join("")).includes(" background-clip: text;")&&!r.includes(" -webkit-background-clip: text;")&&(r=r.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),r):null}catch(e){return null}var r}function i(e){var t=e.cssText;if(s(e))try{t=o(e.styleSheet)||t}catch(e){}return t}function s(e){return"styleSheet"in e}e.NodeType=void 0,(t=e.NodeType||(e.NodeType={}))[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment";var c=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){var t;if(!e)return-1;var r=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=r?r:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,r=this.getId(e);this.idNodeMap.delete(r),e.childNodes&&e.childNodes.forEach((function(e){return t.removeNodeFromMap(e)}))},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){var r=this.getNode(e);if(r){var n=this.nodeMetaMap.get(r);n&&this.nodeMetaMap.set(t,n)}this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function l(e){var t=e.maskInputOptions,r=e.tagName,n=e.type,a=e.value,o=e.maskInputFn,i=a||"";return(t[r.toLowerCase()]||t[n])&&(i=o?o(i):"*".repeat(i.length)),i}var u="__rrweb_original__";function d(e){var t=e.getContext("2d");if(!t)return!0;for(var r=0;r<e.width;r+=50)for(var n=0;n<e.height;n+=50){var a=t.getImageData,o=u in a?a.__rrweb_original__:a;if(new Uint32Array(o.call(t,r,n,Math.min(50,e.width-r),Math.min(50,e.height-n)).data.buffer).some((function(e){return 0!==e})))return!1}return!0}var p,f,m=1,h=new RegExp("[^a-z0-9-_:]");function v(){return m++}var y=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,g=/^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/|#).*/,k=/^(data:)([^,]*),(.*)/i;function T(e,t){return(e||"").replace(y,(function(e,r,n,a,o,i){var s,c=n||o||i,l=r||a||"";if(!c)return e;if(!g.test(c))return"url(".concat(l).concat(c).concat(l,")");if(k.test(c))return"url(".concat(l).concat(c).concat(l,")");if("/"===c[0])return"url(".concat(l).concat((s=t,(s.indexOf("//")>-1?s.split("/").slice(0,3).join("/"):s.split("/")[0]).split("?")[0]+c)).concat(l,")");var u=t.split("/"),d=c.split("/");u.pop();for(var p=0,f=d;p<f.length;p++){var m=f[p];"."!==m&&(".."===m?u.pop():u.push(m))}return"url(".concat(l).concat(u.join("/")).concat(l,")")}))}var b=/^[^ \t\n\r\u000c]+/,N=/^[, \t\n\r\u000c]+/;function S(e,t){if(!t||""===t.trim())return t;var r=e.createElement("a");return r.href=t,r.href}function C(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function w(){var e=document.createElement("a");return e.href="",e.href}function x(e,t,r,n){return"src"===r||"href"===r&&n&&("use"!==t||"#"!==n[0])||"xlink:href"===r&&n&&"#"!==n[0]?S(e,n):"background"!==r||!n||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===r&&n?function(e,t){if(""===t.trim())return t;var r=0;function n(e){var n,a=e.exec(t.substring(r));return a?(n=a[0],r+=n.length,n):""}for(var a=[];n(N),!(r>=t.length);){var o=n(b);if(","===o.slice(-1))o=S(e,o.substring(0,o.length-1)),a.push(o);else{var i="";o=S(e,o);for(var s=!1;;){var c=t.charAt(r);if(""===c){a.push((o+i).trim());break}if(s)")"===c&&(s=!1);else{if(","===c){r+=1,a.push((o+i).trim());break}"("===c&&(s=!0)}i+=c,r+=1}}}return a.join(", ")}(e,n):"style"===r&&n?T(n,w()):"object"===t&&"data"===r&&n?S(e,n):n:S(e,n)}function I(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!r&&I(e.parentNode,t,r);for(var n=e.classList.length;n--;){var a=e.classList[n];if(t.test(a))return!0}return!!r&&I(e.parentNode,t,r)}function E(e,t,r){var n=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===n)return!1;if("string"==typeof t){if(n.classList.contains(t))return!0;if(n.closest(".".concat(t)))return!0}else if(I(n,t,!0))return!0;if(r){if(n.matches(r))return!0;if(n.closest(r))return!0}return!1}function L(t,r){var n=r.doc,a=r.mirror,i=r.blockClass,s=r.blockSelector,c=r.maskTextClass,u=r.maskTextSelector,m=r.inlineStylesheet,v=r.maskInputOptions,y=void 0===v?{}:v,g=r.maskTextFn,k=r.maskInputFn,b=r.dataURLOptions,N=void 0===b?{}:b,S=r.inlineImages,I=r.recordCanvas,L=r.keepIframeSrcFn,M=r.newlyAddedElement,O=void 0!==M&&M,D=function(e,t){if(!t.hasNode(e))return;var r=t.getId(e);return 1===r?void 0:r}(n,a);switch(t.nodeType){case t.DOCUMENT_NODE:return"CSS1Compat"!==t.compatMode?{type:e.NodeType.Document,childNodes:[],compatMode:t.compatMode}:{type:e.NodeType.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:e.NodeType.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:D};case t.ELEMENT_NODE:return function(t,r){for(var n=r.doc,a=r.blockClass,i=r.blockSelector,s=r.inlineStylesheet,c=r.maskInputOptions,u=void 0===c?{}:c,m=r.maskInputFn,v=r.dataURLOptions,y=void 0===v?{}:v,g=r.inlineImages,k=r.recordCanvas,b=r.keepIframeSrcFn,N=r.newlyAddedElement,S=void 0!==N&&N,I=r.rootId,E=function(e,t,r){if("string"==typeof t){if(e.classList.contains(t))return!0}else for(var n=e.classList.length;n--;){var a=e.classList[n];if(t.test(a))return!0}return!!r&&e.matches(r)}(t,a,i),L=function(e){if(e instanceof HTMLFormElement)return"form";var t=e.tagName.toLowerCase().trim();return h.test(t)?"div":t}(t),M={},O=t.attributes.length,D=0;D<O;D++){var _=t.attributes[D];M[_.name]=x(n,L,_.name,_.value)}if("link"===L&&s){var R=Array.from(n.styleSheets).find((function(e){return e.href===t.href})),A=null;R&&(A=o(R)),A&&(delete M.rel,delete M.href,M._cssText=T(A,R.href))}if("style"===L&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){(A=o(t.sheet))&&(M._cssText=T(A,w()))}if("input"===L||"textarea"===L||"select"===L){var F=t.value,U=t.checked;"radio"!==M.type&&"checkbox"!==M.type&&"submit"!==M.type&&"button"!==M.type&&F?M.value=l({type:M.type,tagName:L,value:F,maskInputOptions:u,maskInputFn:m}):U&&(M.checked=U)}"option"===L&&(t.selected&&!u.select?M.selected=!0:delete M.selected);if("canvas"===L&&k)if("2d"===t.__context)d(t)||(M.rr_dataURL=t.toDataURL(y.type,y.quality));else if(!("__context"in t)){var W=t.toDataURL(y.type,y.quality),j=document.createElement("canvas");j.width=t.width,j.height=t.height,W!==j.toDataURL(y.type,y.quality)&&(M.rr_dataURL=W)}if("img"===L&&g){p||(p=n.createElement("canvas"),f=p.getContext("2d"));var P=t,B=P.crossOrigin;P.crossOrigin="anonymous";var H=function(){try{p.width=P.naturalWidth,p.height=P.naturalHeight,f.drawImage(P,0,0),M.rr_dataURL=p.toDataURL(y.type,y.quality)}catch(e){console.warn("Cannot inline img src=".concat(P.currentSrc,"! Error: ").concat(e))}B?M.crossOrigin=B:P.removeAttribute("crossorigin")};P.complete&&0!==P.naturalWidth?H():P.onload=H}"audio"!==L&&"video"!==L||(M.rr_mediaState=t.paused?"paused":"played",M.rr_mediaCurrentTime=t.currentTime);S||(t.scrollLeft&&(M.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(M.rr_scrollTop=t.scrollTop));if(E){var z=t.getBoundingClientRect(),G=z.width,q=z.height;M={class:M.class,rr_width:"".concat(G,"px"),rr_height:"".concat(q,"px")}}"iframe"!==L||b(M.src)||(t.contentDocument||(M.rr_src=M.src),delete M.src);return{type:e.NodeType.Element,tagName:L,attributes:M,childNodes:[],isSVG:C(t)||void 0,needBlock:E,rootId:I}}(t,{doc:n,blockClass:i,blockSelector:s,inlineStylesheet:m,maskInputOptions:y,maskInputFn:k,dataURLOptions:N,inlineImages:S,recordCanvas:I,keepIframeSrcFn:L,newlyAddedElement:O,rootId:D});case t.TEXT_NODE:return function(t,r){var n,a=r.maskTextClass,o=r.maskTextSelector,i=r.maskTextFn,s=r.rootId,c=t.parentNode&&t.parentNode.tagName,l=t.textContent,u="STYLE"===c||void 0,d="SCRIPT"===c||void 0;if(u&&l){try{t.nextSibling||t.previousSibling||(null===(n=t.parentNode.sheet)||void 0===n?void 0:n.cssRules)&&(l=(p=t.parentNode.sheet).cssRules?Array.from(p.cssRules).map((function(e){return e.cssText||""})).join(""):"")}catch(e){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(e),t)}l=T(l,w())}var p;d&&(l="SCRIPT_PLACEHOLDER");!u&&!d&&l&&E(t,a,o)&&(l=i?i(l):l.replace(/[\S]/g,"*"));return{type:e.NodeType.Text,textContent:l||"",isStyle:u,rootId:s}}(t,{maskTextClass:c,maskTextSelector:u,maskTextFn:g,rootId:D});case t.CDATA_SECTION_NODE:return{type:e.NodeType.CDATA,textContent:"",rootId:D};case t.COMMENT_NODE:return{type:e.NodeType.Comment,textContent:t.textContent||"",rootId:D};default:return!1}}function M(e){return void 0===e?"":e.toLowerCase()}function O(t,o){var i,s=o.doc,c=o.mirror,l=o.blockClass,u=o.blockSelector,d=o.maskTextClass,p=o.maskTextSelector,f=o.skipChild,m=void 0!==f&&f,h=o.inlineStylesheet,y=void 0===h||h,g=o.maskInputOptions,k=void 0===g?{}:g,T=o.maskTextFn,b=o.maskInputFn,N=o.slimDOMOptions,S=o.dataURLOptions,C=void 0===S?{}:S,w=o.inlineImages,x=void 0!==w&&w,I=o.recordCanvas,E=void 0!==I&&I,D=o.onSerialize,_=o.onIframeLoad,R=o.iframeLoadTimeout,A=void 0===R?5e3:R,F=o.onStylesheetLoad,U=o.stylesheetLoadTimeout,W=void 0===U?5e3:U,j=o.keepIframeSrcFn,P=void 0===j?function(){return!1}:j,B=o.newlyAddedElement,H=void 0!==B&&B,z=o.preserveWhiteSpace,G=void 0===z||z,q=L(t,{doc:s,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:p,inlineStylesheet:y,maskInputOptions:k,maskTextFn:T,maskInputFn:b,dataURLOptions:C,inlineImages:x,recordCanvas:E,keepIframeSrcFn:P,newlyAddedElement:H});if(!q)return console.warn(t,"not serialized"),null;i=c.hasNode(t)?c.getId(t):!function(t,r){if(r.comment&&t.type===e.NodeType.Comment)return!0;if(t.type===e.NodeType.Element){if(r.script&&("script"===t.tagName||"link"===t.tagName&&"preload"===t.attributes.rel&&"script"===t.attributes.as||"link"===t.tagName&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&t.attributes.href.endsWith(".js")))return!0;if(r.headFavicon&&("link"===t.tagName&&"shortcut icon"===t.attributes.rel||"meta"===t.tagName&&(M(t.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===M(t.attributes.name)||"icon"===M(t.attributes.rel)||"apple-touch-icon"===M(t.attributes.rel)||"shortcut icon"===M(t.attributes.rel))))return!0;if("meta"===t.tagName){if(r.headMetaDescKeywords&&M(t.attributes.name).match(/^description|keywords$/))return!0;if(r.headMetaSocial&&(M(t.attributes.property).match(/^(og|twitter|fb):/)||M(t.attributes.name).match(/^(og|twitter):/)||"pinterest"===M(t.attributes.name)))return!0;if(r.headMetaRobots&&("robots"===M(t.attributes.name)||"googlebot"===M(t.attributes.name)||"bingbot"===M(t.attributes.name)))return!0;if(r.headMetaHttpEquiv&&void 0!==t.attributes["http-equiv"])return!0;if(r.headMetaAuthorship&&("author"===M(t.attributes.name)||"generator"===M(t.attributes.name)||"framework"===M(t.attributes.name)||"publisher"===M(t.attributes.name)||"progid"===M(t.attributes.name)||M(t.attributes.property).match(/^article:/)||M(t.attributes.property).match(/^product:/)))return!0;if(r.headMetaVerification&&("google-site-verification"===M(t.attributes.name)||"yandex-verification"===M(t.attributes.name)||"csrf-token"===M(t.attributes.name)||"p:domain_verify"===M(t.attributes.name)||"verify-v1"===M(t.attributes.name)||"verification"===M(t.attributes.name)||"shopify-checkout-api-token"===M(t.attributes.name)))return!0}}return!1}(q,N)&&(G||q.type!==e.NodeType.Text||q.isStyle||q.textContent.replace(/^\s+|\s+$/gm,"").length)?v():-2;var V=Object.assign(q,{id:i});if(c.add(t,V),-2===i)return null;D&&D(t);var $=!m;if(V.type===e.NodeType.Element){$=$&&!V.needBlock,delete V.needBlock;var Y=t.shadowRoot;Y&&a(Y)&&(V.isShadowHost=!0)}if((V.type===e.NodeType.Document||V.type===e.NodeType.Element)&&$){N.headWhitespace&&V.type===e.NodeType.Element&&"head"===V.tagName&&(G=!1);for(var X={doc:s,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:p,skipChild:m,inlineStylesheet:y,maskInputOptions:k,maskTextFn:T,maskInputFn:b,slimDOMOptions:N,dataURLOptions:C,inlineImages:x,recordCanvas:E,preserveWhiteSpace:G,onSerialize:D,onIframeLoad:_,iframeLoadTimeout:A,onStylesheetLoad:F,stylesheetLoadTimeout:W,keepIframeSrcFn:P},K=0,Z=Array.from(t.childNodes);K<Z.length;K++){(ee=O(Z[K],X))&&V.childNodes.push(ee)}if(r(t)&&t.shadowRoot)for(var J=0,Q=Array.from(t.shadowRoot.childNodes);J<Q.length;J++){var ee;(ee=O(Q[J],X))&&(a(t.shadowRoot)&&(ee.isShadow=!0),V.childNodes.push(ee))}}return t.parentNode&&n(t.parentNode)&&a(t.parentNode)&&(V.isShadow=!0),V.type===e.NodeType.Element&&"iframe"===V.tagName&&function(e,t,r){var n=e.contentWindow;if(n){var a,o=!1;try{a=n.document.readyState}catch(e){return}if("complete"===a){var i="about:blank";if(n.location.href!==i||e.src===i||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}else{var s=setTimeout((function(){o||(t(),o=!0)}),r);e.addEventListener("load",(function(){clearTimeout(s),o=!0,t()}))}}}(t,(function(){var e=t.contentDocument;if(e&&_){var r=O(e,{doc:e,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:p,skipChild:!1,inlineStylesheet:y,maskInputOptions:k,maskTextFn:T,maskInputFn:b,slimDOMOptions:N,dataURLOptions:C,inlineImages:x,recordCanvas:E,preserveWhiteSpace:G,onSerialize:D,onIframeLoad:_,iframeLoadTimeout:A,onStylesheetLoad:F,stylesheetLoadTimeout:W,keepIframeSrcFn:P});r&&_(t,r)}}),A),V.type===e.NodeType.Element&&"link"===V.tagName&&"stylesheet"===V.attributes.rel&&function(e,t,r){var n,a=!1;try{n=e.sheet}catch(e){return}if(!n){var o=setTimeout((function(){a||(t(),a=!0)}),r);e.addEventListener("load",(function(){clearTimeout(o),a=!0,t()}))}}(t,(function(){if(F){var e=O(t,{doc:s,mirror:c,blockClass:l,blockSelector:u,maskTextClass:d,maskTextSelector:p,skipChild:!1,inlineStylesheet:y,maskInputOptions:k,maskTextFn:T,maskInputFn:b,slimDOMOptions:N,dataURLOptions:C,inlineImages:x,recordCanvas:E,preserveWhiteSpace:G,onSerialize:D,onIframeLoad:_,iframeLoadTimeout:A,onStylesheetLoad:F,stylesheetLoadTimeout:W,keepIframeSrcFn:P});e&&F(t,e)}}),W),V}var D=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g;function _(e,t){void 0===t&&(t={});var r=1,n=1;function a(e){var t=e.match(/\n/g);t&&(r+=t.length);var a=e.lastIndexOf("\n");n=-1===a?n+e.length:e.length-a}function o(){var e={line:r,column:n};return function(t){return t.position=new i(e),f(),t}}var i=function(e){this.start=e,this.end={line:r,column:n},this.source=t.source};i.prototype.content=e;var s=[];function c(a){var o=new Error("".concat(t.source||"",":").concat(r,":").concat(n,": ").concat(a));if(o.reason=a,o.filename=t.source,o.line=r,o.column=n,o.source=e,!t.silent)throw o;s.push(o)}function l(){return p(/^{\s*/)}function u(){return p(/^}/)}function d(){var t,r=[];for(f(),m(r);e.length&&"}"!==e.charAt(0)&&(t=w()||x());)!1!==t&&(r.push(t),m(r));return r}function p(t){var r=t.exec(e);if(r){var n=r[0];return a(n),e=e.slice(n.length),r}}function f(){p(/^\s*/)}function m(e){var t;for(void 0===e&&(e=[]);t=h();)!1!==t&&e.push(t),t=h();return e}function h(){var t=o();if("/"===e.charAt(0)&&"*"===e.charAt(1)){for(var r=2;""!==e.charAt(r)&&("*"!==e.charAt(r)||"/"!==e.charAt(r+1));)++r;if(r+=2,""===e.charAt(r-1))return c("End of comment missing");var i=e.slice(2,r-2);return n+=2,a(i),e=e.slice(r),n+=2,t({type:"comment",comment:i})}}function v(){var e=p(/^([^{]+)/);if(e)return R(e[0]).replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g,"").replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,(function(e){return e.replace(/,/g,"‌")})).split(/\s*(?![^(]*\)),\s*/).map((function(e){return e.replace(/\u200C/g,",")}))}function y(){var e=o(),t=p(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);if(t){var r=R(t[0]);if(!p(/^:\s*/))return c("property missing ':'");var n=p(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/),a=e({type:"declaration",property:r.replace(D,""),value:n?R(n[0]).replace(D,""):""});return p(/^[;\s]*/),a}}function g(){var e,t=[];if(!l())return c("missing '{'");for(m(t);e=y();)!1!==e&&(t.push(e),m(t)),e=y();return u()?t:c("missing '}'")}function k(){for(var e,t=[],r=o();e=p(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)t.push(e[1]),p(/^,\s*/);if(t.length)return r({type:"keyframe",values:t,declarations:g()})}var T,b=C("import"),N=C("charset"),S=C("namespace");function C(e){var t=new RegExp("^@"+e+"\\s*([^;]+);");return function(){var r=o(),n=p(t);if(n){var a={type:e};return a[e]=n[1].trim(),r(a)}}}function w(){if("@"===e[0])return function(){var e=o(),t=p(/^@([-\w]+)?keyframes\s*/);if(t){var r=t[1];if(!(t=p(/^([-\w]+)\s*/)))return c("@keyframes missing name");var n,a=t[1];if(!l())return c("@keyframes missing '{'");for(var i=m();n=k();)i.push(n),i=i.concat(m());return u()?e({type:"keyframes",name:a,vendor:r,keyframes:i}):c("@keyframes missing '}'")}}()||function(){var e=o(),t=p(/^@media *([^{]+)/);if(t){var r=R(t[1]);if(!l())return c("@media missing '{'");var n=m().concat(d());return u()?e({type:"media",media:r,rules:n}):c("@media missing '}'")}}()||function(){var e=o(),t=p(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);if(t)return e({type:"custom-media",name:R(t[1]),media:R(t[2])})}()||function(){var e=o(),t=p(/^@supports *([^{]+)/);if(t){var r=R(t[1]);if(!l())return c("@supports missing '{'");var n=m().concat(d());return u()?e({type:"supports",supports:r,rules:n}):c("@supports missing '}'")}}()||b()||N()||S()||function(){var e=o(),t=p(/^@([-\w]+)?document *([^{]+)/);if(t){var r=R(t[1]),n=R(t[2]);if(!l())return c("@document missing '{'");var a=m().concat(d());return u()?e({type:"document",document:n,vendor:r,rules:a}):c("@document missing '}'")}}()||function(){var e=o();if(p(/^@page */)){var t=v()||[];if(!l())return c("@page missing '{'");for(var r,n=m();r=y();)n.push(r),n=n.concat(m());return u()?e({type:"page",selectors:t,declarations:n}):c("@page missing '}'")}}()||function(){var e=o();if(p(/^@host\s*/)){if(!l())return c("@host missing '{'");var t=m().concat(d());return u()?e({type:"host",rules:t}):c("@host missing '}'")}}()||function(){var e=o();if(p(/^@font-face\s*/)){if(!l())return c("@font-face missing '{'");for(var t,r=m();t=y();)r.push(t),r=r.concat(m());return u()?e({type:"font-face",declarations:r}):c("@font-face missing '}'")}}()}function x(){var e=o(),t=v();return t?(m(),e({type:"rule",selectors:t,declarations:g()})):c("selector missing")}return A((T=d(),{type:"stylesheet",stylesheet:{source:t.source,rules:T,parsingErrors:s}}))}function R(e){return e?e.replace(/^\s+|\s+$/g,""):""}function A(e,t){for(var r=e&&"string"==typeof e.type,n=r?e:t,a=0,o=Object.keys(e);a<o.length;a++){var i=e[o[a]];Array.isArray(i)?i.forEach((function(e){A(e,n)})):i&&"object"==typeof i&&A(i,n)}return r&&Object.defineProperty(e,"parent",{configurable:!0,writable:!0,enumerable:!1,value:t||null}),e}var F={script:"noscript",altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};var U=/([^\\]):hover/,W=new RegExp(U.source,"g");function j(e,t){var r=null==t?void 0:t.stylesWithHoverClass.get(e);if(r)return r;var n=_(e,{silent:!0});if(!n.stylesheet)return e;var a=[];if(n.stylesheet.rules.forEach((function(e){"selectors"in e&&(e.selectors||[]).forEach((function(e){U.test(e)&&a.push(e)}))})),0===a.length)return e;var o=new RegExp(a.filter((function(e,t){return a.indexOf(e)===t})).sort((function(e,t){return t.length-e.length})).map((function(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")})).join("|"),"g"),i=e.replace(o,(function(e){var t=e.replace(W,"$1.\\:hover");return"".concat(e,", ").concat(t)}));return null==t||t.stylesWithHoverClass.set(e,i),i}function P(t,r){var n=r.doc,a=r.hackCss,o=r.cache;switch(t.type){case e.NodeType.Document:return n.implementation.createDocument(null,"",null);case e.NodeType.DocumentType:return n.implementation.createDocumentType(t.name||"html",t.publicId,t.systemId);case e.NodeType.Element:var i,s=function(e){var t=F[e.tagName]?F[e.tagName]:e.tagName;return"link"===t&&e.attributes._cssText&&(t="style"),t}(t);i=t.isSVG?n.createElementNS("http://www.w3.org/2000/svg",s):n.createElement(s);var c={};for(var l in t.attributes)if(Object.prototype.hasOwnProperty.call(t.attributes,l)){var u=t.attributes[l];if("option"!==s||"selected"!==l||!1!==u)if(!0===u&&(u=""),l.startsWith("rr_"))c[l]=u;else{var d="textarea"===s&&"value"===l,p="style"===s&&"_cssText"===l;if(p&&a&&"string"==typeof u&&(u=j(u,o)),!d&&!p||"string"!=typeof u)try{if(t.isSVG&&"xlink:href"===l)i.setAttributeNS("http://www.w3.org/1999/xlink",l,u.toString());else if("onload"===l||"onclick"===l||"onmouse"===l.substring(0,7))i.setAttribute("_"+l,u.toString());else{if("meta"===s&&"Content-Security-Policy"===t.attributes["http-equiv"]&&"content"===l){i.setAttribute("csp-content",u.toString());continue}"link"===s&&"preload"===t.attributes.rel&&"script"===t.attributes.as||"link"===s&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&t.attributes.href.endsWith(".js")||("img"===s&&t.attributes.srcset&&t.attributes.rr_dataURL?i.setAttribute("rrweb-original-srcset",t.attributes.srcset):i.setAttribute(l,u.toString()))}}catch(e){}else{for(var f=n.createTextNode(u),m=0,h=Array.from(i.childNodes);m<h.length;m++){var v=h[m];v.nodeType===i.TEXT_NODE&&i.removeChild(v)}i.appendChild(f)}}}var y=function(e){var r=c[e];if("canvas"===s&&"rr_dataURL"===e){var n=document.createElement("img");n.onload=function(){var e=i.getContext("2d");e&&e.drawImage(n,0,0,n.width,n.height)},n.src=r.toString(),i.RRNodeType&&(i.rr_dataURL=r.toString())}else if("img"===s&&"rr_dataURL"===e){var a=i;a.currentSrc.startsWith("data:")||(a.setAttribute("rrweb-original-src",t.attributes.src),a.src=r.toString())}if("rr_width"===e)i.style.width=r.toString();else if("rr_height"===e)i.style.height=r.toString();else if("rr_mediaCurrentTime"===e&&"number"==typeof r)i.currentTime=r;else if("rr_mediaState"===e)switch(r){case"played":i.play().catch((function(e){return console.warn("media playback error",e)}));break;case"paused":i.pause()}};for(var g in c)y(g);if(t.isShadowHost)if(i.shadowRoot)for(;i.shadowRoot.firstChild;)i.shadowRoot.removeChild(i.shadowRoot.firstChild);else i.attachShadow({mode:"open"});return i;case e.NodeType.Text:return n.createTextNode(t.isStyle&&a?j(t.textContent,o):t.textContent);case e.NodeType.CDATA:return n.createCDATASection(t.textContent);case e.NodeType.Comment:return n.createComment(t.textContent);default:return null}}function B(t,n){var a=n.doc,o=n.mirror,i=n.skipChild,s=void 0!==i&&i,c=n.hackCss,l=void 0===c||c,u=n.afterAppend,d=n.cache,p=P(t,{doc:a,hackCss:l,cache:d});if(!p)return null;if(t.rootId&&o.getNode(t.rootId)!==a&&o.replace(t.rootId,a),t.type===e.NodeType.Document&&(a.close(),a.open(),"BackCompat"===t.compatMode&&t.childNodes&&t.childNodes[0].type!==e.NodeType.DocumentType&&(t.childNodes[0].type===e.NodeType.Element&&"xmlns"in t.childNodes[0].attributes&&"http://www.w3.org/1999/xhtml"===t.childNodes[0].attributes.xmlns?a.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'):a.write('<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">')),p=a),o.add(p,t),(t.type===e.NodeType.Document||t.type===e.NodeType.Element)&&!s)for(var f=0,m=t.childNodes;f<m.length;f++){var h=m[f],v=B(h,{doc:a,mirror:o,skipChild:!1,hackCss:l,afterAppend:u,cache:d});v?(h.isShadow&&r(p)&&p.shadowRoot?p.shadowRoot.appendChild(v):p.appendChild(v),u&&u(v,h.id)):console.warn("Failed to rebuild",h)}return p}return e.IGNORED_NODE=-2,e.Mirror=c,e.addHoverClass=j,e.buildNodeWithSN=B,e.classMatchesRegex=I,e.cleanupSnapshot=function(){m=1},e.createCache=function(){return{stylesWithHoverClass:new Map}},e.createMirror=function(){return new c},e.genId=v,e.getCssRuleString=i,e.getCssRulesString=o,e.is2DCanvasBlank=d,e.isCSSImportRule=s,e.isElement=r,e.isNativeShadowDom=a,e.isShadowRoot=n,e.maskInputValue=l,e.needMaskingText=E,e.rebuild=function(t,r){var n=r.doc,a=r.onVisit,o=r.hackCss,i=void 0===o||o,s=r.afterAppend,l=r.cache,u=r.mirror,d=void 0===u?new c:u,p=B(t,{doc:n,mirror:d,skipChild:!1,hackCss:i,afterAppend:s,cache:l});return function(e,t){for(var r=0,n=e.getIds();r<n.length;r++){var a=n[r];e.has(a)&&t(e.getNode(a))}}(d,(function(t){a&&a(t),function(t,r){var n=r.getMeta(t);if((null==n?void 0:n.type)===e.NodeType.Element){var a=t;for(var o in n.attributes)if(Object.prototype.hasOwnProperty.call(n.attributes,o)&&o.startsWith("rr_")){var i=n.attributes[o];"rr_scrollLeft"===o&&(a.scrollLeft=i),"rr_scrollTop"===o&&(a.scrollTop=i)}}}(t,d)})),p},e.serializeNodeWithId=O,e.snapshot=function(e,t){var r=t||{},n=r.mirror,a=void 0===n?new c:n,o=r.blockClass,i=void 0===o?"rr-block":o,s=r.blockSelector,l=void 0===s?null:s,u=r.maskTextClass,d=void 0===u?"rr-mask":u,p=r.maskTextSelector,f=void 0===p?null:p,m=r.inlineStylesheet,h=void 0===m||m,v=r.inlineImages,y=void 0!==v&&v,g=r.recordCanvas,k=void 0!==g&&g,T=r.maskAllInputs,b=void 0!==T&&T,N=r.maskTextFn,S=r.maskInputFn,C=r.slimDOM,w=void 0!==C&&C,x=r.dataURLOptions,I=r.preserveWhiteSpace,E=r.onSerialize,L=r.onIframeLoad,M=r.iframeLoadTimeout,D=r.onStylesheetLoad,_=r.stylesheetLoadTimeout,R=r.keepIframeSrcFn;return O(e,{doc:e,mirror:a,blockClass:i,blockSelector:l,maskTextClass:d,maskTextSelector:f,skipChild:!1,inlineStylesheet:h,maskInputOptions:!0===b?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===b?{password:!0}:b,maskTextFn:N,maskInputFn:S,slimDOMOptions:!0===w||"all"===w?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===w,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===w?{}:w,dataURLOptions:x,inlineImages:y,recordCanvas:k,preserveWhiteSpace:I,onSerialize:E,onIframeLoad:L,iframeLoadTimeout:M,onStylesheetLoad:D,stylesheetLoadTimeout:_,keepIframeSrcFn:void 0===R?function(){return!1}:R,newlyAddedElement:!1})},e.transformAttribute=x,e.visitSnapshot=function(t,r){!function t(n){r(n),n.type!==e.NodeType.Document&&n.type!==e.NodeType.Element||n.childNodes.forEach(t)}(t)},Object.defineProperty(e,"__esModule",{value:!0}),e}({});
//# sourceMappingURL=rrweb-snapshot.min.js.map
