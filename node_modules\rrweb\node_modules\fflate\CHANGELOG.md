## 0.4.8
- Support strict Content Security Policy
  - Remove `new Function`
## 0.4.7
- Fix data streaming bugs
## 0.4.5
- Zip64 support
  - Still not possible to have above 4GB files
## 0.4.4
- Files up to 4GB supported
  - Hey, that's better than even Node.js `zlib`!
## 0.4.1
- Fix ZIP failure bug
- Make ZIP options work better
- Improve docs
- Fix async inflate failure
- Work on Rust version
## 0.3.11
- Fix docs
## 0.3.9
- Fixed issue with unzipping
## 0.3.7
- Patched streaming compression bugs
- Added demo page
## 0.3.6
- Allowed true ESM imports
## 0.3.4
- Fixed rare overflow bug causing corruption
- Added async stream termination
- Added UMD bundle
## 0.3.0
- Added support for asynchronous and synchronous streaming
- Reduced bundle size by autogenerating worker code, even in minified environments
- Error detection rather than hanging
- Improved performance
## 0.2.3
- Improved Zlib autodetection
## 0.2.2
- Fixed Node Worker
## 0.2.1
- Fixed ZIP bug
## 0.2.0
- Added support for ZIP files (parallelized)
- Added ability to terminate running asynchronous operations
## 0.1.0
- Rewrote API: added support for asynchronous (Worker) compression/decompression, fixed critical bug involving fixed <PERSON>ffman trees
## 0.0.1
- Created, works on basic input