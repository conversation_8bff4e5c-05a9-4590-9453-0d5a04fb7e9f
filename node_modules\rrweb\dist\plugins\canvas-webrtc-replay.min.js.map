{"version": 3, "file": "canvas-webrtc-replay.min.js", "sources": ["../../../../node_modules/simple-peer-light/index.js", "../../src/plugins/canvas-webrtc/replay/index.ts"], "sourcesContent": ["/*! simple-peer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nconst MAX_BUFFERED_AMOUNT = 64 * 1024\nconst ICECOMPLETE_TIMEOUT = 5 * 1000\nconst CHANNEL_CLOSING_TIMEOUT = 5 * 1000\n\nfunction randombytes (size) {\n  const array = new Uint8Array(size)\n  for (let i = 0; i < size; i++) {\n    array[i] = (Math.random() * 256) | 0\n  }\n  return array\n}\n\nfunction getBrowserRTC () {\n  if (typeof globalThis === 'undefined') return null\n  const wrtc = {\n    RTCPeerConnection:\n      globalThis.RTCPeerConnection ||\n      globalThis.mozRTCPeerConnection ||\n      globalThis.webkitRTCPeerConnection,\n    RTCSessionDescription:\n      globalThis.RTCSessionDescription ||\n      globalThis.mozRTCSessionDescription ||\n      globalThis.webkitRTCSessionDescription,\n    RTCIceCandidate:\n      globalThis.RTCIceCandidate ||\n      globalThis.mozRTCIceCandidate ||\n      globalThis.webkitRTCIceCandidate\n  }\n  if (!wrtc.RTCPeerConnection) return null\n  return wrtc\n}\n\nfunction errCode (err, code) {\n  Object.defineProperty(err, 'code', {\n    value: code,\n    enumerable: true,\n    configurable: true\n  })\n  return err\n}\n\n// HACK: Filter trickle lines when trickle is disabled #354\nfunction filterTrickle (sdp) {\n  return sdp.replace(/a=ice-options:trickle\\s\\n/g, '')\n}\n\nfunction warn (message) {\n  console.warn(message)\n}\n\n/**\n * WebRTC peer connection.\n * @param {Object} opts\n */\nclass Peer {\n  constructor (opts = {}) {\n    this._map = new Map() // for event emitter\n\n    this._id = randombytes(4).toString('hex').slice(0, 7)\n    this._doDebug = opts.debug\n    this._debug('new peer %o', opts)\n\n    this.channelName = opts.initiator\n      ? opts.channelName || randombytes(20).toString('hex')\n      : null\n\n    this.initiator = opts.initiator || false\n    this.channelConfig = opts.channelConfig || Peer.channelConfig\n    this.channelNegotiated = this.channelConfig.negotiated\n    this.config = Object.assign({}, Peer.config, opts.config)\n    this.offerOptions = opts.offerOptions || {}\n    this.answerOptions = opts.answerOptions || {}\n    this.sdpTransform = opts.sdpTransform || (sdp => sdp)\n    this.streams = opts.streams || (opts.stream ? [opts.stream] : []) // support old \"stream\" option\n    this.trickle = opts.trickle !== undefined ? opts.trickle : true\n    this.allowHalfTrickle =\n      opts.allowHalfTrickle !== undefined ? opts.allowHalfTrickle : false\n    this.iceCompleteTimeout = opts.iceCompleteTimeout || ICECOMPLETE_TIMEOUT\n\n    this.destroyed = false\n    this.destroying = false\n    this._connected = false\n\n    this.remoteAddress = undefined\n    this.remoteFamily = undefined\n    this.remotePort = undefined\n    this.localAddress = undefined\n    this.localFamily = undefined\n    this.localPort = undefined\n\n    this._wrtc =\n      opts.wrtc && typeof opts.wrtc === 'object' ? opts.wrtc : getBrowserRTC()\n\n    if (!this._wrtc) {\n      if (typeof window === 'undefined') {\n        throw errCode(\n          new Error(\n            'No WebRTC support: Specify `opts.wrtc` option in this environment'\n          ),\n          'ERR_WEBRTC_SUPPORT'\n        )\n      } else {\n        throw errCode(\n          new Error('No WebRTC support: Not a supported browser'),\n          'ERR_WEBRTC_SUPPORT'\n        )\n      }\n    }\n\n    this._pcReady = false\n    this._channelReady = false\n    this._iceComplete = false // ice candidate trickle done (got null candidate)\n    this._iceCompleteTimer = null // send an offer/answer anyway after some timeout\n    this._channel = null\n    this._pendingCandidates = []\n\n    this._isNegotiating = false // is this peer waiting for negotiation to complete?\n    this._firstNegotiation = true\n    this._batchedNegotiation = false // batch synchronous negotiations\n    this._queuedNegotiation = false // is there a queued negotiation request?\n    this._sendersAwaitingStable = []\n    this._senderMap = new Map()\n    this._closingInterval = null\n\n    this._remoteTracks = []\n    this._remoteStreams = []\n\n    this._chunk = null\n    this._cb = null\n    this._interval = null\n\n    try {\n      this._pc = new this._wrtc.RTCPeerConnection(this.config)\n    } catch (err) {\n      this.destroy(errCode(err, 'ERR_PC_CONSTRUCTOR'))\n      return\n    }\n\n    // We prefer feature detection whenever possible, but sometimes that's not\n    // possible for certain implementations.\n    this._isReactNativeWebrtc = typeof this._pc._peerConnectionId === 'number'\n\n    this._pc.oniceconnectionstatechange = () => {\n      this._onIceStateChange()\n    }\n    this._pc.onicegatheringstatechange = () => {\n      this._onIceStateChange()\n    }\n    this._pc.onconnectionstatechange = () => {\n      this._onConnectionStateChange()\n    }\n    this._pc.onsignalingstatechange = () => {\n      this._onSignalingStateChange()\n    }\n    this._pc.onicecandidate = event => {\n      this._onIceCandidate(event)\n    }\n\n    // HACK: Fix for odd Firefox behavior, see: https://github.com/feross/simple-peer/pull/783\n    if (typeof this._pc.peerIdentity === 'object') {\n      this._pc.peerIdentity.catch(err => {\n        this.destroy(errCode(err, 'ERR_PC_PEER_IDENTITY'))\n      })\n    }\n\n    // Other spec events, unused by this implementation:\n    // - onconnectionstatechange\n    // - onicecandidateerror\n    // - onfingerprintfailure\n    // - onnegotiationneeded\n\n    if (this.initiator || this.channelNegotiated) {\n      this._setupData({\n        channel: this._pc.createDataChannel(\n          this.channelName,\n          this.channelConfig\n        )\n      })\n    } else {\n      this._pc.ondatachannel = event => {\n        this._setupData(event)\n      }\n    }\n\n    if (this.streams) {\n      this.streams.forEach(stream => {\n        this.addStream(stream)\n      })\n    }\n    this._pc.ontrack = event => {\n      this._onTrack(event)\n    }\n\n    this._debug('initial negotiation')\n    this._needsNegotiation()\n  }\n\n  get bufferSize () {\n    return (this._channel && this._channel.bufferedAmount) || 0\n  }\n\n  // HACK: it's possible channel.readyState is \"closing\" before peer.destroy() fires\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=882743\n  get connected () {\n    return this._connected && this._channel.readyState === 'open'\n  }\n\n  address () {\n    return {\n      port: this.localPort,\n      family: this.localFamily,\n      address: this.localAddress\n    }\n  }\n\n  signal (data) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot signal after peer is destroyed'), 'ERR_DESTROYED')\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data)\n      } catch (err) {\n        data = {}\n      }\n    }\n    this._debug('signal()')\n\n    if (data.renegotiate && this.initiator) {\n      this._debug('got request to renegotiate')\n      this._needsNegotiation()\n    }\n    if (data.transceiverRequest && this.initiator) {\n      this._debug('got request for transceiver')\n      this.addTransceiver(\n        data.transceiverRequest.kind,\n        data.transceiverRequest.init\n      )\n    }\n    if (data.candidate) {\n      if (this._pc.remoteDescription && this._pc.remoteDescription.type) {\n        this._addIceCandidate(data.candidate)\n      } else {\n        this._pendingCandidates.push(data.candidate)\n      }\n    }\n    if (data.sdp) {\n      this._pc\n        .setRemoteDescription(new this._wrtc.RTCSessionDescription(data))\n        .then(() => {\n          if (this.destroyed) return\n\n          this._pendingCandidates.forEach(candidate => {\n            this._addIceCandidate(candidate)\n          })\n          this._pendingCandidates = []\n\n          if (this._pc.remoteDescription.type === 'offer') this._createAnswer()\n        })\n        .catch(err => {\n          this.destroy(errCode(err, 'ERR_SET_REMOTE_DESCRIPTION'))\n        })\n    }\n    if (\n      !data.sdp &&\n      !data.candidate &&\n      !data.renegotiate &&\n      !data.transceiverRequest\n    ) {\n      this.destroy(\n        errCode(\n          new Error('signal() called with invalid signal data'),\n          'ERR_SIGNALING'\n        )\n      )\n    }\n  }\n\n  _addIceCandidate (candidate) {\n    const iceCandidateObj = new this._wrtc.RTCIceCandidate(candidate)\n    this._pc.addIceCandidate(iceCandidateObj).catch(err => {\n      if (\n        !iceCandidateObj.address ||\n        iceCandidateObj.address.endsWith('.local')\n      ) {\n        warn('Ignoring unsupported ICE candidate.')\n      } else {\n        this.destroy(errCode(err, 'ERR_ADD_ICE_CANDIDATE'))\n      }\n    })\n  }\n\n  /**\n   * Send text/binary data to the remote peer.\n   * @param {ArrayBufferView|ArrayBuffer|string|Blob} chunk\n   */\n  send (chunk) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot send after peer is destroyed'), 'ERR_DESTROYED')\n    this._channel.send(chunk)\n  }\n\n  /**\n   * Add a Transceiver to the connection.\n   * @param {String} kind\n   * @param {Object} init\n   */\n  addTransceiver (kind, init) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot addTransceiver after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('addTransceiver()')\n\n    if (this.initiator) {\n      try {\n        this._pc.addTransceiver(kind, init)\n        this._needsNegotiation()\n      } catch (err) {\n        this.destroy(errCode(err, 'ERR_ADD_TRANSCEIVER'))\n      }\n    } else {\n      this.emit('signal', {\n        // request initiator to renegotiate\n        type: 'transceiverRequest',\n        transceiverRequest: { kind, init }\n      })\n    }\n  }\n\n  /**\n   * Add a MediaStream to the connection.\n   * @param {MediaStream} stream\n   */\n  addStream (stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot addStream after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('addStream()')\n\n    stream.getTracks().forEach(track => {\n      this.addTrack(track, stream)\n    })\n  }\n\n  /**\n   * Add a MediaStreamTrack to the connection.\n   * @param {MediaStreamTrack} track\n   * @param {MediaStream} stream\n   */\n  addTrack (track, stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot addTrack after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('addTrack()')\n\n    const submap = this._senderMap.get(track) || new Map() // nested Maps map [track, stream] to sender\n    let sender = submap.get(stream)\n    if (!sender) {\n      sender = this._pc.addTrack(track, stream)\n      submap.set(stream, sender)\n      this._senderMap.set(track, submap)\n      this._needsNegotiation()\n    } else if (sender.removed) {\n      throw errCode(\n        new Error(\n          'Track has been removed. You should enable/disable tracks that you want to re-add.'\n        ),\n        'ERR_SENDER_REMOVED'\n      )\n    } else {\n      throw errCode(\n        new Error('Track has already been added to that stream.'),\n        'ERR_SENDER_ALREADY_ADDED'\n      )\n    }\n  }\n\n  /**\n   * Replace a MediaStreamTrack by another in the connection.\n   * @param {MediaStreamTrack} oldTrack\n   * @param {MediaStreamTrack} newTrack\n   * @param {MediaStream} stream\n   */\n  replaceTrack (oldTrack, newTrack, stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot replaceTrack after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('replaceTrack()')\n\n    const submap = this._senderMap.get(oldTrack)\n    const sender = submap ? submap.get(stream) : null\n    if (!sender) {\n      throw errCode(\n        new Error('Cannot replace track that was never added.'),\n        'ERR_TRACK_NOT_ADDED'\n      )\n    }\n    if (newTrack) this._senderMap.set(newTrack, submap)\n\n    if (sender.replaceTrack != null) {\n      sender.replaceTrack(newTrack)\n    } else {\n      this.destroy(\n        errCode(\n          new Error('replaceTrack is not supported in this browser'),\n          'ERR_UNSUPPORTED_REPLACETRACK'\n        )\n      )\n    }\n  }\n\n  /**\n   * Remove a MediaStreamTrack from the connection.\n   * @param {MediaStreamTrack} track\n   * @param {MediaStream} stream\n   */\n  removeTrack (track, stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot removeTrack after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('removeSender()')\n\n    const submap = this._senderMap.get(track)\n    const sender = submap ? submap.get(stream) : null\n    if (!sender) {\n      throw errCode(\n        new Error('Cannot remove track that was never added.'),\n        'ERR_TRACK_NOT_ADDED'\n      )\n    }\n    try {\n      sender.removed = true\n      this._pc.removeTrack(sender)\n    } catch (err) {\n      if (err.name === 'NS_ERROR_UNEXPECTED') {\n        this._sendersAwaitingStable.push(sender) // HACK: Firefox must wait until (signalingState === stable) https://bugzilla.mozilla.org/show_bug.cgi?id=1133874\n      } else {\n        this.destroy(errCode(err, 'ERR_REMOVE_TRACK'))\n      }\n    }\n    this._needsNegotiation()\n  }\n\n  /**\n   * Remove a MediaStream from the connection.\n   * @param {MediaStream} stream\n   */\n  removeStream (stream) {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot removeStream after peer is destroyed'), 'ERR_DESTROYED')\n    this._debug('removeSenders()')\n\n    stream.getTracks().forEach(track => {\n      this.removeTrack(track, stream)\n    })\n  }\n\n  _needsNegotiation () {\n    this._debug('_needsNegotiation')\n    if (this._batchedNegotiation) return // batch synchronous renegotiations\n    this._batchedNegotiation = true\n    queueMicrotask(() => {\n      this._batchedNegotiation = false\n      if (this.initiator || !this._firstNegotiation) {\n        this._debug('starting batched negotiation')\n        this.negotiate()\n      } else {\n        this._debug('non-initiator initial negotiation request discarded')\n      }\n      this._firstNegotiation = false\n    })\n  }\n\n  negotiate () {\n    if (this.destroying) return\n    if (this.destroyed) throw errCode(new Error('cannot negotiate after peer is destroyed'), 'ERR_DESTROYED')\n\n    if (this.initiator) {\n      if (this._isNegotiating) {\n        this._queuedNegotiation = true\n        this._debug('already negotiating, queueing')\n      } else {\n        this._debug('start negotiation')\n        setTimeout(() => {\n          // HACK: Chrome crashes if we immediately call createOffer\n          this._createOffer()\n        }, 0)\n      }\n    } else {\n      if (this._isNegotiating) {\n        this._queuedNegotiation = true\n        this._debug('already negotiating, queueing')\n      } else {\n        this._debug('requesting negotiation from initiator')\n        this.emit('signal', {\n          // request initiator to renegotiate\n          type: 'renegotiate',\n          renegotiate: true\n        })\n      }\n    }\n    this._isNegotiating = true\n  }\n\n  destroy (err) {\n    if (this.destroyed || this.destroying) return\n    this.destroying = true\n\n    this._debug('destroying (error: %s)', err && (err.message || err))\n\n    queueMicrotask(() => {\n      // allow events concurrent with the call to _destroy() to fire (see #692)\n      this.destroyed = true\n      this.destroying = false\n\n      this._debug('destroy (error: %s)', err && (err.message || err))\n\n      this._connected = false\n      this._pcReady = false\n      this._channelReady = false\n      this._remoteTracks = null\n      this._remoteStreams = null\n      this._senderMap = null\n\n      clearInterval(this._closingInterval)\n      this._closingInterval = null\n\n      clearInterval(this._interval)\n      this._interval = null\n      this._chunk = null\n      this._cb = null\n\n      if (this._channel) {\n        try {\n          this._channel.close()\n        } catch (err) {}\n\n        // allow events concurrent with destruction to be handled\n        this._channel.onmessage = null\n        this._channel.onopen = null\n        this._channel.onclose = null\n        this._channel.onerror = null\n      }\n      if (this._pc) {\n        try {\n          this._pc.close()\n        } catch (err) {}\n\n        // allow events concurrent with destruction to be handled\n        this._pc.oniceconnectionstatechange = null\n        this._pc.onicegatheringstatechange = null\n        this._pc.onsignalingstatechange = null\n        this._pc.onicecandidate = null\n        this._pc.ontrack = null\n        this._pc.ondatachannel = null\n      }\n      this._pc = null\n      this._channel = null\n\n      if (err) this.emit('error', err)\n      this.emit('close')\n    })\n  }\n\n  _setupData (event) {\n    if (!event.channel) {\n      // In some situations `pc.createDataChannel()` returns `undefined` (in wrtc),\n      // which is invalid behavior. Handle it gracefully.\n      // See: https://github.com/feross/simple-peer/issues/163\n      return this.destroy(\n        errCode(\n          new Error('Data channel event is missing `channel` property'),\n          'ERR_DATA_CHANNEL'\n        )\n      )\n    }\n\n    this._channel = event.channel\n    this._channel.binaryType = 'arraybuffer'\n\n    if (typeof this._channel.bufferedAmountLowThreshold === 'number') {\n      this._channel.bufferedAmountLowThreshold = MAX_BUFFERED_AMOUNT\n    }\n\n    this.channelName = this._channel.label\n\n    this._channel.onmessage = event => {\n      this._onChannelMessage(event)\n    }\n    this._channel.onbufferedamountlow = () => {\n      this._onChannelBufferedAmountLow()\n    }\n    this._channel.onopen = () => {\n      this._onChannelOpen()\n    }\n    this._channel.onclose = () => {\n      this._onChannelClose()\n    }\n    this._channel.onerror = err => {\n      this.destroy(errCode(err, 'ERR_DATA_CHANNEL'))\n    }\n\n    // HACK: Chrome will sometimes get stuck in readyState \"closing\", let's check for this condition\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=882743\n    let isClosing = false\n    this._closingInterval = setInterval(() => {\n      // No \"onclosing\" event\n      if (this._channel && this._channel.readyState === 'closing') {\n        if (isClosing) this._onChannelClose() // closing timed out: equivalent to onclose firing\n        isClosing = true\n      } else {\n        isClosing = false\n      }\n    }, CHANNEL_CLOSING_TIMEOUT)\n  }\n\n  _startIceCompleteTimeout () {\n    if (this.destroyed) return\n    if (this._iceCompleteTimer) return\n    this._debug('started iceComplete timeout')\n    this._iceCompleteTimer = setTimeout(() => {\n      if (!this._iceComplete) {\n        this._iceComplete = true\n        this._debug('iceComplete timeout completed')\n        this.emit('iceTimeout')\n        this.emit('_iceComplete')\n      }\n    }, this.iceCompleteTimeout)\n  }\n\n  _createOffer () {\n    if (this.destroyed) return\n\n    this._pc\n      .createOffer(this.offerOptions)\n      .then(offer => {\n        if (this.destroyed) return\n        if (!this.trickle && !this.allowHalfTrickle) { offer.sdp = filterTrickle(offer.sdp) }\n        offer.sdp = this.sdpTransform(offer.sdp)\n\n        const sendOffer = () => {\n          if (this.destroyed) return\n          const signal = this._pc.localDescription || offer\n          this._debug('signal')\n          this.emit('signal', {\n            type: signal.type,\n            sdp: signal.sdp\n          })\n        }\n\n        const onSuccess = () => {\n          this._debug('createOffer success')\n          if (this.destroyed) return\n          if (this.trickle || this._iceComplete) sendOffer()\n          else this.once('_iceComplete', sendOffer) // wait for candidates\n        }\n\n        const onError = err => {\n          this.destroy(errCode(err, 'ERR_SET_LOCAL_DESCRIPTION'))\n        }\n\n        this._pc.setLocalDescription(offer).then(onSuccess).catch(onError)\n      })\n      .catch(err => {\n        this.destroy(errCode(err, 'ERR_CREATE_OFFER'))\n      })\n  }\n\n  _requestMissingTransceivers () {\n    if (this._pc.getTransceivers) {\n      this._pc.getTransceivers().forEach(transceiver => {\n        if (\n          !transceiver.mid &&\n          transceiver.sender.track &&\n          !transceiver.requested\n        ) {\n          transceiver.requested = true // HACK: Safari returns negotiated transceivers with a null mid\n          this.addTransceiver(transceiver.sender.track.kind)\n        }\n      })\n    }\n  }\n\n  _createAnswer () {\n    if (this.destroyed) return\n\n    this._pc\n      .createAnswer(this.answerOptions)\n      .then(answer => {\n        if (this.destroyed) return\n        if (!this.trickle && !this.allowHalfTrickle) { answer.sdp = filterTrickle(answer.sdp) }\n        answer.sdp = this.sdpTransform(answer.sdp)\n\n        const sendAnswer = () => {\n          if (this.destroyed) return\n          const signal = this._pc.localDescription || answer\n          this._debug('signal')\n          this.emit('signal', {\n            type: signal.type,\n            sdp: signal.sdp\n          })\n          if (!this.initiator) this._requestMissingTransceivers()\n        }\n\n        const onSuccess = () => {\n          if (this.destroyed) return\n          if (this.trickle || this._iceComplete) sendAnswer()\n          else this.once('_iceComplete', sendAnswer)\n        }\n\n        const onError = err => {\n          this.destroy(errCode(err, 'ERR_SET_LOCAL_DESCRIPTION'))\n        }\n\n        this._pc.setLocalDescription(answer).then(onSuccess).catch(onError)\n      })\n      .catch(err => {\n        this.destroy(errCode(err, 'ERR_CREATE_ANSWER'))\n      })\n  }\n\n  _onConnectionStateChange () {\n    if (this.destroyed) return\n    if (this._pc.connectionState === 'failed') {\n      this.destroy(\n        errCode(new Error('Connection failed.'), 'ERR_CONNECTION_FAILURE')\n      )\n    }\n  }\n\n  _onIceStateChange () {\n    if (this.destroyed) return\n    const iceConnectionState = this._pc.iceConnectionState\n    const iceGatheringState = this._pc.iceGatheringState\n\n    this._debug(\n      'iceStateChange (connection: %s) (gathering: %s)',\n      iceConnectionState,\n      iceGatheringState\n    )\n    this.emit('iceStateChange', iceConnectionState, iceGatheringState)\n\n    if (\n      iceConnectionState === 'connected' ||\n      iceConnectionState === 'completed'\n    ) {\n      this._pcReady = true\n      this._maybeReady()\n    }\n    if (iceConnectionState === 'failed') {\n      this.destroy(\n        errCode(\n          new Error('Ice connection failed.'),\n          'ERR_ICE_CONNECTION_FAILURE'\n        )\n      )\n    }\n    if (iceConnectionState === 'closed') {\n      this.destroy(\n        errCode(\n          new Error('Ice connection closed.'),\n          'ERR_ICE_CONNECTION_CLOSED'\n        )\n      )\n    }\n  }\n\n  getStats (cb) {\n    // statreports can come with a value array instead of properties\n    const flattenValues = report => {\n      if (Object.prototype.toString.call(report.values) === '[object Array]') {\n        report.values.forEach(value => {\n          Object.assign(report, value)\n        })\n      }\n      return report\n    }\n\n    // Promise-based getStats() (standard)\n    if (this._pc.getStats.length === 0 || this._isReactNativeWebrtc) {\n      this._pc.getStats().then(\n        res => {\n          const reports = []\n          res.forEach(report => {\n            reports.push(flattenValues(report))\n          })\n          cb(null, reports)\n        },\n        err => cb(err)\n      )\n\n      // Single-parameter callback-based getStats() (non-standard)\n    } else if (this._pc.getStats.length > 0) {\n      this._pc.getStats(\n        res => {\n          // If we destroy connection in `connect` callback this code might happen to run when actual connection is already closed\n          if (this.destroyed) return\n\n          const reports = []\n          res.result().forEach(result => {\n            const report = {}\n            result.names().forEach(name => {\n              report[name] = result.stat(name)\n            })\n            report.id = result.id\n            report.type = result.type\n            report.timestamp = result.timestamp\n            reports.push(flattenValues(report))\n          })\n          cb(null, reports)\n        },\n        err => cb(err)\n      )\n\n      // Unknown browser, skip getStats() since it's anyone's guess which style of\n      // getStats() they implement.\n    } else {\n      cb(null, [])\n    }\n  }\n\n  _maybeReady () {\n    this._debug(\n      'maybeReady pc %s channel %s',\n      this._pcReady,\n      this._channelReady\n    )\n    if (\n      this._connected ||\n      this._connecting ||\n      !this._pcReady ||\n      !this._channelReady\n    ) { return }\n\n    this._connecting = true\n\n    // HACK: We can't rely on order here, for details see https://github.com/js-platform/node-webrtc/issues/339\n    const findCandidatePair = () => {\n      if (this.destroyed) return\n\n      this.getStats((err, items) => {\n        if (this.destroyed) return\n\n        // Treat getStats error as non-fatal. It's not essential.\n        if (err) items = []\n\n        const remoteCandidates = {}\n        const localCandidates = {}\n        const candidatePairs = {}\n        let foundSelectedCandidatePair = false\n\n        items.forEach(item => {\n          // TODO: Once all browsers support the hyphenated stats report types, remove\n          // the non-hypenated ones\n          if (\n            item.type === 'remotecandidate' ||\n            item.type === 'remote-candidate'\n          ) {\n            remoteCandidates[item.id] = item\n          }\n          if (\n            item.type === 'localcandidate' ||\n            item.type === 'local-candidate'\n          ) {\n            localCandidates[item.id] = item\n          }\n          if (item.type === 'candidatepair' || item.type === 'candidate-pair') {\n            candidatePairs[item.id] = item\n          }\n        })\n\n        const setSelectedCandidatePair = selectedCandidatePair => {\n          foundSelectedCandidatePair = true\n\n          let local = localCandidates[selectedCandidatePair.localCandidateId]\n\n          if (local && (local.ip || local.address)) {\n            // Spec\n            this.localAddress = local.ip || local.address\n            this.localPort = Number(local.port)\n          } else if (local && local.ipAddress) {\n            // Firefox\n            this.localAddress = local.ipAddress\n            this.localPort = Number(local.portNumber)\n          } else if (\n            typeof selectedCandidatePair.googLocalAddress === 'string'\n          ) {\n            // TODO: remove this once Chrome 58 is released\n            local = selectedCandidatePair.googLocalAddress.split(':')\n            this.localAddress = local[0]\n            this.localPort = Number(local[1])\n          }\n          if (this.localAddress) {\n            this.localFamily = this.localAddress.includes(':')\n              ? 'IPv6'\n              : 'IPv4'\n          }\n\n          let remote =\n            remoteCandidates[selectedCandidatePair.remoteCandidateId]\n\n          if (remote && (remote.ip || remote.address)) {\n            // Spec\n            this.remoteAddress = remote.ip || remote.address\n            this.remotePort = Number(remote.port)\n          } else if (remote && remote.ipAddress) {\n            // Firefox\n            this.remoteAddress = remote.ipAddress\n            this.remotePort = Number(remote.portNumber)\n          } else if (\n            typeof selectedCandidatePair.googRemoteAddress === 'string'\n          ) {\n            // TODO: remove this once Chrome 58 is released\n            remote = selectedCandidatePair.googRemoteAddress.split(':')\n            this.remoteAddress = remote[0]\n            this.remotePort = Number(remote[1])\n          }\n          if (this.remoteAddress) {\n            this.remoteFamily = this.remoteAddress.includes(':')\n              ? 'IPv6'\n              : 'IPv4'\n          }\n\n          this._debug(\n            'connect local: %s:%s remote: %s:%s',\n            this.localAddress,\n            this.localPort,\n            this.remoteAddress,\n            this.remotePort\n          )\n        }\n\n        items.forEach(item => {\n          // Spec-compliant\n          if (item.type === 'transport' && item.selectedCandidatePairId) {\n            setSelectedCandidatePair(\n              candidatePairs[item.selectedCandidatePairId]\n            )\n          }\n\n          // Old implementations\n          if (\n            (item.type === 'googCandidatePair' &&\n              item.googActiveConnection === 'true') ||\n            ((item.type === 'candidatepair' ||\n              item.type === 'candidate-pair') &&\n              item.selected)\n          ) {\n            setSelectedCandidatePair(item)\n          }\n        })\n\n        // Ignore candidate pair selection in browsers like Safari 11 that do not have any local or remote candidates\n        // But wait until at least 1 candidate pair is available\n        if (\n          !foundSelectedCandidatePair &&\n          (!Object.keys(candidatePairs).length ||\n            Object.keys(localCandidates).length)\n        ) {\n          setTimeout(findCandidatePair, 100)\n          return\n        } else {\n          this._connecting = false\n          this._connected = true\n        }\n\n        if (this._chunk) {\n          try {\n            this.send(this._chunk)\n          } catch (err) {\n            return this.destroy(errCode(err, 'ERR_DATA_CHANNEL'))\n          }\n          this._chunk = null\n          this._debug('sent chunk from \"write before connect\"')\n\n          const cb = this._cb\n          this._cb = null\n          cb(null)\n        }\n\n        // If `bufferedAmountLowThreshold` and 'onbufferedamountlow' are unsupported,\n        // fallback to using setInterval to implement backpressure.\n        if (typeof this._channel.bufferedAmountLowThreshold !== 'number') {\n          this._interval = setInterval(() => this._onInterval(), 150)\n          if (this._interval.unref) this._interval.unref()\n        }\n\n        this._debug('connect')\n        this.emit('connect')\n      })\n    }\n    findCandidatePair()\n  }\n\n  _onInterval () {\n    if (\n      !this._cb ||\n      !this._channel ||\n      this._channel.bufferedAmount > MAX_BUFFERED_AMOUNT\n    ) {\n      return\n    }\n    this._onChannelBufferedAmountLow()\n  }\n\n  _onSignalingStateChange () {\n    if (this.destroyed) return\n\n    if (this._pc.signalingState === 'stable') {\n      this._isNegotiating = false\n\n      // HACK: Firefox doesn't yet support removing tracks when signalingState !== 'stable'\n      this._debug('flushing sender queue', this._sendersAwaitingStable)\n      this._sendersAwaitingStable.forEach(sender => {\n        this._pc.removeTrack(sender)\n        this._queuedNegotiation = true\n      })\n      this._sendersAwaitingStable = []\n\n      if (this._queuedNegotiation) {\n        this._debug('flushing negotiation queue')\n        this._queuedNegotiation = false\n        this._needsNegotiation() // negotiate again\n      } else {\n        this._debug('negotiated')\n        this.emit('negotiated')\n      }\n    }\n\n    this._debug('signalingStateChange %s', this._pc.signalingState)\n    this.emit('signalingStateChange', this._pc.signalingState)\n  }\n\n  _onIceCandidate (event) {\n    if (this.destroyed) return\n    if (event.candidate && this.trickle) {\n      this.emit('signal', {\n        type: 'candidate',\n        candidate: {\n          candidate: event.candidate.candidate,\n          sdpMLineIndex: event.candidate.sdpMLineIndex,\n          sdpMid: event.candidate.sdpMid\n        }\n      })\n    } else if (!event.candidate && !this._iceComplete) {\n      this._iceComplete = true\n      this.emit('_iceComplete')\n    }\n    // as soon as we've received one valid candidate start timeout\n    if (event.candidate) {\n      this._startIceCompleteTimeout()\n    }\n  }\n\n  _onChannelMessage (event) {\n    if (this.destroyed) return\n    let data = event.data\n    if (data instanceof ArrayBuffer) data = new Uint8Array(data)\n    this.emit('data', data)\n  }\n\n  _onChannelBufferedAmountLow () {\n    if (this.destroyed || !this._cb) return\n    this._debug(\n      'ending backpressure: bufferedAmount %d',\n      this._channel.bufferedAmount\n    )\n    const cb = this._cb\n    this._cb = null\n    cb(null)\n  }\n\n  _onChannelOpen () {\n    if (this._connected || this.destroyed) return\n    this._debug('on channel open')\n    this._channelReady = true\n    this._maybeReady()\n  }\n\n  _onChannelClose () {\n    if (this.destroyed) return\n    this._debug('on channel close')\n    this.destroy()\n  }\n\n  _onTrack (event) {\n    if (this.destroyed) return\n\n    event.streams.forEach(eventStream => {\n      this._debug('on track')\n      this.emit('track', event.track, eventStream)\n\n      this._remoteTracks.push({\n        track: event.track,\n        stream: eventStream\n      })\n\n      if (\n        this._remoteStreams.some(remoteStream => {\n          return remoteStream.id === eventStream.id\n        })\n      ) { return } // Only fire one 'stream' event, even though there may be multiple tracks per stream\n\n      this._remoteStreams.push(eventStream)\n      queueMicrotask(() => {\n        this._debug('on stream')\n        this.emit('stream', eventStream) // ensure all tracks have been added\n      })\n    })\n  }\n\n  _debug (...args) {\n    if (!this._doDebug) return\n    args[0] = '[' + this._id + '] ' + args[0]\n    console.log(...args)\n  }\n\n  // event emitter\n  on (key, listener) {\n    const map = this._map\n    if (!map.has(key)) map.set(key, new Set())\n    map.get(key).add(listener)\n  }\n\n  off (key, listener) {\n    const map = this._map\n    const listeners = map.get(key)\n    if (!listeners) return\n    listeners.delete(listener)\n    if (listeners.size === 0) map.delete(key)\n  }\n\n  once (key, listener) {\n    const listener_ = (...args) => {\n      this.off(key, listener_)\n      listener(...args)\n    }\n    this.on(key, listener_)\n  }\n\n  emit (key, ...args) {\n    const map = this._map\n    if (!map.has(key)) return\n    for (const listener of map.get(key)) {\n      try {\n        listener(...args)\n      } catch (err) {\n        console.error(err)\n      }\n    }\n  }\n}\n\nPeer.WEBRTC_SUPPORT = !!getBrowserRTC()\n\n/**\n * Expose peer and data channel config for overriding all Peer\n * instances. Otherwise, just set opts.config or opts.channelConfig\n * when constructing a Peer.\n */\nPeer.config = {\n  iceServers: [\n    {\n      urls: [\n        'stun:stun.l.google.com:19302',\n        'stun:global.stun.twilio.com:3478'\n      ]\n    }\n  ],\n  sdpSemantics: 'unified-plan'\n}\n\nPeer.channelConfig = {}\n\n// module.exports = Peer\nexport default Peer\n", "import type { RRNode } from 'rrdom';\nimport type { Mirror } from 'rrweb-snapshot';\nimport SimplePeer from 'simple-peer-light';\nimport type { Replayer } from '../../../replay';\nimport type { ReplayPlugin } from '../../../types';\nimport type { WebRTCDataChannel } from '../types';\n\n// TODO: restrict callback to real nodes only, or make sure callback gets called when real node gets added to dom as well\n\nexport class RRWebPluginCanvasWebRTCReplay {\n  private canvasFoundCallback: (\n    node: Node | RRNode,\n    context: { id: number; replayer: Replayer },\n  ) => void;\n  private signalSendCallback: (signal: RTCSessionDescriptionInit) => void;\n  private mirror: Mirror;\n\n  constructor({\n    canvasFoundCallback,\n    signalSendCallback,\n  }: {\n    canvasFoundCallback: RRWebPluginCanvasWebRTCReplay['canvasFoundCallback'];\n    signalSendCallback: RRWebPluginCanvasWebRTCReplay['signalSendCallback'];\n  }) {\n    this.canvasFoundCallback = canvasFoundCallback;\n    this.signalSendCallback = signalSendCallback;\n  }\n\n  public initPlugin(): ReplayPlugin {\n    return {\n      onBuild: (\n        node: Node | RRNode,\n        context: { id: number; replayer: Replayer },\n      ) => {\n        if (node.nodeName === 'CANVAS') {\n          this.canvasFoundCallback(node, context);\n        }\n      },\n      getMirror: (options) => {\n        this.mirror = options.nodeMirror;\n      },\n    };\n  }\n\n  private startStream(\n    target: HTMLCanvasElement | HTMLVideoElement,\n    stream: MediaStream,\n  ) {\n    if (this.runningStreams.has(stream)) return;\n\n    if (target.tagName === 'VIDEO') {\n      const remoteVideo = target as HTMLVideoElement;\n      remoteVideo.srcObject = stream;\n      void remoteVideo.play();\n      this.runningStreams.add(stream);\n      return;\n    }\n\n    if ('MediaStreamTrackProcessor' in window) {\n      const canvas = target as HTMLCanvasElement;\n      const ctx = canvas.getContext('2d');\n      if (!ctx)\n        throw new Error(\n          `startStream: Could not get 2d canvas context for ${canvas.outerHTML}`,\n        );\n      const track = stream.getVideoTracks()[0]; // MediaStream.getVideoTracks()[0]\n      const processor = new MediaStreamTrackProcessor({ track: track });\n      const reader = processor.readable.getReader();\n      const readChunk = function () {\n        void reader.read().then(({ done, value }) => {\n          if (!value) return;\n          // the MediaStream video can have dynamic size based on bandwidth available\n          if (\n            canvas.width !== value.displayWidth ||\n            canvas.height !== value.displayHeight\n          ) {\n            canvas.width = value.displayWidth;\n            canvas.height = value.displayHeight;\n          }\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          // value is a VideoFrame\n          ctx.drawImage(value, 0, 0);\n          value.close(); // close the VideoFrame when we're done with it\n          if (!done) {\n            readChunk();\n          }\n        });\n      };\n      readChunk();\n      this.runningStreams.add(stream);\n    } else {\n      // Fallback for non-Chromium browsers.\n      // Replaces the canvas element with a video element.\n\n      const remoteVideo = document.createElement('video');\n      remoteVideo.setAttribute('autoplay', 'true');\n      remoteVideo.setAttribute('playsinline', 'true');\n\n      // const { id } = mutation;\n      remoteVideo.setAttribute('width', target.width.toString());\n      remoteVideo.setAttribute('height', target.height.toString());\n      target.replaceWith(remoteVideo);\n\n      this.startStream(remoteVideo, stream);\n    }\n  }\n\n  private peer: SimplePeer.Instance | null = null;\n  private streamNodeMap = new Map<string, number>();\n  private streams = new Set<MediaStream>();\n  private runningStreams = new WeakSet<MediaStream>();\n  public signalReceive(msg: RTCSessionDescriptionInit) {\n    if (!this.peer) {\n      this.peer = new SimplePeer({\n        initiator: false,\n        // trickle: false,\n      });\n\n      this.peer.on('error', (err: Error) => {\n        this.peer = null;\n        console.log('error', err);\n      });\n\n      this.peer.on('close', () => {\n        this.peer = null;\n        console.log('closing');\n      });\n\n      this.peer.on('signal', (data: RTCSessionDescriptionInit) => {\n        this.signalSendCallback(data);\n      });\n\n      this.peer.on('connect', () => {\n        // connected!\n      });\n\n      this.peer.on('data', (data: SimplePeer.SimplePeerData) => {\n        try {\n          const json = JSON.parse(data as string) as WebRTCDataChannel;\n          this.streamNodeMap.set(json.streamId, json.nodeId);\n        } catch (error) {\n          console.error('Could not parse data', error);\n        }\n        this.flushStreams();\n      });\n\n      this.peer.on('stream', (stream: MediaStream) => {\n        this.streams.add(stream);\n        this.flushStreams();\n      });\n    }\n    this.peer.signal(msg);\n  }\n\n  private flushStreams() {\n    this.streams.forEach((stream) => {\n      const nodeId = this.streamNodeMap.get(stream.id);\n      if (!nodeId) return;\n      const target = this.mirror.getNode(nodeId) as\n        | HTMLCanvasElement\n        | HTMLVideoElement\n        | null;\n      // got remote video stream, now let's show it in a video or canvas element\n      if (target) this.startStream(target, stream);\n    });\n  }\n}\n"], "names": ["d"], "mappings": ";;;EAAA;EACA,MAAM,mBAAmB,GAAG,EAAE,GAAG,KAAI;EACrC,MAAM,mBAAmB,GAAG,CAAC,GAAG,KAAI;EACpC,MAAM,uBAAuB,GAAG,CAAC,GAAG,KAAI;AACxC;EACA,SAAS,WAAW,EAAE,IAAI,EAAE;EAC5B,EAAE,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,EAAC;EACpC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;EACjC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,EAAC;EACxC,GAAG;EACH,EAAE,OAAO,KAAK;EACd,CAAC;AACD;EACA,SAAS,aAAa,IAAI;EAC1B,EAAE,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE,OAAO,IAAI;EACpD,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,iBAAiB;EACrB,MAAM,UAAU,CAAC,iBAAiB;EAClC,MAAM,UAAU,CAAC,oBAAoB;EACrC,MAAM,UAAU,CAAC,uBAAuB;EACxC,IAAI,qBAAqB;EACzB,MAAM,UAAU,CAAC,qBAAqB;EACtC,MAAM,UAAU,CAAC,wBAAwB;EACzC,MAAM,UAAU,CAAC,2BAA2B;EAC5C,IAAI,eAAe;EACnB,MAAM,UAAU,CAAC,eAAe;EAChC,MAAM,UAAU,CAAC,kBAAkB;EACnC,MAAM,UAAU,CAAC,qBAAqB;EACtC,IAAG;EACH,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,IAAI;EAC1C,EAAE,OAAO,IAAI;EACb,CAAC;AACD;EACA,SAAS,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE;EAC7B,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE;EACrC,IAAI,KAAK,EAAE,IAAI;EACf,IAAI,UAAU,EAAE,IAAI;EACpB,IAAI,YAAY,EAAE,IAAI;EACtB,GAAG,EAAC;EACJ,EAAE,OAAO,GAAG;EACZ,CAAC;AACD;EACA;EACA,SAAS,aAAa,EAAE,GAAG,EAAE;EAC7B,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC;EACtD,CAAC;AACD;EACA,SAAS,IAAI,EAAE,OAAO,EAAE;EACxB,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,EAAC;EACvB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA,MAAM,IAAI,CAAC;EACX,EAAE,WAAW,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE;EAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAE;AACzB;EACA,IAAI,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAC;EACzD,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAK;EAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAC;AACpC;EACA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS;EACrC,QAAQ,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;EAC3D,QAAQ,KAAI;AACZ;EACA,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,MAAK;EAC5C,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,cAAa;EACjE,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAU;EAC1D,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAC;EAC7D,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,GAAE;EAC/C,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,GAAE;EACjD,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,KAAK,GAAG,IAAI,GAAG,EAAC;EACzD,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,EAAC;EACrE,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,KAAK,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,KAAI;EACnE,IAAI,IAAI,CAAC,gBAAgB;EACzB,MAAM,IAAI,CAAC,gBAAgB,KAAK,SAAS,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAK;EACzE,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,oBAAmB;AAC5E;EACA,IAAI,IAAI,CAAC,SAAS,GAAG,MAAK;EAC1B,IAAI,IAAI,CAAC,UAAU,GAAG,MAAK;EAC3B,IAAI,IAAI,CAAC,UAAU,GAAG,MAAK;AAC3B;EACA,IAAI,IAAI,CAAC,aAAa,GAAG,UAAS;EAClC,IAAI,IAAI,CAAC,YAAY,GAAG,UAAS;EACjC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAS;EAC/B,IAAI,IAAI,CAAC,YAAY,GAAG,UAAS;EACjC,IAAI,IAAI,CAAC,WAAW,GAAG,UAAS;EAChC,IAAI,IAAI,CAAC,SAAS,GAAG,UAAS;AAC9B;EACA,IAAI,IAAI,CAAC,KAAK;EACd,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,aAAa,GAAE;AAC9E;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;EACrB,MAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;EACzC,QAAQ,MAAM,OAAO;EACrB,UAAU,IAAI,KAAK;EACnB,YAAY,mEAAmE;EAC/E,WAAW;EACX,UAAU,oBAAoB;EAC9B,SAAS;EACT,OAAO,MAAM;EACb,QAAQ,MAAM,OAAO;EACrB,UAAU,IAAI,KAAK,CAAC,4CAA4C,CAAC;EACjE,UAAU,oBAAoB;EAC9B,SAAS;EACT,OAAO;EACP,KAAK;AACL;EACA,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAK;EACzB,IAAI,IAAI,CAAC,aAAa,GAAG,MAAK;EAC9B,IAAI,IAAI,CAAC,YAAY,GAAG,MAAK;EAC7B,IAAI,IAAI,CAAC,iBAAiB,GAAG,KAAI;EACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAI;EACxB,IAAI,IAAI,CAAC,kBAAkB,GAAG,GAAE;AAChC;EACA,IAAI,IAAI,CAAC,cAAc,GAAG,MAAK;EAC/B,IAAI,IAAI,CAAC,iBAAiB,GAAG,KAAI;EACjC,IAAI,IAAI,CAAC,mBAAmB,GAAG,MAAK;EACpC,IAAI,IAAI,CAAC,kBAAkB,GAAG,MAAK;EACnC,IAAI,IAAI,CAAC,sBAAsB,GAAG,GAAE;EACpC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,GAAE;EAC/B,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAI;AAChC;EACA,IAAI,IAAI,CAAC,aAAa,GAAG,GAAE;EAC3B,IAAI,IAAI,CAAC,cAAc,GAAG,GAAE;AAC5B;EACA,IAAI,IAAI,CAAC,MAAM,GAAG,KAAI;EACtB,IAAI,IAAI,CAAC,GAAG,GAAG,KAAI;EACnB,IAAI,IAAI,CAAC,SAAS,GAAG,KAAI;AACzB;EACA,IAAI,IAAI;EACR,MAAM,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAC;EAC9D,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,oBAAoB,CAAC,EAAC;EACtD,MAAM,MAAM;EACZ,KAAK;AACL;EACA;EACA;EACA,IAAI,IAAI,CAAC,oBAAoB,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,KAAK,SAAQ;AAC9E;EACA,IAAI,IAAI,CAAC,GAAG,CAAC,0BAA0B,GAAG,MAAM;EAChD,MAAM,IAAI,CAAC,iBAAiB,GAAE;EAC9B,MAAK;EACL,IAAI,IAAI,CAAC,GAAG,CAAC,yBAAyB,GAAG,MAAM;EAC/C,MAAM,IAAI,CAAC,iBAAiB,GAAE;EAC9B,MAAK;EACL,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,GAAG,MAAM;EAC7C,MAAM,IAAI,CAAC,wBAAwB,GAAE;EACrC,MAAK;EACL,IAAI,IAAI,CAAC,GAAG,CAAC,sBAAsB,GAAG,MAAM;EAC5C,MAAM,IAAI,CAAC,uBAAuB,GAAE;EACpC,MAAK;EACL,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,KAAK,IAAI;EACvC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAC;EACjC,MAAK;AACL;EACA;EACA,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,KAAK,QAAQ,EAAE;EACnD,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI;EACzC,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,sBAAsB,CAAC,EAAC;EAC1D,OAAO,EAAC;EACR,KAAK;AACL;EACA;EACA;EACA;EACA;EACA;AACA;EACA,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE;EAClD,MAAM,IAAI,CAAC,UAAU,CAAC;EACtB,QAAQ,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB;EAC3C,UAAU,IAAI,CAAC,WAAW;EAC1B,UAAU,IAAI,CAAC,aAAa;EAC5B,SAAS;EACT,OAAO,EAAC;EACR,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,KAAK,IAAI;EACxC,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;EAC9B,QAAO;EACP,KAAK;AACL;EACA,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;EACtB,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI;EACrC,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAC;EAC9B,OAAO,EAAC;EACR,KAAK;EACL,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,KAAK,IAAI;EAChC,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAC;EAC1B,MAAK;AACL;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAC;EACtC,IAAI,IAAI,CAAC,iBAAiB,GAAE;EAC5B,GAAG;AACH;EACA,EAAE,IAAI,UAAU,CAAC,GAAG;EACpB,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,KAAK,CAAC;EAC/D,GAAG;AACH;EACA;EACA;EACA,EAAE,IAAI,SAAS,CAAC,GAAG;EACnB,IAAI,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,MAAM;EACjE,GAAG;AACH;EACA,EAAE,OAAO,CAAC,GAAG;EACb,IAAI,OAAO;EACX,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS;EAC1B,MAAM,MAAM,EAAE,IAAI,CAAC,WAAW;EAC9B,MAAM,OAAO,EAAE,IAAI,CAAC,YAAY;EAChC,KAAK;EACL,GAAG;AACH;EACA,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE;EAChB,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,EAAE,eAAe,CAAC;EAC1G,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;EAClC,MAAM,IAAI;EACV,QAAQ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAC;EAC/B,OAAO,CAAC,OAAO,GAAG,EAAE;EACpB,QAAQ,IAAI,GAAG,GAAE;EACjB,OAAO;EACP,KAAK;EACL,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAC;AAC3B;EACA,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE;EAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAC;EAC/C,MAAM,IAAI,CAAC,iBAAiB,GAAE;EAC9B,KAAK;EACL,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE;EACnD,MAAM,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAC;EAChD,MAAM,IAAI,CAAC,cAAc;EACzB,QAAQ,IAAI,CAAC,kBAAkB,CAAC,IAAI;EACpC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,IAAI;EACpC,QAAO;EACP,KAAK;EACL,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;EACxB,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE;EACzE,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAC;EAC7C,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAC;EACpD,OAAO;EACP,KAAK;EACL,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;EAClB,MAAM,IAAI,CAAC,GAAG;EACd,SAAS,oBAAoB,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;EACzE,SAAS,IAAI,CAAC,MAAM;EACpB,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AACpC;EACA,UAAU,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,IAAI;EACvD,YAAY,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAC;EAC5C,WAAW,EAAC;EACZ,UAAU,IAAI,CAAC,kBAAkB,GAAG,GAAE;AACtC;EACA,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,KAAK,OAAO,EAAE,IAAI,CAAC,aAAa,GAAE;EAC/E,SAAS,CAAC;EACV,SAAS,KAAK,CAAC,GAAG,IAAI;EACtB,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,4BAA4B,CAAC,EAAC;EAClE,SAAS,EAAC;EACV,KAAK;EACL,IAAI;EACJ,MAAM,CAAC,IAAI,CAAC,GAAG;EACf,MAAM,CAAC,IAAI,CAAC,SAAS;EACrB,MAAM,CAAC,IAAI,CAAC,WAAW;EACvB,MAAM,CAAC,IAAI,CAAC,kBAAkB;EAC9B,MAAM;EACN,MAAM,IAAI,CAAC,OAAO;EAClB,QAAQ,OAAO;EACf,UAAU,IAAI,KAAK,CAAC,0CAA0C,CAAC;EAC/D,UAAU,eAAe;EACzB,SAAS;EACT,QAAO;EACP,KAAK;EACL,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,CAAC,SAAS,EAAE;EAC/B,IAAI,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,EAAC;EACrE,IAAI,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI;EAC3D,MAAM;EACN,QAAQ,CAAC,eAAe,CAAC,OAAO;EAChC,QAAQ,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;EAClD,QAAQ;EACR,QAAQ,IAAI,CAAC,qCAAqC,EAAC;EACnD,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,uBAAuB,CAAC,EAAC;EAC3D,OAAO;EACP,KAAK,EAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE;EACf,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,EAAE,eAAe,CAAC;EACxG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAC;EAC7B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE;EAC9B,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,+CAA+C,CAAC,EAAE,eAAe,CAAC;EAClH,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAC;AACnC;EACA,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;EACxB,MAAM,IAAI;EACV,QAAQ,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAC;EAC3C,QAAQ,IAAI,CAAC,iBAAiB,GAAE;EAChC,OAAO,CAAC,OAAO,GAAG,EAAE;EACpB,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,qBAAqB,CAAC,EAAC;EACzD,OAAO;EACP,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAC1B;EACA,QAAQ,IAAI,EAAE,oBAAoB;EAClC,QAAQ,kBAAkB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;EAC1C,OAAO,EAAC;EACR,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE;EACrB,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,EAAE,eAAe,CAAC;EAC7G,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAC;AAC9B;EACA,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;EACxC,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAC;EAClC,KAAK,EAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE;EAC3B,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,yCAAyC,CAAC,EAAE,eAAe,CAAC;EAC5G,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAC;AAC7B;EACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,GAAE;EAC1D,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,EAAC;EACnC,IAAI,IAAI,CAAC,MAAM,EAAE;EACjB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAC;EAC/C,MAAM,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAC;EAChC,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAC;EACxC,MAAM,IAAI,CAAC,iBAAiB,GAAE;EAC9B,KAAK,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;EAC/B,MAAM,MAAM,OAAO;EACnB,QAAQ,IAAI,KAAK;EACjB,UAAU,mFAAmF;EAC7F,SAAS;EACT,QAAQ,oBAAoB;EAC5B,OAAO;EACP,KAAK,MAAM;EACX,MAAM,MAAM,OAAO;EACnB,QAAQ,IAAI,KAAK,CAAC,8CAA8C,CAAC;EACjE,QAAQ,0BAA0B;EAClC,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,YAAY,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE;EAC5C,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,6CAA6C,CAAC,EAAE,eAAe,CAAC;EAChH,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAC;AACjC;EACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAC;EAChD,IAAI,MAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAI;EACrD,IAAI,IAAI,CAAC,MAAM,EAAE;EACjB,MAAM,MAAM,OAAO;EACnB,QAAQ,IAAI,KAAK,CAAC,4CAA4C,CAAC;EAC/D,QAAQ,qBAAqB;EAC7B,OAAO;EACP,KAAK;EACL,IAAI,IAAI,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAC;AACvD;EACA,IAAI,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,EAAE;EACrC,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAC;EACnC,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,OAAO;EAClB,QAAQ,OAAO;EACf,UAAU,IAAI,KAAK,CAAC,+CAA+C,CAAC;EACpE,UAAU,8BAA8B;EACxC,SAAS;EACT,QAAO;EACP,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE;EAC9B,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,4CAA4C,CAAC,EAAE,eAAe,CAAC;EAC/G,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAC;AACjC;EACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAC;EAC7C,IAAI,MAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAI;EACrD,IAAI,IAAI,CAAC,MAAM,EAAE;EACjB,MAAM,MAAM,OAAO;EACnB,QAAQ,IAAI,KAAK,CAAC,2CAA2C,CAAC;EAC9D,QAAQ,qBAAqB;EAC7B,OAAO;EACP,KAAK;EACL,IAAI,IAAI;EACR,MAAM,MAAM,CAAC,OAAO,GAAG,KAAI;EAC3B,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAC;EAClC,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,qBAAqB,EAAE;EAC9C,QAAQ,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,EAAC;EAChD,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC,EAAC;EACtD,OAAO;EACP,KAAK;EACL,IAAI,IAAI,CAAC,iBAAiB,GAAE;EAC5B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,YAAY,CAAC,CAAC,MAAM,EAAE;EACxB,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,6CAA6C,CAAC,EAAE,eAAe,CAAC;EAChH,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAC;AAClC;EACA,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;EACxC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAC;EACrC,KAAK,EAAC;EACN,GAAG;AACH;EACA,EAAE,iBAAiB,CAAC,GAAG;EACvB,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAC;EACpC,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE,MAAM;EACxC,IAAI,IAAI,CAAC,mBAAmB,GAAG,KAAI;EACnC,IAAI,cAAc,CAAC,MAAM;EACzB,MAAM,IAAI,CAAC,mBAAmB,GAAG,MAAK;EACtC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;EACrD,QAAQ,IAAI,CAAC,MAAM,CAAC,8BAA8B,EAAC;EACnD,QAAQ,IAAI,CAAC,SAAS,GAAE;EACxB,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,MAAM,CAAC,qDAAqD,EAAC;EAC1E,OAAO;EACP,MAAM,IAAI,CAAC,iBAAiB,GAAG,MAAK;EACpC,KAAK,EAAC;EACN,GAAG;AACH;EACA,EAAE,SAAS,CAAC,GAAG;EACf,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,EAAE,eAAe,CAAC;AAC7G;EACA,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;EACxB,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE;EAC/B,QAAQ,IAAI,CAAC,kBAAkB,GAAG,KAAI;EACtC,QAAQ,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAC;EACpD,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAC;EACxC,QAAQ,UAAU,CAAC,MAAM;EACzB;EACA,UAAU,IAAI,CAAC,YAAY,GAAE;EAC7B,SAAS,EAAE,CAAC,EAAC;EACb,OAAO;EACP,KAAK,MAAM;EACX,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE;EAC/B,QAAQ,IAAI,CAAC,kBAAkB,GAAG,KAAI;EACtC,QAAQ,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAC;EACpD,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,MAAM,CAAC,uCAAuC,EAAC;EAC5D,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAC5B;EACA,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,WAAW,EAAE,IAAI;EAC3B,SAAS,EAAC;EACV,OAAO;EACP,KAAK;EACL,IAAI,IAAI,CAAC,cAAc,GAAG,KAAI;EAC9B,GAAG;AACH;EACA,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE;EAChB,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,MAAM;EACjD,IAAI,IAAI,CAAC,UAAU,GAAG,KAAI;AAC1B;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,EAAC;AACtE;EACA,IAAI,cAAc,CAAC,MAAM;EACzB;EACA,MAAM,IAAI,CAAC,SAAS,GAAG,KAAI;EAC3B,MAAM,IAAI,CAAC,UAAU,GAAG,MAAK;AAC7B;EACA,MAAM,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,EAAC;AACrE;EACA,MAAM,IAAI,CAAC,UAAU,GAAG,MAAK;EAC7B,MAAM,IAAI,CAAC,QAAQ,GAAG,MAAK;EAC3B,MAAM,IAAI,CAAC,aAAa,GAAG,MAAK;EAChC,MAAM,IAAI,CAAC,aAAa,GAAG,KAAI;EAC/B,MAAM,IAAI,CAAC,cAAc,GAAG,KAAI;EAChC,MAAM,IAAI,CAAC,UAAU,GAAG,KAAI;AAC5B;EACA,MAAM,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAC;EAC1C,MAAM,IAAI,CAAC,gBAAgB,GAAG,KAAI;AAClC;EACA,MAAM,aAAa,CAAC,IAAI,CAAC,SAAS,EAAC;EACnC,MAAM,IAAI,CAAC,SAAS,GAAG,KAAI;EAC3B,MAAM,IAAI,CAAC,MAAM,GAAG,KAAI;EACxB,MAAM,IAAI,CAAC,GAAG,GAAG,KAAI;AACrB;EACA,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;EACzB,QAAQ,IAAI;EACZ,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAE;EAC/B,SAAS,CAAC,OAAO,GAAG,EAAE,EAAE;AACxB;EACA;EACA,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAI;EACtC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAI;EACnC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAI;EACpC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAI;EACpC,OAAO;EACP,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE;EACpB,QAAQ,IAAI;EACZ,UAAU,IAAI,CAAC,GAAG,CAAC,KAAK,GAAE;EAC1B,SAAS,CAAC,OAAO,GAAG,EAAE,EAAE;AACxB;EACA;EACA,QAAQ,IAAI,CAAC,GAAG,CAAC,0BAA0B,GAAG,KAAI;EAClD,QAAQ,IAAI,CAAC,GAAG,CAAC,yBAAyB,GAAG,KAAI;EACjD,QAAQ,IAAI,CAAC,GAAG,CAAC,sBAAsB,GAAG,KAAI;EAC9C,QAAQ,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,KAAI;EACtC,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,KAAI;EAC/B,QAAQ,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,KAAI;EACrC,OAAO;EACP,MAAM,IAAI,CAAC,GAAG,GAAG,KAAI;EACrB,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAI;AAC1B;EACA,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAC;EACtC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAC;EACxB,KAAK,EAAC;EACN,GAAG;AACH;EACA,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE;EACrB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;EACxB;EACA;EACA;EACA,MAAM,OAAO,IAAI,CAAC,OAAO;EACzB,QAAQ,OAAO;EACf,UAAU,IAAI,KAAK,CAAC,kDAAkD,CAAC;EACvE,UAAU,kBAAkB;EAC5B,SAAS;EACT,OAAO;EACP,KAAK;AACL;EACA,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAO;EACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,cAAa;AAC5C;EACA,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,KAAK,QAAQ,EAAE;EACtE,MAAM,IAAI,CAAC,QAAQ,CAAC,0BAA0B,GAAG,oBAAmB;EACpE,KAAK;AACL;EACA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAK;AAC1C;EACA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,IAAI;EACvC,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAC;EACnC,MAAK;EACL,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,MAAM;EAC9C,MAAM,IAAI,CAAC,2BAA2B,GAAE;EACxC,MAAK;EACL,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM;EACjC,MAAM,IAAI,CAAC,cAAc,GAAE;EAC3B,MAAK;EACL,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,MAAM;EAClC,MAAM,IAAI,CAAC,eAAe,GAAE;EAC5B,MAAK;EACL,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,IAAI;EACnC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC,EAAC;EACpD,MAAK;AACL;EACA;EACA;EACA,IAAI,IAAI,SAAS,GAAG,MAAK;EACzB,IAAI,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAM;EAC9C;EACA,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;EACnE,QAAQ,IAAI,SAAS,EAAE,IAAI,CAAC,eAAe,GAAE;EAC7C,QAAQ,SAAS,GAAG,KAAI;EACxB,OAAO,MAAM;EACb,QAAQ,SAAS,GAAG,MAAK;EACzB,OAAO;EACP,KAAK,EAAE,uBAAuB,EAAC;EAC/B,GAAG;AACH;EACA,EAAE,wBAAwB,CAAC,GAAG;EAC9B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC9B,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE,MAAM;EACtC,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAC;EAC9C,IAAI,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,MAAM;EAC9C,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;EAC9B,QAAQ,IAAI,CAAC,YAAY,GAAG,KAAI;EAChC,QAAQ,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAC;EACpD,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAC;EAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAC;EACjC,OAAO;EACP,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAC;EAC/B,GAAG;AACH;EACA,EAAE,YAAY,CAAC,GAAG;EAClB,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AAC9B;EACA,IAAI,IAAI,CAAC,GAAG;EACZ,OAAO,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;EACrC,OAAO,IAAI,CAAC,KAAK,IAAI;EACrB,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,EAAC,EAAE;EAC7F,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAC;AAChD;EACA,QAAQ,MAAM,SAAS,GAAG,MAAM;EAChC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EACpC,UAAU,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAK;EAC3D,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAC;EAC/B,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAC9B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;EAC7B,YAAY,GAAG,EAAE,MAAM,CAAC,GAAG;EAC3B,WAAW,EAAC;EACZ,UAAS;AACT;EACA,QAAQ,MAAM,SAAS,GAAG,MAAM;EAChC,UAAU,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAC;EAC5C,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EACpC,UAAU,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,SAAS,GAAE;EAC5D,eAAe,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAC;EACnD,UAAS;AACT;EACA,QAAQ,MAAM,OAAO,GAAG,GAAG,IAAI;EAC/B,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAC;EACjE,UAAS;AACT;EACA,QAAQ,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,OAAO,EAAC;EAC1E,OAAO,CAAC;EACR,OAAO,KAAK,CAAC,GAAG,IAAI;EACpB,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC,EAAC;EACtD,OAAO,EAAC;EACR,GAAG;AACH;EACA,EAAE,2BAA2B,CAAC,GAAG;EACjC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE;EAClC,MAAM,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,WAAW,IAAI;EACxD,QAAQ;EACR,UAAU,CAAC,WAAW,CAAC,GAAG;EAC1B,UAAU,WAAW,CAAC,MAAM,CAAC,KAAK;EAClC,UAAU,CAAC,WAAW,CAAC,SAAS;EAChC,UAAU;EACV,UAAU,WAAW,CAAC,SAAS,GAAG,KAAI;EACtC,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAC;EAC5D,SAAS;EACT,OAAO,EAAC;EACR,KAAK;EACL,GAAG;AACH;EACA,EAAE,aAAa,CAAC,GAAG;EACnB,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AAC9B;EACA,IAAI,IAAI,CAAC,GAAG;EACZ,OAAO,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC;EACvC,OAAO,IAAI,CAAC,MAAM,IAAI;EACtB,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,EAAC,EAAE;EAC/F,QAAQ,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAC;AAClD;EACA,QAAQ,MAAM,UAAU,GAAG,MAAM;EACjC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EACpC,UAAU,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAM;EAC5D,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAC;EAC/B,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAC9B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;EAC7B,YAAY,GAAG,EAAE,MAAM,CAAC,GAAG;EAC3B,WAAW,EAAC;EACZ,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,2BAA2B,GAAE;EACjE,UAAS;AACT;EACA,QAAQ,MAAM,SAAS,GAAG,MAAM;EAChC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EACpC,UAAU,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,UAAU,GAAE;EAC7D,eAAe,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,EAAC;EACpD,UAAS;AACT;EACA,QAAQ,MAAM,OAAO,GAAG,GAAG,IAAI;EAC/B,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAC;EACjE,UAAS;AACT;EACA,QAAQ,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,OAAO,EAAC;EAC3E,OAAO,CAAC;EACR,OAAO,KAAK,CAAC,GAAG,IAAI;EACpB,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,mBAAmB,CAAC,EAAC;EACvD,OAAO,EAAC;EACR,GAAG;AACH;EACA,EAAE,wBAAwB,CAAC,GAAG;EAC9B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC9B,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,eAAe,KAAK,QAAQ,EAAE;EAC/C,MAAM,IAAI,CAAC,OAAO;EAClB,QAAQ,OAAO,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,EAAE,wBAAwB,CAAC;EAC1E,QAAO;EACP,KAAK;EACL,GAAG;AACH;EACA,EAAE,iBAAiB,CAAC,GAAG;EACvB,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC9B,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAkB;EAC1D,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAiB;AACxD;EACA,IAAI,IAAI,CAAC,MAAM;EACf,MAAM,iDAAiD;EACvD,MAAM,kBAAkB;EACxB,MAAM,iBAAiB;EACvB,MAAK;EACL,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAC;AACtE;EACA,IAAI;EACJ,MAAM,kBAAkB,KAAK,WAAW;EACxC,MAAM,kBAAkB,KAAK,WAAW;EACxC,MAAM;EACN,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAI;EAC1B,MAAM,IAAI,CAAC,WAAW,GAAE;EACxB,KAAK;EACL,IAAI,IAAI,kBAAkB,KAAK,QAAQ,EAAE;EACzC,MAAM,IAAI,CAAC,OAAO;EAClB,QAAQ,OAAO;EACf,UAAU,IAAI,KAAK,CAAC,wBAAwB,CAAC;EAC7C,UAAU,4BAA4B;EACtC,SAAS;EACT,QAAO;EACP,KAAK;EACL,IAAI,IAAI,kBAAkB,KAAK,QAAQ,EAAE;EACzC,MAAM,IAAI,CAAC,OAAO;EAClB,QAAQ,OAAO;EACf,UAAU,IAAI,KAAK,CAAC,wBAAwB,CAAC;EAC7C,UAAU,2BAA2B;EACrC,SAAS;EACT,QAAO;EACP,KAAK;EACL,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE;EAChB;EACA,IAAI,MAAM,aAAa,GAAG,MAAM,IAAI;EACpC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,gBAAgB,EAAE;EAC9E,QAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;EACvC,UAAU,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAC;EACtC,SAAS,EAAC;EACV,OAAO;EACP,MAAM,OAAO,MAAM;EACnB,MAAK;AACL;EACA;EACA,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE;EACrE,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI;EAC9B,QAAQ,GAAG,IAAI;EACf,UAAU,MAAM,OAAO,GAAG,GAAE;EAC5B,UAAU,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI;EAChC,YAAY,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAC;EAC/C,WAAW,EAAC;EACZ,UAAU,EAAE,CAAC,IAAI,EAAE,OAAO,EAAC;EAC3B,SAAS;EACT,QAAQ,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC;EACtB,QAAO;AACP;EACA;EACA,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;EAC7C,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ;EACvB,QAAQ,GAAG,IAAI;EACf;EACA,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AACpC;EACA,UAAU,MAAM,OAAO,GAAG,GAAE;EAC5B,UAAU,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI;EACzC,YAAY,MAAM,MAAM,GAAG,GAAE;EAC7B,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI;EAC3C,cAAc,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAC;EAC9C,aAAa,EAAC;EACd,YAAY,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,GAAE;EACjC,YAAY,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAI;EACrC,YAAY,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,UAAS;EAC/C,YAAY,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAC;EAC/C,WAAW,EAAC;EACZ,UAAU,EAAE,CAAC,IAAI,EAAE,OAAO,EAAC;EAC3B,SAAS;EACT,QAAQ,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC;EACtB,QAAO;AACP;EACA;EACA;EACA,KAAK,MAAM;EACX,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,EAAC;EAClB,KAAK;EACL,GAAG;AACH;EACA,EAAE,WAAW,CAAC,GAAG;EACjB,IAAI,IAAI,CAAC,MAAM;EACf,MAAM,6BAA6B;EACnC,MAAM,IAAI,CAAC,QAAQ;EACnB,MAAM,IAAI,CAAC,aAAa;EACxB,MAAK;EACL,IAAI;EACJ,MAAM,IAAI,CAAC,UAAU;EACrB,MAAM,IAAI,CAAC,WAAW;EACtB,MAAM,CAAC,IAAI,CAAC,QAAQ;EACpB,MAAM,CAAC,IAAI,CAAC,aAAa;EACzB,MAAM,EAAE,MAAM,EAAE;AAChB;EACA,IAAI,IAAI,CAAC,WAAW,GAAG,KAAI;AAC3B;EACA;EACA,IAAI,MAAM,iBAAiB,GAAG,MAAM;EACpC,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AAChC;EACA,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;EACpC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AAClC;EACA;EACA,QAAQ,IAAI,GAAG,EAAE,KAAK,GAAG,GAAE;AAC3B;EACA,QAAQ,MAAM,gBAAgB,GAAG,GAAE;EACnC,QAAQ,MAAM,eAAe,GAAG,GAAE;EAClC,QAAQ,MAAM,cAAc,GAAG,GAAE;EACjC,QAAQ,IAAI,0BAA0B,GAAG,MAAK;AAC9C;EACA,QAAQ,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;EAC9B;EACA;EACA,UAAU;EACV,YAAY,IAAI,CAAC,IAAI,KAAK,iBAAiB;EAC3C,YAAY,IAAI,CAAC,IAAI,KAAK,kBAAkB;EAC5C,YAAY;EACZ,YAAY,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAI;EAC5C,WAAW;EACX,UAAU;EACV,YAAY,IAAI,CAAC,IAAI,KAAK,gBAAgB;EAC1C,YAAY,IAAI,CAAC,IAAI,KAAK,iBAAiB;EAC3C,YAAY;EACZ,YAAY,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAI;EAC3C,WAAW;EACX,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;EAC/E,YAAY,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAI;EAC1C,WAAW;EACX,SAAS,EAAC;AACV;EACA,QAAQ,MAAM,wBAAwB,GAAG,qBAAqB,IAAI;EAClE,UAAU,0BAA0B,GAAG,KAAI;AAC3C;EACA,UAAU,IAAI,KAAK,GAAG,eAAe,CAAC,qBAAqB,CAAC,gBAAgB,EAAC;AAC7E;EACA,UAAU,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;EACpD;EACA,YAAY,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,QAAO;EACzD,YAAY,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAC;EAC/C,WAAW,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE;EAC/C;EACA,YAAY,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,UAAS;EAC/C,YAAY,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,EAAC;EACrD,WAAW,MAAM;EACjB,YAAY,OAAO,qBAAqB,CAAC,gBAAgB,KAAK,QAAQ;EACtE,YAAY;EACZ;EACA,YAAY,KAAK,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,EAAC;EACrE,YAAY,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,EAAC;EACxC,YAAY,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;EAC7C,WAAW;EACX,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;EACjC,YAAY,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;EAC9D,gBAAgB,MAAM;EACtB,gBAAgB,OAAM;EACtB,WAAW;AACX;EACA,UAAU,IAAI,MAAM;EACpB,YAAY,gBAAgB,CAAC,qBAAqB,CAAC,iBAAiB,EAAC;AACrE;EACA,UAAU,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE;EACvD;EACA,YAAY,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,QAAO;EAC5D,YAAY,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;EACjD,WAAW,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;EACjD;EACA,YAAY,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,UAAS;EACjD,YAAY,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,EAAC;EACvD,WAAW,MAAM;EACjB,YAAY,OAAO,qBAAqB,CAAC,iBAAiB,KAAK,QAAQ;EACvE,YAAY;EACZ;EACA,YAAY,MAAM,GAAG,qBAAqB,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,EAAC;EACvE,YAAY,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,EAAC;EAC1C,YAAY,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC;EAC/C,WAAW;EACX,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE;EAClC,YAAY,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC;EAChE,gBAAgB,MAAM;EACtB,gBAAgB,OAAM;EACtB,WAAW;AACX;EACA,UAAU,IAAI,CAAC,MAAM;EACrB,YAAY,oCAAoC;EAChD,YAAY,IAAI,CAAC,YAAY;EAC7B,YAAY,IAAI,CAAC,SAAS;EAC1B,YAAY,IAAI,CAAC,aAAa;EAC9B,YAAY,IAAI,CAAC,UAAU;EAC3B,YAAW;EACX,UAAS;AACT;EACA,QAAQ,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;EAC9B;EACA,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,uBAAuB,EAAE;EACzE,YAAY,wBAAwB;EACpC,cAAc,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC;EAC1D,cAAa;EACb,WAAW;AACX;EACA;EACA,UAAU;EACV,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB;EAC9C,cAAc,IAAI,CAAC,oBAAoB,KAAK,MAAM;EAClD,aAAa,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe;EAC3C,cAAc,IAAI,CAAC,IAAI,KAAK,gBAAgB;EAC5C,cAAc,IAAI,CAAC,QAAQ,CAAC;EAC5B,YAAY;EACZ,YAAY,wBAAwB,CAAC,IAAI,EAAC;EAC1C,WAAW;EACX,SAAS,EAAC;AACV;EACA;EACA;EACA,QAAQ;EACR,UAAU,CAAC,0BAA0B;EACrC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM;EAC9C,YAAY,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;EAChD,UAAU;EACV,UAAU,UAAU,CAAC,iBAAiB,EAAE,GAAG,EAAC;EAC5C,UAAU,MAAM;EAChB,SAAS,MAAM;EACf,UAAU,IAAI,CAAC,WAAW,GAAG,MAAK;EAClC,UAAU,IAAI,CAAC,UAAU,GAAG,KAAI;EAChC,SAAS;AACT;EACA,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;EACzB,UAAU,IAAI;EACd,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC;EAClC,WAAW,CAAC,OAAO,GAAG,EAAE;EACxB,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;EACjE,WAAW;EACX,UAAU,IAAI,CAAC,MAAM,GAAG,KAAI;EAC5B,UAAU,IAAI,CAAC,MAAM,CAAC,wCAAwC,EAAC;AAC/D;EACA,UAAU,MAAM,EAAE,GAAG,IAAI,CAAC,IAAG;EAC7B,UAAU,IAAI,CAAC,GAAG,GAAG,KAAI;EACzB,UAAU,EAAE,CAAC,IAAI,EAAC;EAClB,SAAS;AACT;EACA;EACA;EACA,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,KAAK,QAAQ,EAAE;EAC1E,UAAU,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,GAAG,EAAC;EACrE,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAE;EAC1D,SAAS;AACT;EACA,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAC;EAC9B,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAC;EAC5B,OAAO,EAAC;EACR,MAAK;EACL,IAAI,iBAAiB,GAAE;EACvB,GAAG;AACH;EACA,EAAE,WAAW,CAAC,GAAG;EACjB,IAAI;EACJ,MAAM,CAAC,IAAI,CAAC,GAAG;EACf,MAAM,CAAC,IAAI,CAAC,QAAQ;EACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,mBAAmB;EACxD,MAAM;EACN,MAAM,MAAM;EACZ,KAAK;EACL,IAAI,IAAI,CAAC,2BAA2B,GAAE;EACtC,GAAG;AACH;EACA,EAAE,uBAAuB,CAAC,GAAG;EAC7B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AAC9B;EACA,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,QAAQ,EAAE;EAC9C,MAAM,IAAI,CAAC,cAAc,GAAG,MAAK;AACjC;EACA;EACA,MAAM,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAAC,sBAAsB,EAAC;EACvE,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,IAAI;EACpD,QAAQ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAC;EACpC,QAAQ,IAAI,CAAC,kBAAkB,GAAG,KAAI;EACtC,OAAO,EAAC;EACR,MAAM,IAAI,CAAC,sBAAsB,GAAG,GAAE;AACtC;EACA,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;EACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAC;EACjD,QAAQ,IAAI,CAAC,kBAAkB,GAAG,MAAK;EACvC,QAAQ,IAAI,CAAC,iBAAiB,GAAE;EAChC,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY,EAAC;EACjC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAC;EAC/B,OAAO;EACP,KAAK;AACL;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAC;EACnE,IAAI,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAC;EAC9D,GAAG;AACH;EACA,EAAE,eAAe,CAAC,CAAC,KAAK,EAAE;EAC1B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC9B,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE;EACzC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAC1B,QAAQ,IAAI,EAAE,WAAW;EACzB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS;EAC9C,UAAU,aAAa,EAAE,KAAK,CAAC,SAAS,CAAC,aAAa;EACtD,UAAU,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,MAAM;EACxC,SAAS;EACT,OAAO,EAAC;EACR,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;EACvD,MAAM,IAAI,CAAC,YAAY,GAAG,KAAI;EAC9B,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAC;EAC/B,KAAK;EACL;EACA,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE;EACzB,MAAM,IAAI,CAAC,wBAAwB,GAAE;EACrC,KAAK;EACL,GAAG;AACH;EACA,EAAE,iBAAiB,CAAC,CAAC,KAAK,EAAE;EAC5B,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC9B,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,KAAI;EACzB,IAAI,IAAI,IAAI,YAAY,WAAW,EAAE,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,EAAC;EAChE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAC;EAC3B,GAAG;AACH;EACA,EAAE,2BAA2B,CAAC,GAAG;EACjC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM;EAC3C,IAAI,IAAI,CAAC,MAAM;EACf,MAAM,wCAAwC;EAC9C,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc;EAClC,MAAK;EACL,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,IAAG;EACvB,IAAI,IAAI,CAAC,GAAG,GAAG,KAAI;EACnB,IAAI,EAAE,CAAC,IAAI,EAAC;EACZ,GAAG;AACH;EACA,EAAE,cAAc,CAAC,GAAG;EACpB,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EACjD,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAC;EAClC,IAAI,IAAI,CAAC,aAAa,GAAG,KAAI;EAC7B,IAAI,IAAI,CAAC,WAAW,GAAE;EACtB,GAAG;AACH;EACA,EAAE,eAAe,CAAC,GAAG;EACrB,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAC;EACnC,IAAI,IAAI,CAAC,OAAO,GAAE;EAClB,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,CAAC,KAAK,EAAE;EACnB,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;AAC9B;EACA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,IAAI;EACzC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAC;EAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,WAAW,EAAC;AAClD;EACA,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;EAC9B,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;EAC1B,QAAQ,MAAM,EAAE,WAAW;EAC3B,OAAO,EAAC;AACR;EACA,MAAM;EACN,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,IAAI;EACjD,UAAU,OAAO,YAAY,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE;EACnD,SAAS,CAAC;EACV,QAAQ,EAAE,MAAM,EAAE;AAClB;EACA,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAC;EAC3C,MAAM,cAAc,CAAC,MAAM;EAC3B,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAC;EAChC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAC;EACxC,OAAO,EAAC;EACR,KAAK,EAAC;EACN,GAAG;AACH;EACA,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,EAAE;EACnB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM;EAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,EAAC;EAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,EAAC;EACxB,GAAG;AACH;EACA;EACA,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;EACrB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,KAAI;EACzB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAC;EAC9C,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAC;EAC9B,GAAG;AACH;EACA,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;EACtB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,KAAI;EACzB,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;EAClC,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM;EAC1B,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAC;EAC9B,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,EAAC;EAC7C,GAAG;AACH;EACA,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;EACvB,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,KAAK;EACnC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAC;EAC9B,MAAM,QAAQ,CAAC,GAAG,IAAI,EAAC;EACvB,MAAK;EACL,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,SAAS,EAAC;EAC3B,GAAG;AACH;EACA,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;EACtB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,KAAI;EACzB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM;EAC7B,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;EACzC,MAAM,IAAI;EACV,QAAQ,QAAQ,CAAC,GAAG,IAAI,EAAC;EACzB,OAAO,CAAC,OAAO,GAAG,EAAE;EACpB,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,EAAC;EAC1B,OAAO;EACP,KAAK;EACL,GAAG;EACH,CAAC;AACD;EACA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,aAAa,GAAE;AACvC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC,MAAM,GAAG;EACd,EAAE,UAAU,EAAE;EACd,IAAI;EACJ,MAAM,IAAI,EAAE;EACZ,QAAQ,8BAA8B;EACtC,QAAQ,kCAAkC;EAC1C,OAAO;EACP,KAAK;EACL,GAAG;EACH,EAAE,YAAY,EAAE,cAAc;EAC9B,EAAC;AACD;EACA,IAAI,CAAC,aAAa,GAAG;;EC/oCmB,MAAM,6BAA6B,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAC,CAAC,UAAU,EAAE,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,WAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,2BAA2B,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,iDAAiD,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,yBAAyB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAIA,IAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,YAAY,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,GAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC;;;;;;;;;;;;"}