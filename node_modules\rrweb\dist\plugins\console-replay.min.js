var rrwebConsoleReplay=function(c){"use strict";var i;(function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"})(i||(i={}));const r=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`;let d={map:{},getId(){return console.error(r),-1},getNode(){return console.error(r),null},removeNodeFromMap(){console.error(r)},has(){return console.error(r),!1},reset(){console.error(r)}};typeof window<"u"&&window.Proxy&&window.Reflect&&(d=new Proxy(d,{get(e,t,o){return t==="map"&&console.error(r),Reflect.get(e,t,o)}}));const p="rrweb/console@1";var u=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(u||{}),g=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e))(g||{}),m=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(m||{}),y=(e=>(e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e))(y||{}),S=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(S||{}),C=(e=>(e.Start="start",e.Pause="pause",e.Resume="resume",e.Resize="resize",e.Finish="finish",e.FullsnapshotRebuilded="fullsnapshot-rebuilded",e.LoadStylesheetStart="load-stylesheet-start",e.LoadStylesheetEnd="load-stylesheet-end",e.SkipStart="skip-start",e.SkipEnd="skip-end",e.MouseInteraction="mouse-interaction",e.EventCast="event-cast",e.CustomEvent="custom-event",e.Flush="flush",e.StateChange="state-change",e.PlayBack="play-back",e.Destroy="destroy",e))(C||{});const s="__rrweb_original__",M={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],replayLogger:void 0};class f{constructor(t){this.config=Object.assign(M,t)}getConsoleLogger(){const t={};for(const o of this.config.level)o==="trace"?t[o]=n=>{(console.log[s]?console.log[s]:console.log)(...n.payload.map(l=>JSON.parse(l)),this.formatMessage(n))}:t[o]=n=>{(console[o][s]?console[o][s]:console[o])(...n.payload.map(l=>JSON.parse(l)),this.formatMessage(n))};return t}formatMessage(t){if(t.trace.length===0)return"";const o=`
	at `;let n=o;return n+=t.trace.join(o),n}}const v=e=>{const t=e?.replayLogger||new f(e).getConsoleLogger();return{handler(o,n,l){let a=null;if(o.type===u.IncrementalSnapshot&&o.data.source===g.Log?a=o.data:o.type===u.Plugin&&o.data.plugin===p&&(a=o.data.payload),a)try{typeof t[a.level]=="function"&&t[a.level](a)}catch(D){l.replayer.config.showWarning&&console.warn(D)}}}};return c.getReplayConsolePlugin=v,Object.defineProperty(c,"__esModule",{value:!0}),c}({});
//# sourceMappingURL=console-replay.min.js.map
