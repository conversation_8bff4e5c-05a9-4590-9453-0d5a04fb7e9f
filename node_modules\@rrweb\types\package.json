{"name": "@rrweb/types", "version": "2.0.0-alpha.18", "publishConfig": {"access": "public"}, "keywords": ["rrweb", "@rrweb/types"], "scripts": {"dev": "vite build --watch", "build": "yarn turbo run prepublish", "check-types": "tsc -noEmit", "prepublish": "tsc -noEmit && vite build", "lint": "yarn eslint src/**/*.ts"}, "homepage": "https://github.com/rrweb-io/rrweb/tree/main/packages/@rrweb/types#readme", "bugs": {"url": "https://github.com/rrweb-io/rrweb/issues"}, "repository": {"type": "git", "url": "git+https://github.com/rrweb-io/rrweb.git"}, "license": "MIT", "type": "module", "main": "./dist/types.umd.cjs", "module": "./dist/types.js", "unpkg": "./dist/types.umd.cjs", "typings": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/types.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/types.umd.cjs"}}}, "files": ["dist", "package.json"], "devDependencies": {"vite": "^5.3.1", "vite-plugin-dts": "^3.9.1"}, "browserslist": ["supports es6-class"]}