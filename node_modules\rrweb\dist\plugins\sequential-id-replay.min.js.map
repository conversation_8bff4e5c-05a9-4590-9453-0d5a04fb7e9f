{"version": 3, "file": "sequential-id-replay.min.js", "sources": ["../../src/plugins/sequential-id/replay/index.ts"], "sourcesContent": ["import type { SequentialIdOptions } from '../record';\nimport type { ReplayPlugin } from '../../../types';\nimport type { eventWithTime } from '@rrweb/types';\n\ntype Options = SequentialIdOptions & {\n  warnOnMissingId: boolean;\n};\n\nconst defaultOptions: Options = {\n  key: '_sid',\n  warnOnMissingId: true,\n};\n\nexport const getReplaySequentialIdPlugin: (\n  options?: Partial<Options>,\n) => ReplayPlugin = (options) => {\n  const { key, warnOnMissingId } = options\n    ? Object.assign({}, defaultOptions, options)\n    : defaultOptions;\n  let currentId = 1;\n\n  return {\n    handler(event: eventWithTime) {\n      if (key in event) {\n        const id = ((event as unknown) as Record<string, number>)[key];\n        if (id !== currentId) {\n          console.error(\n            `[sequential-id-plugin]: expect to get an id with value \"${currentId}\", but got \"${id}\"`,\n          );\n        } else {\n          currentId++;\n        }\n      } else if (warnOnMissingId) {\n        console.warn(\n          `[sequential-id-plugin]: failed to get id in key: \"${key}\"`,\n        );\n      }\n    },\n  };\n};\n"], "names": [], "mappings": ";;;EAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAa,MAAC,2BAA2B,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,wDAAwD,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,kDAAkD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;;;;;;;;;;;;"}