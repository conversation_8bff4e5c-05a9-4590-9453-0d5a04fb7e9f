export declare enum NodeType {
    Document = 0,
    DocumentType = 1,
    Element = 2,
    Text = 3,
    CDATA = 4,
    Comment = 5
}
export declare type documentNode = {
    type: NodeType.Document;
    childNodes: serializedNodeWithId[];
    compatMode?: string;
};
export declare type documentTypeNode = {
    type: NodeType.DocumentType;
    name: string;
    publicId: string;
    systemId: string;
};
export declare type attributes = {
    [key: string]: string | number | true;
};
export declare type legacyAttributes = {
    selected: false;
};
export declare type elementNode = {
    type: NodeType.Element;
    tagName: string;
    attributes: attributes;
    childNodes: serializedNodeWithId[];
    isSVG?: true;
    needBlock?: boolean;
};
export declare type textNode = {
    type: NodeType.Text;
    textContent: string;
    isStyle?: true;
};
export declare type cdataNode = {
    type: NodeType.CDATA;
    textContent: '';
};
export declare type commentNode = {
    type: NodeType.Comment;
    textContent: string;
};
export declare type serializedNode = (documentNode | documentTypeNode | elementNode | textNode | cdataNode | commentNode) & {
    rootId?: number;
    isShadowHost?: boolean;
    isShadow?: boolean;
};
export declare type serializedNodeWithId = serializedNode & {
    id: number;
};
export declare type serializedElementNodeWithId = Extract<serializedNodeWithId, Record<'type', NodeType.Element>>;
export declare type tagMap = {
    [key: string]: string;
};
export interface INode extends Node {
    __sn: serializedNodeWithId;
}
export interface ICanvas extends HTMLCanvasElement {
    __context: string;
}
export interface IMirror<TNode> {
    getId(n: TNode | undefined | null): number;
    getNode(id: number): TNode | null;
    getIds(): number[];
    getMeta(n: TNode): serializedNodeWithId | null;
    removeNodeFromMap(n: TNode): void;
    has(id: number): boolean;
    hasNode(node: TNode): boolean;
    add(n: TNode, meta: serializedNodeWithId): void;
    replace(id: number, n: TNode): void;
    reset(): void;
}
export declare type idNodeMap = Map<number, Node>;
export declare type nodeMetaMap = WeakMap<Node, serializedNodeWithId>;
export declare type MaskInputOptions = Partial<{
    color: boolean;
    date: boolean;
    'datetime-local': boolean;
    email: boolean;
    month: boolean;
    number: boolean;
    range: boolean;
    search: boolean;
    tel: boolean;
    text: boolean;
    time: boolean;
    url: boolean;
    week: boolean;
    textarea: boolean;
    select: boolean;
    password: boolean;
}>;
export declare type SlimDOMOptions = Partial<{
    script: boolean;
    comment: boolean;
    headFavicon: boolean;
    headWhitespace: boolean;
    headMetaDescKeywords: boolean;
    headMetaSocial: boolean;
    headMetaRobots: boolean;
    headMetaHttpEquiv: boolean;
    headMetaAuthorship: boolean;
    headMetaVerification: boolean;
}>;
export declare type DataURLOptions = Partial<{
    type: string;
    quality: number;
}>;
export declare type MaskTextFn = (text: string) => string;
export declare type MaskInputFn = (text: string) => string;
export declare type KeepIframeSrcFn = (src: string) => boolean;
export declare type BuildCache = {
    stylesWithHoverClass: Map<string, string>;
};
