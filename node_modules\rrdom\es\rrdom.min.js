var e;!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(e||(e={}));var t=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){var t;if(!e)return-1;var n=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=n?n:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,n=this.getId(e);this.idNodeMap.delete(n),e.childNodes&&e.childNodes.forEach((function(e){return t.removeNodeFromMap(e)}))},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){var n=this.getNode(e);if(n){var o=this.nodeMetaMap.get(n);o&&this.nodeMetaMap.set(t,o)}this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function n(e){const t=[];for(const n in e){const o=e[n];if("string"!=typeof o)continue;const s=a(n);t.push(`${s}: ${o};`)}return t.join(" ")}const o=/-([a-z])/g,s=/^--[a-zA-Z0-9-]+$/,r=e=>s.test(e)?e:e.replace(o,((e,t)=>t?t.toUpperCase():"")),i=/\B([A-Z])/g,a=e=>e.replace(i,"-$1").toLowerCase();class d{constructor(...e){this.childNodes=[],this.parentElement=null,this.parentNode=null,this.ELEMENT_NODE=E.ELEMENT_NODE,this.TEXT_NODE=E.TEXT_NODE}get firstChild(){return this.childNodes[0]||null}get lastChild(){return this.childNodes[this.childNodes.length-1]||null}get nextSibling(){const e=this.parentNode;if(!e)return null;const t=e.childNodes,n=t.indexOf(this);return t[n+1]||null}contains(e){if(e===this)return!0;for(const t of this.childNodes)if(t.contains(e))return!0;return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}function c(t){return class n extends t{constructor(){super(...arguments),this.nodeType=E.DOCUMENT_NODE,this.nodeName="#document",this.compatMode="CSS1Compat",this.RRNodeType=e.Document,this.textContent=null}get documentElement(){return this.childNodes.find((t=>t.RRNodeType===e.Element&&"HTML"===t.tagName))||null}get body(){var t;return(null===(t=this.documentElement)||void 0===t?void 0:t.childNodes.find((t=>t.RRNodeType===e.Element&&"BODY"===t.tagName)))||null}get head(){var t;return(null===(t=this.documentElement)||void 0===t?void 0:t.childNodes.find((t=>t.RRNodeType===e.Element&&"HEAD"===t.tagName)))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(t){const n=t.RRNodeType;if((n===e.Element||n===e.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===n)))throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${n===e.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);return t.parentElement=null,t.parentNode=this,this.childNodes.push(t),t}insertBefore(t,n){const o=t.RRNodeType;if((o===e.Element||o===e.DocumentType)&&this.childNodes.some((e=>e.RRNodeType===o)))throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${o===e.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);if(null===n)return this.appendChild(t);const s=this.childNodes.indexOf(n);if(-1==s)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(s,0,t),t.parentElement=null,t.parentNode=this,t}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}open(){this.childNodes=[]}close(){}write(e){let t;if('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'===e?t="-//W3C//DTD XHTML 1.0 Transitional//EN":'<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">'===e&&(t="-//W3C//DTD HTML 4.0 Transitional//EN"),t){const e=this.createDocumentType("html",t,"");this.open(),this.appendChild(e)}}createDocument(e,t,o){return new n}createDocumentType(e,t,n){const o=new(l(d))(e,t,n);return o.ownerDocument=this,o}createElement(e){const t=new(h(d))(e);return t.ownerDocument=this,t}createElementNS(e,t){return this.createElement(t)}createTextNode(e){const t=new(p(d))(e);return t.ownerDocument=this,t}createComment(e){const t=new(m(d))(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new(N(d))(e);return t.ownerDocument=this,t}toString(){return"RRDocument"}}}function l(t){return class extends t{constructor(t,n,o){super(),this.nodeType=E.DOCUMENT_TYPE_NODE,this.RRNodeType=e.DocumentType,this.textContent=null,this.name=t,this.publicId=n,this.systemId=o,this.nodeName=t}toString(){return"RRDocumentType"}}}function h(t){return class extends t{constructor(t){super(),this.nodeType=E.ELEMENT_NODE,this.RRNodeType=e.Element,this.attributes={},this.shadowRoot=null,this.tagName=t.toUpperCase(),this.nodeName=t.toUpperCase()}get textContent(){let e="";return this.childNodes.forEach((t=>e+=t.textContent)),e}set textContent(e){this.childNodes=[this.ownerDocument.createTextNode(e)]}get classList(){return new f(this.attributes.class,(e=>{this.attributes.class=e}))}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const e=this.attributes.style?function(e){const t={},n=/:(.+)/;return e.replace(/\/\*.*?\*\//g,"").split(/;(?![^(]*\))/g).forEach((function(e){if(e){const o=e.split(n);o.length>1&&(t[r(o[0].trim())]=o[1].trim())}})),t}(this.attributes.style):{},t=/\B([A-Z])/g;return e.setProperty=(o,s,i)=>{if(t.test(o))return;const a=r(o);s?e[a]=s:delete e[a],"important"===i&&(e[a]+=" !important"),this.attributes.style=n(e)},e.removeProperty=o=>{if(t.test(o))return"";const s=r(o),i=e[s]||"";return delete e[s],this.attributes.style=n(e),i},e}getAttribute(e){return this.attributes[e]||null}setAttribute(e,t){this.attributes[e]=t}setAttributeNS(e,t,n){this.setAttribute(t,n)}removeAttribute(e){delete this.attributes[e]}appendChild(e){return this.childNodes.push(e),e.parentNode=this,e.parentElement=this,e}insertBefore(e,t){if(null===t)return this.appendChild(e);const n=this.childNodes.indexOf(t);if(-1==n)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(n,0,e),e.parentElement=this,e.parentNode=this,e}removeChild(e){const t=this.childNodes.indexOf(e);if(-1===t)throw new Error("Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(t,1),e.parentElement=null,e.parentNode=null,e}attachShadow(e){const t=this.ownerDocument.createElement("SHADOWROOT");return this.shadowRoot=t,t}dispatchEvent(e){return!0}toString(){let e="";for(const t in this.attributes)e+=`${t}="${this.attributes[t]}" `;return`${this.tagName} ${e}`}}}function u(e){return class extends e{attachShadow(e){throw new Error("RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow")}play(){this.paused=!1}pause(){this.paused=!0}}}function p(t){return class extends t{constructor(t){super(),this.nodeType=E.TEXT_NODE,this.nodeName="#text",this.RRNodeType=e.Text,this.data=t}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRText text=${JSON.stringify(this.data)}`}}}function m(t){return class extends t{constructor(t){super(),this.nodeType=E.COMMENT_NODE,this.nodeName="#comment",this.RRNodeType=e.Comment,this.data=t}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}}function N(t){return class extends t{constructor(t){super(),this.nodeName="#cdata-section",this.nodeType=E.CDATA_SECTION_NODE,this.RRNodeType=e.CDATA,this.data=t}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}}class f{constructor(e,t){if(this.classes=[],this.add=(...e)=>{for(const t of e){const e=String(t);this.classes.indexOf(e)>=0||this.classes.push(e)}this.onChange&&this.onChange(this.classes.join(" "))},this.remove=(...e)=>{this.classes=this.classes.filter((t=>-1===e.indexOf(t))),this.onChange&&this.onChange(this.classes.join(" "))},e){const t=e.trim().split(/\s+/);this.classes.push(...t)}this.onChange=t}}var E;!function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(E||(E={}));const T={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"},R={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function D(t,n,o,s){const r=t.childNodes,i=n.childNodes;s=s||n.mirror||n.ownerDocument.mirror,(r.length>0||i.length>0)&&g(Array.from(r),i,t,o,s);let a=null,d=null;switch(n.RRNodeType){case e.Document:d=n.scrollData;break;case e.Element:{const e=t,r=n;switch(function(e,t,n){const o=e.attributes,s=t.attributes;for(const o in s){const r=s[o],i=n.getMeta(t);if(i&&"isSVG"in i&&i.isSVG&&T[o])e.setAttributeNS(T[o],o,r);else if("CANVAS"===t.tagName&&"rr_dataURL"===o){const t=document.createElement("img");t.src=r,t.onload=()=>{const n=e.getContext("2d");n&&n.drawImage(t,0,0,t.width,t.height)}}else e.setAttribute(o,r)}for(const{name:t}of Array.from(o))t in s||e.removeAttribute(t);t.scrollLeft&&(e.scrollLeft=t.scrollLeft),t.scrollTop&&(e.scrollTop=t.scrollTop)}(e,r,s),d=r.scrollData,a=r.inputData,r.tagName){case"AUDIO":case"VIDEO":{const e=t,n=r;void 0!==n.paused&&(n.paused?e.pause():e.play()),void 0!==n.muted&&(e.muted=n.muted),void 0!==n.volume&&(e.volume=n.volume),void 0!==n.currentTime&&(e.currentTime=n.currentTime),void 0!==n.playbackRate&&(e.playbackRate=n.playbackRate);break}case"CANVAS":{const s=n;if(null!==s.rr_dataURL){const t=document.createElement("img");t.onload=()=>{const n=e.getContext("2d");n&&n.drawImage(t,0,0,t.width,t.height)},t.src=s.rr_dataURL}s.canvasMutations.forEach((e=>o.applyCanvas(e.event,e.mutation,t)))}break;case"STYLE":{const t=e.sheet;t&&n.rules.forEach((e=>o.applyStyleSheetMutation(e,t)))}}if(r.shadowRoot){e.shadowRoot||e.attachShadow({mode:"open"});const t=e.shadowRoot.childNodes,n=r.shadowRoot.childNodes;(t.length>0||n.length>0)&&g(Array.from(t),n,e.shadowRoot,o,s)}break}case e.Text:case e.Comment:case e.CDATA:t.textContent!==n.data&&(t.textContent=n.data)}if(d&&o.applyScroll(d,!0),a&&o.applyInput(a),"IFRAME"===n.nodeName){const e=t.contentDocument,r=n;if(e){const t=s.getMeta(r.contentDocument);t&&o.mirror.add(e,Object.assign({},t)),D(e,r.contentDocument,o,s)}}}function g(t,n,o,s,r){var i;let a,d,c=0,l=t.length-1,h=0,u=n.length-1,p=t[c],m=t[l],N=n[h],f=n[u];for(;c<=l&&h<=u;){const E=s.mirror.getId(p),T=s.mirror.getId(m),R=r.getId(N),g=r.getId(f);if(void 0===p)p=t[++c];else if(void 0===m)m=t[--l];else if(-1!==E&&E===R)D(p,N,s,r),p=t[++c],N=n[++h];else if(-1!==T&&T===g)D(m,f,s,r),m=t[--l],f=n[--u];else if(-1!==E&&E===g)o.insertBefore(p,m.nextSibling),D(p,f,s,r),p=t[++c],f=n[--u];else if(-1!==T&&T===R)o.insertBefore(m,p),D(m,N,s,r),m=t[--l],N=n[++h];else{if(!a){a={};for(let e=c;e<=l;e++){const n=t[e];n&&s.mirror.hasNode(n)&&(a[s.mirror.getId(n)]=e)}}if(d=a[r.getId(N)],d){const e=t[d];o.insertBefore(e,p),D(e,N,s,r),t[d]=void 0}else{const n=C(N,s.mirror,r);"#document"===o.nodeName&&(null===(i=s.mirror.getMeta(n))||void 0===i?void 0:i.type)===e.Element&&o.documentElement&&(o.removeChild(o.documentElement),t[c]=void 0,p=void 0),o.insertBefore(n,p||null),D(n,N,s,r)}N=n[++h]}}if(c>l){const e=n[u+1];let t=null;for(e&&o.childNodes.forEach((n=>{s.mirror.getId(n)===r.getId(e)&&(t=n)}));h<=u;++h){const e=C(n[h],s.mirror,r);o.insertBefore(e,t),D(e,n[h],s,r)}}else if(h>u)for(;c<=l;c++){const e=t[c];e&&(o.removeChild(e),s.mirror.removeNodeFromMap(e))}}function C(t,n,o){const s=o.getId(t),r=o.getMeta(t);let i=null;if(s>-1&&(i=n.getNode(s)),null!==i)return i;switch(t.RRNodeType){case e.Document:i=new Document;break;case e.DocumentType:i=document.implementation.createDocumentType(t.name,t.publicId,t.systemId);break;case e.Element:{let e=t.tagName.toLowerCase();e=R[e]||e,i=r&&"isSVG"in r&&(null==r?void 0:r.isSVG)?document.createElementNS(T.svg,e):document.createElement(t.tagName);break}case e.Text:i=document.createTextNode(t.data);break;case e.Comment:i=document.createComment(t.data);break;case e.CDATA:i=document.createCDATASection(t.data)}return r&&n.add(i,Object.assign({},r)),i}class M extends(c(d)){constructor(e){super(),this.UNSERIALIZED_STARTING_ID=-2,this._unserializedId=this.UNSERIALIZED_STARTING_ID,this.mirror=F(),this.scrollData=null,e&&(this.mirror=e)}get unserializedId(){return this._unserializedId--}createDocument(e,t,n){return new M}createDocumentType(e,t,n){const o=new y(e,t,n);return o.ownerDocument=this,o}createElement(e){const t=e.toUpperCase();let n;switch(t){case"AUDIO":case"VIDEO":n=new O(t);break;case"IFRAME":n=new I(t,this.mirror);break;case"CANVAS":n=new x(t);break;case"STYLE":n=new A(t);break;default:n=new w(t)}return n.ownerDocument=this,n}createComment(e){const t=new _(e);return t.ownerDocument=this,t}createCDATASection(e){const t=new v(e);return t.ownerDocument=this,t}createTextNode(e){const t=new b(e);return t.ownerDocument=this,t}destroyTree(){this.childNodes=[],this.mirror.reset()}open(){super.open(),this._unserializedId=this.UNSERIALIZED_STARTING_ID}}const y=l(d);class w extends(h(d)){constructor(){super(...arguments),this.inputData=null,this.scrollData=null}}class O extends(u(w)){}class x extends w{constructor(){super(...arguments),this.rr_dataURL=null,this.canvasMutations=[]}getContext(){return null}}class A extends w{constructor(){super(...arguments),this.rules=[]}}class I extends w{constructor(e,t){super(e),this.contentDocument=new M,this.contentDocument.mirror=t}}const b=p(d),_=m(d),v=N(d);function S(e,t,n,o){let s;switch(e.nodeType){case E.DOCUMENT_NODE:o&&"IFRAME"===o.nodeName?s=o.contentDocument:(s=t,s.compatMode=e.compatMode);break;case E.DOCUMENT_TYPE_NODE:{const n=e;s=t.createDocumentType(n.name,n.publicId,n.systemId);break}case E.ELEMENT_NODE:{const n=e,o=(r=n)instanceof HTMLFormElement?"FORM":r.tagName.toUpperCase();s=t.createElement(o);const i=s;for(const{name:e,value:t}of Array.from(n.attributes))i.attributes[e]=t;n.scrollLeft&&(i.scrollLeft=n.scrollLeft),n.scrollTop&&(i.scrollTop=n.scrollTop);break}case E.TEXT_NODE:s=t.createTextNode(e.textContent||"");break;case E.CDATA_SECTION_NODE:s=t.createCDATASection(e.data);break;case E.COMMENT_NODE:s=t.createComment(e.textContent||"");break;case E.DOCUMENT_FRAGMENT_NODE:s=o.attachShadow({mode:"open"});break;default:return null}var r;let i=n.getMeta(e);return t instanceof M&&(i||(i=k(s,t.unserializedId),n.add(e,i)),t.mirror.add(s,Object.assign({},i))),s}function L(e,n=function(){return new t}(),o=new M){return function e(t,s){const r=S(t,o,n,s);if(null!==r)if("IFRAME"!==(null==s?void 0:s.nodeName)&&t.nodeType!==E.DOCUMENT_FRAGMENT_NODE&&(null==s||s.appendChild(r),r.parentNode=s,r.parentElement=s),"IFRAME"===t.nodeName){const n=t.contentDocument;n&&e(n,r)}else t.nodeType!==E.DOCUMENT_NODE&&t.nodeType!==E.ELEMENT_NODE&&t.nodeType!==E.DOCUMENT_FRAGMENT_NODE||(t.nodeType===E.ELEMENT_NODE&&t.shadowRoot&&e(t.shadowRoot,r),t.childNodes.forEach((t=>e(t,r))))}(e,null),o}function F(){return new U}class U{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){var t;if(!e)return-1;const n=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=n?n:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}replace(e,t){const n=this.getNode(e);if(n){const e=this.nodeMetaMap.get(n);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function k(t,n){switch(t.RRNodeType){case e.Document:return{id:n,type:t.RRNodeType,childNodes:[]};case e.DocumentType:{const e=t;return{id:n,type:t.RRNodeType,name:e.name,publicId:e.publicId,systemId:e.systemId}}case e.Element:return{id:n,type:t.RRNodeType,tagName:t.tagName.toLowerCase(),attributes:{},childNodes:[]};case e.Text:case e.Comment:return{id:n,type:t.RRNodeType,textContent:t.textContent||""};case e.CDATA:return{id:n,type:t.RRNodeType,textContent:""}}}function B(e,t){return G(e,t,"")}function G(t,n,o){let s=`${o}${n.getId(t)} ${t.toString()}\n`;if(t.RRNodeType===e.Element){const e=t;e.shadowRoot&&(s+=G(e.shadowRoot,n,o+"  "))}for(const e of t.childNodes)s+=G(e,n,o+"  ");return"IFRAME"===t.nodeName&&(s+=G(t.contentDocument,n,o+"  ")),s}export{N as BaseRRCDATASectionImpl,m as BaseRRCommentImpl,c as BaseRRDocumentImpl,l as BaseRRDocumentTypeImpl,h as BaseRRElementImpl,u as BaseRRMediaElementImpl,d as BaseRRNode,p as BaseRRTextImpl,f as ClassList,U as Mirror,E as NodeType,v as RRCDATASection,x as RRCanvasElement,_ as RRComment,M as RRDocument,y as RRDocumentType,w as RRElement,I as RRIFrameElement,O as RRMediaElement,d as RRNode,A as RRStyleElement,b as RRText,L as buildFromDom,S as buildFromNode,F as createMirror,C as createOrGetNode,D as diff,k as getDefaultSN,B as printRRDom};
//# sourceMappingURL=rrdom.min.js.map
